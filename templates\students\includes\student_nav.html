{% load i18n %}

<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-user-graduate me-2"></i>{% trans "Student Management" %}
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="list-group list-group-flush">
            <a href="{% url 'students:dashboard' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                <i class="fas fa-tachometer-alt me-2"></i>{% trans "Dashboard" %}
            </a>
            <a href="{% url 'students:list' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'list' %}active{% endif %}">
                <i class="fas fa-users me-2"></i>{% trans "Students" %}
            </a>
            <a href="{% url 'students:class_list' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'class_list' %}active{% endif %}">
                <i class="fas fa-chalkboard me-2"></i>{% trans "Classes" %}
            </a>
            <a href="{% url 'students:grade_list' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'grade_list' %}active{% endif %}">
                <i class="fas fa-layer-group me-2"></i>{% trans "Grades" %}
            </a>
            <a href="{% url 'students:parent_list' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'parent_list' %}active{% endif %}">
                <i class="fas fa-user-friends me-2"></i>{% trans "Parents" %}
            </a>
            <a href="{% url 'students:documents' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'documents' %}active{% endif %}">
                <i class="fas fa-file-alt me-2"></i>{% trans "Documents" %}
            </a>
            <a href="{% url 'students:transfers' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'transfers' %}active{% endif %}">
                <i class="fas fa-exchange-alt me-2"></i>{% trans "Transfers" %}
            </a>
            <a href="{% url 'students:infractions' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'infractions' %}active{% endif %}">
                <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Infractions" %}
            </a>
            <a href="{% url 'students:reports' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'reports' %}active{% endif %}">
                <i class="fas fa-chart-bar me-2"></i>{% trans "Reports" %}
            </a>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">
            <i class="fas fa-tools me-2"></i>{% trans "Quick Tools" %}
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="list-group list-group-flush">
            <a href="{% url 'students:add_student' %}" class="list-group-item list-group-item-action">
                <i class="fas fa-user-plus me-2"></i>{% trans "Add Student" %}
            </a>
            <a href="{% url 'students:bulk_import' %}" class="list-group-item list-group-item-action">
                <i class="fas fa-upload me-2"></i>{% trans "Bulk Import" %}
            </a>
            <a href="{% url 'students:id_cards' %}" class="list-group-item list-group-item-action">
                <i class="fas fa-id-card me-2"></i>{% trans "ID Cards" %}
            </a>
            <a href="{% url 'students:certificates' %}" class="list-group-item list-group-item-action">
                <i class="fas fa-certificate me-2"></i>{% trans "Certificates" %}
            </a>
        </div>
    </div>
</div>