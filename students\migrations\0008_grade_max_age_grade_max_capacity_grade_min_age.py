# Generated by Django 5.2.4 on 2025-07-30 12:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("students", "0007_add_disciplinary_models"),
    ]

    operations = [
        migrations.AddField(
            model_name="grade",
            name="max_age",
            field=models.PositiveIntegerField(
                blank=True, null=True, verbose_name="Maximum Age"
            ),
        ),
        migrations.AddField(
            model_name="grade",
            name="max_capacity",
            field=models.PositiveIntegerField(
                default=30, verbose_name="Maximum Capacity"
            ),
        ),
        migrations.AddField(
            model_name="grade",
            name="min_age",
            field=models.PositiveIntegerField(
                blank=True, null=True, verbose_name="Minimum Age"
            ),
        ),
    ]
