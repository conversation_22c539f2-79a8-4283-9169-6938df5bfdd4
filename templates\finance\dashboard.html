{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Finance Dashboard" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .finance-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    
    .finance-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }
    
    .revenue-card {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    }
    
    .expense-card {
        background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
    }
    
    .profit-card {
        background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
    }
    
    .pending-card {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-line text-primary me-2"></i>{% trans "Finance Dashboard" %}
                    </h2>
                    <p class="text-muted">{% trans "Monitor financial performance and manage accounts" %}</p>
                </div>
                <div>
                    <a href="{% url 'finance:add_journal_entry' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>{% trans "New Transaction" %}
                    </a>
                    <a href="{% url 'finance:financial_reports' %}" class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export Report" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Financial Overview Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card finance-card revenue-card">
                <div class="card-body text-center text-white">
                    <i class="fas fa-arrow-up fa-2x mb-2"></i>
                    <h3 class="mb-1">${{ total_revenue|floatformat:0|default:"0" }}</h3>
                    <p class="mb-0">{% trans "Total Revenue" %}</p>
                    <small class="opacity-75">{% trans "This Month" %}</small>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card finance-card expense-card">
                <div class="card-body text-center text-white">
                    <i class="fas fa-arrow-down fa-2x mb-2"></i>
                    <h3 class="mb-1">${{ total_expenses|floatformat:0|default:"0" }}</h3>
                    <p class="mb-0">{% trans "Total Expenses" %}</p>
                    <small class="opacity-75">{% trans "This Month" %}</small>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card finance-card profit-card">
                <div class="card-body text-center text-white">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h3 class="mb-1">${{ net_profit|floatformat:0|default:"0" }}</h3>
                    <p class="mb-0">{% trans "Net Profit" %}</p>
                    <small class="opacity-75">{% trans "This Month" %}</small>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card finance-card pending-card">
                <div class="card-body text-center text-white">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h3 class="mb-1">${{ pending_payments|floatformat:0|default:"0" }}</h3>
                    <p class="mb-0">{% trans "Pending Payments" %}</p>
                    <small class="opacity-75">{% trans "Outstanding" %}</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts and Analytics -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card finance-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area me-2"></i>{% trans "Revenue vs Expenses" %}
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueExpenseChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card finance-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>{% trans "Expense Categories" %}
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="expenseCategoriesChart" height="150"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Transactions and Quick Actions -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card finance-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>{% trans "Recent Transactions" %}
                        </h5>
                        <a href="{% url 'finance:daily_entries' %}" class="btn btn-sm btn-outline-primary">
                            {% trans "View All" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Description" %}</th>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Status" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>{{ transaction.entry_date|date:"Y-m-d" }}</td>
                                    <td>{{ transaction.description|default:transaction.account.name }}</td>
                                    <td>
                                        {% if transaction.debit_amount > 0 %}
                                            <span class="badge bg-danger">{% trans "Expense" %}</span>
                                        {% else %}
                                            <span class="badge bg-success">{% trans "Income" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transaction.debit_amount > 0 %}
                                            <span class="text-danger">-${{ transaction.debit_amount|floatformat:0 }}</span>
                                        {% else %}
                                            <span class="text-success">+${{ transaction.credit_amount|floatformat:0 }}</span>
                                        {% endif %}
                                    </td>
                                    <td><span class="badge bg-success">{% trans "Completed" %}</span></td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted py-4">
                                        <i class="fas fa-receipt fa-2x mb-2"></i>
                                        <br>
                                        {% trans "No recent transactions found." %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card finance-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'finance:fees_payment' %}" class="btn btn-primary">
                            <i class="fas fa-money-bill me-2"></i>{% trans "Record Payment" %}
                        </a>
                        <a href="{% url 'finance:receipt_voucher' %}" class="btn btn-success">
                            <i class="fas fa-receipt me-2"></i>{% trans "Receipt Voucher" %}
                        </a>
                        <a href="{% url 'finance:exchange_permission' %}" class="btn btn-warning">
                            <i class="fas fa-exchange-alt me-2"></i>{% trans "Exchange Permission" %}
                        </a>
                        <a href="{% url 'finance:trial_balance' %}" class="btn btn-info">
                            <i class="fas fa-balance-scale me-2"></i>{% trans "Trial Balance" %}
                        </a>
                        <a href="{% url 'finance:account_statement' %}" class="btn btn-secondary">
                            <i class="fas fa-file-alt me-2"></i>{% trans "Account Statement" %}
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Financial Alerts -->
            <div class="card finance-card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Financial Alerts" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if overdue_payments %}
                        <div class="alert alert-warning alert-sm">
                            <i class="fas fa-clock me-2"></i>
                            <strong>{{ overdue_payments|default:0 }}</strong> {% trans "overdue payments" %}
                        </div>
                    {% endif %}
                    {% if monthly_target_percentage %}
                        <div class="alert alert-info alert-sm">
                            <i class="fas fa-chart-line me-2"></i>
                            {% trans "Monthly target:" %} {{ monthly_target_percentage|default:0 }}% {% trans "achieved" %}
                        </div>
                    {% endif %}
                    {% if accounts_reconciled %}
                        <div class="alert alert-success alert-sm">
                            <i class="fas fa-check me-2"></i>
                            {% trans "All accounts reconciled" %}
                        </div>
                    {% endif %}
                    {% if not overdue_payments and not monthly_target_percentage and not accounts_reconciled %}
                        <div class="alert alert-info alert-sm">
                            <i class="fas fa-info-circle me-2"></i>
                            {% trans "No financial alerts at this time" %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue vs Expenses Chart
    const revenueExpenseCtx = document.getElementById('revenueExpenseChart');
    if (revenueExpenseCtx) {
        new Chart(revenueExpenseCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: '{% trans "Revenue" %}',
                    data: [120000, 125000, 118000, 135000, 142000, 138000, 145000, 152000, 148000, 155000, 162000, 158000],
                    borderColor: '#11998e',
                    backgroundColor: 'rgba(17, 153, 142, 0.1)',
                    tension: 0.4
                }, {
                    label: '{% trans "Expenses" %}',
                    data: [85000, 88000, 82000, 92000, 95000, 90000, 98000, 102000, 96000, 105000, 108000, 103000],
                    borderColor: '#fc466b',
                    backgroundColor: 'rgba(252, 70, 107, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Expense Categories Chart
    const expenseCategoriesCtx = document.getElementById('expenseCategoriesChart');
    if (expenseCategoriesCtx) {
        new Chart(expenseCategoriesCtx, {
            type: 'doughnut',
            data: {
                labels: ['{% trans "Salaries" %}', '{% trans "Utilities" %}', '{% trans "Supplies" %}', '{% trans "Maintenance" %}', '{% trans "Other" %}'],
                datasets: [{
                    data: [45, 20, 15, 12, 8],
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
});
</script>
{% endblock %}
