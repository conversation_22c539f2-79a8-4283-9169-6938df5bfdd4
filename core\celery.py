"""
Celery configuration for School ERP system
"""
import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')

app = Celery('school_erp')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery beat schedule for periodic tasks
app.conf.beat_schedule = {
    'send-daily-attendance-reports': {
        'task': 'core.tasks.send_daily_attendance_reports',
        'schedule': 60.0 * 60.0 * 24.0,  # Daily at midnight
    },
    'backup-database': {
        'task': 'core.tasks.backup_database',
        'schedule': 60.0 * 60.0 * 24.0 * 7.0,  # Weekly
    },
    'cleanup-old-logs': {
        'task': 'core.tasks.cleanup_old_logs',
        'schedule': 60.0 * 60.0 * 24.0,  # Daily
    },
    'send-fee-reminders': {
        'task': 'finance.tasks.send_fee_reminders',
        'schedule': 60.0 * 60.0 * 24.0,  # Daily
    },
    'generate-monthly-reports': {
        'task': 'reports.tasks.generate_monthly_reports',
        'schedule': 60.0 * 60.0 * 24.0 * 30.0,  # Monthly
    },
}

app.conf.timezone = 'UTC'

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')