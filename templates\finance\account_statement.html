{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}{% trans "Account Statement" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .finance-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .finance-card:hover {
        transform: translateY(-2px);
    }
    .statement-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .transaction-row {
        border-bottom: 1px solid #eee;
        transition: background-color 0.2s;
    }
    .transaction-row:hover {
        background-color: #f8f9fa;
    }
    .debit {
        color: #dc3545;
    }
    .credit {
        color: #28a745;
    }
    .balance {
        font-weight: bold;
        color: #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-file-invoice-dollar text-primary me-2"></i>{% trans "Account Statement" %}
                    </h2>
                    <p class="text-muted">{% trans "View detailed account transactions and balances" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2">
                        <i class="fas fa-download me-2"></i>{% trans "Export PDF" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-excel me-2"></i>{% trans "Export Excel" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card finance-card">
                <div class="card-body">
                    <h5 class="card-title mb-3">{% trans "Filter Options" %}</h5>
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="account" class="form-label">{% trans "Account" %}</label>
                            <select class="form-select" id="account" name="account">
                                <option value="">{% trans "All Accounts" %}</option>
                                <option value="cash">{% trans "Cash Account" %}</option>
                                <option value="bank">{% trans "Bank Account" %}</option>
                                <option value="receivables">{% trans "Accounts Receivable" %}</option>
                                <option value="payables">{% trans "Accounts Payable" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                            <input type="date" class="form-control" id="date_from" name="date_from">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                            <input type="date" class="form-control" id="date_to" name="date_to">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card finance-card text-center">
                <div class="card-body">
                    <i class="fas fa-wallet fa-2x text-primary mb-2"></i>
                    <h4 class="balance">$125,450.00</h4>
                    <p class="mb-0">{% trans "Current Balance" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card finance-card text-center">
                <div class="card-body">
                    <i class="fas fa-arrow-up fa-2x text-success mb-2"></i>
                    <h4 class="credit">$45,230.00</h4>
                    <p class="mb-0">{% trans "Total Credits" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card finance-card text-center">
                <div class="card-body">
                    <i class="fas fa-arrow-down fa-2x text-danger mb-2"></i>
                    <h4 class="debit">$32,180.00</h4>
                    <p class="mb-0">{% trans "Total Debits" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card finance-card text-center">
                <div class="card-body">
                    <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                    <h4 class="text-info">156</h4>
                    <p class="mb-0">{% trans "Total Transactions" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statement Table -->
    <div class="row">
        <div class="col-12">
            <div class="card finance-card">
                <div class="statement-header card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "Transaction Details" %}
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Reference" %}</th>
                                    <th>{% trans "Description" %}</th>
                                    <th>{% trans "Account" %}</th>
                                    <th class="text-end">{% trans "Debit" %}</th>
                                    <th class="text-end">{% trans "Credit" %}</th>
                                    <th class="text-end">{% trans "Balance" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Sample transactions -->
                                <tr class="transaction-row">
                                    <td>2025-01-13</td>
                                    <td>INV-2025-001</td>
                                    <td>{% trans "Student fees payment" %}</td>
                                    <td>{% trans "Cash Account" %}</td>
                                    <td class="text-end">-</td>
                                    <td class="text-end credit">$1,500.00</td>
                                    <td class="text-end balance">$125,450.00</td>
                                </tr>
                                <tr class="transaction-row">
                                    <td>2025-01-12</td>
                                    <td>EXP-2025-015</td>
                                    <td>{% trans "Office supplies purchase" %}</td>
                                    <td>{% trans "Expenses Account" %}</td>
                                    <td class="text-end debit">$350.00</td>
                                    <td class="text-end">-</td>
                                    <td class="text-end balance">$123,950.00</td>
                                </tr>
                                <tr class="transaction-row">
                                    <td>2025-01-11</td>
                                    <td>SAL-2025-008</td>
                                    <td>{% trans "Teacher salary payment" %}</td>
                                    <td>{% trans "Payroll Account" %}</td>
                                    <td class="text-end debit">$2,800.00</td>
                                    <td class="text-end">-</td>
                                    <td class="text-end balance">$124,300.00</td>
                                </tr>
                                <tr class="transaction-row">
                                    <td>2025-01-10</td>
                                    <td>INV-2025-002</td>
                                    <td>{% trans "Registration fees" %}</td>
                                    <td>{% trans "Revenue Account" %}</td>
                                    <td class="text-end">-</td>
                                    <td class="text-end credit">$2,200.00</td>
                                    <td class="text-end balance">$127,100.00</td>
                                </tr>
                                <tr class="transaction-row">
                                    <td>2025-01-09</td>
                                    <td>UTL-2025-003</td>
                                    <td>{% trans "Electricity bill payment" %}</td>
                                    <td>{% trans "Utilities Account" %}</td>
                                    <td class="text-end debit">$450.00</td>
                                    <td class="text-end">-</td>
                                    <td class="text-end balance">$124,900.00</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">{% trans "Showing 5 of 156 transactions" %}</span>
                        <nav>
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item disabled">
                                    <span class="page-link">{% trans "Previous" %}</span>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link">1</span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">2</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">3</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">{% trans "Next" %}</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize date inputs with current month
    document.addEventListener('DOMContentLoaded', function() {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        
        document.getElementById('date_from').value = firstDay.toISOString().split('T')[0];
        document.getElementById('date_to').value = lastDay.toISOString().split('T')[0];
    });
</script>
{% endblock %}
