{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Student Management Dashboard" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2>
                <i class="fas fa-user-graduate text-primary me-2"></i>{% trans "Student Management Dashboard" %}
            </h2>
            <p class="text-muted">{% trans "Overview of student affairs and quick access to common tasks" %}</p>
        </div>
    </div>

    <!-- Statistics cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center py-4">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h2 class="display-4">{{ total_students|default:"0" }}</h2>
                    <p class="mb-0">{% trans "Total Students" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center py-4">
                    <i class="fas fa-user-plus fa-2x mb-2"></i>
                    <h2 class="display-4">{{ new_admissions|default:"0" }}</h2>
                    <p class="mb-0">{% trans "New Admissions" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center py-4">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h2 class="display-4">{{ active_students|default:"0" }}</h2>
                    <p class="mb-0">{% trans "Active Students" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center py-4">
                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                    <h2 class="display-4">{{ graduating_students|default:"0" }}</h2>
                    <p class="mb-0">{% trans "Graduating" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3">{% trans "Quick Actions" %}</h4>
        </div>
        <div class="col-md-2">
            <a href="{% url 'students:add_student' %}" class="card text-decoration-none mb-3">
                <div class="card-body text-center py-4 bg-pink text-white">
                    <i class="fas fa-user-plus fa-2x mb-2"></i>
                    <h5 class="mb-0">{% trans "Add Student" %}</h5>
                </div>
            </a>
        </div>
        <div class="col-md-2">
            <a href="{% url 'students:list' %}" class="card text-decoration-none mb-3">
                <div class="card-body text-center py-4 bg-pink text-white">
                    <i class="fas fa-list fa-2x mb-2"></i>
                    <h5 class="mb-0">{% trans "View All" %}</h5>
                </div>
            </a>
        </div>
        <div class="col-md-2">
            <a href="{% url 'students:class_list' %}" class="card text-decoration-none mb-3">
                <div class="card-body text-center py-4 bg-pink text-white">
                    <i class="fas fa-chalkboard fa-2x mb-2"></i>
                    <h5 class="mb-0">{% trans "Classes" %}</h5>
                </div>
            </a>
        </div>
        <div class="col-md-2">
            <a href="{% url 'students:bulk_import' %}" class="card text-decoration-none mb-3">
                <div class="card-body text-center py-4 bg-pink text-white">
                    <i class="fas fa-file-import fa-2x mb-2"></i>
                    <h5 class="mb-0">{% trans "Bulk Import" %}</h5>
                </div>
            </a>
        </div>
        <div class="col-md-2">
            <a href="{% url 'students:reports' %}" class="card text-decoration-none mb-3">
                <div class="card-body text-center py-4 bg-pink text-white">
                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                    <h5 class="mb-0">{% trans "Reports" %}</h5>
                </div>
            </a>
        </div>
        <div class="col-md-2">
            <a href="{% url 'students:id_cards' %}" class="card text-decoration-none mb-3">
                <div class="card-body text-center py-4 bg-pink text-white">
                    <i class="fas fa-id-card fa-2x mb-2"></i>
                    <h5 class="mb-0">{% trans "ID Cards" %}</h5>
                </div>
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Recent Students -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">{% trans "Recent Students" %}</h5>
                    <a href="{% url 'students:list' %}" class="btn btn-sm btn-outline-primary">{% trans "View All" %}</a>
                </div>
                <div class="card-body p-0">
                    {% if recent_students %}
                        <div class="list-group list-group-flush">
                            {% for student in recent_students %}
                                <a href="{% url 'students:detail' student.id %}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar me-3">
                                                {% if student.photo %}
                                                    <img src="{{ student.photo.url }}" alt="{{ student.full_name }}" class="rounded-circle" width="40" height="40">
                                                {% else %}
                                                    <div class="avatar-placeholder rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                        {{ student.first_name|first }}{{ student.last_name|first }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ student.full_name }}</h6>
                                                <small class="text-muted">{{ student.student_id }} | {{ student.current_class }}</small>
                                            </div>
                                        </div>
                                        <small>{{ student.admission_date|date:"M d" }}</small>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="p-4 text-center">
                            <p class="text-muted">{% trans "No recent students found" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Pending Tasks -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Pending Tasks" %}</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% if pending_admissions > 0 %}
                            <a href="{% url 'students:electronic_registration' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-user-plus text-primary me-2"></i>
                                    {% trans "Pending Admissions" %}
                                </div>
                                <span class="badge bg-primary rounded-pill">{{ pending_admissions }}</span>
                            </a>
                        {% endif %}
                        
                        {% if pending_documents > 0 %}
                            <a href="{% url 'students:documents' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-file-alt text-warning me-2"></i>
                                    {% trans "Pending Documents" %}
                                </div>
                                <span class="badge bg-warning rounded-pill">{{ pending_documents }}</span>
                            </a>
                        {% endif %}
                        
                        {% if pending_fees > 0 %}
                            <a href="{% url 'finance:fees_payment' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-money-bill-wave text-danger me-2"></i>
                                    {% trans "Pending Fees" %}
                                </div>
                                <span class="badge bg-danger rounded-pill">{{ pending_fees }}</span>
                            </a>
                        {% endif %}
                        
                        {% if transfer_requests > 0 %}
                            <a href="{% url 'students:transfers' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-exchange-alt text-info me-2"></i>
                                    {% trans "Transfer Requests" %}
                                </div>
                                <span class="badge bg-info rounded-pill">{{ transfer_requests }}</span>
                            </a>
                        {% endif %}
                        
                        {% if id_card_requests > 0 %}
                            <a href="{% url 'students:id_cards' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-id-card text-success me-2"></i>
                                    {% trans "ID Card Requests" %}
                                </div>
                                <span class="badge bg-success rounded-pill">{{ id_card_requests }}</span>
                            </a>
                        {% endif %}
                        
                        {% if not pending_admissions and not pending_documents and not pending_fees and not transfer_requests and not id_card_requests %}
                            <div class="list-group-item text-center py-4">
                                <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                                <p class="mb-0">{% trans "No pending tasks!" %}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}