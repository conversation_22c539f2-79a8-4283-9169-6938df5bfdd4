{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}
    {% if student %}
        {% trans "Edit Student" %} - {{ student.first_name }} {{ student.last_name }}
    {% else %}
        {% trans "Add New Student" %}
    {% endif %}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .form-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #007bff;
    }
    .form-section h5 {
        color: #007bff;
        margin-bottom: 1rem;
    }
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: border-color 0.3s ease;
    }
    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    .btn-save {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: bold;
    }
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
    .photo-upload {
        border: 2px dashed #dee2e6;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        transition: border-color 0.3s ease;
    }
    .photo-upload:hover {
        border-color: #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'students:list' %}">{% trans "Students" %}</a></li>
                    {% if student %}
                        <li class="breadcrumb-item"><a href="{% url 'students:detail' student.pk %}">{{ student.first_name }} {{ student.last_name }}</a></li>
                        <li class="breadcrumb-item active">{% trans "Edit" %}</li>
                    {% else %}
                        <li class="breadcrumb-item active">{% trans "Add New Student" %}</li>
                    {% endif %}
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-user-plus text-primary me-2"></i>
                        {% if student %}
                            {% trans "Edit Student" %}
                        {% else %}
                            {% trans "Add New Student" %}
                        {% endif %}
                    </h2>
                    <p class="text-muted">
                        {% if student %}
                            {% trans "Update student information and academic details" %}
                        {% else %}
                            {% trans "Enter student information and academic details" %}
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{% url 'students:list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to List" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <form method="post" enctype="multipart/form-data" id="studentForm">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Personal Information Section -->
                <div class="form-section">
                    <h5><i class="fas fa-user me-2"></i>{% trans "Personal Information" %}</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label required-field">{% trans "First Name" %}</label>
                            {{ form.first_name|add_class:"form-control" }}
                            {% if form.first_name.errors %}
                                <div class="text-danger small">{{ form.first_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label required-field">{% trans "Last Name" %}</label>
                            {{ form.last_name|add_class:"form-control" }}
                            {% if form.last_name.errors %}
                                <div class="text-danger small">{{ form.last_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.date_of_birth.id_for_label }}" class="form-label required-field">{% trans "Date of Birth" %}</label>
                            {{ form.date_of_birth|add_class:"form-control" }}
                            {% if form.date_of_birth.errors %}
                                <div class="text-danger small">{{ form.date_of_birth.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.gender.id_for_label }}" class="form-label required-field">{% trans "Gender" %}</label>
                            {{ form.gender|add_class:"form-select" }}
                            {% if form.gender.errors %}
                                <div class="text-danger small">{{ form.gender.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.blood_group.id_for_label }}" class="form-label">{% trans "Blood Group" %}</label>
                            {{ form.blood_group|add_class:"form-control" }}
                            {% if form.blood_group.errors %}
                                <div class="text-danger small">{{ form.blood_group.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.religion.id_for_label }}" class="form-label">{% trans "Religion" %}</label>
                            {{ form.religion|add_class:"form-control" }}
                            {% if form.religion.errors %}
                                <div class="text-danger small">{{ form.religion.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Contact Information Section -->
                <div class="form-section">
                    <h5><i class="fas fa-phone me-2"></i>{% trans "Contact Information" %}</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">{% trans "Phone Number" %}</label>
                            {{ form.phone|add_class:"form-control" }}
                            {% if form.phone.errors %}
                                <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">{% trans "Email Address" %}</label>
                            {{ form.email|add_class:"form-control" }}
                            {% if form.email.errors %}
                                <div class="text-danger small">{{ form.email.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-12 mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">{% trans "Address" %}</label>
                            {{ form.address|add_class:"form-control" }}
                            {% if form.address.errors %}
                                <div class="text-danger small">{{ form.address.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.city.id_for_label }}" class="form-label">{% trans "City" %}</label>
                            {{ form.city|add_class:"form-control" }}
                            {% if form.city.errors %}
                                <div class="text-danger small">{{ form.city.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.state.id_for_label }}" class="form-label">{% trans "State" %}</label>
                            {{ form.state|add_class:"form-control" }}
                            {% if form.state.errors %}
                                <div class="text-danger small">{{ form.state.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.country.id_for_label }}" class="form-label">{% trans "Country" %}</label>
                            {{ form.country|add_class:"form-control" }}
                            {% if form.country.errors %}
                                <div class="text-danger small">{{ form.country.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Academic Information Section -->
                <div class="form-section">
                    <h5><i class="fas fa-graduation-cap me-2"></i>{% trans "Academic Information" %}</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.admission_number.id_for_label }}" class="form-label">{% trans "Admission Number" %}</label>
                            {{ form.admission_number|add_class:"form-control" }}
                            {% if form.admission_number.errors %}
                                <div class="text-danger small">{{ form.admission_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.admission_date.id_for_label }}" class="form-label required-field">{% trans "Admission Date" %}</label>
                            {{ form.admission_date|add_class:"form-control" }}
                            {% if form.admission_date.errors %}
                                <div class="text-danger small">{{ form.admission_date.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.current_class.id_for_label }}" class="form-label required-field">{% trans "Current Class" %}</label>
                            {{ form.current_class|add_class:"form-select" }}
                            {% if form.current_class.errors %}
                                <div class="text-danger small">{{ form.current_class.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.roll_number.id_for_label }}" class="form-label">{% trans "Roll Number" %}</label>
                            {{ form.roll_number|add_class:"form-control" }}
                            {% if form.roll_number.errors %}
                                <div class="text-danger small">{{ form.roll_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-12 mb-3">
                            <label for="{{ form.previous_school.id_for_label }}" class="form-label">{% trans "Previous School" %}</label>
                            {{ form.previous_school|add_class:"form-control" }}
                            {% if form.previous_school.errors %}
                                <div class="text-danger small">{{ form.previous_school.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Photo Upload Section -->
                <div class="form-section">
                    <h5><i class="fas fa-camera me-2"></i>{% trans "Student Photo" %}</h5>
                    <div class="photo-upload">
                        {% if student and student.photo %}
                            <img src="{{ student.photo.url }}" alt="Student Photo" class="img-fluid rounded mb-3" style="max-height: 200px;">
                        {% else %}
                            <i class="fas fa-user fa-5x text-muted mb-3"></i>
                        {% endif %}
                        <div>
                            {{ form.photo|add_class:"form-control" }}
                            {% if form.photo.errors %}
                                <div class="text-danger small mt-2">{{ form.photo.errors.0 }}</div>
                            {% endif %}
                            <small class="text-muted">{% trans "Upload a clear photo of the student (JPG, PNG)" %}</small>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle me-2"></i>{% trans "Additional Information" %}</h5>
                    <div class="mb-3">
                        <label for="{{ form.medical_conditions.id_for_label }}" class="form-label">{% trans "Medical Conditions" %}</label>
                        {{ form.medical_conditions|add_class:"form-control" }}
                        {% if form.medical_conditions.errors %}
                            <div class="text-danger small">{{ form.medical_conditions.errors.0 }}</div>
                        {% endif %}
                        <small class="text-muted">{% trans "Any medical conditions or allergies" %}</small>
                    </div>
                    <div class="mb-3">
                        <label for="{{ form.emergency_contact.id_for_label }}" class="form-label">{% trans "Emergency Contact" %}</label>
                        {{ form.emergency_contact|add_class:"form-control" }}
                        {% if form.emergency_contact.errors %}
                            <div class="text-danger small">{{ form.emergency_contact.errors.0 }}</div>
                        {% endif %}
                        <small class="text-muted">{% trans "Emergency contact person and phone number" %}</small>
                    </div>
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">{% trans "Notes" %}</label>
                        {{ form.notes|add_class:"form-control" }}
                        {% if form.notes.errors %}
                            <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                        {% endif %}
                        <small class="text-muted">{% trans "Additional notes about the student" %}</small>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-section">
                    <h5><i class="fas fa-save me-2"></i>{% trans "Save Student" %}</h5>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-save">
                            <i class="fas fa-save me-2"></i>
                            {% if student %}
                                {% trans "Update Student" %}
                            {% else %}
                                {% trans "Add Student" %}
                            {% endif %}
                        </button>
                        <a href="{% url 'students:list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                        </a>
                        {% if student %}
                            <a href="{% url 'students:detail' student.pk %}" class="btn btn-outline-info">
                                <i class="fas fa-eye me-2"></i>{% trans "View Details" %}
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.getElementById('studentForm');
        
        form.addEventListener('submit', function(e) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('{% trans "Please fill in all required fields." %}');
            }
        });

        // Auto-generate admission number if empty
        const admissionNumberField = document.getElementById('{{ form.admission_number.id_for_label }}');
        if (admissionNumberField && !admissionNumberField.value) {
            const currentYear = new Date().getFullYear();
            const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
            admissionNumberField.value = `ADM${currentYear}${randomNum}`;
        }

        // Photo preview
        const photoInput = document.getElementById('{{ form.photo.id_for_label }}');
        if (photoInput) {
            photoInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const photoUpload = document.querySelector('.photo-upload');
                        const existingImg = photoUpload.querySelector('img');
                        const existingIcon = photoUpload.querySelector('.fa-user');
                        
                        if (existingImg) {
                            existingImg.src = e.target.result;
                        } else {
                            if (existingIcon) {
                                existingIcon.remove();
                            }
                            const img = document.createElement('img');
                            img.src = e.target.result;
                            img.className = 'img-fluid rounded mb-3';
                            img.style.maxHeight = '200px';
                            photoUpload.insertBefore(img, photoUpload.firstChild);
                        }
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
    });
</script>
{% endblock %}
