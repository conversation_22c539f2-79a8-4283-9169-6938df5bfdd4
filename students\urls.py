from django.urls import path
from . import views

app_name = 'students'

urlpatterns = [
    # Student Management
    path('', views.StudentListView.as_view(), name='list'),
    path('dashboard/', views.StudentDashboardView.as_view(), name='dashboard'),
    path('add/', views.StudentCreateView.as_view(), name='add_student'),
    path('bulk-import/', views.BulkImportView.as_view(), name='bulk_import'),
    path('<int:pk>/', views.StudentDetailView.as_view(), name='detail'),
    path('<int:pk>/edit/', views.StudentUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.StudentDeleteView.as_view(), name='delete'),
    path('advanced-search/', views.StudentAdvancedSearchView.as_view(), name='advanced_search'),
    path('distribute/', views.DistributeStudentsView.as_view(), name='distribute'),
    path('transfer/', views.StudentTransferView.as_view(), name='transfer'),
    path('cards/', views.StudentCardsView.as_view(), name='cards'),
    path('reports/', views.StudentReportsView.as_view(), name='reports'),
    path('id-cards/', views.StudentIDCardsView.as_view(), name='id_cards'),
    path('certificates/', views.StudentCertificatesView.as_view(), name='certificates'),
    path('certificates/preview/', views.CertificatePreviewView.as_view(), name='certificate_preview'),
    path('certificates/view/<str:certificate_id>/', views.CertificateViewView.as_view(), name='certificate_view'),
    path('certificates/download/<str:certificate_id>/', views.CertificateDownloadView.as_view(), name='certificate_download'),
    path('certificates/print/<str:certificate_id>/', views.CertificatePrintView.as_view(), name='certificate_print'),

    # Parent Management
    path('parents/', views.ParentListView.as_view(), name='parent_list'),
    path('parents/add/', views.ParentCreateView.as_view(), name='add_parent'),
    path('parents/<int:pk>/', views.ParentDetailView.as_view(), name='parent_detail'),
    path('parents/<int:pk>/edit/', views.ParentUpdateView.as_view(), name='parent_edit'),
    path('parents/manual/', views.AddParentManualView.as_view(), name='add_parent_manual'),

    # Class Management
    path('classes/', views.ClassListView.as_view(), name='class_list'),
    path('classes/add/', views.ClassCreateView.as_view(), name='class_add'),
    path('classes/<int:pk>/', views.ClassDetailView.as_view(), name='class_detail'),
    path('classes/<int:pk>/edit/', views.ClassUpdateView.as_view(), name='class_edit'),
    path('classes/<int:pk>/delete/', views.ClassDeleteView.as_view(), name='class_delete'),

    # Grade Management
    path('grades/', views.GradeListView.as_view(), name='grade_list'),
    path('grades/add/', views.GradeCreateView.as_view(), name='grade_add'),
    path('grades/<int:pk>/edit/', views.GradeUpdateView.as_view(), name='grade_edit'),

    # Student Affairs
    path('infractions/', views.StudentInfractionsView.as_view(), name='infractions'),
    path('infractions/add/', views.StudentInfractionCreateView.as_view(), name='add_infraction'),
    path('infractions/<int:pk>/edit/', views.StudentInfractionUpdateView.as_view(), name='edit_infraction'),
    path('suspension/', views.SuspensionAndBlockView.as_view(), name='suspension'),
    path('suspensions/', views.StudentSuspensionListView.as_view(), name='suspension_list'),
    path('disciplinary-actions/', views.DisciplinaryActionListView.as_view(), name='disciplinary_actions'),
    path('disciplinary-actions/add/', views.DisciplinaryActionCreateView.as_view(), name='add_disciplinary_action'),
    path('discipline-reports/', views.DisciplineReportView.as_view(), name='discipline_reports'),
    path('discipline-reports/<uuid:pk>/', views.DisciplineReportDetailView.as_view(), name='discipline_report_detail'),
    path('vacation-requests/', views.VacationRequestsView.as_view(), name='vacation_requests'),
    path('vacation-requests/add/', views.VacationRequestCreateView.as_view(), name='add_vacation_request'),
    path('vacation-requests/approve/', views.ApproveVacationRequestsView.as_view(), name='approve_vacation'),
    path('vacation-calendar/', views.VacationCalendarView.as_view(), name='vacation_calendar'),
    path('vacation-reports/', views.VacationReportsView.as_view(), name='vacation_reports'),
    path('vacation-reports/<int:pk>/', views.VacationReportDetailView.as_view(), name='vacation_report_detail'),
    path('vacation-notifications/', views.VacationNotificationView.as_view(), name='vacation_notifications'),
    path('second-language/', views.StudentSecondLanguageView.as_view(), name='second_language'),

    # Documents and Attachments
    path('documents/', views.StudentDocumentsView.as_view(), name='documents'),
    path('documents/add/', views.StudentDocumentCreateView.as_view(), name='add_document'),
    path('documents/receiver/', views.StudentDocumentReceiverView.as_view(), name='document_receiver'),
    path('attachments/', views.StudentAttachmentsView.as_view(), name='attachments'),
    path('attachments/add/', views.StudentAttachmentCreateView.as_view(), name='add_attachment'),

    # Transfers
    path('transfers/', views.StudentTransferListView.as_view(), name='transfers'),
    path('transfers/add/', views.StudentTransferCreateView.as_view(), name='add_transfer'),
    path('transfers/<int:pk>/', views.TransferDetailView.as_view(), name='transfer_detail'),
    path('transfers/workflow/', views.TransferWorkflowView.as_view(), name='transfer_workflow'),
    path('transfers/documents/<int:pk>/', views.TransferDocumentView.as_view(), name='transfer_document'),
    path('academic-history/', views.AcademicHistoryView.as_view(), name='academic_history'),

    # Electronic Registration
    path('electronic-registration/', views.ElectronicRegistrationView.as_view(), name='electronic_registration'),
    path('electronic-registration/add/', views.ElectronicRegistrationCreateView.as_view(), name='add_electronic_registration'),

    # Admission and Registration System
    path('admission/online/', views.OnlineAdmissionView.as_view(), name='online_admission'),
    path('admission/success/', views.AdmissionSuccessView.as_view(), name='admission_success'),
    path('admission/documents/upload/', views.AdmissionDocumentUploadView.as_view(), name='admission_document_upload'),
    path('admission/documents/success/', views.AdmissionDocumentsSuccessView.as_view(), name='admission_documents_success'),
    path('admission/review/', views.AdmissionReviewListView.as_view(), name='admission_review_list'),
    path('admission/review/<int:pk>/', views.AdmissionReviewDetailView.as_view(), name='admission_review_detail'),
    path('admission/workflow/', views.AdmissionWorkflowView.as_view(), name='admission_workflow'),
    
    # Student ID Generation
    path('student-id/generation/', views.StudentIDGenerationView.as_view(), name='student_id_generation'),
    
    # Document Verification
    path('documents/verification/', views.DocumentVerificationView.as_view(), name='document_verification'),

    # Settings
    path('settings/', views.StudentSettingsView.as_view(), name='settings'),
    path('settings/fields/', views.StudentFieldsSettingsView.as_view(), name='fields_settings'),
    path('settings/tabs/', views.StudentTabsSettingsView.as_view(), name='tabs_settings'),
]