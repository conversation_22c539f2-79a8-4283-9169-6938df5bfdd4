{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Chart of Accounts" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .accounts-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .accounts-card:hover {
        transform: translateY(-2px);
    }
    .account-tree {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        max-height: 600px;
        overflow-y: auto;
    }
    .account-node {
        padding: 0.5rem;
        margin: 0.25rem 0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        border-left: 4px solid transparent;
    }
    .account-node:hover {
        background: #e3f2fd;
        border-left-color: #2196f3;
    }
    .account-node.selected {
        background: #e8f5e8;
        border-left-color: #4caf50;
    }
    .account-node.asset {
        border-left-color: #2196f3;
    }
    .account-node.liability {
        border-left-color: #ff9800;
    }
    .account-node.equity {
        border-left-color: #9c27b0;
    }
    .account-node.revenue {
        border-left-color: #4caf50;
    }
    .account-node.expense {
        border-left-color: #f44336;
    }
    .account-level-0 {
        font-weight: bold;
        font-size: 1.1rem;
        background: #e3f2fd;
        margin-left: 0;
    }
    .account-level-1 {
        margin-left: 1rem;
        font-weight: 600;
    }
    .account-level-2 {
        margin-left: 2rem;
    }
    .account-level-3 {
        margin-left: 3rem;
        font-size: 0.9rem;
    }
    .account-balance {
        font-weight: bold;
        color: #2e7d32;
    }
    .account-balance.negative {
        color: #d32f2f;
    }
    .account-code {
        font-family: 'Courier New', monospace;
        background: #f5f5f5;
        padding: 0.2rem 0.4rem;
        border-radius: 4px;
        font-size: 0.8rem;
    }
    .account-type-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
        border-radius: 12px;
        margin-left: 0.5rem;
    }
    .type-asset {
        background: #e3f2fd;
        color: #1976d2;
    }
    .type-liability {
        background: #fff3e0;
        color: #f57c00;
    }
    .type-equity {
        background: #f3e5f5;
        color: #7b1fa2;
    }
    .type-revenue {
        background: #e8f5e8;
        color: #388e3c;
    }
    .type-expense {
        background: #ffebee;
        color: #d32f2f;
    }
    .summary-widget {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-sitemap text-primary me-2"></i>{% trans "Chart of Accounts" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage your organization's account structure and balances" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#newAccountModal">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Account" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export Chart" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-2-4 col-md-6 mb-3">
            <div class="summary-widget">
                <i class="fas fa-building fa-2x mb-2"></i>
                <h3 class="mb-1">$2,450,000</h3>
                <p class="mb-0">{% trans "Total Assets" %}</p>
            </div>
        </div>
        <div class="col-xl-2-4 col-md-6 mb-3">
            <div class="summary-widget">
                <i class="fas fa-credit-card fa-2x mb-2"></i>
                <h3 class="mb-1">$850,000</h3>
                <p class="mb-0">{% trans "Total Liabilities" %}</p>
            </div>
        </div>
        <div class="col-xl-2-4 col-md-6 mb-3">
            <div class="summary-widget">
                <i class="fas fa-chart-pie fa-2x mb-2"></i>
                <h3 class="mb-1">$1,600,000</h3>
                <p class="mb-0">{% trans "Total Equity" %}</p>
            </div>
        </div>
        <div class="col-xl-2-4 col-md-6 mb-3">
            <div class="summary-widget">
                <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                <h3 class="mb-1">$3,200,000</h3>
                <p class="mb-0">{% trans "Total Revenue" %}</p>
            </div>
        </div>
        <div class="col-xl-2-4 col-md-6 mb-3">
            <div class="summary-widget">
                <i class="fas fa-receipt fa-2x mb-2"></i>
                <h3 class="mb-1">$2,100,000</h3>
                <p class="mb-0">{% trans "Total Expenses" %}</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Account Tree -->
        <div class="col-lg-8">
            <div class="card accounts-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-tree me-2"></i>{% trans "Account Hierarchy" %}
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" id="expandAll">
                                <i class="fas fa-expand-arrows-alt"></i> {% trans "Expand All" %}
                            </button>
                            <button class="btn btn-outline-secondary" id="collapseAll">
                                <i class="fas fa-compress-arrows-alt"></i> {% trans "Collapse All" %}
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="account-tree">
                        <!-- Assets -->
                        <div class="account-node account-level-0 asset" data-account-id="1000">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-chevron-down me-2"></i>
                                    <span class="account-code">1000</span>
                                    <strong>{% trans "Assets" %}</strong>
                                    <span class="account-type-badge type-asset">{% trans "Asset" %}</span>
                                </div>
                                <span class="account-balance">$2,450,000</span>
                            </div>
                        </div>
                        
                        <!-- Current Assets -->
                        <div class="account-node account-level-1 asset" data-account-id="1100">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-chevron-down me-2"></i>
                                    <span class="account-code">1100</span>
                                    {% trans "Current Assets" %}
                                </div>
                                <span class="account-balance">$850,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-2 asset" data-account-id="1110">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">1110</span>
                                    {% trans "Cash and Cash Equivalents" %}
                                </div>
                                <span class="account-balance">$125,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-2 asset" data-account-id="1120">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">1120</span>
                                    {% trans "Accounts Receivable" %}
                                </div>
                                <span class="account-balance">$450,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-2 asset" data-account-id="1130">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">1130</span>
                                    {% trans "Inventory" %}
                                </div>
                                <span class="account-balance">$275,000</span>
                            </div>
                        </div>

                        <!-- Fixed Assets -->
                        <div class="account-node account-level-1 asset" data-account-id="1200">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-chevron-down me-2"></i>
                                    <span class="account-code">1200</span>
                                    {% trans "Fixed Assets" %}
                                </div>
                                <span class="account-balance">$1,600,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-2 asset" data-account-id="1210">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">1210</span>
                                    {% trans "Buildings and Improvements" %}
                                </div>
                                <span class="account-balance">$1,200,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-2 asset" data-account-id="1220">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">1220</span>
                                    {% trans "Equipment and Furniture" %}
                                </div>
                                <span class="account-balance">$400,000</span>
                            </div>
                        </div>

                        <!-- Liabilities -->
                        <div class="account-node account-level-0 liability" data-account-id="2000">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-chevron-down me-2"></i>
                                    <span class="account-code">2000</span>
                                    <strong>{% trans "Liabilities" %}</strong>
                                    <span class="account-type-badge type-liability">{% trans "Liability" %}</span>
                                </div>
                                <span class="account-balance">$850,000</span>
                            </div>
                        </div>
                        
                        <!-- Current Liabilities -->
                        <div class="account-node account-level-1 liability" data-account-id="2100">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-chevron-down me-2"></i>
                                    <span class="account-code">2100</span>
                                    {% trans "Current Liabilities" %}
                                </div>
                                <span class="account-balance">$350,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-2 liability" data-account-id="2110">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">2110</span>
                                    {% trans "Accounts Payable" %}
                                </div>
                                <span class="account-balance">$180,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-2 liability" data-account-id="2120">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">2120</span>
                                    {% trans "Accrued Expenses" %}
                                </div>
                                <span class="account-balance">$170,000</span>
                            </div>
                        </div>

                        <!-- Equity -->
                        <div class="account-node account-level-0 equity" data-account-id="3000">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-chevron-down me-2"></i>
                                    <span class="account-code">3000</span>
                                    <strong>{% trans "Equity" %}</strong>
                                    <span class="account-type-badge type-equity">{% trans "Equity" %}</span>
                                </div>
                                <span class="account-balance">$1,600,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-1 equity" data-account-id="3100">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">3100</span>
                                    {% trans "Retained Earnings" %}
                                </div>
                                <span class="account-balance">$1,600,000</span>
                            </div>
                        </div>

                        <!-- Revenue -->
                        <div class="account-node account-level-0 revenue" data-account-id="4000">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-chevron-down me-2"></i>
                                    <span class="account-code">4000</span>
                                    <strong>{% trans "Revenue" %}</strong>
                                    <span class="account-type-badge type-revenue">{% trans "Revenue" %}</span>
                                </div>
                                <span class="account-balance">$3,200,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-1 revenue" data-account-id="4100">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">4100</span>
                                    {% trans "Tuition Revenue" %}
                                </div>
                                <span class="account-balance">$2,800,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-1 revenue" data-account-id="4200">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">4200</span>
                                    {% trans "Other Revenue" %}
                                </div>
                                <span class="account-balance">$400,000</span>
                            </div>
                        </div>

                        <!-- Expenses -->
                        <div class="account-node account-level-0 expense" data-account-id="5000">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-chevron-down me-2"></i>
                                    <span class="account-code">5000</span>
                                    <strong>{% trans "Expenses" %}</strong>
                                    <span class="account-type-badge type-expense">{% trans "Expense" %}</span>
                                </div>
                                <span class="account-balance">$2,100,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-1 expense" data-account-id="5100">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">5100</span>
                                    {% trans "Salaries and Benefits" %}
                                </div>
                                <span class="account-balance">$1,500,000</span>
                            </div>
                        </div>
                        
                        <div class="account-node account-level-1 expense" data-account-id="5200">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="account-code">5200</span>
                                    {% trans "Operating Expenses" %}
                                </div>
                                <span class="account-balance">$600,000</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Details -->
        <div class="col-lg-4">
            <div class="card accounts-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>{% trans "Account Details" %}
                    </h5>
                </div>
                <div class="card-body" id="accountDetails">
                    <div class="text-center text-muted">
                        <i class="fas fa-mouse-pointer fa-3x mb-3"></i>
                        <p>{% trans "Select an account from the tree to view details" %}</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card accounts-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>{% trans "Add New Account" %}
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-exchange-alt me-2"></i>{% trans "Journal Entry" %}
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-chart-bar me-2"></i>{% trans "Trial Balance" %}
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-file-alt me-2"></i>{% trans "Financial Reports" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Account Modal -->
<div class="modal fade" id="newAccountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add New Account" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="accountCode" class="form-label">{% trans "Account Code" %}</label>
                        <input type="text" class="form-control" id="accountCode" placeholder="{% trans 'e.g., 1150' %}">
                    </div>
                    <div class="mb-3">
                        <label for="accountName" class="form-label">{% trans "Account Name" %}</label>
                        <input type="text" class="form-control" id="accountName" placeholder="{% trans 'Enter account name' %}">
                    </div>
                    <div class="mb-3">
                        <label for="accountType" class="form-label">{% trans "Account Type" %}</label>
                        <select class="form-select" id="accountType">
                            <option value="asset">{% trans "Asset" %}</option>
                            <option value="liability">{% trans "Liability" %}</option>
                            <option value="equity">{% trans "Equity" %}</option>
                            <option value="revenue">{% trans "Revenue" %}</option>
                            <option value="expense">{% trans "Expense" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="parentAccount" class="form-label">{% trans "Parent Account" %}</label>
                        <select class="form-select" id="parentAccount">
                            <option value="">{% trans "Select parent account" %}</option>
                            <option value="1000">1000 - {% trans "Assets" %}</option>
                            <option value="2000">2000 - {% trans "Liabilities" %}</option>
                            <option value="3000">3000 - {% trans "Equity" %}</option>
                            <option value="4000">4000 - {% trans "Revenue" %}</option>
                            <option value="5000">5000 - {% trans "Expenses" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">{% trans "Description" %}</label>
                        <textarea class="form-control" id="description" rows="3" placeholder="{% trans 'Account description' %}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Create Account" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Account tree interaction
    document.querySelectorAll('.account-node').forEach(node => {
        node.addEventListener('click', function() {
            // Remove previous selection
            document.querySelectorAll('.account-node').forEach(n => n.classList.remove('selected'));
            // Add selection to clicked node
            this.classList.add('selected');
            
            // Show account details
            showAccountDetails(this.dataset.accountId);
        });
    });

    function showAccountDetails(accountId) {
        const detailsContainer = document.getElementById('accountDetails');
        
        // Sample account details
        const accountData = {
            '1110': {
                name: '{% trans "Cash and Cash Equivalents" %}',
                code: '1110',
                type: '{% trans "Asset" %}',
                balance: '$125,000',
                description: '{% trans "Cash on hand and in bank accounts" %}',
                lastTransaction: '2025-01-13'
            },
            '1120': {
                name: '{% trans "Accounts Receivable" %}',
                code: '1120',
                type: '{% trans "Asset" %}',
                balance: '$450,000',
                description: '{% trans "Money owed by students and other debtors" %}',
                lastTransaction: '2025-01-12'
            }
        };
        
        const account = accountData[accountId] || {
            name: '{% trans "Selected Account" %}',
            code: accountId,
            type: '{% trans "Account" %}',
            balance: '$0.00',
            description: '{% trans "Account details" %}',
            lastTransaction: '{% trans "N/A" %}'
        };
        
        detailsContainer.innerHTML = `
            <h6>${account.name}</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>{% trans "Code" %}:</strong></td>
                    <td>${account.code}</td>
                </tr>
                <tr>
                    <td><strong>{% trans "Type" %}:</strong></td>
                    <td>${account.type}</td>
                </tr>
                <tr>
                    <td><strong>{% trans "Balance" %}:</strong></td>
                    <td class="account-balance">${account.balance}</td>
                </tr>
                <tr>
                    <td><strong>{% trans "Last Transaction" %}:</strong></td>
                    <td>${account.lastTransaction}</td>
                </tr>
            </table>
            <p class="small text-muted">${account.description}</p>
            <div class="d-grid gap-2 mt-3">
                <button class="btn btn-sm btn-primary">{% trans "View Transactions" %}</button>
                <button class="btn btn-sm btn-outline-secondary">{% trans "Edit Account" %}</button>
            </div>
        `;
    }

    // Expand/Collapse functionality
    document.getElementById('expandAll').addEventListener('click', function() {
        document.querySelectorAll('.account-node i.fa-chevron-right').forEach(icon => {
            icon.classList.remove('fa-chevron-right');
            icon.classList.add('fa-chevron-down');
        });
    });

    document.getElementById('collapseAll').addEventListener('click', function() {
        document.querySelectorAll('.account-node i.fa-chevron-down').forEach(icon => {
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-right');
        });
    });
});
</script>
{% endblock %}
