{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Student ID Generation" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-id-card"></i>
                        {% trans "Generate Student IDs" %}
                    </h3>
                </div>
                <div class="card-body">
                    {% if students_without_ids %}
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                {% crispy form %}
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h5>{% trans "ID Generation Options" %}</h5>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>{% trans "Auto Generate:" %}</strong> {% trans "Uses school code + year + random number" %}</p>
                                        <p><strong>{% trans "Custom Format:" %}</strong> {% trans "Uses your prefix + sequential numbers" %}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h5>{% trans "Students Without IDs" %}</h5>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-sm btn-primary" onclick="selectAll()">
                                        {% trans "Select All" %}
                                    </button>
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="selectNone()">
                                        {% trans "Select None" %}
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <input type="checkbox" id="select-all">
                                                </th>
                                                <th>{% trans "Name" %}</th>
                                                <th>{% trans "Class" %}</th>
                                                <th>{% trans "Admission Date" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for student in students_without_ids %}
                                            <tr>
                                                <td>
                                                    <input type="checkbox" name="student_ids" value="{{ student.id }}" class="student-checkbox">
                                                </td>
                                                <td>{{ student.full_name }}</td>
                                                <td>{{ student.current_class|default:"-" }}</td>
                                                <td>{{ student.admission_date }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-id-card"></i>
                                {% trans "Generate IDs" %}
                            </button>
                        </div>
                    </form>
                    {% else %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        {% trans "All students have been assigned IDs!" %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectAll() {
    document.querySelectorAll('.student-checkbox').forEach(cb => cb.checked = true);
    document.getElementById('select-all').checked = true;
}

function selectNone() {
    document.querySelectorAll('.student-checkbox').forEach(cb => cb.checked = false);
    document.getElementById('select-all').checked = false;
}

document.getElementById('select-all').addEventListener('change', function() {
    document.querySelectorAll('.student-checkbox').forEach(cb => cb.checked = this.checked);
});
</script>
{% endblock %}