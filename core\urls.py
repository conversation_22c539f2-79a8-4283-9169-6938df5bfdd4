from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    # Admin Area
    path('', views.AdminDashboardView.as_view(), name='dashboard'),
    path('admin-dashboard/', views.AdminDashboardView.as_view(), name='admin_dashboard'),
    path('sync/', views.SyncView.as_view(), name='sync'),
    path('backup/', views.BackupView.as_view(), name='backup'),
    path('history-report/', views.HistoryReportView.as_view(), name='history_report'),
    path('report-designer/', views.ReportDesignerView.as_view(), name='report_designer'),
    path('structure-admin/', views.StructureAdministrativeView.as_view(), name='structure_admin'),
    path('quick-support/', views.QuickSupportView.as_view(), name='quick_support'),

    # User Management
    path('users/create/', views.UserCreateView.as_view(), name='user_create'),

    # General Site Settings
    path('settings/', views.SettingsView.as_view(), name='settings'),
    path('login-page-settings/', views.LoginPageSettingsView.as_view(), name='login_page_settings'),

    # School Information
    path('school-info/', views.SchoolInfoView.as_view(), name='school_info'),
    path('school-data/', views.SchoolDataView.as_view(), name='school_data'),
    path('school-data-settings/', views.SchoolDataSettingsView.as_view(), name='school_data_settings'),
    path('nationality/', views.NationalityView.as_view(), name='nationality'),
    path('currency/', views.CurrencyView.as_view(), name='currency'),

    # Taxation System
    path('tax-settings/', views.EgyptianTaxSystemSettingsView.as_view(), name='tax_settings'),
    path('zakat-income/', views.ZakatAndIncomeView.as_view(), name='zakat_income'),

    # Email Settings
    path('email-settings/', views.EmailSettingsView.as_view(), name='email_settings'),
    path('sent-emails/', views.SentEmailView.as_view(), name='sent_emails'),
    path('email-format-settings/', views.EmailFormatSettingsView.as_view(), name='email_format_settings'),

    # Students Receiver
    path('students-receiver/', views.StudentsReceiverView.as_view(), name='students_receiver'),
    path('receiver-reports/', views.StudentReceiverReportsView.as_view(), name='receiver_reports'),
    path('show-receiver-report/', views.ShowReceiverReportView.as_view(), name='show_receiver_report'),

    # Service Providers - SMS
    path('sms-settings/', views.SMSSettingsView.as_view(), name='sms_settings'),
    path('message-templates/', views.MessageTemplatesView.as_view(), name='message_templates'),
    path('sent-messages/', views.SentMessagesView.as_view(), name='sent_messages'),
    path('auto-messages/', views.AutoMessagesView.as_view(), name='auto_messages'),
    path('sms-report/', views.SMSReportView.as_view(), name='sms_report'),

    # Service Providers - WhatsApp
    path('whatsapp-settings/', views.WhatsAppSettingsView.as_view(), name='whatsapp_settings'),

    # Service Providers - Payment
    path('payment-provider/', views.PaymentProviderView.as_view(), name='payment_provider'),
]
