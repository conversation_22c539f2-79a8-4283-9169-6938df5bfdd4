from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView, View
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Sum, Count
from django.http import HttpResponse, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.exceptions import ValidationError
from datetime import datetime, timedelta
from decimal import Decimal
from .models import (
    Account, AccountType, Payment, PaymentItem, StudentFee, FeeType, GradeFee,
    CostCenter, JournalEntry, FinancialYear, Bank, Invoice, InvoiceItem,
    Transaction, TransactionEntry, TransactionAuditLog
)
from .forms import (
    AccountForm, JournalEntryForm, PaymentForm, InvoiceForm, CostCenterForm,
    BankForm, FeeTypeForm, GradeFeeForm, StudentFeeForm, FinancialYearForm, QuickPaymentForm,
    TransactionForm, TransactionEntryInlineFormSet, TransactionApprovalForm, 
    TransactionSearchForm, DoubleEntryTransactionForm
)
from students.models import Student

# Finance Dashboard
class FinanceDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current month data
        current_month = timezone.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        # Calculate financial metrics
        context['total_revenue'] = Payment.objects.filter(
            payment_date__gte=current_month,
            payment_date__lt=next_month
        ).aggregate(total=Sum('amount'))['total'] or 0

        context['total_expenses'] = JournalEntry.objects.filter(
            entry_date__gte=current_month,
            entry_date__lt=next_month,
            debit_amount__gt=0,
            account__account_type__type='expense'
        ).aggregate(total=Sum('debit_amount'))['total'] or 0

        # Recent transactions for the dashboard
        context['recent_transactions'] = JournalEntry.objects.select_related(
            'account', 'account__account_type'
        ).order_by('-entry_date', '-created_at')[:5]

        # Outstanding payments
        context['outstanding_payments'] = StudentFee.objects.filter(
            is_paid=False
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Monthly statistics
        context['monthly_payments'] = Payment.objects.filter(
            payment_date__gte=current_month,
            payment_date__lt=next_month
        ).count()

        # Net income
        context['net_income'] = context['total_revenue'] - context['total_expenses']

        # Pending and overdue payments
        pending_invoices = Invoice.objects.filter(status='sent')
        context['pending_payments'] = pending_invoices.aggregate(total=Sum('total_amount'))['total'] or 0
        context['pending_count'] = pending_invoices.count()

        overdue_invoices = Invoice.objects.filter(
            status='sent',
            due_date__lt=timezone.now().date()
        )
        context['overdue_payments'] = overdue_invoices.aggregate(total=Sum('total_amount'))['total'] or 0
        context['overdue_count'] = overdue_invoices.count()

        # Recent payments
        context['recent_payments'] = Payment.objects.select_related('student').order_by('-payment_date')[:10]

        # Outstanding fees
        outstanding_fees = StudentFee.objects.filter(is_paid=False)
        context['outstanding_fees_count'] = outstanding_fees.values('student').distinct().count()
        context['outstanding_fees_amount'] = outstanding_fees.aggregate(
            total=Sum('amount') - Sum('discount_amount')
        )['total'] or 0

        # Chart data (last 6 months)
        months = []
        revenue_data = []
        expense_data = []

        for i in range(6):
            month_start = (current_month - timedelta(days=i*30)).replace(day=1)
            month_end = (month_start + timedelta(days=32)).replace(day=1)

            months.append(month_start.strftime('%b %Y'))

            month_revenue = Payment.objects.filter(
                payment_date__gte=month_start,
                payment_date__lt=month_end
            ).aggregate(total=Sum('amount'))['total'] or 0

            month_expenses = JournalEntry.objects.filter(
                entry_date__gte=month_start,
                entry_date__lt=month_end,
                debit_amount__gt=0,
                account__account_type__type='expense'
            ).aggregate(total=Sum('debit_amount'))['total'] or 0

            revenue_data.append(float(month_revenue))
            expense_data.append(float(month_expenses))

        context['chart_labels'] = list(reversed(months))
        context['revenue_data'] = list(reversed(revenue_data))
        context['expense_data'] = list(reversed(expense_data))

        # Payment methods data
        payment_methods = Payment.objects.values('payment_method').annotate(
            count=Count('id'),
            total=Sum('amount')
        ).order_by('-total')

        context['payment_method_labels'] = [pm['payment_method'] for pm in payment_methods]
        context['payment_method_data'] = [float(pm['total']) for pm in payment_methods]

        return context

# Accounts Management Views
class AccountsTreeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_tree.html'

class CostCenterView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cost_center.html'

class CostCenterReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cost_center_report.html'

class DailyEntriesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_entries.html'

class CreateDailyEntryView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/create_daily_entry.html'

class DailyEntriesHistoryView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_entries_history.html'

class FinancialYearsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/financial_years.html'

class BanksView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/banks.html'

class SafesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/safes.html'

class ReceiptVoucherView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/receipt_voucher.html'

class ExchangePermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/exchange_permission.html'

class TrialBalanceView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/trial_balance.html'

class AccountStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/account_statement.html'

class SearchReceiptsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/search_receipts.html'

class MonthlyAccountStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/monthly_statement.html'

class OpeningBalancesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/opening_balances.html'

class CashBankStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cash_bank_statement.html'

class BalanceSheetView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/balance_sheet.html'

class IncomeStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/income_statement.html'

# Student Accounts Views
class FeesItemsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/fees_items.html'

class GroupedOptionalFeesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_fees.html'

class GradeFeesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grade_fees.html'

class FeesPaymentView(LoginRequiredMixin, ListView):
    model = Payment
    template_name = 'finance/fees_payment.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = Payment.objects.select_related('student', 'received_by').all()
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(student__first_name__icontains=search) |
                Q(student__last_name__icontains=search) |
                Q(receipt_number__icontains=search)
            )
        return queryset.order_by('-payment_date')


class PaymentCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Payment
    form_class = PaymentForm
    template_name = 'finance/payment_form.html'
    permission_required = 'finance.add_payment'
    success_url = reverse_lazy('finance:payments')

    def form_valid(self, form):
        form.instance.received_by = self.request.user
        messages.success(self.request, _('Payment recorded successfully!'))
        return super().form_valid(form)


class PaymentHistoryView(LoginRequiredMixin, ListView):
    model = Payment
    template_name = 'finance/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = Payment.objects.select_related('student', 'received_by').all()
        student_id = self.request.GET.get('student')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')

        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if date_from:
            queryset = queryset.filter(payment_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(payment_date__lte=date_to)

        return queryset.order_by('-payment_date')


class InvoiceListView(LoginRequiredMixin, ListView):
    model = Invoice
    template_name = 'finance/invoices.html'
    context_object_name = 'invoices'
    paginate_by = 20

    def get_queryset(self):
        queryset = Invoice.objects.select_related('student', 'created_by').all()
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        return queryset.order_by('-invoice_date')


class InvoiceCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Invoice
    form_class = InvoiceForm
    template_name = 'finance/invoice_form.html'
    permission_required = 'finance.add_invoice'
    success_url = reverse_lazy('finance:invoices')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        # Auto-generate invoice number
        last_invoice = Invoice.objects.order_by('-id').first()
        if last_invoice:
            last_number = int(last_invoice.invoice_number.split('-')[-1])
            form.instance.invoice_number = f"INV-{last_number + 1:06d}"
        else:
            form.instance.invoice_number = "INV-000001"

        messages.success(self.request, _('Invoice created successfully!'))
        return super().form_valid(form)

class AccountsTreeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_tree.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import ChartOfAccountsService
            
            context['account_tree'] = ChartOfAccountsService.get_account_tree(school)
            context['account_types'] = AccountType.objects.filter(school=school).prefetch_related('accounts')
            context['total_accounts'] = Account.objects.filter(school=school).active().count()
            context['archived_accounts'] = Account.objects.filter(school=school).archived().count()
            
            # Get account balances
            balances = ChartOfAccountsService.get_account_balances(school)
            context['account_balances'] = balances
        
        return context


class AccountCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Account
    form_class = AccountForm
    template_name = 'finance/account_form.html'
    permission_required = 'finance.add_account'
    success_url = reverse_lazy('finance:accounts_tree')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if hasattr(self.request.user, 'employee'):
            kwargs['school'] = self.request.user.employee.school
        return kwargs

    def form_valid(self, form):
        if hasattr(self.request.user, 'employee'):
            form.instance.school = self.request.user.employee.school
        form.instance.created_by = self.request.user
        messages.success(self.request, _('Account created successfully!'))
        return super().form_valid(form)


class AccountUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Account
    form_class = AccountForm
    template_name = 'finance/account_form.html'
    permission_required = 'finance.change_account'
    success_url = reverse_lazy('finance:accounts_tree')

    def get_queryset(self):
        if hasattr(self.request.user, 'employee'):
            return Account.objects.filter(school=self.request.user.employee.school)
        return Account.objects.none()

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if hasattr(self.request.user, 'employee'):
            kwargs['school'] = self.request.user.employee.school
        return kwargs

    def form_valid(self, form):
        form.instance.updated_by = self.request.user
        messages.success(self.request, _('Account updated successfully!'))
        return super().form_valid(form)


class AccountDetailView(LoginRequiredMixin, DetailView):
    model = Account
    template_name = 'finance/account_detail.html'
    context_object_name = 'account'

    def get_queryset(self):
        if hasattr(self.request.user, 'employee'):
            return Account.objects.filter(school=self.request.user.employee.school)
        return Account.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        account = self.get_object()
        
        # Get account balance
        context['current_balance'] = account.get_balance()
        
        # Get recent journal entries
        context['recent_entries'] = account.journal_entries.select_related(
            'created_by'
        ).order_by('-entry_date', '-created_at')[:10]
        
        # Get child accounts
        context['child_accounts'] = account.children.active().select_related('account_type')
        
        # Check if account can be deleted/archived
        context['can_delete'], context['delete_error'] = account.can_be_deleted()
        context['can_archive'], context['archive_error'] = account.can_be_archived()
        
        return context


class AccountArchiveView(LoginRequiredMixin, PermissionRequiredMixin, View):
    permission_required = 'finance.change_account'

    def post(self, request, pk):
        account = get_object_or_404(Account, pk=pk, school=request.user.employee.school)
        
        from .forms import AccountArchiveForm
        form = AccountArchiveForm(request.POST)
        
        if form.is_valid():
            try:
                from .services import ChartOfAccountsService
                ChartOfAccountsService.archive_account(
                    account, 
                    request.user, 
                    form.cleaned_data['reason']
                )
                messages.success(request, _('Account archived successfully!'))
            except ValidationError as e:
                messages.error(request, str(e))
        else:
            messages.error(request, _('Please provide a valid reason for archiving.'))
        
        return redirect('finance:account_detail', pk=pk)


class AccountUnarchiveView(LoginRequiredMixin, PermissionRequiredMixin, View):
    permission_required = 'finance.change_account'

    def post(self, request, pk):
        account = get_object_or_404(Account, pk=pk, school=request.user.employee.school)
        
        try:
            account.unarchive()
            messages.success(request, _('Account unarchived successfully!'))
        except Exception as e:
            messages.error(request, str(e))
        
        return redirect('finance:account_detail', pk=pk)


class AccountReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/account_reports.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import ChartOfAccountsService
            
            report_type = self.request.GET.get('type', 'trial_balance')
            as_of_date = self.request.GET.get('as_of_date')
            
            if as_of_date:
                try:
                    as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
                except ValueError:
                    as_of_date = None
            
            context['report_data'] = ChartOfAccountsService.generate_account_report(
                school, report_type, as_of_date
            )
            context['report_type'] = report_type
        
        return context


class JournalEntriesView(LoginRequiredMixin, ListView):
    model = JournalEntry
    template_name = 'finance/journal_entries.html'
    context_object_name = 'entries'
    paginate_by = 20

    def get_queryset(self):
        queryset = JournalEntry.objects.select_related(
            'account', 'cost_center', 'created_by', 'posted_by'
        ).all()

        account_id = self.request.GET.get('account')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        is_posted = self.request.GET.get('is_posted')

        if account_id:
            queryset = queryset.filter(account_id=account_id)
        if date_from:
            queryset = queryset.filter(entry_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(entry_date__lte=date_to)
        if is_posted:
            queryset = queryset.filter(is_posted=is_posted == 'true')

        return queryset.order_by('-entry_date', '-created_at')


class JournalEntryCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = JournalEntry
    form_class = JournalEntryForm
    template_name = 'finance/journal_entry_form.html'
    permission_required = 'finance.add_journalentry'
    success_url = reverse_lazy('finance:journal_entries')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        # Auto-generate reference number
        last_entry = JournalEntry.objects.order_by('-id').first()
        if last_entry:
            last_number = int(last_entry.reference_number.split('-')[-1])
            form.instance.reference_number = f"JE-{last_number + 1:06d}"
        else:
            form.instance.reference_number = "JE-000001"

        messages.success(self.request, _('Journal entry created successfully!'))
        return super().form_valid(form)

class TaxCodeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/tax_code.html'

class DailyPaymentsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_payments_report.html'

class StudentDebitsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/student_debits.html'

class AccountsAggregateReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_aggregate_report.html'

class PaymentsByStagesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payments_by_stages.html'

class PaymentAccountReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_account_report.html'

class ItemsSavesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/items_saves_report.html'

class TaxPaymentReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/tax_payment_report.html'

# Payment Permissions Views
class PaymentPermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_permission.html'

class PayPaymentPermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/pay_permission.html'

class PermissionReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/permission_reports.html'

# Registration and Discounts Views
class RegistrationFeesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/registration_fees.html'

class DiscountSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_settings.html'

class DiscountSettingsDetailsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_details.html'

class AddDiscountView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/add_discount.html'

class RemoveStudentDebitsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/remove_debits.html'

class DiscountReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_report.html'

class BusPaymentsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/bus_payments_report.html'

class FeesRequestsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/fees_requests.html'

class RecoveryReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/recovery_report.html'

class PaymentStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_statement.html'

class TotalPaymentStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/total_payment_statement.html'

class UnpaidFeesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/unpaid_fees_report.html'

class AddStudentOpeningBalanceView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/opening_balance_student.html'

class PaymentRequestView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_request.html'

class GroupedDiscountView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_discount.html'

class GroupedPaymentTransactionsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_payments.html'

# Reports Views
class FinancialReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/reports.html'

class EInvoiceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/einvoice_report.html'

class ZakatIncomeReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/zakat_income_report.html'


# Double-Entry Bookkeeping Views
class TransactionListView(LoginRequiredMixin, ListView):
    model = Transaction
    template_name = 'finance/transactions_list.html'
    context_object_name = 'transactions'
    paginate_by = 20

    def get_queryset(self):
        from .models import Transaction
        queryset = Transaction.objects.select_related(
            'created_by', 'approved_by', 'posted_by'
        ).prefetch_related('entries__account').all()

        # Apply filters
        transaction_id = self.request.GET.get('transaction_id')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        transaction_type = self.request.GET.get('transaction_type')
        status = self.request.GET.get('status')
        account_id = self.request.GET.get('account')

        if transaction_id:
            queryset = queryset.filter(transaction_id__icontains=transaction_id)
        if date_from:
            queryset = queryset.filter(transaction_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(transaction_date__lte=date_to)
        if transaction_type:
            queryset = queryset.filter(transaction_type=transaction_type)
        if status:
            queryset = queryset.filter(status=status)
        if account_id:
            queryset = queryset.filter(entries__account_id=account_id).distinct()

        return queryset.order_by('-transaction_date', '-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from .forms import TransactionSearchForm
        
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        context['search_form'] = TransactionSearchForm(self.request.GET, school=school)
        
        return context


class TransactionDetailView(LoginRequiredMixin, DetailView):
    model = Transaction
    template_name = 'finance/transaction_detail.html'
    context_object_name = 'transaction'

    def get_queryset(self):
        from .models import Transaction
        if hasattr(self.request.user, 'employee'):
            return Transaction.objects.filter(school=self.request.user.employee.school)
        return Transaction.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        transaction_obj = self.get_object()
        
        # Get transaction entries
        context['entries'] = transaction_obj.entries.select_related('account', 'cost_center').all()
        
        # Get audit trail
        from .services import DoubleEntryBookkeepingService
        context['audit_trail'] = DoubleEntryBookkeepingService.get_transaction_audit_trail(transaction_obj)
        
        # Check permissions
        context['can_approve'] = (
            transaction_obj.status == 'pending_approval' and 
            self.request.user.has_perm('finance.approve_transaction')
        )
        context['can_post'] = (
            transaction_obj.status in ['approved', 'draft'] and 
            self.request.user.has_perm('finance.post_transaction')
        )
        context['can_cancel'] = (
            transaction_obj.status != 'posted' and 
            self.request.user.has_perm('finance.cancel_transaction')
        )
        
        return context


class TransactionCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Transaction
    template_name = 'finance/transaction_form.html'
    permission_required = 'finance.add_transaction'
    
    def get_form_class(self):
        from .forms import DoubleEntryTransactionForm
        return DoubleEntryTransactionForm

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if hasattr(self.request.user, 'employee'):
            kwargs['school'] = self.request.user.employee.school
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            # Get accounts for entries
            context['accounts'] = Account.objects.filter(
                school=school,
                is_header=False,
                is_active=True,
                allow_manual_entries=True
            ).select_related('account_type').order_by('code')
            
            # Get cost centers
            context['cost_centers'] = CostCenter.objects.filter(
                school=school,
                is_active=True
            ).order_by('code')
        
        return context

    def form_valid(self, form):
        if hasattr(self.request.user, 'employee'):
            form.instance.school = self.request.user.employee.school
        form.instance.created_by = self.request.user
        
        # Handle transaction entries from POST data
        entries_data = self._extract_entries_data()
        
        try:
            from .services import DoubleEntryBookkeepingService
            transaction_obj = DoubleEntryBookkeepingService.create_transaction(
                form.instance.school,
                self.request.user,
                {
                    'transaction_date': form.cleaned_data['transaction_date'],
                    'transaction_type': form.cleaned_data['transaction_type'],
                    'description': form.cleaned_data['description'],
                    'reference': form.cleaned_data.get('reference', ''),
                    'total_amount': 0,  # Will be calculated
                    'requires_approval': form.cleaned_data.get('requires_approval', False)
                },
                entries_data
            )
            
            messages.success(self.request, _('Transaction created successfully!'))
            return redirect('finance:transaction_detail', pk=transaction_obj.pk)
            
        except ValidationError as e:
            messages.error(self.request, str(e))
            return self.form_invalid(form)

    def _extract_entries_data(self):
        """Extract transaction entries data from POST"""
        entries_data = []
        entry_count = 0
        
        while f'entry_{entry_count}_account' in self.request.POST:
            account_id = self.request.POST.get(f'entry_{entry_count}_account')
            description = self.request.POST.get(f'entry_{entry_count}_description', '')
            debit_amount = self.request.POST.get(f'entry_{entry_count}_debit_amount', '0')
            credit_amount = self.request.POST.get(f'entry_{entry_count}_credit_amount', '0')
            cost_center_id = self.request.POST.get(f'entry_{entry_count}_cost_center')
            reference = self.request.POST.get(f'entry_{entry_count}_reference', '')
            
            if account_id:
                try:
                    entries_data.append({
                        'account_id': int(account_id),
                        'description': description,
                        'debit_amount': Decimal(debit_amount or '0'),
                        'credit_amount': Decimal(credit_amount or '0'),
                        'cost_center_id': int(cost_center_id) if cost_center_id else None,
                        'reference': reference
                    })
                except (ValueError, TypeError):
                    pass  # Skip invalid entries
            
            entry_count += 1
        
        return entries_data


class TransactionUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Transaction
    template_name = 'finance/transaction_form.html'
    permission_required = 'finance.change_transaction'
    
    def get_form_class(self):
        from .forms import TransactionForm
        return TransactionForm

    def get_queryset(self):
        from .models import Transaction
        if hasattr(self.request.user, 'employee'):
            return Transaction.objects.filter(
                school=self.request.user.employee.school,
                status__in=['draft', 'pending_approval']  # Only allow editing draft/pending transactions
            )
        return Transaction.objects.none()

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if hasattr(self.request.user, 'employee'):
            kwargs['school'] = self.request.user.employee.school
        return kwargs

    def form_valid(self, form):
        form.instance.modified_by = self.request.user
        messages.success(self.request, _('Transaction updated successfully!'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('finance:transaction_detail', kwargs={'pk': self.object.pk})


class TransactionApprovalView(LoginRequiredMixin, PermissionRequiredMixin, View):
    permission_required = 'finance.approve_transaction'

    def post(self, request, pk):
        from .models import Transaction
        from .forms import TransactionApprovalForm
        from .services import DoubleEntryBookkeepingService
        
        transaction_obj = get_object_or_404(
            Transaction, 
            pk=pk, 
            school=request.user.employee.school,
            status='pending_approval'
        )
        
        form = TransactionApprovalForm(request.POST)
        
        if form.is_valid():
            action = form.cleaned_data['action']
            notes = form.cleaned_data.get('approval_notes', '')
            
            try:
                if action == 'approve':
                    DoubleEntryBookkeepingService.approve_transaction(transaction_obj, request.user, notes)
                    messages.success(request, _('Transaction approved successfully!'))
                else:  # reject
                    DoubleEntryBookkeepingService.cancel_transaction(transaction_obj, request.user, notes)
                    messages.success(request, _('Transaction rejected successfully!'))
                    
            except ValidationError as e:
                messages.error(request, str(e))
        else:
            messages.error(request, _('Invalid form data'))
        
        return redirect('finance:transaction_detail', pk=pk)


class TransactionPostView(LoginRequiredMixin, PermissionRequiredMixin, View):
    permission_required = 'finance.post_transaction'

    def post(self, request, pk):
        from .models import Transaction
        from .services import DoubleEntryBookkeepingService
        
        transaction_obj = get_object_or_404(
            Transaction, 
            pk=pk, 
            school=request.user.employee.school
        )
        
        try:
            DoubleEntryBookkeepingService.post_transaction(transaction_obj, request.user)
            messages.success(request, _('Transaction posted successfully!'))
        except ValidationError as e:
            messages.error(request, str(e))
        
        return redirect('finance:transaction_detail', pk=pk)


class TransactionCancelView(LoginRequiredMixin, PermissionRequiredMixin, View):
    permission_required = 'finance.cancel_transaction'

    def post(self, request, pk):
        from .models import Transaction
        from .services import DoubleEntryBookkeepingService
        
        transaction_obj = get_object_or_404(
            Transaction, 
            pk=pk, 
            school=request.user.employee.school,
            status__in=['draft', 'pending_approval', 'approved']
        )
        
        reason = request.POST.get('reason', '')
        
        try:
            DoubleEntryBookkeepingService.cancel_transaction(transaction_obj, request.user, reason)
            messages.success(request, _('Transaction cancelled successfully!'))
        except ValidationError as e:
            messages.error(request, str(e))
        
        return redirect('finance:transaction_detail', pk=pk)


class PendingApprovalsView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    template_name = 'finance/pending_approvals.html'
    context_object_name = 'transactions'
    permission_required = 'finance.approve_transaction'
    paginate_by = 20

    def get_queryset(self):
        from .models import Transaction
        if hasattr(self.request.user, 'employee'):
            return Transaction.objects.filter(
                school=self.request.user.employee.school,
                status='pending_approval'
            ).select_related('created_by').order_by('-created_at')
        return Transaction.objects.none()


class BulkTransactionApprovalView(LoginRequiredMixin, PermissionRequiredMixin, View):
    permission_required = 'finance.approve_transaction'

    def post(self, request):
        from .forms import BulkTransactionApprovalForm
        from .services import TransactionApprovalService
        
        form = BulkTransactionApprovalForm(request.POST)
        
        if form.is_valid():
            transaction_ids = [int(id) for id in form.cleaned_data['transaction_ids'].split(',')]
            action = form.cleaned_data['action']
            notes = form.cleaned_data.get('notes', '')
            
            try:
                results = TransactionApprovalService.bulk_approve_transactions(
                    transaction_ids, request.user, notes
                )
                
                if results['approved']:
                    messages.success(
                        request, 
                        _('Successfully processed {} transactions').format(len(results['approved']))
                    )
                
                if results['failed']:
                    messages.warning(
                        request,
                        _('Failed to process {} transactions').format(len(results['failed']))
                    )
                    
            except Exception as e:
                messages.error(request, str(e))
        else:
            messages.error(request, _('Invalid form data'))
        
        return redirect('finance:pending_approvals')


class JournalEntryInterfaceView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/journal_entry_interface.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            context['accounts'] = Account.objects.filter(
                school=school,
                is_header=False,
                is_active=True,
                allow_manual_entries=True
            ).select_related('account_type').order_by('code')
            
            context['cost_centers'] = CostCenter.objects.filter(
                school=school,
                is_active=True
            ).order_by('code')
        
        return context

    def post(self, request):
        """Handle journal entry creation"""
        from .services import DoubleEntryBookkeepingService
        
        try:
            # Extract journal entries data
            entries_data = self._extract_journal_entries_data(request)
            
            if not entries_data:
                messages.error(request, _('No valid entries provided'))
                return redirect('finance:journal_entry_interface')
            
            # Validate entries are balanced
            total_debits = sum(entry['debit_amount'] for entry in entries_data)
            total_credits = sum(entry['credit_amount'] for entry in entries_data)
            
            if abs(total_debits - total_credits) > 0.01:
                messages.error(request, _('Journal entries must be balanced'))
                return redirect('finance:journal_entry_interface')
            
            # Create transaction with entries
            school = request.user.employee.school
            transaction_data = {
                'transaction_date': request.POST.get('transaction_date'),
                'transaction_type': 'journal',
                'description': request.POST.get('description', 'Journal Entry'),
                'reference': request.POST.get('reference', ''),
                'total_amount': total_debits,
                'requires_approval': request.POST.get('requires_approval') == 'on'
            }
            
            transaction_obj = DoubleEntryBookkeepingService.create_transaction(
                school, request.user, transaction_data, entries_data
            )
            
            messages.success(request, _('Journal entries created successfully!'))
            return redirect('finance:transaction_detail', pk=transaction_obj.pk)
            
        except ValidationError as e:
            messages.error(request, str(e))
        except Exception as e:
            messages.error(request, _('Error creating journal entries: {}').format(str(e)))
        
        return redirect('finance:journal_entry_interface')

    def _extract_journal_entries_data(self, request):
        """Extract journal entries data from POST"""
        entries_data = []
        entry_count = 0
        
        while f'account_{entry_count}' in request.POST:
            account_id = request.POST.get(f'account_{entry_count}')
            description = request.POST.get(f'description_{entry_count}', '')
            debit_amount = request.POST.get(f'debit_{entry_count}', '0')
            credit_amount = request.POST.get(f'credit_{entry_count}', '0')
            cost_center_id = request.POST.get(f'cost_center_{entry_count}')
            reference = request.POST.get(f'reference_{entry_count}', '')
            
            if account_id:
                try:
                    debit_val = Decimal(debit_amount or '0')
                    credit_val = Decimal(credit_amount or '0')
                    
                    if debit_val > 0 or credit_val > 0:
                        entries_data.append({
                            'account_id': int(account_id),
                            'description': description,
                            'debit_amount': debit_val,
                            'credit_amount': credit_val,
                            'cost_center_id': int(cost_center_id) if cost_center_id else None,
                            'reference': reference
                        })
                except (ValueError, TypeError):
                    pass  # Skip invalid entries
            
            entry_count += 1
        
        return entries_data

# Double-Entry Bookkeeping Views

class TransactionListView(LoginRequiredMixin, ListView):
    """List all transactions with filtering and search"""
    model = Transaction
    template_name = 'finance/transactions/list.html'
    context_object_name = 'transactions'
    paginate_by = 25

    def get_queryset(self):
        queryset = Transaction.objects.filter(
            school=self.request.user.profile.school
        ).select_related('created_by', 'approved_by', 'posted_by').order_by('-transaction_date', '-created_at')
        
        # Apply search filters
        form = TransactionSearchForm(self.request.GET, school=self.request.user.profile.school)
        if form.is_valid():
            if form.cleaned_data.get('transaction_id'):
                queryset = queryset.filter(
                    transaction_id__icontains=form.cleaned_data['transaction_id']
                )
            
            if form.cleaned_data.get('date_from'):
                queryset = queryset.filter(
                    transaction_date__gte=form.cleaned_data['date_from']
                )
            
            if form.cleaned_data.get('date_to'):
                queryset = queryset.filter(
                    transaction_date__lte=form.cleaned_data['date_to']
                )
            
            if form.cleaned_data.get('transaction_type'):
                queryset = queryset.filter(
                    transaction_type=form.cleaned_data['transaction_type']
                )
            
            if form.cleaned_data.get('status'):
                queryset = queryset.filter(
                    status=form.cleaned_data['status']
                )
            
            if form.cleaned_data.get('account'):
                queryset = queryset.filter(
                    entries__account=form.cleaned_data['account']
                ).distinct()
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = TransactionSearchForm(
            self.request.GET, 
            school=self.request.user.profile.school
        )
        
        # Summary statistics
        queryset = self.get_queryset()
        context['total_transactions'] = queryset.count()
        context['pending_approval'] = queryset.filter(status='pending_approval').count()
        context['draft_transactions'] = queryset.filter(status='draft').count()
        context['posted_transactions'] = queryset.filter(status='posted').count()
        
        return context


class TransactionDetailView(LoginRequiredMixin, DetailView):
    """View transaction details with entries and audit trail"""
    model = Transaction
    template_name = 'finance/transactions/detail.html'
    context_object_name = 'transaction'

    def get_queryset(self):
        return Transaction.objects.filter(
            school=self.request.user.profile.school
        ).select_related('created_by', 'approved_by', 'posted_by')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        transaction = self.get_object()
        
        # Get transaction entries
        context['entries'] = transaction.entries.select_related(
            'account', 'account__account_type', 'cost_center'
        ).order_by('id')
        
        # Get audit trail
        from .services import DoubleEntryBookkeepingService
        context['audit_trail'] = DoubleEntryBookkeepingService.get_transaction_audit_trail(transaction)
        
        # Check if user can approve/post
        context['can_approve'] = (
            transaction.status == 'pending_approval' and 
            self.request.user.has_perm('finance.approve_transaction')
        )
        context['can_post'] = (
            transaction.status in ['approved', 'draft'] and 
            self.request.user.has_perm('finance.post_transaction')
        )
        
        return context


class TransactionCreateView(LoginRequiredMixin, CreateView):
    """Create new transaction with entries"""
    model = Transaction
    form_class = TransactionForm
    template_name = 'finance/transactions/create.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.request.user.profile.school
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        if self.request.POST:
            context['entry_formset'] = TransactionEntryInlineFormSet(
                self.request.POST, 
                instance=self.object,
                form_kwargs={'school': self.request.user.profile.school}
            )
        else:
            context['entry_formset'] = TransactionEntryInlineFormSet(
                instance=self.object,
                form_kwargs={'school': self.request.user.profile.school}
            )
        
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        entry_formset = context['entry_formset']
        
        if entry_formset.is_valid():
            # Set school and user
            form.instance.school = self.request.user.profile.school
            form.instance.created_by = self.request.user
            
            # Save transaction
            self.object = form.save()
            
            # Save entries
            entry_formset.instance = self.object
            entries = entry_formset.save(commit=False)
            
            for entry in entries:
                entry.school = self.request.user.profile.school
                entry.created_by = self.request.user
                entry.entry_date = self.object.transaction_date
                entry.save()
            
            # Delete removed entries
            for entry in entry_formset.deleted_objects:
                entry.delete()
            
            # Create audit log
            self.object.create_audit_log('created', self.request.user)
            
            messages.success(
                self.request, 
                _('Transaction {} created successfully.').format(self.object.transaction_id)
            )
            return redirect('finance:transaction_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)

    def form_invalid(self, form):
        messages.error(self.request, _('Please correct the errors below.'))
        return super().form_invalid(form)


class TransactionUpdateView(LoginRequiredMixin, UpdateView):
    """Update transaction (only if not posted)"""
    model = Transaction
    form_class = TransactionForm
    template_name = 'finance/transactions/update.html'

    def get_queryset(self):
        return Transaction.objects.filter(
            school=self.request.user.profile.school,
            status__in=['draft', 'pending_approval']  # Only allow editing of non-posted transactions
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.request.user.profile.school
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        if self.request.POST:
            context['entry_formset'] = TransactionEntryInlineFormSet(
                self.request.POST, 
                instance=self.object,
                form_kwargs={'school': self.request.user.profile.school}
            )
        else:
            context['entry_formset'] = TransactionEntryInlineFormSet(
                instance=self.object,
                form_kwargs={'school': self.request.user.profile.school}
            )
        
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        entry_formset = context['entry_formset']
        
        if entry_formset.is_valid():
            # Set modified by
            form.instance.modified_by = self.request.user
            
            # Save transaction
            self.object = form.save()
            
            # Save entries
            entry_formset.instance = self.object
            entries = entry_formset.save(commit=False)
            
            for entry in entries:
                if not entry.pk:  # New entry
                    entry.school = self.request.user.profile.school
                    entry.created_by = self.request.user
                entry.entry_date = self.object.transaction_date
                entry.save()
            
            # Delete removed entries
            for entry in entry_formset.deleted_objects:
                entry.delete()
            
            # Create audit log
            self.object.create_audit_log('modified', self.request.user)
            
            messages.success(
                self.request, 
                _('Transaction {} updated successfully.').format(self.object.transaction_id)
            )
            return redirect('finance:transaction_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)


class TransactionApprovalView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """Approve or reject transactions"""
    permission_required = 'finance.approve_transaction'

    def get(self, request, pk):
        transaction = get_object_or_404(
            Transaction,
            pk=pk,
            school=request.user.profile.school,
            status='pending_approval'
        )
        
        form = TransactionApprovalForm()
        
        return render(request, 'finance/transactions/approve.html', {
            'transaction': transaction,
            'form': form,
            'entries': transaction.entries.select_related('account', 'cost_center')
        })

    def post(self, request, pk):
        transaction = get_object_or_404(
            Transaction,
            pk=pk,
            school=request.user.profile.school,
            status='pending_approval'
        )
        
        form = TransactionApprovalForm(request.POST)
        
        if form.is_valid():
            action = form.cleaned_data['action']
            notes = form.cleaned_data['approval_notes']
            
            try:
                if action == 'approve':
                    from .services import DoubleEntryBookkeepingService
                    DoubleEntryBookkeepingService.approve_transaction(transaction, request.user, notes)
                    messages.success(
                        request, 
                        _('Transaction {} approved successfully.').format(transaction.transaction_id)
                    )
                else:  # reject
                    transaction.cancel(request.user, notes)
                    messages.success(
                        request, 
                        _('Transaction {} rejected.').format(transaction.transaction_id)
                    )
                
                return redirect('finance:transaction_detail', pk=transaction.pk)
                
            except ValidationError as e:
                messages.error(request, str(e))
        
        return render(request, 'finance/transactions/approve.html', {
            'transaction': transaction,
            'form': form,
            'entries': transaction.entries.select_related('account', 'cost_center')
        })


class TransactionPostView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """Post approved transactions"""
    permission_required = 'finance.post_transaction'

    def post(self, request, pk):
        transaction = get_object_or_404(
            Transaction,
            pk=pk,
            school=request.user.profile.school,
            status__in=['approved', 'draft']
        )
        
        try:
            from .services import DoubleEntryBookkeepingService
            DoubleEntryBookkeepingService.post_transaction(transaction, request.user)
            
            messages.success(
                request, 
                _('Transaction {} posted successfully.').format(transaction.transaction_id)
            )
        except ValidationError as e:
            messages.error(request, str(e))
        
        return redirect('finance:transaction_detail', pk=transaction.pk)


class JournalEntryInterfaceView(LoginRequiredMixin, TemplateView):
    """Modern journal entry interface for double-entry bookkeeping"""
    template_name = 'finance/journal/entry_interface.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get accounts for dropdown
        context['accounts'] = Account.objects.filter(
            school=self.request.user.profile.school,
            is_header=False,
            is_active=True,
            allow_manual_entries=True
        ).select_related('account_type').order_by('code')
        
        # Get cost centers
        context['cost_centers'] = CostCenter.objects.filter(
            school=self.request.user.profile.school,
            is_active=True
        ).order_by('code')
        
        # Transaction form
        context['transaction_form'] = DoubleEntryTransactionForm(
            school=self.request.user.profile.school
        )
        
        return context

    def post(self, request):
        """Handle journal entry creation via AJAX"""
        import json
        
        try:
            data = json.loads(request.body)
            
            # Extract transaction data
            transaction_data = {
                'transaction_date': datetime.strptime(data['transaction_date'], '%Y-%m-%d').date(),
                'transaction_type': data.get('transaction_type', 'manual'),
                'description': data['description'],
                'reference': data.get('reference', ''),
                'total_amount': Decimal(str(data['total_amount'])),
                'requires_approval': data.get('requires_approval', False)
            }
            
            # Extract entries data
            entries_data = []
            for entry in data['entries']:
                entries_data.append({
                    'account_id': entry['account_id'],
                    'description': entry.get('description', transaction_data['description']),
                    'debit_amount': Decimal(str(entry.get('debit_amount', 0))),
                    'credit_amount': Decimal(str(entry.get('credit_amount', 0))),
                    'cost_center_id': entry.get('cost_center_id'),
                    'reference': entry.get('reference', '')
                })
            
            # Create transaction using service
            from .services import DoubleEntryBookkeepingService
            transaction = DoubleEntryBookkeepingService.create_transaction(
                request.user.profile.school,
                request.user,
                transaction_data,
                entries_data
            )
            
            return JsonResponse({
                'success': True,
                'transaction_id': transaction.transaction_id,
                'message': _('Transaction created successfully'),
                'redirect_url': f'/finance/transactions/{transaction.pk}/'
            })
            
        except ValidationError as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': _('An error occurred while creating the transaction')
            }, status=500)


class AccountLedgerView(LoginRequiredMixin, DetailView):
    """View account ledger with all transactions"""
    model = Account
    template_name = 'finance/accounts/ledger.html'
    context_object_name = 'account'

    def get_queryset(self):
        return Account.objects.filter(
            school=self.request.user.profile.school,
            is_active=True
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        account = self.get_object()
        
        # Get date range from request
        from datetime import date, timedelta
        end_date = date.today()
        start_date = end_date - timedelta(days=365)  # Default to last year
        
        if self.request.GET.get('start_date'):
            start_date = datetime.strptime(self.request.GET['start_date'], '%Y-%m-%d').date()
        if self.request.GET.get('end_date'):
            end_date = datetime.strptime(self.request.GET['end_date'], '%Y-%m-%d').date()
        
        # Get ledger data
        from .services import DoubleEntryBookkeepingService
        context['ledger_data'] = DoubleEntryBookkeepingService.get_account_ledger(
            account, start_date, end_date
        )
        
        context['start_date'] = start_date
        context['end_date'] = end_date
        
        return context


class TrialBalanceView(LoginRequiredMixin, TemplateView):
    """Generate trial balance report"""
    template_name = 'finance/reports/trial_balance.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get date from request or use today
        from datetime import date
        as_of_date = date.today()
        
        if self.request.GET.get('as_of_date'):
            as_of_date = datetime.strptime(self.request.GET['as_of_date'], '%Y-%m-%d').date()
        
        # Generate trial balance
        from .services import ChartOfAccountsService
        context['trial_balance'] = ChartOfAccountsService.generate_account_report(
            self.request.user.profile.school,
            'trial_balance',
            as_of_date
        )
        
        context['as_of_date'] = as_of_date
        
        return context


class TransactionAuditLogView(LoginRequiredMixin, ListView):
    """View transaction audit logs"""
    model = TransactionAuditLog
    template_name = 'finance/audit/transaction_logs.html'
    context_object_name = 'audit_logs'
    paginate_by = 50

    def get_queryset(self):
        return TransactionAuditLog.objects.filter(
            school=self.request.user.profile.school
        ).select_related('transaction', 'user').order_by('-timestamp')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Summary statistics
        queryset = self.get_queryset()
        context['total_logs'] = queryset.count()
        context['recent_actions'] = queryset[:10]
        
        return context