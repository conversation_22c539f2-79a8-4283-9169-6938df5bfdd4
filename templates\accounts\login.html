{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Login" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .login-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        max-width: 900px;
        width: 100%;
    }
    
    .login-form {
        padding: 3rem;
    }
    
    .login-image {
        background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        min-height: 500px;
    }
    
    .school-logo {
        font-size: 5rem;
        margin-bottom: 1rem;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 12px 30px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
    
    .language-switcher {
        position: absolute;
        top: 20px;
        right: 20px;
    }
    
    .language-switcher .btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        margin: 0 5px;
    }
    
    .language-switcher .btn:hover {
        background: rgba(255, 255, 255, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="language-switcher">
    <form method="post" action="{% url 'accounts:change_language' %}" style="display: inline;">
        {% csrf_token %}
        <input type="hidden" name="language" value="en">
        <button type="submit" class="btn btn-sm">English</button>
    </form>
    <form method="post" action="{% url 'accounts:change_language' %}" style="display: inline;">
        {% csrf_token %}
        <input type="hidden" name="language" value="ar">
        <button type="submit" class="btn btn-sm">العربية</button>
    </form>
</div>

<div class="login-container">
    <div class="row g-0">
        <div class="col-lg-6">
            <div class="login-image">
                <div class="text-center">
                    <div class="school-logo">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h2 class="mb-3">{% trans "School ERP System" %}</h2>
                    <p class="lead">{% trans "Comprehensive School Management Solution" %}</p>
                    <div class="mt-4">
                        <div class="row text-center">
                            <div class="col-4">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <p class="small">{% trans "Student Management" %}</p>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <p class="small">{% trans "Analytics & Reports" %}</p>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-mobile-alt fa-2x mb-2"></i>
                                <p class="small">{% trans "Mobile Ready" %}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="login-form">
                <div class="text-center mb-4">
                    <h3 class="text-primary">{% trans "Welcome Back" %}</h3>
                    <p class="text-muted">{% trans "Please sign in to your account" %}</p>
                </div>
                
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {{ form.non_field_errors }}
                    </div>
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user me-2"></i>{{ form.username.label }}
                        </label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger small">{{ form.username.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="fas fa-lock me-2"></i>{{ form.password.label }}
                        </label>
                        {{ form.password }}
                        {% if form.password.errors %}
                            <div class="text-danger small">{{ form.password.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.remember_me }}
                        <label class="form-check-label" for="{{ form.remember_me.id_for_label }}">
                            {{ form.remember_me.label }}
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>{% trans "Sign In" %}
                        </button>
                    </div>
                </form>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        {% trans "Forgot your password?" %} 
                        <a href="#" class="text-primary">{% trans "Reset it here" %}</a>
                    </small>
                </div>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <small class="text-muted">
                        {% trans "Demo Accounts:" %}<br>
                        <strong>Admin:</strong> admin / admin123<br>
                        <strong>Teacher:</strong> teacher / teacher123<br>
                        <strong>Student:</strong> student / student123<br>
                        <strong>Parent:</strong> parent / parent123
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-focus on username field
    document.getElementById('{{ form.username.id_for_label }}').focus();
    
    // Add loading state to login button
    document.querySelector('form').addEventListener('submit', function() {
        const button = this.querySelector('button[type="submit"]');
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Signing In..." %}';
        button.disabled = true;
    });
</script>
{% endblock %}
