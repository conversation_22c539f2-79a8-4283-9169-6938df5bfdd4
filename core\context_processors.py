from django.conf import settings
from django.utils import timezone
from django.db.models import Count, Q
from django.core.cache import cache
from students.models import Student, Class, Grade
from hr.models import Employee, Department
from academics.models import Subject, Teacher
from core.models import AcademicYear


def school_context(request):
    """
    Context processor to provide common school data across all templates
    """
    context = {}
    
    # School basic information
    context['school_name'] = getattr(settings, 'SCHOOL_NAME', 'School ERP System')
    context['school_name_ar'] = getattr(settings, 'SCHOOL_NAME_AR', 'نظام إدارة المدرسة')
    context['current_year'] = timezone.now().year
    
    # Current academic year
    try:
        current_academic_year = AcademicYear.objects.get(is_current=True)
        context['current_academic_year'] = current_academic_year
    except AcademicYear.DoesNotExist:
        context['current_academic_year'] = None
    
    # Basic statistics (cached for performance)
    if request.user.is_authenticated:
        cache_key = 'school_stats'
        stats = cache.get(cache_key)
        
        if not stats:
            stats = {
                'total_students': Student.objects.filter(is_active=True).count(),
                'total_teachers': Teacher.objects.filter(is_active=True).count(),
                'total_employees': Employee.objects.filter(is_active=True, employment_status='active').count(),
                'total_classes': Class.objects.filter(is_active=True).count(),
                'total_subjects': Subject.objects.filter(is_active=True).count(),
                'total_departments': Department.objects.filter(is_active=True).count(),
            }
            cache.set(cache_key, stats, 300)  # Cache for 5 minutes
        
        context.update(stats)
    
    return context


def dashboard_context(request):
    """
    Context processor for dashboard-specific data
    """
    context = {}
    
    if request.user.is_authenticated:
        today = timezone.now().date()
        
        # User-specific dashboard data
        if hasattr(request.user, 'student_profile'):
            student = request.user.student_profile
            context['user_student'] = student
            context['user_class'] = student.current_class
            
        elif hasattr(request.user, 'teacher_profile'):
            teacher = request.user.teacher_profile
            context['user_teacher'] = teacher
            context['teacher_classes'] = Class.objects.filter(class_teacher=request.user, is_active=True)
            
        elif hasattr(request.user, 'employee_profile'):
            employee = request.user.employee_profile
            context['user_employee'] = employee
            
        # Recent activities and notifications
        context['recent_students'] = Student.objects.filter(
            is_active=True,
            created_at__gte=today - timezone.timedelta(days=7)
        ).order_by('-created_at')[:5]
        
    return context


def navigation_context(request):
    """
    Context processor for navigation-specific data
    """
    context = {}
    
    if request.user.is_authenticated:
        # User permissions for navigation
        context['can_manage_students'] = request.user.has_perm('students.view_student')
        context['can_manage_hr'] = request.user.has_perm('hr.view_employee')
        context['can_manage_academics'] = request.user.has_perm('academics.view_subject')
        context['can_view_reports'] = request.user.has_perm('reports.view_report')
        context['can_manage_finance'] = request.user.has_perm('finance.view_account')
        
        # Quick stats for navigation badges
        if context['can_manage_students']:
            context['pending_registrations'] = Student.objects.filter(
                is_active=False
            ).count()
            
        if context['can_manage_hr']:
            from hr.models import LeaveRequest
            context['pending_leave_requests'] = LeaveRequest.objects.filter(
                status='pending'
            ).count()
    
    return context
