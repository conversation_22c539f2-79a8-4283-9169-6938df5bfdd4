{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Grade Management" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-layer-group text-primary me-2"></i>{% trans "Grade Management" %}
                            </h2>
                            <p class="text-muted">{% trans "Manage school grade levels" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:grade_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add New Grade" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Total Grades" %}</h5>
                            <h2 class="display-4">{{ total_grades|default:"0" }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Active Grades" %}</h5>
                            <h2 class="display-4">{{ active_grades|default:"0" }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Total Classes" %}</h5>
                            <h2 class="display-4">{{ total_classes|default:"0" }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Total Students" %}</h5>
                            <h2 class="display-4">{{ total_students|default:"0" }}</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Grade list -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Grades" %}</h5>
                        </div>
                        <div class="card-body">
                            {% if grades %}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Level" %}</th>
                                                <th>{% trans "Grade Name" %}</th>
                                                <th>{% trans "Arabic Name" %}</th>
                                                <th>{% trans "Classes" %}</th>
                                                <th>{% trans "Students" %}</th>
                                                <th>{% trans "Status" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for grade in grades %}
                                                <tr>
                                                    <td>{{ grade.level }}</td>
                                                    <td>{{ grade.name }}</td>
                                                    <td>{{ grade.name_ar|default:"-" }}</td>
                                                    <td>{{ grade.class_count }}</td>
                                                    <td>{{ grade.student_count }}</td>
                                                    <td>
                                                        {% if grade.is_active %}
                                                            <span class="badge bg-success">{% trans "Active" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="{% url 'students:grade_edit' grade.id %}" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#classesModal{{ grade.id }}">
                                                                <i class="fas fa-chalkboard"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>{% trans "No grades found." %}
                                </div>
                                <div class="text-center mt-4">
                                    <a href="{% url 'students:grade_add' %}" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>{% trans "Add New Grade" %}
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Classes Modals -->
{% for grade in grades %}
    <div class="modal fade" id="classesModal{{ grade.id }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{% trans "Classes in" %} {{ grade.name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    {% with grade_classes=grade.classes.all %}
                        {% if grade_classes %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Class Name" %}</th>
                                            <th>{% trans "Academic Year" %}</th>
                                            <th>{% trans "Class Teacher" %}</th>
                                            <th>{% trans "Students" %}</th>
                                            <th>{% trans "Actions" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for class_obj in grade_classes %}
                                            <tr>
                                                <td>{{ class_obj.name }}</td>
                                                <td>{{ class_obj.academic_year }}</td>
                                                <td>
                                                    {% if class_obj.class_teacher %}
                                                        {{ class_obj.class_teacher.first_name }} {{ class_obj.class_teacher.last_name }}
                                                    {% else %}
                                                        <span class="text-muted">{% trans "Not assigned" %}</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ class_obj.current_students_count }}</td>
                                                <td>
                                                    <a href="{% url 'students:class_detail' class_obj.id %}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>{% trans "No classes found for this grade." %}
                            </div>
                        {% endif %}
                        
                        <div class="mt-3">
                            <a href="{% url 'students:class_add' %}?grade={{ grade.id }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add Class to" %} {{ grade.name }}
                            </a>
                        </div>
                    {% endwith %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                </div>
            </div>
        </div>
    </div>
{% endfor %}
{% endblock %}