"""
Advanced Scheduling System for Task 4.2: Develop Class Scheduling System

This module provides:
- Timetable generation algorithms
- Resource conflict detection
- Schedule optimization features
- Teacher availability management
- Room allocation system
"""

from datetime import datetime, timedelta, time
from typing import List, Dict, Tuple, Optional, Set
from django.db.models import Q, Count
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from .models import (
    Schedule, ClassSubject, Teacher, Room, TeacherAvailability, 
    TimetableTemplate, Subject
)
from core.models import AcademicYear, Semester


class SchedulingConflict:
    """Represents a scheduling conflict"""
    
    def __init__(self, conflict_type: str, message: str, schedules: List[Schedule] = None):
        self.conflict_type = conflict_type  # 'teacher', 'room', 'class', 'availability'
        self.message = message
        self.schedules = schedules or []
    
    def __str__(self):
        return f"{self.conflict_type}: {self.message}"


class SchedulingConstraint:
    """Represents a scheduling constraint"""
    
    def __init__(self, constraint_type: str, weight: float = 1.0, description: str = ""):
        self.constraint_type = constraint_type
        self.weight = weight
        self.description = description


class TimetableGenerator:
    """
    Advanced timetable generation with optimization algorithms
    """
    
    def __init__(self, school, academic_year: AcademicYear):
        self.school = school
        self.academic_year = academic_year
        self.constraints = []
        self.conflicts = []
        
        # Default constraints
        self.add_constraint('no_teacher_conflicts', weight=10.0)
        self.add_constraint('no_room_conflicts', weight=10.0)
        self.add_constraint('no_class_conflicts', weight=10.0)
        self.add_constraint('teacher_availability', weight=8.0)
        self.add_constraint('room_suitability', weight=6.0)
        self.add_constraint('balanced_distribution', weight=4.0)
        self.add_constraint('minimize_gaps', weight=3.0)
    
    def add_constraint(self, constraint_type: str, weight: float = 1.0, description: str = ""):
        """Add a scheduling constraint"""
        constraint = SchedulingConstraint(constraint_type, weight, description)
        self.constraints.append(constraint)
    
    def get_class_subjects_to_schedule(self) -> List[ClassSubject]:
        """Get all class subjects that need scheduling"""
        return ClassSubject.objects.filter(
            school=self.school,
            academic_year=self.academic_year,
            is_active=True
        ).select_related('class_obj', 'subject', 'teacher', 'semester')
    
    def get_available_time_slots(self, template: TimetableTemplate) -> List[Dict]:
        """Get available time slots from timetable template"""
        return template.generate_time_slots()
    
    def get_working_days(self, template: TimetableTemplate) -> List[str]:
        """Get working days from template"""
        return template.working_days or ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
    
    def find_suitable_rooms(self, class_subject: ClassSubject) -> List[Room]:
        """Find rooms suitable for a class subject"""
        rooms = Room.objects.filter(
            school=self.school,
            is_available=True,
            capacity__gte=class_subject.effective_max_students
        )
        
        suitable_rooms = []
        for room in rooms:
            if room.is_suitable_for_subject(class_subject.subject):
                suitable_rooms.append(room)
        
        return suitable_rooms
    
    def check_teacher_availability(self, teacher: Teacher, day: str, start_time: time, end_time: time) -> bool:
        """Check if teacher is available at specified time"""
        availability = TeacherAvailability.objects.filter(
            teacher=teacher,
            day_of_week=day,
            start_time__lte=start_time,
            end_time__gte=end_time,
            academic_year=self.academic_year
        ).exists()
        
        return availability
    
    def detect_conflicts(self, schedule: Schedule) -> List[SchedulingConflict]:
        """Detect all conflicts for a proposed schedule"""
        conflicts = []
        
        # Teacher conflicts
        teacher_conflicts = schedule.check_teacher_conflicts()
        if teacher_conflicts:
            conflicts.append(SchedulingConflict(
                'teacher',
                f'Teacher {schedule.class_subject.teacher} has conflicting schedules',
                teacher_conflicts
            ))
        
        # Room conflicts
        room_conflicts = schedule.check_room_conflicts()
        if room_conflicts:
            conflicts.append(SchedulingConflict(
                'room',
                f'Room {schedule.room} has conflicting schedules',
                room_conflicts
            ))
        
        # Class conflicts
        class_conflicts = schedule.check_class_conflicts()
        if class_conflicts:
            conflicts.append(SchedulingConflict(
                'class',
                f'Class {schedule.class_subject.class_obj} has conflicting schedules',
                class_conflicts
            ))
        
        # Availability conflicts
        if not schedule.is_teacher_available():
            conflicts.append(SchedulingConflict(
                'availability',
                f'Teacher {schedule.class_subject.teacher} is not available at this time'
            ))
        
        return conflicts
    
    def calculate_schedule_score(self, schedules: List[Schedule]) -> float:
        """Calculate optimization score for a set of schedules"""
        score = 0.0
        
        for schedule in schedules:
            # Penalty for conflicts
            conflicts = self.detect_conflicts(schedule)
            for conflict in conflicts:
                constraint = next((c for c in self.constraints if c.constraint_type == conflict.conflict_type), None)
                if constraint:
                    score -= constraint.weight * 10
            
            # Bonus for preferred times
            if hasattr(schedule.class_subject.teacher, 'availability_slots'):
                preferred_slots = schedule.class_subject.teacher.availability_slots.filter(
                    day_of_week=schedule.day_of_week,
                    is_preferred=True
                )
                for slot in preferred_slots:
                    if slot.start_time <= schedule.start_time and slot.end_time >= schedule.end_time:
                        score += 5.0
            
            # Bonus for room suitability
            if schedule.is_room_suitable():
                score += 3.0
        
        return score
    
    def generate_schedule_for_class_subject(self, class_subject: ClassSubject, 
                                          template: TimetableTemplate) -> List[Schedule]:
        """Generate optimal schedule for a single class subject"""
        schedules = []
        time_slots = self.get_available_time_slots(template)
        working_days = self.get_working_days(template)
        suitable_rooms = self.find_suitable_rooms(class_subject)
        
        # Calculate how many sessions per week are needed
        sessions_needed = class_subject.weekly_hours
        
        # Try to distribute sessions across the week
        sessions_per_day = max(1, sessions_needed // len(working_days))
        remaining_sessions = sessions_needed % len(working_days)
        
        day_index = 0
        sessions_scheduled = 0
        
        while sessions_scheduled < sessions_needed and day_index < len(working_days):
            day = working_days[day_index]
            sessions_for_today = sessions_per_day
            
            if remaining_sessions > 0:
                sessions_for_today += 1
                remaining_sessions -= 1
            
            # Find best time slots for this day
            best_slots = self._find_best_time_slots(
                class_subject, day, sessions_for_today, time_slots, suitable_rooms
            )
            
            for slot_info in best_slots:
                schedule = Schedule(
                    school=self.school,
                    class_subject=class_subject,
                    day_of_week=day,
                    start_time=slot_info['start_time'],
                    end_time=slot_info['end_time'],
                    room=slot_info.get('room'),
                    period_number=slot_info.get('period'),
                    status='active'
                )
                
                schedules.append(schedule)
                sessions_scheduled += 1
            
            day_index += 1
        
        return schedules
    
    def _find_best_time_slots(self, class_subject: ClassSubject, day: str, 
                             sessions_needed: int, time_slots: List[Dict], 
                             suitable_rooms: List[Room]) -> List[Dict]:
        """Find best time slots for a class subject on a specific day"""
        best_slots = []
        teacher = class_subject.teacher
        
        # Get teacher's availability for this day
        teacher_availability = TeacherAvailability.objects.filter(
            teacher=teacher,
            day_of_week=day,
            academic_year=self.academic_year
        ).order_by('start_time')
        
        for availability in teacher_availability:
            available_slots = []
            
            # Find time slots that fit within teacher's availability
            for slot in time_slots:
                if (availability.start_time <= slot['start_time'] and 
                    availability.end_time >= slot['end_time']):
                    
                    # Check if any suitable room is available
                    for room in suitable_rooms:
                        room_conflicts = Schedule.objects.filter(
                            room=room,
                            day_of_week=day,
                            start_time__lt=slot['end_time'],
                            end_time__gt=slot['start_time'],
                            status='active'
                        ).exists()
                        
                        if not room_conflicts:
                            slot_info = slot.copy()
                            slot_info['room'] = room
                            slot_info['score'] = self._calculate_slot_score(
                                class_subject, day, slot, room, availability
                            )
                            available_slots.append(slot_info)
                            break
            
            # Sort by score and take the best slots
            available_slots.sort(key=lambda x: x['score'], reverse=True)
            best_slots.extend(available_slots[:sessions_needed])
            
            if len(best_slots) >= sessions_needed:
                break
        
        return best_slots[:sessions_needed]
    
    def _calculate_slot_score(self, class_subject: ClassSubject, day: str, 
                             slot: Dict, room: Room, availability: TeacherAvailability) -> float:
        """Calculate score for a specific time slot"""
        score = 0.0
        
        # Prefer preferred time slots
        if availability.is_preferred:
            score += 10.0
        
        # Prefer earlier time slots (morning classes)
        hour = slot['start_time'].hour
        if 8 <= hour <= 10:
            score += 5.0
        elif 10 <= hour <= 12:
            score += 3.0
        elif 14 <= hour <= 16:
            score += 2.0
        
        # Prefer rooms with better equipment match
        if class_subject.subject.requires_special_equipment:
            if room.has_projector:
                score += 2.0
            if room.has_computer:
                score += 2.0
        
        # Prefer larger rooms for larger classes
        capacity_ratio = room.capacity / class_subject.effective_max_students
        if 1.2 <= capacity_ratio <= 1.5:  # Optimal capacity ratio
            score += 3.0
        elif capacity_ratio > 2.0:  # Too large
            score -= 1.0
        
        return score
    
    def generate_complete_timetable(self, template: TimetableTemplate = None) -> Dict:
        """Generate complete timetable for all classes"""
        if not template:
            template = TimetableTemplate.objects.filter(
                school=self.school,
                academic_year=self.academic_year,
                is_active=True
            ).first()
        
        if not template:
            raise ValidationError(_('No active timetable template found'))
        
        class_subjects = self.get_class_subjects_to_schedule()
        all_schedules = []
        generation_report = {
            'total_class_subjects': len(class_subjects),
            'successfully_scheduled': 0,
            'failed_to_schedule': 0,
            'conflicts_detected': 0,
            'warnings': []
        }
        
        # Sort class subjects by priority (core subjects first, then by weekly hours)
        class_subjects = sorted(
            class_subjects,
            key=lambda cs: (
                0 if cs.subject.subject_type == 'core' else 1,
                -cs.weekly_hours,
                cs.subject.name
            )
        )
        
        for class_subject in class_subjects:
            try:
                schedules = self.generate_schedule_for_class_subject(class_subject, template)
                
                # Validate schedules
                valid_schedules = []
                for schedule in schedules:
                    conflicts = self.detect_conflicts(schedule)
                    if not conflicts:
                        valid_schedules.append(schedule)
                    else:
                        generation_report['conflicts_detected'] += len(conflicts)
                        generation_report['warnings'].append(
                            f'Conflicts detected for {class_subject}: {[str(c) for c in conflicts]}'
                        )
                
                if valid_schedules:
                    all_schedules.extend(valid_schedules)
                    generation_report['successfully_scheduled'] += 1
                else:
                    generation_report['failed_to_schedule'] += 1
                    generation_report['warnings'].append(
                        f'Failed to schedule {class_subject} - no valid time slots found'
                    )
                    
            except Exception as e:
                generation_report['failed_to_schedule'] += 1
                generation_report['warnings'].append(
                    f'Error scheduling {class_subject}: {str(e)}'
                )
        
        return {
            'schedules': all_schedules,
            'report': generation_report,
            'template': template
        }
    
    def optimize_existing_timetable(self) -> Dict:
        """Optimize existing timetable using genetic algorithm approach"""
        existing_schedules = Schedule.objects.filter(
            school=self.school,
            class_subject__academic_year=self.academic_year,
            status='active'
        )
        
        optimization_report = {
            'initial_score': self.calculate_schedule_score(existing_schedules),
            'final_score': 0,
            'improvements_made': 0,
            'conflicts_resolved': 0
        }
        
        # Identify problematic schedules
        problematic_schedules = []
        for schedule in existing_schedules:
            conflicts = self.detect_conflicts(schedule)
            if conflicts:
                problematic_schedules.append((schedule, conflicts))
        
        # Try to resolve conflicts by rescheduling
        for schedule, conflicts in problematic_schedules:
            improved_schedule = self._find_better_time_slot(schedule)
            if improved_schedule:
                # Update the schedule
                schedule.day_of_week = improved_schedule.day_of_week
                schedule.start_time = improved_schedule.start_time
                schedule.end_time = improved_schedule.end_time
                schedule.room = improved_schedule.room
                
                optimization_report['improvements_made'] += 1
                optimization_report['conflicts_resolved'] += len(conflicts)
        
        optimization_report['final_score'] = self.calculate_schedule_score(existing_schedules)
        
        return optimization_report
    
    def _find_better_time_slot(self, schedule: Schedule) -> Optional[Schedule]:
        """Find a better time slot for a problematic schedule"""
        template = TimetableTemplate.objects.filter(
            school=self.school,
            academic_year=self.academic_year,
            is_active=True
        ).first()
        
        if not template:
            return None
        
        time_slots = self.get_available_time_slots(template)
        working_days = self.get_working_days(template)
        suitable_rooms = self.find_suitable_rooms(schedule.class_subject)
        
        best_alternative = None
        best_score = -float('inf')
        
        for day in working_days:
            for slot in time_slots:
                for room in suitable_rooms:
                    alternative = Schedule(
                        school=self.school,
                        class_subject=schedule.class_subject,
                        day_of_week=day,
                        start_time=slot['start_time'],
                        end_time=slot['end_time'],
                        room=room,
                        status='active'
                    )
                    
                    conflicts = self.detect_conflicts(alternative)
                    if not conflicts:
                        score = self._calculate_slot_score(
                            schedule.class_subject, day, slot, room,
                            TeacherAvailability.objects.filter(
                                teacher=schedule.class_subject.teacher,
                                day_of_week=day
                            ).first()
                        )
                        
                        if score > best_score:
                            best_score = score
                            best_alternative = alternative
        
        return best_alternative


class ResourceAllocator:
    """
    Advanced resource allocation system for rooms and equipment
    """
    
    def __init__(self, school, academic_year: AcademicYear):
        self.school = school
        self.academic_year = academic_year
    
    def get_room_utilization(self) -> Dict[Room, Dict]:
        """Calculate room utilization statistics"""
        rooms = Room.objects.filter(school=self.school, is_available=True)
        utilization_data = {}
        
        for room in rooms:
            schedules = Schedule.objects.filter(
                room=room,
                class_subject__academic_year=self.academic_year,
                status='active'
            )
            
            total_hours = sum(schedule.get_duration_minutes() for schedule in schedules) / 60
            
            # Assume 8 hours per day, 5 days per week
            max_possible_hours = 8 * 5
            utilization_percentage = (total_hours / max_possible_hours) * 100 if max_possible_hours > 0 else 0
            
            utilization_data[room] = {
                'total_hours': total_hours,
                'utilization_percentage': utilization_percentage,
                'schedule_count': schedules.count(),
                'peak_hours': self._get_peak_hours(schedules)
            }
        
        return utilization_data
    
    def _get_peak_hours(self, schedules) -> List[str]:
        """Get peak usage hours for a room"""
        hour_counts = {}
        
        for schedule in schedules:
            hour = schedule.start_time.hour
            hour_counts[hour] = hour_counts.get(hour, 0) + 1
        
        if not hour_counts:
            return []
        
        max_count = max(hour_counts.values())
        peak_hours = [f"{hour}:00" for hour, count in hour_counts.items() if count == max_count]
        
        return peak_hours
    
    def suggest_room_allocation(self, class_subject: ClassSubject) -> List[Tuple[Room, float]]:
        """Suggest optimal room allocation with scoring"""
        suitable_rooms = Room.objects.filter(
            school=self.school,
            is_available=True,
            capacity__gte=class_subject.effective_max_students
        )
        
        room_scores = []
        
        for room in suitable_rooms:
            score = self._calculate_room_score(room, class_subject)
            room_scores.append((room, score))
        
        # Sort by score (highest first)
        room_scores.sort(key=lambda x: x[1], reverse=True)
        
        return room_scores
    
    def _calculate_room_score(self, room: Room, class_subject: ClassSubject) -> float:
        """Calculate suitability score for a room"""
        score = 0.0
        
        # Base score for capacity match
        capacity_ratio = room.capacity / class_subject.effective_max_students
        if 1.0 <= capacity_ratio <= 1.5:
            score += 10.0
        elif 1.5 < capacity_ratio <= 2.0:
            score += 7.0
        elif capacity_ratio > 2.0:
            score += 3.0  # Too large
        
        # Equipment match
        subject = class_subject.subject
        if subject.requires_special_equipment:
            if 'projector' in subject.equipment_requirements.lower() and room.has_projector:
                score += 5.0
            if 'computer' in subject.equipment_requirements.lower() and room.has_computer:
                score += 5.0
        
        # Room type match
        if subject.requires_lab and room.room_type in ['laboratory', 'computer_lab']:
            score += 8.0
        elif not subject.requires_lab and room.room_type == 'classroom':
            score += 5.0
        
        # Accessibility
        if room.is_accessible:
            score += 2.0
        
        # Current utilization (prefer less utilized rooms)
        current_schedules = Schedule.objects.filter(
            room=room,
            class_subject__academic_year=self.academic_year,
            status='active'
        ).count()
        
        if current_schedules < 10:
            score += 3.0
        elif current_schedules < 20:
            score += 1.0
        
        return score
    
    def detect_resource_conflicts(self) -> List[Dict]:
        """Detect resource allocation conflicts"""
        conflicts = []
        
        # Room double-booking conflicts
        room_conflicts = Schedule.objects.filter(
            school=self.school,
            class_subject__academic_year=self.academic_year,
            status='active'
        ).values('room', 'day_of_week', 'start_time', 'end_time').annotate(
            count=Count('id')
        ).filter(count__gt=1)
        
        for conflict in room_conflicts:
            if conflict['room']:
                room = Room.objects.get(pk=conflict['room'])
                conflicts.append({
                    'type': 'room_conflict',
                    'resource': room,
                    'day': conflict['day_of_week'],
                    'time': f"{conflict['start_time']} - {conflict['end_time']}",
                    'count': conflict['count']
                })
        
        # Capacity violations
        schedules = Schedule.objects.filter(
            school=self.school,
            class_subject__academic_year=self.academic_year,
            status='active'
        ).select_related('room', 'class_subject')
        
        for schedule in schedules:
            if schedule.room and schedule.room.capacity < schedule.class_subject.effective_max_students:
                conflicts.append({
                    'type': 'capacity_violation',
                    'schedule': schedule,
                    'room_capacity': schedule.room.capacity,
                    'required_capacity': schedule.class_subject.effective_max_students
                })
        
        return conflicts


class ScheduleAnalyzer:
    """
    Analytics and reporting for scheduling system
    """
    
    def __init__(self, school, academic_year: AcademicYear):
        self.school = school
        self.academic_year = academic_year
    
    def generate_schedule_report(self) -> Dict:
        """Generate comprehensive schedule analysis report"""
        schedules = Schedule.objects.filter(
            school=self.school,
            class_subject__academic_year=self.academic_year,
            status='active'
        ).select_related('class_subject', 'room')
        
        report = {
            'total_schedules': schedules.count(),
            'by_day': self._analyze_by_day(schedules),
            'by_time': self._analyze_by_time(schedules),
            'teacher_workload': self._analyze_teacher_workload(schedules),
            'room_utilization': self._analyze_room_utilization(schedules),
            'subject_distribution': self._analyze_subject_distribution(schedules),
            'conflicts': self._analyze_conflicts(schedules)
        }
        
        return report
    
    def _analyze_by_day(self, schedules) -> Dict:
        """Analyze schedule distribution by day"""
        day_counts = {}
        for schedule in schedules:
            day = schedule.day_of_week
            day_counts[day] = day_counts.get(day, 0) + 1
        
        return day_counts
    
    def _analyze_by_time(self, schedules) -> Dict:
        """Analyze schedule distribution by time"""
        time_slots = {}
        for schedule in schedules:
            hour = schedule.start_time.hour
            time_slots[f"{hour}:00"] = time_slots.get(f"{hour}:00", 0) + 1
        
        return time_slots
    
    def _analyze_teacher_workload(self, schedules) -> Dict:
        """Analyze teacher workload distribution"""
        teacher_hours = {}
        
        for schedule in schedules:
            teacher = schedule.class_subject.teacher
            teacher_key = f"{teacher.user.first_name} {teacher.user.last_name}"
            
            if teacher_key not in teacher_hours:
                teacher_hours[teacher_key] = 0
            
            teacher_hours[teacher_key] += schedule.get_duration_minutes() / 60
        
        return teacher_hours
    
    def _analyze_room_utilization(self, schedules) -> Dict:
        """Analyze room utilization"""
        room_usage = {}
        
        for schedule in schedules:
            if schedule.room:
                room_key = str(schedule.room)
                if room_key not in room_usage:
                    room_usage[room_key] = 0
                
                room_usage[room_key] += schedule.get_duration_minutes() / 60
        
        return room_usage
    
    def _analyze_subject_distribution(self, schedules) -> Dict:
        """Analyze subject distribution"""
        subject_counts = {}
        
        for schedule in schedules:
            subject = schedule.class_subject.subject.name
            subject_counts[subject] = subject_counts.get(subject, 0) + 1
        
        return subject_counts
    
    def _analyze_conflicts(self, schedules) -> Dict:
        """Analyze scheduling conflicts"""
        conflicts = {
            'teacher_conflicts': 0,
            'room_conflicts': 0,
            'class_conflicts': 0
        }
        
        for schedule in schedules:
            schedule_conflicts = schedule.get_all_conflicts()
            conflicts['teacher_conflicts'] += len(schedule_conflicts['teacher'])
            conflicts['room_conflicts'] += len(schedule_conflicts['room'])
            conflicts['class_conflicts'] += len(schedule_conflicts['class'])
        
        return conflicts