from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone
from datetime import date, timedelta
from decimal import Decimal

from core.models import School, AcademicYear
from students.models import (
    Grade, Class, Parent, Student, StudentDocument, StudentEnrollment,
    VacationRequest, StudentInfraction, StudentTransfer, StudentAttachment,
    ElectronicRegistration
)

User = get_user_model()


class StudentModelTestCase(TestCase):
    """Test cases for Student model"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1
        )
        
        self.class_obj = Class.objects.create(
            school=self.school,
            name="A",
            grade=self.grade,
            academic_year=self.academic_year,
            max_students=30
        )
        
        # Create users
        self.parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        self.student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            is_staff=True
        )
        
        # Create parent
        self.parent = Parent.objects.create(
            school=self.school,
            user=self.parent_user,
            father_name="John Doe",
            father_phone="+1234567890",
            home_address="123 Parent St"
        )

    def test_student_creation(self):
        """Test student creation with valid data"""
        student = Student.objects.create(
            school=self.school,
            user=self.student_user,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=self.parent,
            admission_date=date(2024, 9, 1)
        )
        
        self.assertEqual(student.full_name, "Jane Doe")
        self.assertEqual(student.age, 10)  # Assuming current year is 2025
        self.assertTrue(student.student_id)  # Auto-generated
        self.assertTrue(student.admission_number)  # Auto-generated
        self.assertFalse(student.is_graduated)

    def test_student_id_generation(self):
        """Test automatic student ID generation"""
        student = Student.objects.create(
            school=self.school,
            user=self.student_user,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=self.parent,
            admission_date=date(2024, 9, 1)
        )
        
        self.assertTrue(student.student_id.startswith("TES"))  # School code
        self.assertIn("2025", student.student_id)  # Current year

    def test_student_age_validation(self):
        """Test student age validation"""
        # Test age too young
        with self.assertRaises(ValidationError):
            student = Student(
                school=self.school,
                user=self.student_user,
                first_name="Baby",
                last_name="Doe",
                date_of_birth=date(2023, 1, 1),  # 2 years old
                gender="M",
                nationality="US",
                parent=self.parent,
                admission_date=date(2024, 9, 1)
            )
            student.full_clean()
        
        # Test age too old
        with self.assertRaises(ValidationError):
            student = Student(
                school=self.school,
                user=self.student_user,
                first_name="Old",
                last_name="Doe",
                date_of_birth=date(1995, 1, 1),  # 30 years old
                gender="M",
                nationality="US",
                parent=self.parent,
                admission_date=date(2024, 9, 1)
            )
            student.full_clean()

    def test_student_date_validation(self):
        """Test student date validation"""
        # Test future birth date
        with self.assertRaises(ValidationError):
            student = Student(
                school=self.school,
                user=self.student_user,
                first_name="Future",
                last_name="Doe",
                date_of_birth=date(2030, 1, 1),
                gender="M",
                nationality="US",
                parent=self.parent,
                admission_date=date(2024, 9, 1)
            )
            student.full_clean()
        
        # Test admission date before birth date
        with self.assertRaises(ValidationError):
            student = Student(
                school=self.school,
                user=self.student_user,
                first_name="Wrong",
                last_name="Doe",
                date_of_birth=date(2015, 1, 1),
                gender="M",
                nationality="US",
                parent=self.parent,
                admission_date=date(2010, 1, 1)  # Before birth
            )
            student.full_clean()


class ParentModelTestCase(TestCase):
    """Test cases for Parent model"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )

    def test_parent_creation(self):
        """Test parent creation with valid data"""
        parent = Parent.objects.create(
            school=self.school,
            user=self.parent_user,
            father_name="John Doe",
            mother_name="Jane Doe",
            father_phone="+1234567890",
            mother_phone="+1234567891",
            home_address="123 Parent St"
        )
        
        self.assertEqual(parent.primary_contact_name, "John Doe")
        self.assertEqual(parent.primary_contact_phone, "+1234567890")

    def test_parent_phone_validation(self):
        """Test parent phone number validation"""
        with self.assertRaises(ValidationError):
            parent = Parent(
                school=self.school,
                user=self.parent_user,
                father_name="John Doe",
                father_phone="invalid-phone",
                home_address="123 Parent St"
            )
            parent.full_clean()

    def test_parent_name_validation(self):
        """Test that at least one parent name is required"""
        with self.assertRaises(ValidationError):
            parent = Parent(
                school=self.school,
                user=self.parent_user,
                father_phone="+1234567890",
                home_address="123 Parent St"
            )
            parent.full_clean()


class StudentEnrollmentTestCase(TestCase):
    """Test cases for StudentEnrollment model"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1
        )
        
        self.class_obj = Class.objects.create(
            school=self.school,
            name="A",
            grade=self.grade,
            academic_year=self.academic_year,
            max_students=2  # Small capacity for testing
        )
        
        # Create users and parent
        self.parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        self.student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            is_staff=True
        )
        
        self.parent = Parent.objects.create(
            school=self.school,
            user=self.parent_user,
            father_name="John Doe",
            father_phone="+1234567890",
            home_address="123 Parent St"
        )
        
        self.student = Student.objects.create(
            school=self.school,
            user=self.student_user,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=self.parent,
            admission_date=date(2024, 9, 1)
        )

    def test_enrollment_creation(self):
        """Test enrollment creation with valid data"""
        enrollment = StudentEnrollment.objects.create(
            school=self.school,
            student=self.student,
            class_obj=self.class_obj,
            academic_year=self.academic_year,
            enrollment_date=date(2024, 9, 1),
            enrolled_by=self.admin_user
        )
        
        self.assertEqual(enrollment.status, 'active')
        self.assertTrue(enrollment.roll_number)  # Auto-generated
        self.assertTrue(enrollment.is_active)

    def test_enrollment_date_validation(self):
        """Test enrollment date validation"""
        # Test enrollment date outside academic year
        with self.assertRaises(ValidationError):
            enrollment = StudentEnrollment(
                school=self.school,
                student=self.student,
                class_obj=self.class_obj,
                academic_year=self.academic_year,
                enrollment_date=date(2023, 1, 1),  # Before academic year
                enrolled_by=self.admin_user
            )
            enrollment.full_clean()

    def test_class_capacity_validation(self):
        """Test class capacity validation"""
        # Create first enrollment
        StudentEnrollment.objects.create(
            school=self.school,
            student=self.student,
            class_obj=self.class_obj,
            academic_year=self.academic_year,
            enrollment_date=date(2024, 9, 1),
            enrolled_by=self.admin_user
        )
        
        # Create second student and enrollment
        student_user2 = User.objects.create_user(
            username="student2",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student2 = Student.objects.create(
            school=self.school,
            user=student_user2,
            first_name="John",
            last_name="Smith",
            date_of_birth=date(2015, 2, 1),
            gender="M",
            nationality="US",
            parent=self.parent,
            admission_date=date(2024, 9, 2)  # Different admission date
        )
        
        StudentEnrollment.objects.create(
            school=self.school,
            student=student2,
            class_obj=self.class_obj,
            academic_year=self.academic_year,
            enrollment_date=date(2024, 9, 1),
            enrolled_by=self.admin_user
        )
        
        # Try to create third enrollment (should fail due to capacity)
        student_user3 = User.objects.create_user(
            username="student3",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student3 = Student.objects.create(
            school=self.school,
            user=student_user3,
            first_name="Bob",
            last_name="Johnson",
            date_of_birth=date(2015, 3, 1),
            gender="M",
            nationality="US",
            parent=self.parent,
            admission_date=date(2024, 9, 3)  # Different admission date
        )
        
        with self.assertRaises(ValidationError):
            enrollment = StudentEnrollment(
                school=self.school,
                student=student3,
                class_obj=self.class_obj,
                academic_year=self.academic_year,
                enrollment_date=date(2024, 9, 1),
                enrolled_by=self.admin_user
            )
            enrollment.full_clean()

    def test_roll_number_generation(self):
        """Test automatic roll number generation"""
        enrollment1 = StudentEnrollment.objects.create(
            school=self.school,
            student=self.student,
            class_obj=self.class_obj,
            academic_year=self.academic_year,
            enrollment_date=date(2024, 9, 1),
            enrolled_by=self.admin_user
        )
        
        self.assertEqual(enrollment1.roll_number, '001')


class VacationRequestTestCase(TestCase):
    """Test cases for VacationRequest model"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        # Create users and parent
        self.parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        self.student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            is_staff=True
        )
        
        self.parent = Parent.objects.create(
            school=self.school,
            user=self.parent_user,
            father_name="John Doe",
            father_phone="+1234567890",
            home_address="123 Parent St"
        )
        
        self.student = Student.objects.create(
            school=self.school,
            user=self.student_user,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=self.parent,
            admission_date=date(2024, 9, 1)
        )

    def test_vacation_request_creation(self):
        """Test vacation request creation with valid data"""
        tomorrow = date.today() + timedelta(days=1)
        end_date = tomorrow + timedelta(days=5)
        
        vacation = VacationRequest.objects.create(
            school=self.school,
            student=self.student,
            start_date=tomorrow,
            end_date=end_date,
            reason="Family vacation",
            requested_by=self.parent_user
        )
        
        self.assertEqual(vacation.status, 'pending')
        self.assertEqual(vacation.duration_days, 6)

    def test_vacation_date_validation(self):
        """Test vacation date validation"""
        tomorrow = date.today() + timedelta(days=1)
        
        # Test end date before start date
        with self.assertRaises(ValidationError):
            vacation = VacationRequest(
                school=self.school,
                student=self.student,
                start_date=tomorrow,
                end_date=date.today(),  # Before start date
                reason="Test",
                requested_by=self.parent_user
            )
            vacation.full_clean()

    def test_vacation_duration_validation(self):
        """Test vacation duration validation"""
        tomorrow = date.today() + timedelta(days=1)
        end_date = tomorrow + timedelta(days=35)  # 36 days
        
        with self.assertRaises(ValidationError):
            vacation = VacationRequest(
                school=self.school,
                student=self.student,
                start_date=tomorrow,
                end_date=end_date,
                reason="Too long vacation",
                requested_by=self.parent_user
            )
            vacation.full_clean()

    def test_vacation_approval(self):
        """Test vacation approval process"""
        tomorrow = date.today() + timedelta(days=1)
        end_date = tomorrow + timedelta(days=5)
        
        vacation = VacationRequest.objects.create(
            school=self.school,
            student=self.student,
            start_date=tomorrow,
            end_date=end_date,
            reason="Family vacation",
            requested_by=self.parent_user
        )
        
        vacation.approve(self.admin_user)
        
        self.assertEqual(vacation.status, 'approved')
        self.assertEqual(vacation.approved_by, self.admin_user)
        self.assertIsNotNone(vacation.approved_at)


class StudentDocumentTestCase(TestCase):
    """Test cases for StudentDocument model"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        # Create users and parent
        self.parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        self.student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            is_staff=True
        )
        
        self.parent = Parent.objects.create(
            school=self.school,
            user=self.parent_user,
            father_name="John Doe",
            father_phone="+1234567890",
            home_address="123 Parent St"
        )
        
        self.student = Student.objects.create(
            school=self.school,
            user=self.student_user,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=self.parent,
            admission_date=date(2024, 9, 1)
        )

    def test_document_creation(self):
        """Test document creation with valid data"""
        # Create a simple test file
        test_file = SimpleUploadedFile(
            "test.pdf",
            b"file_content",
            content_type="application/pdf"
        )
        
        document = StudentDocument.objects.create(
            school=self.school,
            student=self.student,
            document_type='birth_certificate',
            title='Birth Certificate',
            file=test_file,
            uploaded_by=self.admin_user
        )
        
        self.assertEqual(document.title, 'Birth Certificate')
        self.assertFalse(document.is_verified)

    def test_document_verification(self):
        """Test document verification process"""
        test_file = SimpleUploadedFile(
            "test.pdf",
            b"file_content",
            content_type="application/pdf"
        )
        
        document = StudentDocument.objects.create(
            school=self.school,
            student=self.student,
            document_type='birth_certificate',
            title='Birth Certificate',
            file=test_file,
            uploaded_by=self.admin_user
        )
        
        document.verify_document(self.admin_user)
        
        self.assertTrue(document.is_verified)
        self.assertEqual(document.verified_by, self.admin_user)
        self.assertIsNotNone(document.verified_at)


class StudentInfractionTestCase(TestCase):
    """Test cases for StudentInfraction model"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        # Create users and parent
        self.parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        self.student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        self.teacher_user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        self.parent = Parent.objects.create(
            school=self.school,
            user=self.parent_user,
            father_name="John Doe",
            father_phone="+1234567890",
            home_address="123 Parent St"
        )
        
        self.student = Student.objects.create(
            school=self.school,
            user=self.student_user,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=self.parent,
            admission_date=date(2024, 9, 1)
        )

    def test_infraction_creation(self):
        """Test infraction creation with valid data"""
        infraction = StudentInfraction.objects.create(
            school=self.school,
            student=self.student,
            incident_date=date.today(),
            description="Talking in class",
            severity='minor',
            action_taken='warning',
            reported_by=self.teacher_user
        )
        
        self.assertEqual(infraction.severity, 'minor')
        self.assertEqual(infraction.action_taken, 'warning')
        self.assertFalse(infraction.parent_notified)

    def test_infraction_date_validation(self):
        """Test infraction date validation"""
        # Test future incident date
        with self.assertRaises(ValidationError):
            infraction = StudentInfraction(
                school=self.school,
                student=self.student,
                incident_date=date.today() + timedelta(days=1),
                description="Future incident",
                severity='minor',
                action_taken='warning',
                reported_by=self.teacher_user
            )
            infraction.full_clean()

    def test_infraction_severity_methods(self):
        """Test infraction severity-related methods"""
        # Test minor infraction
        minor_infraction = StudentInfraction.objects.create(
            school=self.school,
            student=self.student,
            incident_date=date.today(),
            description="Minor issue",
            severity='minor',
            action_taken='warning',
            reported_by=self.teacher_user
        )
        
        self.assertFalse(minor_infraction.requires_immediate_action())
        self.assertEqual(minor_infraction.get_severity_color(), '#28a745')
        
        # Test severe infraction
        severe_infraction = StudentInfraction.objects.create(
            school=self.school,
            student=self.student,
            incident_date=date.today(),
            description="Severe issue",
            severity='severe',
            action_taken='suspension',
            reported_by=self.teacher_user
        )
        
        self.assertTrue(severe_infraction.requires_immediate_action())
        self.assertEqual(severe_infraction.get_severity_color(), '#dc3545')


class StudentTransferTestCase(TestCase):
    """Test cases for StudentTransfer model"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1
        )
        
        self.class1 = Class.objects.create(
            school=self.school,
            name="A",
            grade=self.grade,
            academic_year=self.academic_year
        )
        
        self.class2 = Class.objects.create(
            school=self.school,
            name="B",
            grade=self.grade,
            academic_year=self.academic_year
        )
        
        # Create users and parent
        self.parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        self.student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            is_staff=True
        )
        
        self.parent = Parent.objects.create(
            school=self.school,
            user=self.parent_user,
            father_name="John Doe",
            father_phone="+1234567890",
            home_address="123 Parent St"
        )
        
        self.student = Student.objects.create(
            school=self.school,
            user=self.student_user,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=self.parent,
            admission_date=date(2024, 9, 1),
            current_class=self.class1
        )

    def test_internal_transfer_creation(self):
        """Test internal transfer creation"""
        transfer = StudentTransfer.objects.create(
            school=self.school,
            student=self.student,
            transfer_type='internal',
            from_class=self.class1,
            to_class=self.class2,
            transfer_date=date.today() + timedelta(days=1),
            reason="Better fit for student",
            requested_by=self.admin_user
        )
        
        self.assertEqual(transfer.transfer_type, 'internal')
        self.assertEqual(transfer.status, 'pending')

    def test_transfer_validation(self):
        """Test transfer validation"""
        # Test internal transfer with same from and to class
        with self.assertRaises(ValidationError):
            transfer = StudentTransfer(
                school=self.school,
                student=self.student,
                transfer_type='internal',
                from_class=self.class1,
                to_class=self.class1,  # Same as from_class
                transfer_date=date.today() + timedelta(days=1),
                reason="Invalid transfer",
                requested_by=self.admin_user
            )
            transfer.full_clean()

    def test_transfer_approval_process(self):
        """Test transfer approval and completion process"""
        transfer = StudentTransfer.objects.create(
            school=self.school,
            student=self.student,
            transfer_type='internal',
            from_class=self.class1,
            to_class=self.class2,
            transfer_date=date.today() + timedelta(days=1),
            reason="Better fit for student",
            requested_by=self.admin_user
        )
        
        # Approve transfer
        transfer.approve(self.admin_user)
        self.assertEqual(transfer.status, 'approved')
        
        # Mark documents and fees as cleared
        transfer.documents_transferred = True
        transfer.fees_cleared = True
        transfer.save()
        
        # Check if transfer can be completed
        self.assertTrue(transfer.can_be_completed())
        
        # Complete transfer
        transfer.complete_transfer()
        self.assertEqual(transfer.status, 'completed')
        
        # Check if student's current class was updated
        self.student.refresh_from_db()
        self.assertEqual(self.student.current_class, self.class2)


class AdmissionSystemTestCase(TestCase):
    """Test cases for the admission system"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1
        )
        
        self.class_obj = Class.objects.create(
            school=self.school,
            name="A",
            grade=self.grade,
            academic_year=self.academic_year
        )
        
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            is_staff=True
        )

    def test_electronic_registration_creation(self):
        """Test electronic registration creation"""
        registration = ElectronicRegistration.objects.create(
            school=self.school,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            father_name="John Doe Sr",
            father_phone="+1234567890",
            father_email="<EMAIL>",
            mother_name="Jane Doe",
            home_address="123 Test St",
            desired_grade=self.grade
        )
        
        self.assertEqual(registration.status, 'draft')
        self.assertEqual(registration.full_name, "John Doe")

    def test_electronic_registration_validation(self):
        """Test electronic registration validation"""
        # Test invalid email
        with self.assertRaises(ValidationError):
            registration = ElectronicRegistration(
                school=self.school,
                first_name="John",
                last_name="Doe",
                date_of_birth=date(2015, 1, 1),
                gender="M",
                nationality="US",
                father_name="John Doe Sr",
                father_phone="+1234567890",
                father_email="invalid-email",
                home_address="123 Test St",
                desired_grade=self.grade
            )
            registration.full_clean()

    def test_student_id_generation_uniqueness(self):
        """Test that student ID generation creates unique IDs"""
        # Create parent and users first
        parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        parent = Parent.objects.create(
            school=self.school,
            user=parent_user,
            father_name="John Doe",
            father_phone="+1234567890",
            home_address="123 Parent St"
        )
        
        # Create multiple students
        students = []
        for i in range(5):
            student_user = User.objects.create_user(
                username=f"student{i}",
                email=f"student{i}@test.com",
                password="testpass123",
                user_type="student"
            )
            
            student = Student.objects.create(
                school=self.school,
                user=student_user,
                first_name=f"Student{i}",
                last_name="Test",
                date_of_birth=date(2015, 1, i+1),
                gender="M",
                nationality="US",
                parent=parent,
                admission_date=date(2024, 9, i+1)
            )
            students.append(student)
        
        # Check that all student IDs are unique
        student_ids = [s.student_id for s in students]
        self.assertEqual(len(student_ids), len(set(student_ids)))

    def test_admission_number_generation(self):
        """Test admission number generation"""
        parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        parent = Parent.objects.create(
            school=self.school,
            user=parent_user,
            father_name="John Doe",
            father_phone="+1234567890",
            home_address="123 Parent St"
        )
        
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=self.school,
            user=student_user,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )
        
        # Check admission number format
        self.assertTrue(student.admission_number.startswith("2024-"))
        self.assertEqual(len(student.admission_number), 9)  # YYYY-NNNN format

    def test_document_verification_workflow(self):
        """Test document verification workflow"""
        # Create student first
        parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        parent = Parent.objects.create(
            school=self.school,
            user=parent_user,
            father_name="John Doe",
            father_phone="+1234567890",
            home_address="123 Parent St"
        )
        
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=self.school,
            user=student_user,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )
        
        # Create a test file
        from django.core.files.uploadedfile import SimpleUploadedFile
        test_file = SimpleUploadedFile(
            "test.pdf",
            b"file_content",
            content_type="application/pdf"
        )
        
        # Create document
        document = StudentDocument.objects.create(
            school=self.school,
            student=student,
            document_type='birth_certificate',
            title='Birth Certificate',
            file=test_file,
            uploaded_by=self.admin_user
        )
        
        # Test verification
        self.assertFalse(document.is_verified)
        document.verify_document(self.admin_user)
        self.assertTrue(document.is_verified)
        self.assertEqual(document.verified_by, self.admin_user)
        self.assertIsNotNone(document.verified_at)


class AdmissionWorkflowIntegrationTestCase(TestCase):
    """Integration tests for the complete admission workflow"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1
        )
        
        self.class_obj = Class.objects.create(
            school=self.school,
            name="A",
            grade=self.grade,
            academic_year=self.academic_year
        )
        
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            is_staff=True
        )

    def test_complete_admission_workflow(self):
        """Test the complete admission workflow from application to student creation"""
        # Step 1: Create electronic registration
        registration = ElectronicRegistration.objects.create(
            school=self.school,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            father_name="John Doe Sr",
            father_phone="+1234567890",
            father_email="<EMAIL>",
            mother_name="Jane Doe",
            home_address="123 Test St",
            desired_grade=self.grade,
            status='submitted'
        )
        
        # Step 2: Create a temporary student for document upload (in real workflow, this would be handled differently)
        parent_user = User.objects.create_user(
            username="temp_parent",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        parent = Parent.objects.create(
            school=self.school,
            user=parent_user,
            father_name="Temp Parent",
            father_phone="+1234567890",
            home_address="123 Temp St"
        )
        
        student_user = User.objects.create_user(
            username="temp_student",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        temp_student = Student.objects.create(
            school=self.school,
            user=student_user,
            first_name="Temp",
            last_name="Student",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )
        
        # Upload documents (simulated)
        from django.core.files.uploadedfile import SimpleUploadedFile
        test_file = SimpleUploadedFile(
            "birth_cert.pdf",
            b"birth certificate content",
            content_type="application/pdf"
        )
        
        document = StudentDocument.objects.create(
            school=self.school,
            student=temp_student,  # Link to temporary student
            document_type='birth_certificate',
            title=f"{registration.full_name} - Birth Certificate",
            file=test_file,
            description=f"Admission document for {registration.full_name}",
            uploaded_by=self.admin_user
        )
        
        # Step 3: Review and approve application
        registration.status = 'approved'
        registration.reviewed_by = self.admin_user
        registration.reviewed_at = timezone.now()
        registration.save()
        
        # Step 4: Verify that workflow completed successfully
        self.assertEqual(registration.status, 'approved')
        self.assertEqual(registration.reviewed_by, self.admin_user)
        self.assertIsNotNone(registration.reviewed_at)
        
        # Verify document exists
        self.assertTrue(StudentDocument.objects.filter(
            title__icontains=registration.full_name
        ).exists())


class TransferSystemTestCase(TestCase):
    """Test cases for the transfer system"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1
        )
        
        self.class1 = Class.objects.create(
            school=self.school,
            name="A",
            grade=self.grade,
            academic_year=self.academic_year
        )
        
        self.class2 = Class.objects.create(
            school=self.school,
            name="B",
            grade=self.grade,
            academic_year=self.academic_year
        )
        
        # Create users and parent
        self.parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        self.student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            is_staff=True
        )
        
        self.parent = Parent.objects.create(
            school=self.school,
            user=self.parent_user,
            father_name="John Doe",
            father_phone="+1234567890",
            home_address="123 Parent St"
        )
        
        self.student = Student.objects.create(
            school=self.school,
            user=self.student_user,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=self.parent,
            admission_date=date(2024, 9, 1),
            current_class=self.class1
        )
        
        # Create enrollment
        from students.models import StudentEnrollment
        self.enrollment = StudentEnrollment.objects.create(
            school=self.school,
            student=self.student,
            class_obj=self.class1,
            academic_year=self.academic_year,
            enrollment_date=date(2024, 9, 1),
            enrolled_by=self.admin_user
        )

    def test_academic_history_creation(self):
        """Test academic history creation"""
        from students.models import AcademicHistory
        
        history = AcademicHistory.objects.create(
            school=self.school,
            student=self.student,
            academic_year=self.academic_year,
            class_obj=self.class1,
            enrollment_date=date(2024, 9, 1),
            completion_date=date(2025, 1, 15),
            status='transferred'
        )
        
        self.assertEqual(history.student, self.student)
        self.assertEqual(history.status, 'transferred')
        self.assertTrue(history.get_duration_days() > 0)

    def test_transfer_document_generation(self):
        """Test transfer document generation"""
        from students.models import TransferDocument
        
        transfer = StudentTransfer.objects.create(
            school=self.school,
            student=self.student,
            transfer_type='internal',
            from_class=self.class1,
            to_class=self.class2,
            transfer_date=date.today() + timedelta(days=1),
            reason="Better fit for student",
            requested_by=self.admin_user,
            status='approved',
            approved_by=self.admin_user,
            approved_at=timezone.now()
        )
        
        # Generate transfer certificate
        document = TransferDocument.objects.create(
            school=self.school,
            transfer=transfer,
            document_type='transfer_certificate',
            issued_by=self.admin_user
        )
        
        self.assertTrue(document.document_number)
        self.assertTrue(document.verification_code)
        self.assertEqual(document.transfer, transfer)

    def test_transfer_workflow_completion(self):
        """Test complete transfer workflow"""
        # Create transfer request
        transfer = StudentTransfer.objects.create(
            school=self.school,
            student=self.student,
            transfer_type='internal',
            from_class=self.class1,
            to_class=self.class2,
            transfer_date=date.today() + timedelta(days=1),
            reason="Better fit for student",
            requested_by=self.admin_user
        )
        
        # Approve transfer
        transfer.approve(self.admin_user)
        self.assertEqual(transfer.status, 'approved')
        
        # Preserve academic history
        academic_history = transfer.preserve_academic_history()
        self.assertIsNotNone(academic_history)
        self.assertEqual(academic_history.status, 'transferred')
        
        # Generate documents
        documents = transfer.generate_transfer_documents(['transfer_certificate'])
        self.assertEqual(len(documents), 1)
        self.assertEqual(documents[0].document_type, 'transfer_certificate')
        
        # Complete transfer
        transfer.documents_transferred = True
        transfer.fees_cleared = True
        transfer.save()
        
        self.assertTrue(transfer.can_be_completed())
        transfer.complete_transfer()
        self.assertEqual(transfer.status, 'completed')

    def test_transfer_timeline(self):
        """Test transfer timeline generation"""
        transfer = StudentTransfer.objects.create(
            school=self.school,
            student=self.student,
            transfer_type='internal',
            from_class=self.class1,
            to_class=self.class2,
            transfer_date=date.today() + timedelta(days=1),
            reason="Better fit for student",
            requested_by=self.admin_user
        )
        
        timeline = transfer.get_transfer_timeline()
        self.assertTrue(len(timeline) >= 1)
        self.assertEqual(timeline[0]['event'], 'Transfer Requested')
        
        # Approve and check timeline
        transfer.approve(self.admin_user)
        timeline = transfer.get_transfer_timeline()
        self.assertTrue(len(timeline) >= 2)

    def test_required_approvals(self):
        """Test required approvals for different transfer types"""
        # Test outgoing transfer
        outgoing_transfer = StudentTransfer.objects.create(
            school=self.school,
            student=self.student,
            transfer_type='outgoing',
            to_school="Another School",
            transfer_date=date.today() + timedelta(days=1),
            reason="Family relocation",
            requested_by=self.admin_user
        )
        
        approvals = outgoing_transfer.get_required_approvals()
        self.assertIn('Principal Approval', approvals)
        self.assertIn('Finance Clearance', approvals)
        
        # Test internal transfer
        internal_transfer = StudentTransfer.objects.create(
            school=self.school,
            student=self.student,
            transfer_type='internal',
            from_class=self.class1,
            to_class=self.class2,
            transfer_date=date.today() + timedelta(days=1),
            reason="Better fit",
            requested_by=self.admin_user
        )
        
        approvals = internal_transfer.get_required_approvals()
        self.assertIn('Class Teacher Approval', approvals)

    def test_document_content_generation(self):
        """Test document content generation"""
        from students.models import TransferDocument
        
        transfer = StudentTransfer.objects.create(
            school=self.school,
            student=self.student,
            transfer_type='internal',
            from_class=self.class1,
            to_class=self.class2,
            transfer_date=date.today() + timedelta(days=1),
            reason="Better fit for student",
            requested_by=self.admin_user,
            status='approved',
            approved_by=self.admin_user,
            approved_at=timezone.now()
        )
        
        document = TransferDocument.objects.create(
            school=self.school,
            transfer=transfer,
            document_type='transfer_certificate',
            issued_by=self.admin_user
        )
        
        document.generate_content()
        
        self.assertIn('student_name', document.content)
        self.assertIn('student_id', document.content)
        self.assertIn('reason_for_leaving', document.content)
        self.assertEqual(document.content['student_name'], self.student.full_name)


class VacationSystemTestCase(TestCase):
    """Test cases for the vacation management system"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        # Create users and parent
        self.parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        self.student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            is_staff=True
        )
        
        self.parent = Parent.objects.create(
            school=self.school,
            user=self.parent_user,
            father_name="John Doe",
            father_phone="+1234567890",
            home_address="123 Parent St"
        )
        
        self.student = Student.objects.create(
            school=self.school,
            user=self.student_user,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=self.parent,
            admission_date=date(2024, 9, 1)
        )

    def test_vacation_calendar_creation(self):
        """Test vacation calendar creation"""
        from students.models import VacationCalendar
        
        holiday = VacationCalendar.objects.create(
            school=self.school,
            name="Winter Break",
            vacation_type="school_holiday",
            start_date=date(2024, 12, 20),
            end_date=date(2025, 1, 5),
            academic_year=self.academic_year,
            description="Winter holiday break"
        )
        
        self.assertEqual(holiday.name, "Winter Break")
        self.assertEqual(holiday.duration_days, 17)
        self.assertTrue(holiday.is_active_on_date(date(2024, 12, 25)))
        self.assertFalse(holiday.is_active_on_date(date(2024, 12, 15)))

    def test_vacation_request_overlapping(self):
        """Test vacation request overlap detection"""
        # Create first vacation request
        vacation1 = VacationRequest.objects.create(
            school=self.school,
            student=self.student,
            start_date=date(2024, 12, 20),
            end_date=date(2024, 12, 25),
            reason="Family vacation",
            requested_by=self.parent_user,
            status='approved'
        )
        
        # Create overlapping vacation request
        vacation2 = VacationRequest.objects.create(
            school=self.school,
            student=self.student,
            start_date=date(2024, 12, 23),
            end_date=date(2024, 12, 28),
            reason="Extended vacation",
            requested_by=self.parent_user
        )
        
        # Test overlap detection
        self.assertTrue(vacation1.is_overlapping(vacation2))
        
        # Test getting overlapping requests
        overlapping = vacation2.get_overlapping_requests()
        self.assertEqual(overlapping.count(), 1)
        self.assertEqual(overlapping.first(), vacation1)

    def test_academic_days_calculation(self):
        """Test academic days calculation for vacation requests"""
        # Create vacation request spanning weekdays and weekends
        vacation = VacationRequest.objects.create(
            school=self.school,
            student=self.student,
            start_date=date(2024, 12, 16),  # Monday
            end_date=date(2024, 12, 22),    # Sunday (7 days total)
            reason="Test vacation",
            requested_by=self.parent_user
        )
        
        academic_days = vacation.get_academic_days_count()
        # Should exclude Saturday (21st) and Sunday (22nd)
        self.assertEqual(academic_days, 5)

    def test_vacation_report_generation(self):
        """Test vacation report generation"""
        from students.models import VacationReport
        
        # Create some vacation requests
        VacationRequest.objects.create(
            school=self.school,
            student=self.student,
            start_date=date(2024, 12, 20),
            end_date=date(2024, 12, 25),
            reason="Family vacation",
            requested_by=self.parent_user,
            status='approved'
        )
        
        VacationRequest.objects.create(
            school=self.school,
            student=self.student,
            start_date=date(2025, 1, 10),
            end_date=date(2025, 1, 15),
            reason="Medical leave",
            requested_by=self.parent_user,
            status='pending'
        )
        
        # Generate report
        report = VacationReport.objects.create(
            school=self.school,
            report_type='custom',
            start_date=date(2024, 12, 1),
            end_date=date(2025, 1, 31),
            generated_by=self.admin_user
        )
        
        report.generate_report_data()
        
        # Verify report data
        self.assertIn('summary', report.report_data)
        self.assertEqual(report.report_data['summary']['total_requests'], 2)
        self.assertEqual(report.report_data['summary']['approved_requests'], 1)
        self.assertEqual(report.report_data['summary']['pending_requests'], 1)

    def test_vacation_notification_system(self):
        """Test vacation notification system"""
        vacation = VacationRequest.objects.create(
            school=self.school,
            student=self.student,
            start_date=date.today() + timedelta(days=5),
            end_date=date.today() + timedelta(days=10),
            reason="Family vacation",
            requested_by=self.parent_user,
            status='approved'
        )
        
        # Test notification sending (would normally send email)
        # In test environment, this will just return True/False
        result = vacation.send_notification('approved')
        # Since we don't have email configured in tests, this might fail
        # but the method should handle it gracefully
        self.assertIsInstance(result, bool)

    def test_vacation_calendar_overlap_with_request(self):
        """Test vacation calendar overlap with vacation requests"""
        from students.models import VacationCalendar
        
        # Create school holiday
        holiday = VacationCalendar.objects.create(
            school=self.school,
            name="Winter Break",
            vacation_type="school_holiday",
            start_date=date(2024, 12, 20),
            end_date=date(2025, 1, 5),
            academic_year=self.academic_year
        )
        
        # Create vacation request that overlaps with holiday
        vacation = VacationRequest.objects.create(
            school=self.school,
            student=self.student,
            start_date=date(2024, 12, 25),
            end_date=date(2024, 12, 30),
            reason="Family vacation during break",
            requested_by=self.parent_user
        )
        
        # Test overlap detection
        self.assertTrue(holiday.overlaps_with_request(vacation))

    def test_vacation_request_enhanced_methods(self):
        """Test enhanced vacation request methods"""
        vacation = VacationRequest.objects.create(
            school=self.school,
            student=self.student,
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=5),
            reason="Family vacation",
            requested_by=self.parent_user
        )
        
        # Test approval with notification
        vacation.approve(self.admin_user)
        self.assertEqual(vacation.status, 'approved')
        self.assertEqual(vacation.approved_by, self.admin_user)
        
        # Test rejection
        vacation2 = VacationRequest.objects.create(
            school=self.school,
            student=self.student,
            start_date=date.today() + timedelta(days=10),
            end_date=date.today() + timedelta(days=15),
            reason="Another vacation",
            requested_by=self.parent_user
        )
        
        vacation2.reject(self.admin_user, "Too many vacation days")
        self.assertEqual(vacation2.status, 'rejected')
        self.assertEqual(vacation2.rejection_reason, "Too many vacation days")


class StudentSuspensionTestCase(TestCase):
    """Test cases for StudentSuspension model"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TS001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            established_date=date(2020, 1, 1)
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.student = Student.objects.create(
            school=self.school,
            student_id="TS2024001",
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2010, 5, 15),
            gender='male',
            admission_date=date(2024, 9, 1)
        )
        
        self.teacher_user = User.objects.create_user(
            username='teacher1',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.infraction = StudentInfraction.objects.create(
            school=self.school,
            student=self.student,
            incident_date=date.today(),
            description="Test infraction",
            severity='moderate',
            action_taken='warning',
            reported_by=self.teacher_user
        )

    def test_suspension_creation(self):
        """Test creating a student suspension"""
        suspension = StudentSuspension.objects.create(
            school=self.school,
            student=self.student,
            infraction=self.infraction,
            suspension_type='in_school',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=3),
            reason="Repeated violations",
            suspended_by=self.teacher_user
        )
        
        self.assertEqual(suspension.student, self.student)
        self.assertEqual(suspension.suspension_type, 'in_school')
        self.assertEqual(suspension.status, 'active')
        self.assertFalse(suspension.is_completed)

    def test_suspension_duration_calculation(self):
        """Test suspension duration calculation"""
        suspension = StudentSuspension.objects.create(
            school=self.school,
            student=self.student,
            infraction=self.infraction,
            suspension_type='out_of_school',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=5),
            reason="Serious violation",
            suspended_by=self.teacher_user
        )
        
        self.assertEqual(suspension.duration_days, 5)

    def test_suspension_completion(self):
        """Test suspension completion"""
        suspension = StudentSuspension.objects.create(
            school=self.school,
            student=self.student,
            infraction=self.infraction,
            suspension_type='in_school',
            start_date=date.today() - timedelta(days=3),
            end_date=date.today() - timedelta(days=1),
            reason="Test suspension",
            suspended_by=self.teacher_user
        )
        
        suspension.complete_suspension()
        suspension.refresh_from_db()
        
        self.assertEqual(suspension.status, 'completed')
        self.assertTrue(suspension.is_completed)
        self.assertIsNotNone(suspension.completion_date)

    def test_suspension_validation(self):
        """Test suspension validation"""
        # Test end date before start date
        with self.assertRaises(ValidationError):
            suspension = StudentSuspension(
                school=self.school,
                student=self.student,
                infraction=self.infraction,
                suspension_type='in_school',
                start_date=date.today(),
                end_date=date.today() - timedelta(days=1),
                reason="Invalid dates",
                suspended_by=self.teacher_user
            )
            suspension.full_clean()

    def test_suspension_notification(self):
        """Test suspension notification creation"""
        suspension = StudentSuspension.objects.create(
            school=self.school,
            student=self.student,
            infraction=self.infraction,
            suspension_type='out_of_school',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=3),
            reason="Test notification",
            suspended_by=self.teacher_user
        )
        
        # Test that notification is created
        self.assertTrue(suspension.notify_parents())


class DisciplinaryActionTestCase(TestCase):
    """Test cases for DisciplinaryAction model"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TS001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            established_date=date(2020, 1, 1)
        )
        
        self.student = Student.objects.create(
            school=self.school,
            student_id="TS2024001",
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2010, 5, 15),
            gender='male',
            admission_date=date(2024, 9, 1)
        )
        
        self.teacher_user = User.objects.create_user(
            username='teacher1',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.infraction = StudentInfraction.objects.create(
            school=self.school,
            student=self.student,
            incident_date=date.today(),
            description="Test infraction",
            severity='moderate',
            action_taken='warning',
            reported_by=self.teacher_user
        )

    def test_disciplinary_action_creation(self):
        """Test creating a disciplinary action"""
        action = DisciplinaryAction.objects.create(
            school=self.school,
            student=self.student,
            infraction=self.infraction,
            action_type='counseling',
            description="Mandatory counseling session",
            assigned_to=self.teacher_user,
            due_date=date.today() + timedelta(days=7)
        )
        
        self.assertEqual(action.student, self.student)
        self.assertEqual(action.action_type, 'counseling')
        self.assertEqual(action.status, 'pending')
        self.assertFalse(action.is_completed)

    def test_disciplinary_action_completion(self):
        """Test disciplinary action completion"""
        action = DisciplinaryAction.objects.create(
            school=self.school,
            student=self.student,
            infraction=self.infraction,
            action_type='community_service',
            description="Clean school grounds",
            assigned_to=self.teacher_user,
            due_date=date.today() + timedelta(days=3)
        )
        
        action.complete_action("Task completed successfully")
        action.refresh_from_db()
        
        self.assertEqual(action.status, 'completed')
        self.assertTrue(action.is_completed)
        self.assertIsNotNone(action.completion_date)
        self.assertEqual(action.completion_notes, "Task completed successfully")

    def test_disciplinary_action_overdue(self):
        """Test overdue disciplinary action detection"""
        action = DisciplinaryAction.objects.create(
            school=self.school,
            student=self.student,
            infraction=self.infraction,
            action_type='detention',
            description="After school detention",
            assigned_to=self.teacher_user,
            due_date=date.today() - timedelta(days=1)
        )
        
        self.assertTrue(action.is_overdue)

    def test_disciplinary_action_validation(self):
        """Test disciplinary action validation"""
        # Test due date in the past
        with self.assertRaises(ValidationError):
            action = DisciplinaryAction(
                school=self.school,
                student=self.student,
                infraction=self.infraction,
                action_type='counseling',
                description="Test action",
                assigned_to=self.teacher_user,
                due_date=date.today() - timedelta(days=1)
            )
            action.full_clean()


class DisciplineReportTestCase(TestCase):
    """Test cases for DisciplineReport model"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TS001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            established_date=date(2020, 1, 1)
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.student = Student.objects.create(
            school=self.school,
            student_id="TS2024001",
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2010, 5, 15),
            gender='male',
            admission_date=date(2024, 9, 1)
        )
        
        self.teacher_user = User.objects.create_user(
            username='teacher1',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_discipline_report_creation(self):
        """Test creating a discipline report"""
        report = DisciplineReport.objects.create(
            school=self.school,
            report_type='monthly',
            start_date=date(2024, 10, 1),
            end_date=date(2024, 10, 31),
            generated_by=self.teacher_user
        )
        
        self.assertEqual(report.report_type, 'monthly')
        self.assertEqual(report.status, 'pending')
        self.assertIsNone(report.report_data)

    def test_discipline_report_generation(self):
        """Test discipline report data generation"""
        # Create some test infractions
        StudentInfraction.objects.create(
            school=self.school,
            student=self.student,
            incident_date=date(2024, 10, 15),
            description="Test infraction 1",
            severity='minor',
            action_taken='warning',
            reported_by=self.teacher_user
        )
        
        StudentInfraction.objects.create(
            school=self.school,
            student=self.student,
            incident_date=date(2024, 10, 20),
            description="Test infraction 2",
            severity='moderate',
            action_taken='detention',
            reported_by=self.teacher_user
        )
        
        report = DisciplineReport.objects.create(
            school=self.school,
            report_type='monthly',
            start_date=date(2024, 10, 1),
            end_date=date(2024, 10, 31),
            generated_by=self.teacher_user
        )
        
        report.generate_report_data()
        report.refresh_from_db()
        
        self.assertEqual(report.status, 'completed')
        self.assertIsNotNone(report.report_data)
        self.assertIn('total_infractions', report.report_data)
        self.assertEqual(report.report_data['total_infractions'], 2)

    def test_discipline_report_analytics(self):
        """Test discipline report analytics"""
        # Create infractions with different severities
        for severity in ['minor', 'moderate', 'severe']:
            StudentInfraction.objects.create(
                school=self.school,
                student=self.student,
                incident_date=date(2024, 10, 15),
                description=f"Test {severity} infraction",
                severity=severity,
                action_taken='warning',
                reported_by=self.teacher_user
            )
        
        report = DisciplineReport.objects.create(
            school=self.school,
            report_type='analytics',
            start_date=date(2024, 10, 1),
            end_date=date(2024, 10, 31),
            generated_by=self.teacher_user
        )
        
        report.generate_report_data()
        report.refresh_from_db()
        
        self.assertIn('severity_breakdown', report.report_data)
        self.assertEqual(report.report_data['severity_breakdown']['minor'], 1)
        self.assertEqual(report.report_data['severity_breakdown']['moderate'], 1)
        self.assertEqual(report.report_data['severity_breakdown']['severe'], 1)


class DisciplineSystemIntegrationTestCase(TestCase):
    """Integration tests for the complete discipline system"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TS001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            established_date=date(2020, 1, 1)
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.student = Student.objects.create(
            school=self.school,
            student_id="TS2024001",
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2010, 5, 15),
            gender='male',
            admission_date=date(2024, 9, 1)
        )
        
        self.teacher_user = User.objects.create_user(
            username='teacher1',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_complete_discipline_workflow(self):
        """Test the complete discipline workflow from infraction to resolution"""
        # Step 1: Create an infraction
        infraction = StudentInfraction.objects.create(
            school=self.school,
            student=self.student,
            incident_date=date.today(),
            description="Disruptive behavior in class",
            severity='moderate',
            action_taken='warning',
            reported_by=self.teacher_user
        )
        
        # Step 2: Create a disciplinary action
        action = DisciplinaryAction.objects.create(
            school=self.school,
            student=self.student,
            infraction=infraction,
            action_type='counseling',
            description="Meet with school counselor",
            assigned_to=self.teacher_user,
            due_date=date.today() + timedelta(days=7)
        )
        
        # Step 3: If severe, create a suspension
        if infraction.severity == 'severe':
            suspension = StudentSuspension.objects.create(
                school=self.school,
                student=self.student,
                infraction=infraction,
                suspension_type='in_school',
                start_date=date.today(),
                end_date=date.today() + timedelta(days=3),
                reason="Severe behavioral issue",
                suspended_by=self.teacher_user
            )
        
        # Step 4: Complete the disciplinary action
        action.complete_action("Student attended counseling session")
        
        # Step 5: Generate a discipline report
        report = DisciplineReport.objects.create(
            school=self.school,
            report_type='student',
            start_date=date.today() - timedelta(days=30),
            end_date=date.today(),
            generated_by=self.teacher_user
        )
        report.generate_report_data()
        
        # Verify the workflow
        self.assertEqual(infraction.student, self.student)
        self.assertEqual(action.status, 'completed')
        self.assertTrue(action.is_completed)
        self.assertEqual(report.status, 'completed')
        self.assertIsNotNone(report.report_data)

    def test_discipline_escalation_workflow(self):
        """Test discipline escalation for repeat offenders"""
        # Create multiple infractions for the same student
        infractions = []
        for i in range(3):
            infraction = StudentInfraction.objects.create(
                school=self.school,
                student=self.student,
                incident_date=date.today() - timedelta(days=i*7),
                description=f"Infraction {i+1}",
                severity='moderate',
                action_taken='warning' if i < 2 else 'suspension',
                reported_by=self.teacher_user
            )
            infractions.append(infraction)
        
        # Create escalating disciplinary actions
        for i, infraction in enumerate(infractions):
            if i == 0:
                action_type = 'warning'
            elif i == 1:
                action_type = 'detention'
            else:
                action_type = 'counseling'
            
            DisciplinaryAction.objects.create(
                school=self.school,
                student=self.student,
                infraction=infraction,
                action_type=action_type,
                description=f"Escalated action {i+1}",
                assigned_to=self.teacher_user,
                due_date=date.today() + timedelta(days=7)
            )
        
        # For the third infraction, create a suspension
        suspension = StudentSuspension.objects.create(
            school=self.school,
            student=self.student,
            infraction=infractions[2],
            suspension_type='out_of_school',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=3),
            reason="Repeated violations - escalation required",
            suspended_by=self.teacher_user
        )
        
        # Verify escalation
        student_infractions = StudentInfraction.objects.filter(student=self.student)
        self.assertEqual(student_infractions.count(), 3)
        
        student_actions = DisciplinaryAction.objects.filter(student=self.student)
        self.assertEqual(student_actions.count(), 3)
        
        student_suspensions = StudentSuspension.objects.filter(student=self.student)
        self.assertEqual(student_suspensions.count(), 1)
        self.assertEqual(suspension.suspension_type, 'out_of_school')