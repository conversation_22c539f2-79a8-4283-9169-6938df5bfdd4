"""
Core utilities for the School ERP system
"""
import uuid
import hashlib
import secrets
from typing import Any, Dict, Optional
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model
from cryptography.fernet import Fernet
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class CacheManager:
    """
    Centralized cache management
    """
    DEFAULT_TIMEOUT = getattr(settings, 'CACHE_TTL', 300)  # 5 minutes
    
    @staticmethod
    def get_key(prefix: str, *args) -> str:
        """Generate cache key with prefix and arguments"""
        key_parts = [str(arg) for arg in args]
        return f"{prefix}:{'_'.join(key_parts)}"
    
    @classmethod
    def get(cls, prefix: str, *args, default=None):
        """Get value from cache"""
        key = cls.get_key(prefix, *args)
        return cache.get(key, default)
    
    @classmethod
    def set(cls, prefix: str, *args, value: Any, timeout: Optional[int] = None):
        """Set value in cache"""
        key = cls.get_key(prefix, *args)
        timeout = timeout or cls.DEFAULT_TIMEOUT
        cache.set(key, value, timeout)
    
    @classmethod
    def delete(cls, prefix: str, *args):
        """Delete value from cache"""
        key = cls.get_key(prefix, *args)
        cache.delete(key)
    
    @classmethod
    def get_or_set(cls, prefix: str, *args, default_func=None, timeout: Optional[int] = None):
        """Get value from cache or set it using default_func"""
        key = cls.get_key(prefix, *args)
        timeout = timeout or cls.DEFAULT_TIMEOUT
        
        value = cache.get(key)
        if value is None and default_func:
            value = default_func()
            cache.set(key, value, timeout)
        return value


class EncryptionManager:
    """
    Field-level encryption manager
    """
    def __init__(self):
        encryption_key = getattr(settings, 'ENCRYPTION_KEY', None)
        if encryption_key:
            self.cipher = Fernet(encryption_key.encode())
        else:
            self.cipher = None
            logger.warning("ENCRYPTION_KEY not set, encryption disabled")
    
    def encrypt(self, data: str) -> str:
        """Encrypt string data"""
        if not self.cipher or not data:
            return data
        
        try:
            return self.cipher.encrypt(data.encode()).decode()
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            return data
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt string data"""
        if not self.cipher or not encrypted_data:
            return encrypted_data
        
        try:
            # Check if data is already encrypted (Fernet tokens start with 'gAAAAA')
            if not encrypted_data.startswith('gAAAAA'):
                return encrypted_data
            
            return self.cipher.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            return encrypted_data


class IDGenerator:
    """
    Generate various types of IDs
    """
    @staticmethod
    def generate_student_id(school_code: str, year: int = None) -> str:
        """Generate student ID"""
        if year is None:
            year = timezone.now().year
        
        # Get next sequence number for this school and year
        cache_key = f"student_id_seq:{school_code}:{year}"
        seq = cache.get(cache_key, 0) + 1
        cache.set(cache_key, seq, timeout=86400)  # 24 hours
        
        return f"{school_code}{year % 100:02d}{seq:04d}"
    
    @staticmethod
    def generate_employee_id(school_code: str) -> str:
        """Generate employee ID"""
        cache_key = f"employee_id_seq:{school_code}"
        seq = cache.get(cache_key, 0) + 1
        cache.set(cache_key, seq, timeout=86400)  # 24 hours
        
        return f"EMP{school_code}{seq:05d}"
    
    @staticmethod
    def generate_invoice_number(school_code: str) -> str:
        """Generate invoice number"""
        year = timezone.now().year
        cache_key = f"invoice_seq:{school_code}:{year}"
        seq = cache.get(cache_key, 0) + 1
        cache.set(cache_key, seq, timeout=86400)  # 24 hours
        
        return f"INV{school_code}{year}{seq:06d}"
    
    @staticmethod
    def generate_receipt_number(school_code: str) -> str:
        """Generate receipt number"""
        year = timezone.now().year
        cache_key = f"receipt_seq:{school_code}:{year}"
        seq = cache.get(cache_key, 0) + 1
        cache.set(cache_key, seq, timeout=86400)  # 24 hours
        
        return f"REC{school_code}{year}{seq:06d}"


class SecurityUtils:
    """
    Security-related utilities
    """
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """Generate cryptographically secure token"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def hash_password(password: str, salt: str = None) -> tuple:
        """Hash password with salt"""
        if salt is None:
            salt = secrets.token_hex(16)
        
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000
        )
        
        return password_hash.hex(), salt
    
    @staticmethod
    def verify_password(password: str, password_hash: str, salt: str) -> bool:
        """Verify password against hash"""
        computed_hash, _ = SecurityUtils.hash_password(password, salt)
        return secrets.compare_digest(computed_hash, password_hash)
    
    @staticmethod
    def get_client_ip(request) -> str:
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class DateUtils:
    """
    Date and time utilities
    """
    @staticmethod
    def get_academic_year_for_date(date, school):
        """Get academic year for a given date"""
        from .models import AcademicYear
        
        return AcademicYear.objects.filter(
            school=school,
            start_date__lte=date,
            end_date__gte=date,
            is_active=True
        ).first()
    
    @staticmethod
    def get_current_academic_year(school):
        """Get current academic year for school"""
        from .models import AcademicYear
        
        return AcademicYear.objects.filter(
            school=school,
            is_current=True,
            is_active=True
        ).first()
    
    @staticmethod
    def get_current_semester(school):
        """Get current semester for school"""
        from .models import Semester
        
        return Semester.objects.filter(
            school=school,
            is_current=True,
            is_active=True
        ).first()


class ValidationUtils:
    """
    Common validation utilities
    """
    @staticmethod
    def validate_phone_number(phone: str) -> bool:
        """Validate phone number format"""
        import re
        pattern = r'^\+?1?\d{9,15}$'
        return bool(re.match(pattern, phone))
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def validate_national_id(national_id: str, country_code: str = 'US') -> bool:
        """Validate national ID based on country"""
        # This is a simplified validation - implement country-specific logic
        if country_code == 'US':
            # SSN format: XXX-XX-XXXX
            import re
            pattern = r'^\d{3}-?\d{2}-?\d{4}$'
            return bool(re.match(pattern, national_id))
        
        # Default validation - just check if it's not empty and alphanumeric
        return bool(national_id and national_id.replace('-', '').replace(' ', '').isalnum())


class FileUtils:
    """
    File handling utilities
    """
    @staticmethod
    def get_upload_path(instance, filename: str, folder: str = 'uploads') -> str:
        """Generate upload path for files"""
        ext = filename.split('.')[-1]
        filename = f"{uuid.uuid4().hex}.{ext}"
        
        school_code = instance.school.code if hasattr(instance, 'school') else 'default'
        return f"{folder}/{school_code}/{filename}"
    
    @staticmethod
    def validate_file_size(file, max_size_mb: int = 5) -> bool:
        """Validate file size"""
        max_size = max_size_mb * 1024 * 1024  # Convert to bytes
        return file.size <= max_size
    
    @staticmethod
    def validate_file_type(file, allowed_types: list) -> bool:
        """Validate file type"""
        file_type = file.content_type
        return file_type in allowed_types


# Global instances
cache_manager = CacheManager()
encryption_manager = EncryptionManager()
id_generator = IDGenerator()
security_utils = SecurityUtils()
date_utils = DateUtils()
validation_utils = ValidationUtils()
file_utils = FileUtils()