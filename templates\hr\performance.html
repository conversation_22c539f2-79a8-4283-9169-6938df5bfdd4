{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Employee Performance" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .performance-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .performance-card:hover {
        transform: translateY(-2px);
    }
    .performance-header {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .rating-stars {
        color: #ffc107;
    }
    .performance-score {
        font-size: 2rem;
        font-weight: bold;
    }
    .score-excellent {
        color: #28a745;
    }
    .score-good {
        color: #17a2b8;
    }
    .score-average {
        color: #ffc107;
    }
    .score-poor {
        color: #dc3545;
    }
    .progress-ring {
        width: 120px;
        height: 120px;
        position: relative;
    }
    .progress-ring svg {
        width: 100%;
        height: 100%;
        transform: rotate(-90deg);
    }
    .progress-ring circle {
        fill: none;
        stroke-width: 8;
        stroke-linecap: round;
    }
    .progress-ring .bg {
        stroke: #e9ecef;
    }
    .progress-ring .progress {
        stroke: #007bff;
        stroke-dasharray: 283;
        stroke-dashoffset: 283;
        transition: stroke-dashoffset 0.5s ease-in-out;
    }
    .employee-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
    }
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
    }
    .goal-progress {
        height: 8px;
        border-radius: 4px;
        background: #e9ecef;
        overflow: hidden;
    }
    .goal-progress-bar {
        height: 100%;
        border-radius: 4px;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-line text-primary me-2"></i>{% trans "Employee Performance" %}
                    </h2>
                    <p class="text-muted">{% trans "Monitor and evaluate employee performance metrics" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>{% trans "New Evaluation" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export Report" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Overview -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="metric-card">
                <i class="fas fa-star fa-2x mb-2"></i>
                <h3 class="mb-1">{{ average_rating|floatformat:1|default:"0.0" }}</h3>
                <p class="mb-0">{% trans "Average Rating" %}</p>
                <small class="opacity-75">{% trans "Out of 5.0" %}</small>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="metric-card">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h3 class="mb-1">{{ employees_evaluated|default:0 }}</h3>
                <p class="mb-0">{% trans "Employees Evaluated" %}</p>
                <small class="opacity-75">{% trans "This quarter" %}</small>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="metric-card">
                <i class="fas fa-trophy fa-2x mb-2"></i>
                <h3 class="mb-1">{{ top_performers|default:0 }}</h3>
                <p class="mb-0">{% trans "Top Performers" %}</p>
                <small class="opacity-75">{% trans "Above 4.5 rating" %}</small>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="metric-card">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h3 class="mb-1">{% if performance_growth %}{{ performance_growth|floatformat:0 }}%{% else %}0%{% endif %}</h3>
                <p class="mb-0">{% trans "Performance Growth" %}</p>
                <small class="opacity-75">{% trans "Compared to last quarter" %}</small>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card performance-card">
                <div class="card-body">
                    <h5 class="card-title mb-3">{% trans "Filter Performance Data" %}</h5>
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="department" class="form-label">{% trans "Department" %}</label>
                            <select class="form-select" id="department" name="department">
                                <option value="">{% trans "All Departments" %}</option>
                                <option value="teaching">{% trans "Teaching" %}</option>
                                <option value="administration">{% trans "Administration" %}</option>
                                <option value="support">{% trans "Support Staff" %}</option>
                                <option value="management">{% trans "Management" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="period" class="form-label">{% trans "Evaluation Period" %}</label>
                            <select class="form-select" id="period" name="period">
                                <option value="current">{% trans "Current Quarter" %}</option>
                                <option value="last">{% trans "Last Quarter" %}</option>
                                <option value="year">{% trans "This Year" %}</option>
                                <option value="custom">{% trans "Custom Range" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="rating" class="form-label">{% trans "Rating Range" %}</label>
                            <select class="form-select" id="rating" name="rating">
                                <option value="">{% trans "All Ratings" %}</option>
                                <option value="excellent">{% trans "Excellent (4.5-5.0)" %}</option>
                                <option value="good">{% trans "Good (3.5-4.4)" %}</option>
                                <option value="average">{% trans "Average (2.5-3.4)" %}</option>
                                <option value="poor">{% trans "Poor (1.0-2.4)" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Dashboard -->
    <div class="row">
        <!-- Top Performers -->
        <div class="col-lg-8 mb-4">
            <div class="card performance-card">
                <div class="performance-header card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-medal me-2"></i>{% trans "Employee Performance Rankings" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Rank" %}</th>
                                    <th>{% trans "Employee" %}</th>
                                    <th>{% trans "Department" %}</th>
                                    <th>{% trans "Overall Rating" %}</th>
                                    <th>{% trans "Goals Achieved" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-trophy"></i> 1
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                            <div>
                                                <h6 class="mb-1">أحمد محمد علي</h6>
                                                <small class="text-muted">Senior Teacher</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{% trans "Teaching" %}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="performance-score score-excellent me-2">4.8</span>
                                            <div class="rating-stars">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="goal-progress">
                                            <div class="goal-progress-bar bg-success" style="width: 95%"></div>
                                        </div>
                                        <small class="text-muted">95% (19/20)</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Send Feedback' %}">
                                                <i class="fas fa-comment"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-medal"></i> 2
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                            <div>
                                                <h6 class="mb-1">فاطمة أحمد حسن</h6>
                                                <small class="text-muted">Math Teacher</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{% trans "Teaching" %}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="performance-score score-excellent me-2">4.6</span>
                                            <div class="rating-stars">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star-half-alt"></i>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="goal-progress">
                                            <div class="goal-progress-bar bg-success" style="width: 88%"></div>
                                        </div>
                                        <small class="text-muted">88% (17/20)</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Send Feedback' %}">
                                                <i class="fas fa-comment"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-award"></i> 3
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                            <div>
                                                <h6 class="mb-1">محمد عبدالله سالم</h6>
                                                <small class="text-muted">Science Teacher</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{% trans "Teaching" %}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="performance-score score-good me-2">4.3</span>
                                            <div class="rating-stars">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="far fa-star"></i>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="goal-progress">
                                            <div class="goal-progress-bar bg-info" style="width: 82%"></div>
                                        </div>
                                        <small class="text-muted">82% (16/20)</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Send Feedback' %}">
                                                <i class="fas fa-comment"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge bg-light text-dark">4</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                            <div>
                                                <h6 class="mb-1">سارة محمد أحمد</h6>
                                                <small class="text-muted">English Teacher</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{% trans "Teaching" %}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="performance-score score-good me-2">4.1</span>
                                            <div class="rating-stars">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="far fa-star"></i>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="goal-progress">
                                            <div class="goal-progress-bar bg-warning" style="width: 75%"></div>
                                        </div>
                                        <small class="text-muted">75% (15/20)</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Send Feedback' %}">
                                                <i class="fas fa-comment"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Analytics -->
        <div class="col-lg-4 mb-4">
            <div class="card performance-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>{% trans "Performance Distribution" %}
                    </h5>
                </div>
                <div class="card-body text-center">
                    <canvas id="performanceChart" width="300" height="300"></canvas>
                    
                    <div class="mt-4">
                        <div class="row">
                            <div class="col-6">
                                <div class="progress-ring">
                                    <svg>
                                        <circle class="bg" cx="60" cy="60" r="45"></circle>
                                        <circle class="progress" cx="60" cy="60" r="45" style="stroke-dashoffset: 70;"></circle>
                                    </svg>
                                    <div class="position-absolute top-50 start-50 translate-middle">
                                        <div class="performance-score score-excellent">85%</div>
                                        <small>{% trans "Satisfaction" %}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="progress-ring">
                                    <svg>
                                        <circle class="bg" cx="60" cy="60" r="45"></circle>
                                        <circle class="progress" cx="60" cy="60" r="45" style="stroke-dashoffset: 113;"></circle>
                                    </svg>
                                    <div class="position-absolute top-50 start-50 translate-middle">
                                        <div class="performance-score score-good">75%</div>
                                        <small>{% trans "Goals Met" %}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card performance-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>{% trans "Schedule Review" %}
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-award me-2"></i>{% trans "Recognition Program" %}
                        </button>
                        <button class="btn btn-info">
                            <i class="fas fa-chart-bar me-2"></i>{% trans "Performance Report" %}
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Improvement Plans" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Performance Distribution Chart
    const ctx = document.getElementById('performanceChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['{% trans "Excellent" %}', '{% trans "Good" %}', '{% trans "Average" %}', '{% trans "Poor" %}'],
            datasets: [{
                data: [27, 35, 23, 15],
                backgroundColor: [
                    '#28a745',
                    '#17a2b8',
                    '#ffc107',
                    '#dc3545'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });

    // Handle action buttons
    document.querySelectorAll('.btn-outline-primary').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "View employee performance details" %}');
        });
    });

    document.querySelectorAll('.btn-outline-success').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "Send feedback to employee" %}');
        });
    });
});
</script>
{% endblock %}
