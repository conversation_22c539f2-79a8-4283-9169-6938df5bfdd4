{% load i18n %}
<div class="table-responsive">
    <table class="table table-striped table-hover" id="{{ table_id }}">
        <thead class="table-dark">
            <tr>
                {% for header in headers %}
                    <th>{{ header }}</th>
                {% endfor %}
                {% if actions %}
                    <th>{% trans "Actions" %}</th>
                {% endif %}
            </tr>
        </thead>
        <tbody>
            {% for row in rows %}
                <tr>
                    {% for cell in row %}
                        <td>{{ cell }}</td>
                    {% endfor %}
                    {% if actions %}
                        <td>
                            <div class="btn-group btn-group-sm">
                                {% for action in actions %}
                                    <a href="{{ action.url }}" class="btn btn-outline-{{ action.color|default:'primary' }}" 
                                       title="{{ action.title }}">
                                        <i class="fas fa-{{ action.icon }}"></i>
                                    </a>
                                {% endfor %}
                            </div>
                        </td>
                    {% endif %}
                </tr>
            {% empty %}
                <tr>
                    <td colspan="{% if actions %}{{ headers|length|add:1 }}{% else %}{{ headers|length }}{% endif %}" 
                        class="text-center text-muted">
                        {% trans "No data available" %}
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
