# Generated by Django 5.2.4 on 2025-07-31 09:27

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        (
            "finance",
            "0003_account_created_by_account_school_account_updated_by_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="allow_manual_entries",
            field=models.BooleanField(
                default=True,
                help_text="Allow manual journal entries to this account",
                verbose_name="Allow Manual Entries",
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="archive_reason",
            field=models.TextField(
                blank=True, null=True, verbose_name="Archive Reason"
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="archived_at",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="Archived At"
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="archived_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="archived_accounts",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Archived By",
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="current_balance",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=15,
                verbose_name="Current Balance",
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="is_reconcilable",
            field=models.BooleanField(
                default=False,
                help_text="Account requires reconciliation (e.g., bank accounts)",
                verbose_name="Is Reconcilable",
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="is_system_account",
            field=models.BooleanField(
                default=False,
                help_text="System accounts cannot be deleted",
                verbose_name="Is System Account",
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="level",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Hierarchy level (0 for root accounts)",
                verbose_name="Account Level",
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="opening_balance",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=15,
                verbose_name="Opening Balance",
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="path",
            field=models.CharField(
                blank=True,
                help_text="Full path from root to this account",
                max_length=500,
                verbose_name="Account Path",
            ),
        ),
        migrations.AlterField(
            model_name="account",
            name="code",
            field=models.CharField(
                help_text="Unique account code (e.g., 1000, 1100)",
                max_length=20,
                verbose_name="Account Code",
            ),
        ),
        migrations.AlterField(
            model_name="account",
            name="is_header",
            field=models.BooleanField(
                default=False,
                help_text="Header accounts cannot have transactions posted to them",
                verbose_name="Is Header Account",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="account",
            unique_together={("school", "code")},
        ),
        migrations.AddIndex(
            model_name="account",
            index=models.Index(
                fields=["school", "code"], name="finance_acc_school__d9d7c9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="account",
            index=models.Index(
                fields=["school", "account_type"], name="finance_acc_school__1362d5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="account",
            index=models.Index(
                fields=["school", "parent"], name="finance_acc_school__c59418_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="account",
            index=models.Index(
                fields=["school", "is_active"], name="finance_acc_school__419ec5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="account",
            index=models.Index(
                fields=["school", "archived_at"], name="finance_acc_school__5725e6_idx"
            ),
        ),
    ]
