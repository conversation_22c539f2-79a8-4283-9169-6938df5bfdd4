{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Electronic Registration" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-laptop text-primary me-2"></i>{% trans "Electronic Registration" %}
                            </h2>
                            <p class="text-muted">{% trans "Manage online student registration applications" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:add_electronic_registration' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "New Registration" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                            <h3 class="mb-1">{{ total_registrations }}</h3>
                            <p class="mb-0">{% trans "Total Registrations" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <h3 class="mb-1">{{ pending_registrations }}</h3>
                            <p class="mb-0">{% trans "Pending Review" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h3 class="mb-1">{{ approved_registrations }}</h3>
                            <p class="mb-0">{% trans "Approved" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-user-check fa-2x mb-2"></i>
                            <h3 class="mb-1">{{ completed_registrations }}</h3>
                            <p class="mb-0">{% trans "Completed" %}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter options -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <label for="status" class="form-label">{% trans "Status" %}</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">{% trans "All Statuses" %}</option>
                                        <option value="submitted" {% if request.GET.status == 'submitted' %}selected{% endif %}>{% trans "Submitted" %}</option>
                                        <option value="under_review" {% if request.GET.status == 'under_review' %}selected{% endif %}>{% trans "Under Review" %}</option>
                                        <option value="approved" {% if request.GET.status == 'approved' %}selected{% endif %}>{% trans "Approved" %}</option>
                                        <option value="rejected" {% if request.GET.status == 'rejected' %}selected{% endif %}>{% trans "Rejected" %}</option>
                                        <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>{% trans "Completed" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="grade" class="form-label">{% trans "Desired Grade" %}</label>
                                    <select class="form-select" id="grade" name="grade">
                                        <option value="">{% trans "All Grades" %}</option>
                                        <!-- Add grade options here -->
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter me-2"></i>{% trans "Apply Filters" %}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Registration List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Registration Applications" %}</h5>
                        </div>
                        <div class="card-body">
                            {% if registrations %}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Student Name" %}</th>
                                                <th>{% trans "Desired Grade" %}</th>
                                                <th>{% trans "Father Name" %}</th>
                                                <th>{% trans "Contact" %}</th>
                                                <th>{% trans "Status" %}</th>
                                                <th>{% trans "Submitted" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for registration in registrations %}
                                                <tr>
                                                    <td>
                                                        <div>
                                                            <h6 class="mb-0">{{ registration.full_name }}</h6>
                                                            <small class="text-muted">{{ registration.get_gender_display }}</small>
                                                        </div>
                                                    </td>
                                                    <td>{{ registration.desired_grade.name }}</td>
                                                    <td>{{ registration.father_name }}</td>
                                                    <td>
                                                        <div>
                                                            <small>{{ registration.father_phone }}</small><br>
                                                            <small>{{ registration.father_email }}</small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        {% if registration.status == 'submitted' %}
                                                            <span class="badge bg-info">{% trans "Submitted" %}</span>
                                                        {% elif registration.status == 'under_review' %}
                                                            <span class="badge bg-warning">{% trans "Under Review" %}</span>
                                                        {% elif registration.status == 'approved' %}
                                                            <span class="badge bg-success">{% trans "Approved" %}</span>
                                                        {% elif registration.status == 'rejected' %}
                                                            <span class="badge bg-danger">{% trans "Rejected" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-primary">{% trans "Completed" %}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ registration.submitted_at|date:"d/m/Y" }}</td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#registrationModal{{ registration.id }}">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            {% if registration.status == 'submitted' %}
                                                                <button type="button" class="btn btn-sm btn-outline-success">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-sm btn-outline-danger">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            {% endif %}
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>{% trans "No registration applications found." %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Registration Detail Modals -->
{% for registration in registrations %}
    <div class="modal fade" id="registrationModal{{ registration.id }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{% trans "Registration Details" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>{% trans "Student Information" %}</h6>
                            <p><strong>{% trans "Name:" %}</strong> {{ registration.full_name }}</p>
                            <p><strong>{% trans "Date of Birth:" %}</strong> {{ registration.date_of_birth }}</p>
                            <p><strong>{% trans "Gender:" %}</strong> {{ registration.get_gender_display }}</p>
                            <p><strong>{% trans "Nationality:" %}</strong> {{ registration.nationality }}</p>
                            <p><strong>{% trans "Desired Grade:" %}</strong> {{ registration.desired_grade.name }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>{% trans "Parent Information" %}</h6>
                            <p><strong>{% trans "Father:" %}</strong> {{ registration.father_name }}</p>
                            <p><strong>{% trans "Father Phone:" %}</strong> {{ registration.father_phone }}</p>
                            <p><strong>{% trans "Father Email:" %}</strong> {{ registration.father_email }}</p>
                            <p><strong>{% trans "Mother:" %}</strong> {{ registration.mother_name|default:"-" }}</p>
                            <p><strong>{% trans "Mother Phone:" %}</strong> {{ registration.mother_phone|default:"-" }}</p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6>{% trans "Address" %}</h6>
                        <p>{{ registration.home_address }}</p>
                    </div>
                    
                    {% if registration.previous_school %}
                        <div class="mb-3">
                            <h6>{% trans "Previous School" %}</h6>
                            <p>{{ registration.previous_school }}</p>
                        </div>
                    {% endif %}
                    
                    {% if registration.notes %}
                        <div class="mb-3">
                            <h6>{% trans "Notes" %}</h6>
                            <p>{{ registration.notes }}</p>
                        </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                    {% if registration.status == 'submitted' %}
                        <button type="button" class="btn btn-success">
                            <i class="fas fa-check me-2"></i>{% trans "Approve" %}
                        </button>
                        <button type="button" class="btn btn-danger">
                            <i class="fas fa-times me-2"></i>{% trans "Reject" %}
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endfor %}
{% endblock %}