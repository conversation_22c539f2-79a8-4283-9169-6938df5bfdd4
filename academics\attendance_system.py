"""
Attendance Management System Services for School ERP

This module implements service classes for comprehensive attendance tracking:
- Attendance analytics and reporting
- Parent notification system

Requirements: 3.5
"""

import io
import base64
try:
    import qrcode
    HAS_QRCODE = True
except ImportError:
    HAS_QRCODE = False
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
from django.db import models, transaction
from django.db.models import Count, Avg, Q, F
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.core.cache import cache
from django.conf import settings
from students.models import Student, Class


class AttendanceAnalytics:
    """
    Attendance analytics and reporting utilities
    """
    
    @staticmethod
    def get_student_attendance_summary(student: Student, academic_year=None, 
                                     subject=None) -> Dict:
        """Get comprehensive attendance summary for a student"""
        from .models import StudentAttendance
        
        filters = {'student': student}
        
        if academic_year:
            filters['session__class_subject__academic_year'] = academic_year
        
        if subject:
            filters['session__class_subject__subject'] = subject
        
        attendance_data = StudentAttendance.objects.filter(**filters).aggregate(
            total_sessions=Count('id'),
            present_count=Count('id', filter=Q(status='present')),
            absent_count=Count('id', filter=Q(status='absent')),
            late_count=Count('id', filter=Q(status='late')),
            excused_count=Count('id', filter=Q(status='excused'))
        )
        
        total = attendance_data['total_sessions'] or 1
        
        return {
            'total_sessions': attendance_data['total_sessions'],
            'present_count': attendance_data['present_count'],
            'absent_count': attendance_data['absent_count'],
            'late_count': attendance_data['late_count'],
            'excused_count': attendance_data['excused_count'],
            'attendance_rate': round((attendance_data['present_count'] / total) * 100, 2),
            'absence_rate': round((attendance_data['absent_count'] / total) * 100, 2),
            'late_rate': round((attendance_data['late_count'] / total) * 100, 2),
            'punctuality_rate': round(((attendance_data['present_count'] - attendance_data['late_count']) / total) * 100, 2)
        }
    
    @staticmethod
    def get_class_attendance_summary(class_obj, academic_year=None, 
                                   date_from=None, date_to=None) -> Dict:
        """Get attendance summary for a class"""
        from .models import StudentAttendance
        
        filters = {'class_subject__class_obj': class_obj}
        
        if academic_year:
            filters['class_subject__academic_year'] = academic_year
        
        if date_from:
            filters['session__session_date__gte'] = date_from
        
        if date_to:
            filters['session__session_date__lte'] = date_to
        
        attendance_data = StudentAttendance.objects.filter(**filters).aggregate(
            total_records=Count('id'),
            present_count=Count('id', filter=Q(status='present')),
            absent_count=Count('id', filter=Q(status='absent')),
            late_count=Count('id', filter=Q(status='late')),
            excused_count=Count('id', filter=Q(status='excused'))
        )
        
        total = attendance_data['total_records'] or 1
        
        return {
            'total_records': attendance_data['total_records'],
            'present_count': attendance_data['present_count'],
            'absent_count': attendance_data['absent_count'],
            'late_count': attendance_data['late_count'],
            'excused_count': attendance_data['excused_count'],
            'overall_attendance_rate': round((attendance_data['present_count'] / total) * 100, 2),
            'absence_rate': round((attendance_data['absent_count'] / total) * 100, 2),
            'late_rate': round((attendance_data['late_count'] / total) * 100, 2)
        }
    
    @staticmethod
    def get_attendance_trends(school, period_days=30) -> Dict:
        """Get attendance trends for the school over specified period"""
        from .models import StudentAttendance
        
        end_date = date.today()
        start_date = end_date - timedelta(days=period_days)
        
        daily_attendance = StudentAttendance.objects.filter(
            school=school,
            session__session_date__range=[start_date, end_date]
        ).values('session__session_date').annotate(
            total=Count('id'),
            present=Count('id', filter=Q(status='present')),
            absent=Count('id', filter=Q(status='absent')),
            late=Count('id', filter=Q(status='late'))
        ).order_by('session__session_date')
        
        trends = []
        for day_data in daily_attendance:
            total = day_data['total'] or 1
            trends.append({
                'date': day_data['session__session_date'],
                'attendance_rate': round((day_data['present'] / total) * 100, 2),
                'absence_rate': round((day_data['absent'] / total) * 100, 2),
                'late_rate': round((day_data['late'] / total) * 100, 2),
                'total_sessions': day_data['total']
            })
        
        return {
            'period_start': start_date,
            'period_end': end_date,
            'daily_trends': trends,
            'average_attendance_rate': sum(t['attendance_rate'] for t in trends) / len(trends) if trends else 0
        }
    
    @staticmethod
    def identify_at_risk_students(school, threshold=75.0, academic_year=None) -> List[Dict]:
        """Identify students with attendance below threshold"""
        from .models import StudentAttendance
        
        filters = {'school': school}
        
        if academic_year:
            filters['session__class_subject__academic_year'] = academic_year
        
        # Get students with low attendance
        student_attendance = StudentAttendance.objects.filter(**filters).values(
            'student__id',
            'student__first_name',
            'student__last_name',
            'student__student_id'
        ).annotate(
            total_sessions=Count('id'),
            present_count=Count('id', filter=Q(status='present')),
            attendance_rate=F('present_count') * 100.0 / F('total_sessions')
        ).filter(
            total_sessions__gt=5,  # At least 5 sessions
            attendance_rate__lt=threshold
        ).order_by('attendance_rate')
        
        at_risk_students = []
        for student_data in student_attendance:
            at_risk_students.append({
                'student_id': student_data['student__id'],
                'student_name': f"{student_data['student__first_name']} {student_data['student__last_name']}",
                'student_number': student_data['student__student_id'],
                'total_sessions': student_data['total_sessions'],
                'present_count': student_data['present_count'],
                'attendance_rate': round(student_data['attendance_rate'], 2),
                'risk_level': 'high' if student_data['attendance_rate'] < 60 else 'medium'
            })
        
        return at_risk_students


class AttendanceNotificationService:
    """
    Service for handling attendance-related notifications
    """
    
    @staticmethod
    def send_absence_notification(student: Student, session):
        """Send absence notification to parents"""
        try:
            from communications.models import NotificationTemplate
            from communications.tasks import send_notification
            
            template = NotificationTemplate.objects.get(
                school=student.school,
                name='student_absence',
                is_active=True
            )
            
            context = {
                'student_name': f"{student.first_name} {student.last_name}",
                'class_name': session.class_subject.class_obj.name,
                'subject_name': session.class_subject.subject.name,
                'session_date': session.session_date.strftime('%Y-%m-%d'),
                'session_time': session.start_time.strftime('%H:%M'),
                'school_name': student.school.name
            }
            
            # Send to all parents
            for parent in student.parents.all():
                if parent.email:
                    send_notification.delay(
                        recipient_email=parent.email,
                        template_id=template.id,
                        context=context
                    )
                
                if parent.phone:
                    send_notification.delay(
                        recipient_phone=parent.phone,
                        template_id=template.id,
                        context=context,
                        notification_type='sms'
                    )
        
        except Exception:
            # Log error or create default template
            pass
    
    @staticmethod
    def send_attendance_summary(student: Student, period='weekly'):
        """Send periodic attendance summary to parents"""
        try:
            from communications.models import NotificationTemplate
            from communications.tasks import send_notification
            
            # Calculate period dates
            end_date = date.today()
            if period == 'weekly':
                start_date = end_date - timedelta(days=7)
            elif period == 'monthly':
                start_date = end_date - timedelta(days=30)
            else:
                start_date = end_date - timedelta(days=7)
            
            # Get attendance summary
            summary = AttendanceAnalytics.get_student_attendance_summary(
                student=student
            )
            
            template = NotificationTemplate.objects.get(
                school=student.school,
                name=f'attendance_summary_{period}',
                is_active=True
            )
            
            context = {
                'student_name': f"{student.first_name} {student.last_name}",
                'period': period,
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d'),
                'attendance_rate': summary['attendance_rate'],
                'total_sessions': summary['total_sessions'],
                'present_count': summary['present_count'],
                'absent_count': summary['absent_count'],
                'late_count': summary['late_count'],
                'school_name': student.school.name
            }
            
            # Send to all parents
            for parent in student.parents.all():
                if parent.email:
                    send_notification.delay(
                        recipient_email=parent.email,
                        template_id=template.id,
                        context=context
                    )
        
        except Exception:
            # Log error or create default template
            pass


def generate_qr_code_for_session(session) -> str:
    """Generate QR code for attendance session"""
    if not HAS_QRCODE:
        return "QR code generation not available - qrcode library not installed"
    
    qr_data = {
        'session_id': str(session.id),
        'token': str(session.qr_code_token),
        'expires_at': session.qr_code_expires_at.isoformat() if session.qr_code_expires_at else None
    }
    
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(str(qr_data))
    qr.make(fit=True)
    
    img = qr.make_image(fill_color="black", back_color="white")
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    
    return base64.b64encode(buffer.getvalue()).decode()