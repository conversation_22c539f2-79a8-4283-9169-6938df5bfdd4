"""
Management command to initialize the database
"""
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import connection
from django.conf import settings


class Command(BaseCommand):
    help = 'Initialize the database with migrations and cache table'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset the database (WARNING: This will delete all data!)'
        )

    def handle(self, *args, **options):
        reset_db = options['reset']

        if reset_db:
            self.stdout.write(
                self.style.WARNING('Resetting database...')
            )
            
            # Drop all tables (be careful with this!)
            with connection.cursor() as cursor:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                for table in tables:
                    if table[0] != 'sqlite_sequence':
                        cursor.execute(f"DROP TABLE IF EXISTS {table[0]};")
            
            self.stdout.write(
                self.style.SUCCESS('Database reset completed.')
            )

        # Run migrations
        self.stdout.write('Running migrations...')
        call_command('migrate', verbosity=1)
        
        # Create cache table if using database cache
        if 'DatabaseCache' in str(settings.CACHES.get('default', {}).get('BACKEND', '')):
            self.stdout.write('Creating cache table...')
            call_command('createcachetable')
        
        # Collect static files
        self.stdout.write('Collecting static files...')
        call_command('collectstatic', '--noinput', verbosity=1)
        
        self.stdout.write(
            self.style.SUCCESS('Database initialization completed successfully!')
        )