{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Transactions" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i>
                        {% trans "Transactions" %}
                    </h3>
                    <div>
                        <a href="{% url 'finance:add_transaction' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            {% trans "New Transaction" %}
                        </a>
                        <a href="{% url 'finance:journal_entry_interface' %}" class="btn btn-success">
                            <i class="fas fa-book"></i>
                            {% trans "Journal Entry" %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search Form -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-search"></i>
                                        {% trans "Search & Filter" %}
                                    </h5>
                                </div>
                                <div class="card-body">
                                    {% crispy search_form %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Summary Statistics -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-list"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Total Transactions" %}</span>
                                    <span class="info-box-number">{{ total_transactions }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-clock"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Pending Approval" %}</span>
                                    <span class="info-box-number">{{ pending_approval }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-secondary">
                                    <i class="fas fa-edit"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Draft" %}</span>
                                    <span class="info-box-number">{{ draft_transactions }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-check"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Posted" %}</span>
                                    <span class="info-box-number">{{ posted_transactions }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Transactions Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Transaction ID" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Description" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Created By" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr>
                                    <td>
                                        <a href="{% url 'finance:transaction_detail' transaction.pk %}">
                                            {{ transaction.transaction_id }}
                                        </a>
                                    </td>
                                    <td>{{ transaction.transaction_date|date:"Y-m-d" }}</td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ transaction.get_transaction_type_display }}
                                        </span>
                                    </td>
                                    <td>{{ transaction.description|truncatechars:50 }}</td>
                                    <td class="text-right">{{ transaction.total_amount|floatformat:2 }}</td>
                                    <td>
                                        {% if transaction.status == 'draft' %}
                                            <span class="badge badge-secondary">{% trans "Draft" %}</span>
                                        {% elif transaction.status == 'pending_approval' %}
                                            <span class="badge badge-warning">{% trans "Pending Approval" %}</span>
                                        {% elif transaction.status == 'approved' %}
                                            <span class="badge badge-primary">{% trans "Approved" %}</span>
                                        {% elif transaction.status == 'posted' %}
                                            <span class="badge badge-success">{% trans "Posted" %}</span>
                                        {% elif transaction.status == 'cancelled' %}
                                            <span class="badge badge-danger">{% trans "Cancelled" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ transaction.created_by.get_full_name|default:transaction.created_by.username }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'finance:transaction_detail' transaction.pk %}" 
                                               class="btn btn-sm btn-outline-primary" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if transaction.status in 'draft,pending_approval' %}
                                                <a href="{% url 'finance:edit_transaction' transaction.pk %}" 
                                                   class="btn btn-sm btn-outline-secondary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            {% endif %}
                                            {% if transaction.status == 'pending_approval' and perms.finance.approve_transaction %}
                                                <a href="{% url 'finance:approve_transaction' transaction.pk %}" 
                                                   class="btn btn-sm btn-outline-warning" title="{% trans 'Approve' %}">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            {% endif %}
                                            {% if transaction.status in 'approved,draft' and perms.finance.post_transaction %}
                                                <form method="post" action="{% url 'finance:post_transaction' transaction.pk %}" 
                                                      style="display: inline;" onsubmit="return confirm('{% trans "Are you sure you want to post this transaction?" %}')">
                                                    {% csrf_token %}
                                                    <button type="submit" class="btn btn-sm btn-outline-success" title="{% trans 'Post' %}">
                                                        <i class="fas fa-paper-plane"></i>
                                                    </button>
                                                </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i>
                                            {% trans "No transactions found." %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="{% trans 'Transactions pagination' %}">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                        {% trans "First" %}
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                        {% trans "Previous" %}
                                    </a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                        {% trans "Next" %}
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                        {% trans "Last" %}
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}