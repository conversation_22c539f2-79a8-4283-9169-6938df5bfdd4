"""
Management command to generate encryption keys
"""
from django.core.management.base import BaseCommand
from cryptography.fernet import Fernet
import secrets


class Command(BaseCommand):
    help = 'Generate encryption keys for the application'

    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['fernet', 'secret'],
            default='fernet',
            help='Type of key to generate (fernet or secret)'
        )

    def handle(self, *args, **options):
        key_type = options['type']

        if key_type == 'fernet':
            # Generate Fernet encryption key
            key = Fernet.generate_key()
            self.stdout.write(
                self.style.SUCCESS(
                    f'Fernet encryption key generated:\n'
                    f'ENCRYPTION_KEY={key.decode()}\n\n'
                    f'Add this to your .env file for field-level encryption.'
                )
            )
        
        elif key_type == 'secret':
            # Generate Django secret key
            key = ''.join([
                secrets.choice('abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*(-_=+)')
                for _ in range(50)
            ])
            self.stdout.write(
                self.style.SUCCESS(
                    f'Django secret key generated:\n'
                    f'SECRET_KEY={key}\n\n'
                    f'Add this to your .env file as your Django secret key.'
                )
            )

        self.stdout.write(
            self.style.WARNING(
                'Keep these keys secure and never commit them to version control!'
            )
        )