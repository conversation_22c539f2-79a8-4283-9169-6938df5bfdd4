"""
Test cases for Task 4.3: Build Grade Management System

This test file validates the implementation of:
- Grade entry and calculation system
- GPA calculation algorithms
- Transcript generation features
- Grade analytics and reporting
- Parent/student grade access

Requirements: 3.3
"""

import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from datetime import date, timedelta
from decimal import Decimal

from core.models import School, AcademicYear, Semester
from students.models import Grade, Class, Student, Parent
from academics.models import (
    Subject, Teacher, ClassSubject, Exam, StudentGrade, GradingScale,
    StudentGPA, Transcript, GradeReport
)
from academics.grade_management import (
    GradeCalculator, GPACalculator, TranscriptGenerator,
    GradeReportGenerator, GradeAnalytics
)

User = get_user_model()


class Task43GradeManagementTest(TestCase):
    """
    Comprehensive test cases for Task 4.3 Grade Management System
    """
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.semester = Semester.objects.create(
            school=self.school,
            academic_year=self.academic_year,
            name="First Semester",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 10",
            level=10,
            max_capacity=60
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )
        
        # Create teacher
        self.teacher_user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        self.teacher = Teacher.objects.create(
            school=self.school,
            user=self.teacher_user,
            employee_id="T001",
            hire_date=date.today(),
            qualification="Master's in Mathematics"
        )
        
        # Create subjects
        self.math_subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH101",
            credit_hours=4,
            weekly_hours=5,
            created_by=self.user
        )
        
        self.science_subject = Subject.objects.create(
            school=self.school,
            name="Science",
            code="SCI101",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        
        self.teacher.subjects.add(self.math_subject, self.science_subject)
        
        # Create class
        self.class_obj = Class.objects.create(
            school=self.school,
            name="Section A",
            grade=self.grade,
            academic_year=self.academic_year,
            class_teacher=self.teacher_user,
            max_students=30
        )
        
        # Create parent
        self.parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        self.parent = Parent.objects.create(
            school=self.school,
            user=self.parent_user,
            father_name="Test Father",
            father_phone="+1234567890"
        )
        
        # Create student
        self.student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        self.student = Student.objects.create(
            school=self.school,
            user=self.student_user,
            student_id="STU001",
            current_class=self.class_obj,
            parent=self.parent,
            admission_date=date.today(),
            date_of_birth=date(2008, 1, 1)
        )
        
        # Create class subjects
        self.math_class_subject = ClassSubject.objects.create(
            school=self.school,
            class_obj=self.class_obj,
            subject=self.math_subject,
            teacher=self.teacher,
            academic_year=self.academic_year,
            semester=self.semester,
            weekly_hours=5
        )
        
        self.science_class_subject = ClassSubject.objects.create(
            school=self.school,
            class_obj=self.class_obj,
            subject=self.science_subject,
            teacher=self.teacher,
            academic_year=self.academic_year,
            semester=self.semester,
            weekly_hours=4
        )

    def test_grading_scale_model(self):
        """Test GradingScale model functionality"""
        # Create grading scale
        scale_definition = {
            'A+': {'min': 97, 'max': 100, 'points': 4.0},
            'A': {'min': 93, 'max': 96, 'points': 4.0},
            'A-': {'min': 90, 'max': 92, 'points': 3.7},
            'B+': {'min': 87, 'max': 89, 'points': 3.3},
            'B': {'min': 83, 'max': 86, 'points': 3.0},
            'B-': {'min': 80, 'max': 82, 'points': 2.7},
            'C+': {'min': 77, 'max': 79, 'points': 2.3},
            'C': {'min': 73, 'max': 76, 'points': 2.0},
            'C-': {'min': 70, 'max': 72, 'points': 1.7},
            'D': {'min': 65, 'max': 69, 'points': 1.0},
            'F': {'min': 0, 'max': 64, 'points': 0.0}
        }
        
        grading_scale = GradingScale.objects.create(
            school=self.school,
            name="Standard 4.0 Scale",
            description="Standard university grading scale",
            scale_definition=scale_definition,
            is_default=True,
            is_active=True,
            created_by=self.user
        )
        
        # Test grading scale properties
        self.assertEqual(grading_scale.name, "Standard 4.0 Scale")
        self.assertTrue(grading_scale.is_default)
        self.assertTrue(grading_scale.is_active)
        self.assertEqual(len(grading_scale.scale_definition), 11)
        
        # Test that only one default scale exists
        another_scale = GradingScale.objects.create(
            school=self.school,
            name="Alternative Scale",
            scale_definition=scale_definition,
            is_default=True,
            created_by=self.user
        )
        
        # Refresh from database
        grading_scale.refresh_from_db()
        self.assertFalse(grading_scale.is_default)
        self.assertTrue(another_scale.is_default)

    def test_enhanced_student_grade_model(self):
        """Test enhanced StudentGrade model with grading scale integration"""
        # Create grading scale
        scale_definition = {
            'A': {'min': 90, 'max': 100, 'points': 4.0},
            'B': {'min': 80, 'max': 89, 'points': 3.0},
            'C': {'min': 70, 'max': 79, 'points': 2.0},
            'D': {'min': 60, 'max': 69, 'points': 1.0},
            'F': {'min': 0, 'max': 59, 'points': 0.0}
        }
        
        GradingScale.objects.create(
            school=self.school,
            name="Test Scale",
            scale_definition=scale_definition,
            is_default=True,
            created_by=self.user
        )
        
        # Create exam
        exam = Exam.objects.create(
            school=self.school,
            name="Midterm Exam",
            class_subject=self.math_class_subject,
            exam_type="midterm",
            total_marks=100,
            weightage=30.0,
            exam_date=date.today(),
            academic_year=self.academic_year,
            semester=self.semester,
            created_by=self.user
        )
        
        # Create student grade
        student_grade = StudentGrade.objects.create(
            school=self.school,
            student=self.student,
            exam=exam,
            marks_obtained=85,
            graded_by=self.teacher_user
        )
        
        # Test automatic calculations
        self.assertEqual(student_grade.percentage, 85.0)
        self.assertEqual(student_grade.grade_letter, 'B')
        self.assertEqual(student_grade.get_grade_points(), 3.0)
        self.assertTrue(student_grade.is_passing_grade())
        
        # Test improvement suggestions
        suggestions = student_grade.get_improvement_suggestions()
        self.assertIsInstance(suggestions, list)
        self.assertIn("Focus on advanced problem solving", suggestions)

    def test_student_gpa_model(self):
        """Test StudentGPA model functionality"""
        # Create GPA record
        gpa_record = StudentGPA.objects.create(
            school=self.school,
            student=self.student,
            academic_year=self.academic_year,
            semester=self.semester,
            gpa_type='semester',
            gpa_value=Decimal('3.25'),
            total_credit_hours=15,
            earned_credit_hours=15,
            quality_points=Decimal('48.75'),
            class_rank=5,
            total_students=30
        )
        
        # Test GPA properties
        self.assertEqual(gpa_record.gpa_value, Decimal('3.25'))
        self.assertEqual(gpa_record.gpa_letter_grade, 'B+')
        self.assertEqual(gpa_record.academic_standing, 'Good Standing')
        self.assertEqual(gpa_record.calculate_percentile_rank(), 86.7)

    def test_grade_calculator(self):
        """Test GradeCalculator functionality"""
        calculator = GradeCalculator(self.school)
        
        # Test percentage calculation
        percentage = calculator.calculate_percentage(85, 100)
        self.assertEqual(percentage, 85.0)
        
        # Test grade letter calculation
        grade_letter = calculator.get_grade_letter(85)
        self.assertIn(grade_letter, ['A', 'B', 'C', 'D', 'F'])
        
        # Test grade points calculation
        grade_points = calculator.get_grade_points('B')
        self.assertIsInstance(grade_points, float)
        self.assertGreaterEqual(grade_points, 0.0)
        self.assertLessEqual(grade_points, 4.0)
        
        # Test weighted average calculation
        grades = [
            {'percentage': 85, 'weight': 0.3, 'credit_hours': 3},
            {'percentage': 90, 'weight': 0.4, 'credit_hours': 4},
            {'percentage': 78, 'weight': 0.3, 'credit_hours': 2}
        ]
        
        weighted_avg = calculator.calculate_weighted_average(grades)
        self.assertIsInstance(weighted_avg, float)
        self.assertGreater(weighted_avg, 0)
        self.assertLess(weighted_avg, 100)

    def test_grade_calculator_subject_grade(self):
        """Test subject grade calculation"""
        calculator = GradeCalculator(self.school)
        
        # Create exams for the subject
        midterm = Exam.objects.create(
            school=self.school,
            name="Midterm",
            class_subject=self.math_class_subject,
            exam_type="midterm",
            total_marks=100,
            weightage=40.0,
            exam_date=date.today(),
            academic_year=self.academic_year,
            semester=self.semester,
            created_by=self.user
        )
        
        final = Exam.objects.create(
            school=self.school,
            name="Final",
            class_subject=self.math_class_subject,
            exam_type="final",
            total_marks=100,
            weightage=60.0,
            exam_date=date.today() + timedelta(days=30),
            academic_year=self.academic_year,
            semester=self.semester,
            created_by=self.user
        )
        
        # Create student grades
        StudentGrade.objects.create(
            school=self.school,
            student=self.student,
            exam=midterm,
            marks_obtained=85,
            graded_by=self.teacher_user
        )
        
        StudentGrade.objects.create(
            school=self.school,
            student=self.student,
            exam=final,
            marks_obtained=90,
            graded_by=self.teacher_user
        )
        
        # Calculate subject grade
        subject_grade = calculator.calculate_subject_grade(
            self.student, self.math_class_subject, self.academic_year, self.semester
        )
        
        # Test results
        self.assertIn('overall_percentage', subject_grade)
        self.assertIn('grade_letter', subject_grade)
        self.assertIn('grade_points', subject_grade)
        self.assertEqual(subject_grade['total_exams'], 2)
        self.assertEqual(subject_grade['completed_exams'], 2)
        self.assertEqual(subject_grade['status'], 'complete')
        
        # Test weighted calculation (40% * 85 + 60% * 90 = 88)
        expected_percentage = (40 * 85 + 60 * 90) / 100
        self.assertAlmostEqual(subject_grade['overall_percentage'], expected_percentage, places=1)

    def test_gpa_calculator(self):
        """Test GPACalculator functionality"""
        calculator = GPACalculator(self.school)
        
        # Create exams and grades for multiple subjects
        self._create_sample_grades()
        
        # Test semester GPA calculation
        semester_gpa = calculator.calculate_semester_gpa(self.student, self.semester)
        
        self.assertIn('gpa', semester_gpa)
        self.assertIn('total_credit_hours', semester_gpa)
        self.assertIn('subject_grades', semester_gpa)
        self.assertGreaterEqual(semester_gpa['gpa'], 0.0)
        self.assertLessEqual(semester_gpa['gpa'], 4.0)
        
        # Test cumulative GPA calculation
        cumulative_gpa = calculator.calculate_cumulative_gpa(self.student)
        
        self.assertIn('cumulative_gpa', cumulative_gpa)
        self.assertIn('total_credit_hours', cumulative_gpa)
        self.assertIn('semester_gpas', cumulative_gpa)
        
        # Test class rank calculation
        rank_data = calculator.calculate_class_rank(self.student, self.academic_year)
        
        self.assertIn('rank', rank_data)
        self.assertIn('total_students', rank_data)
        self.assertIn('percentile', rank_data)

    def test_transcript_generator(self):
        """Test TranscriptGenerator functionality"""
        generator = TranscriptGenerator(self.school)
        
        # Create sample grades
        self._create_sample_grades()
        
        # Generate transcript
        transcript = generator.generate_transcript(
            student=self.student,
            transcript_type='unofficial',
            generated_by=self.user
        )
        
        # Test transcript properties
        self.assertEqual(transcript.student, self.student)
        self.assertEqual(transcript.transcript_type, 'unofficial')
        self.assertIsNotNone(transcript.transcript_data)
        self.assertGreaterEqual(transcript.cumulative_gpa, 0)
        self.assertGreater(transcript.total_credit_hours, 0)
        
        # Test transcript data structure
        data = transcript.transcript_data
        self.assertIn('student_info', data)
        self.assertIn('academic_records', data)
        self.assertIn('summary', data)
        
        # Test student info
        student_info = data['student_info']
        self.assertEqual(student_info['name'], self.student.full_name)
        self.assertEqual(student_info['student_id'], self.student.student_id)
        
        # Test summary
        summary = data['summary']
        self.assertIn('cumulative_gpa', summary)
        self.assertIn('total_credit_hours', summary)
        self.assertIn('academic_standing', summary)

    def test_grade_report_generator(self):
        """Test GradeReportGenerator functionality"""
        generator = GradeReportGenerator(self.school)
        
        # Create sample grades
        self._create_sample_grades()
        
        # Generate progress report
        report = generator.generate_progress_report(
            student=self.student,
            report_period_start=self.semester.start_date,
            report_period_end=self.semester.end_date,
            generated_by=self.user
        )
        
        # Test report properties
        self.assertEqual(report.student, self.student)
        self.assertEqual(report.report_type, 'progress')
        self.assertEqual(report.semester, self.semester)
        self.assertIsNotNone(report.report_data)
        self.assertGreater(report.total_subjects, 0)
        
        # Test report data structure
        data = report.report_data
        self.assertIn('student_info', data)
        self.assertIn('subjects', data)
        self.assertIn('summary', data)
        
        # Test subjects data
        subjects = data['subjects']
        self.assertGreater(len(subjects), 0)
        
        for subject in subjects:
            self.assertIn('subject_name', subject)
            self.assertIn('teacher', subject)
            self.assertIn('overall_percentage', subject)
            self.assertIn('grade_letter', subject)

    def test_grade_analytics(self):
        """Test GradeAnalytics functionality"""
        analytics = GradeAnalytics(self.school)
        
        # Create additional students for meaningful analytics
        self._create_additional_students()
        self._create_sample_grades_for_all_students()
        
        # Get class performance analytics
        class_analytics = analytics.get_class_performance_analytics(
            self.class_obj, self.academic_year
        )
        
        # Test analytics structure
        self.assertIn('class_info', class_analytics)
        self.assertIn('gpa_statistics', class_analytics)
        self.assertIn('grade_distribution', class_analytics)
        
        # Test class info
        class_info = class_analytics['class_info']
        self.assertEqual(class_info['class_name'], str(self.class_obj))
        self.assertGreater(class_info['total_students'], 0)
        
        # Test GPA statistics
        gpa_stats = class_analytics['gpa_statistics']
        self.assertIn('average_gpa', gpa_stats)
        self.assertIn('highest_gpa', gpa_stats)
        self.assertIn('lowest_gpa', gpa_stats)
        self.assertIn('median_gpa', gpa_stats)
        
        # Test grade distribution
        distribution = class_analytics['grade_distribution']
        self.assertIsInstance(distribution, dict)
        total_students = sum(distribution.values())
        self.assertGreater(total_students, 0)

    def test_transcript_model_functionality(self):
        """Test Transcript model specific functionality"""
        # Create transcript
        transcript_data = {
            'student_info': {'name': 'Test Student'},
            'academic_records': [],
            'summary': {'gpa': 3.5}
        }
        
        transcript = Transcript.objects.create(
            school=self.school,
            student=self.student,
            transcript_type='official',
            academic_year_start=self.academic_year,
            academic_year_end=self.academic_year,
            generated_by=self.user,
            transcript_data=transcript_data,
            cumulative_gpa=Decimal('3.50'),
            total_credit_hours=120
        )
        
        # Test transcript properties
        self.assertFalse(transcript.is_sealed)
        self.assertTrue(transcript.can_modify())
        
        # Test sealing functionality
        transcript.seal_transcript(self.user)
        self.assertTrue(transcript.is_sealed)
        self.assertFalse(transcript.can_modify())
        self.assertIsNotNone(transcript.seal_date)

    def test_grade_report_model_functionality(self):
        """Test GradeReport model specific functionality"""
        # Create grade report
        report_data = {
            'student_info': {'name': 'Test Student'},
            'subjects': [],
            'summary': {'gpa': 3.2}
        }
        
        report = GradeReport.objects.create(
            school=self.school,
            student=self.student,
            report_type='midterm',
            academic_year=self.academic_year,
            semester=self.semester,
            report_period_start=self.semester.start_date,
            report_period_end=self.semester.end_date,
            generated_by=self.user,
            report_data=report_data,
            overall_gpa=Decimal('3.20'),
            total_subjects=5,
            subjects_passed=4,
            attendance_percentage=Decimal('92.50')
        )
        
        # Test report properties
        self.assertFalse(report.parent_viewed)
        self.assertEqual(report.pass_percentage, 80.0)
        
        # Test parent viewing functionality
        report.mark_viewed_by_parent()
        self.assertTrue(report.parent_viewed)
        self.assertIsNotNone(report.parent_viewed_at)

    def test_integration_complete_grade_workflow(self):
        """Test complete grade management workflow integration"""
        # 1. Create grading scale
        scale_definition = {
            'A': {'min': 90, 'max': 100, 'points': 4.0},
            'B': {'min': 80, 'max': 89, 'points': 3.0},
            'C': {'min': 70, 'max': 79, 'points': 2.0},
            'D': {'min': 60, 'max': 69, 'points': 1.0},
            'F': {'min': 0, 'max': 59, 'points': 0.0}
        }
        
        GradingScale.objects.create(
            school=self.school,
            name="Integration Test Scale",
            scale_definition=scale_definition,
            is_default=True,
            created_by=self.user
        )
        
        # 2. Create comprehensive exam structure
        exams = []
        for i, exam_type in enumerate(['quiz', 'midterm', 'assignment', 'final']):
            exam = Exam.objects.create(
                school=self.school,
                name=f"Math {exam_type.title()}",
                class_subject=self.math_class_subject,
                exam_type=exam_type,
                total_marks=100,
                weightage=25.0,
                exam_date=date.today() + timedelta(days=i*10),
                academic_year=self.academic_year,
                semester=self.semester,
                created_by=self.user
            )
            exams.append(exam)
        
        # 3. Create student grades
        marks = [85, 90, 78, 88]  # Different marks for each exam
        for exam, mark in zip(exams, marks):
            StudentGrade.objects.create(
                school=self.school,
                student=self.student,
                exam=exam,
                marks_obtained=mark,
                graded_by=self.teacher_user
            )
        
        # 4. Test grade calculation
        calculator = GradeCalculator(self.school)
        subject_grade = calculator.calculate_subject_grade(
            self.student, self.math_class_subject, self.academic_year, self.semester
        )
        
        self.assertEqual(subject_grade['status'], 'complete')
        self.assertEqual(subject_grade['completed_exams'], 4)
        
        # 5. Test GPA calculation
        gpa_calculator = GPACalculator(self.school)
        semester_gpa = gpa_calculator.calculate_semester_gpa(self.student, self.semester)
        
        self.assertGreater(semester_gpa['gpa'], 0)
        self.assertGreater(semester_gpa['total_credit_hours'], 0)
        
        # 6. Save GPA record
        gpa_record = gpa_calculator.save_gpa_record(
            self.student, semester_gpa, 'semester', self.academic_year, self.semester
        )
        
        self.assertIsInstance(gpa_record, StudentGPA)
        self.assertEqual(gpa_record.gpa_type, 'semester')
        
        # 7. Generate transcript
        transcript_generator = TranscriptGenerator(self.school)
        transcript = transcript_generator.generate_transcript(
            self.student, 'unofficial', generated_by=self.user
        )
        
        self.assertIsInstance(transcript, Transcript)
        self.assertGreater(transcript.total_credit_hours, 0)
        
        # 8. Generate grade report
        report_generator = GradeReportGenerator(self.school)
        report = report_generator.generate_progress_report(
            self.student,
            self.semester.start_date,
            self.semester.end_date,
            self.user
        )
        
        self.assertIsInstance(report, GradeReport)
        self.assertGreater(report.total_subjects, 0)
        
        # 9. Test analytics
        analytics = GradeAnalytics(self.school)
        class_analytics = analytics.get_class_performance_analytics(
            self.class_obj, self.academic_year
        )
        
        self.assertIn('gpa_statistics', class_analytics)
        
        # Verify complete workflow integration
        self.assertEqual(len(StudentGrade.objects.filter(student=self.student)), 4)
        self.assertEqual(StudentGPA.objects.filter(student=self.student).count(), 1)
        self.assertEqual(Transcript.objects.filter(student=self.student).count(), 1)
        self.assertEqual(GradeReport.objects.filter(student=self.student).count(), 1)

    def test_validation_and_error_handling(self):
        """Test validation and error handling in grade management"""
        # Test invalid grading scale
        with self.assertRaises(ValidationError):
            invalid_scale = GradingScale(
                school=self.school,
                name="Invalid Scale",
                scale_definition="not a dict",  # Should be dict
                created_by=self.user
            )
            invalid_scale.full_clean()
        
        # Test invalid GPA value
        with self.assertRaises(ValidationError):
            invalid_gpa = StudentGPA(
                school=self.school,
                student=self.student,
                academic_year=self.academic_year,
                gpa_type='semester',
                gpa_value=Decimal('5.00'),  # Exceeds maximum
                total_credit_hours=15,
                earned_credit_hours=15,
                quality_points=Decimal('75.00')
            )
            invalid_gpa.full_clean()
        
        # Test transcript generation without grades
        generator = TranscriptGenerator(self.school)
        transcript = generator.generate_transcript(
            self.student, 'unofficial', generated_by=self.user
        )
        
        # Should still generate transcript with zero values
        self.assertEqual(transcript.cumulative_gpa, 0)
        self.assertEqual(transcript.total_credit_hours, 0)

    def _create_sample_grades(self):
        """Helper method to create sample grades for testing"""
        # Create exams for math
        math_midterm = Exam.objects.create(
            school=self.school,
            name="Math Midterm",
            class_subject=self.math_class_subject,
            exam_type="midterm",
            total_marks=100,
            weightage=50.0,
            exam_date=date.today(),
            academic_year=self.academic_year,
            semester=self.semester,
            created_by=self.user
        )
        
        math_final = Exam.objects.create(
            school=self.school,
            name="Math Final",
            class_subject=self.math_class_subject,
            exam_type="final",
            total_marks=100,
            weightage=50.0,
            exam_date=date.today() + timedelta(days=30),
            academic_year=self.academic_year,
            semester=self.semester,
            created_by=self.user
        )
        
        # Create exams for science
        science_midterm = Exam.objects.create(
            school=self.school,
            name="Science Midterm",
            class_subject=self.science_class_subject,
            exam_type="midterm",
            total_marks=100,
            weightage=50.0,
            exam_date=date.today(),
            academic_year=self.academic_year,
            semester=self.semester,
            created_by=self.user
        )
        
        science_final = Exam.objects.create(
            school=self.school,
            name="Science Final",
            class_subject=self.science_class_subject,
            exam_type="final",
            total_marks=100,
            weightage=50.0,
            exam_date=date.today() + timedelta(days=30),
            academic_year=self.academic_year,
            semester=self.semester,
            created_by=self.user
        )
        
        # Create student grades
        StudentGrade.objects.create(
            school=self.school,
            student=self.student,
            exam=math_midterm,
            marks_obtained=85,
            graded_by=self.teacher_user
        )
        
        StudentGrade.objects.create(
            school=self.school,
            student=self.student,
            exam=math_final,
            marks_obtained=90,
            graded_by=self.teacher_user
        )
        
        StudentGrade.objects.create(
            school=self.school,
            student=self.student,
            exam=science_midterm,
            marks_obtained=78,
            graded_by=self.teacher_user
        )
        
        StudentGrade.objects.create(
            school=self.school,
            student=self.student,
            exam=science_final,
            marks_obtained=82,
            graded_by=self.teacher_user
        )

    def _create_additional_students(self):
        """Helper method to create additional students for analytics testing"""
        self.additional_students = []
        
        for i in range(5):
            user = User.objects.create_user(
                username=f"student{i+2}",
                email=f"student{i+2}@school.com",
                password="testpass123",
                user_type="student"
            )
            
            student = Student.objects.create(
                school=self.school,
                user=user,
                student_id=f"STU00{i+2}",
                current_class=self.class_obj,
                parent=self.parent,  # Use the same parent for simplicity
                admission_date=date.today(),
                date_of_birth=date(2008, 1, 1)
            )
            
            self.additional_students.append(student)

    def _create_sample_grades_for_all_students(self):
        """Helper method to create sample grades for all students"""
        # Create basic exams
        exam = Exam.objects.create(
            school=self.school,
            name="Test Exam",
            class_subject=self.math_class_subject,
            exam_type="midterm",
            total_marks=100,
            weightage=100.0,
            exam_date=date.today(),
            academic_year=self.academic_year,
            semester=self.semester,
            created_by=self.user
        )
        
        # Create grades for all students
        all_students = [self.student] + getattr(self, 'additional_students', [])
        marks = [85, 90, 75, 88, 92, 78]  # Different marks for variety
        
        for i, student in enumerate(all_students):
            if i < len(marks):
                StudentGrade.objects.create(
                    school=self.school,
                    student=student,
                    exam=exam,
                    marks_obtained=marks[i],
                    graded_by=self.teacher_user
                )
        
        # Create GPA records for analytics
        gpa_calculator = GPACalculator(self.school)
        for student in all_students:
            semester_gpa = gpa_calculator.calculate_semester_gpa(student, self.semester)
            if semester_gpa['gpa'] > 0:
                gpa_calculator.save_gpa_record(
                    student, semester_gpa, 'cumulative', self.academic_year
                )


@pytest.mark.django_db
class TestTask43Integration:
    """Integration tests for Task 4.3 Grade Management System"""
    
    def test_complete_grade_management_workflow(self):
        """Test complete grade management workflow integration"""
        # Create school and academic structure
        school = School.objects.create(
            name="Integration Test School",
            code="ITS",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        academic_year = AcademicYear.objects.create(
            school=school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        semester = Semester.objects.create(
            school=school,
            academic_year=academic_year,
            name="First Semester",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        # Create complete academic structure
        grade = Grade.objects.create(
            school=school,
            name="Grade 10",
            level=10,
            max_capacity=60
        )
        
        user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )
        
        teacher_user = User.objects.create_user(
            username="teacher",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        teacher = Teacher.objects.create(
            school=school,
            user=teacher_user,
            employee_id="T001",
            hire_date=date.today(),
            qualification="Master's in Mathematics"
        )
        
        subject = Subject.objects.create(
            school=school,
            name="Mathematics",
            code="MATH101",
            credit_hours=4,
            weekly_hours=5,
            created_by=user
        )
        
        teacher.subjects.add(subject)
        
        class_obj = Class.objects.create(
            school=school,
            name="Section A",
            grade=grade,
            academic_year=academic_year,
            class_teacher=teacher_user,
            max_students=30
        )
        
        parent_user = User.objects.create_user(
            username="parent",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        parent = Parent.objects.create(
            school=school,
            user=parent_user,
            father_name="Test Father",
            father_phone="+1234567890"
        )
        
        student_user = User.objects.create_user(
            username="student",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=school,
            user=student_user,
            student_id="STU001",
            current_class=class_obj,
            parent=parent,
            admission_date=date.today(),
            date_of_birth=date(2008, 1, 1)
        )
        
        class_subject = ClassSubject.objects.create(
            school=school,
            class_obj=class_obj,
            subject=subject,
            teacher=teacher,
            academic_year=academic_year,
            semester=semester,
            weekly_hours=5
        )
        
        # Create grading scale
        scale_definition = {
            'A': {'min': 90, 'max': 100, 'points': 4.0},
            'B': {'min': 80, 'max': 89, 'points': 3.0},
            'C': {'min': 70, 'max': 79, 'points': 2.0},
            'D': {'min': 60, 'max': 69, 'points': 1.0},
            'F': {'min': 0, 'max': 59, 'points': 0.0}
        }
        
        GradingScale.objects.create(
            school=school,
            name="Standard Scale",
            scale_definition=scale_definition,
            is_default=True,
            created_by=user
        )
        
        # Create exams and grades
        exam = Exam.objects.create(
            school=school,
            name="Final Exam",
            class_subject=class_subject,
            exam_type="final",
            total_marks=100,
            weightage=100.0,
            exam_date=date.today(),
            academic_year=academic_year,
            semester=semester,
            created_by=user
        )
        
        StudentGrade.objects.create(
            school=school,
            student=student,
            exam=exam,
            marks_obtained=85,
            graded_by=teacher_user
        )
        
        # Test complete workflow
        
        # 1. Grade calculation
        calculator = GradeCalculator(school)
        subject_grade = calculator.calculate_subject_grade(
            student, class_subject, academic_year, semester
        )
        
        assert subject_grade['overall_percentage'] == 85.0
        assert subject_grade['grade_letter'] == 'B'
        assert subject_grade['status'] == 'complete'
        
        # 2. GPA calculation
        gpa_calculator = GPACalculator(school)
        semester_gpa = gpa_calculator.calculate_semester_gpa(student, semester)
        
        assert semester_gpa['gpa'] == 3.0  # B grade = 3.0 points
        assert semester_gpa['total_credit_hours'] == 4
        
        # 3. Save GPA record
        gpa_record = gpa_calculator.save_gpa_record(
            student, semester_gpa, 'semester', academic_year, semester
        )
        
        assert gpa_record.gpa_value == Decimal('3.00')
        assert gpa_record.gpa_type == 'semester'
        
        # 4. Generate transcript
        transcript_generator = TranscriptGenerator(school)
        transcript = transcript_generator.generate_transcript(
            student, 'unofficial', generated_by=user
        )
        
        assert transcript.cumulative_gpa == Decimal('3.00')
        assert transcript.total_credit_hours == 4
        assert 'student_info' in transcript.transcript_data
        
        # 5. Generate grade report
        report_generator = GradeReportGenerator(school)
        report = report_generator.generate_progress_report(
            student, semester.start_date, semester.end_date, user
        )
        
        assert report.total_subjects == 1
        assert report.subjects_passed == 1
        assert report.overall_gpa == Decimal('3.00')
        
        # 6. Test analytics
        analytics = GradeAnalytics(school)
        class_analytics = analytics.get_class_performance_analytics(class_obj, academic_year)
        
        assert 'gpa_statistics' in class_analytics
        assert class_analytics['gpa_statistics']['average_gpa'] == 3.0
        
        # Verify all components work together
        assert StudentGrade.objects.filter(student=student).count() == 1
        assert StudentGPA.objects.filter(student=student).count() == 1
        assert Transcript.objects.filter(student=student).count() == 1
        assert GradeReport.objects.filter(student=student).count() == 1