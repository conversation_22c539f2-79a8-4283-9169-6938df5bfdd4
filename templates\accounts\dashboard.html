{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Dashboard" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .stat-card .card-body {
        padding: 2rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 1.1rem;
        opacity: 0.9;
    }
    
    .quick-action-btn {
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        text-decoration: none;
        color: inherit;
        display: block;
        transition: all 0.3s ease;
    }
    
    .quick-action-btn:hover {
        color: inherit;
        transform: translateY(-2px);
    }
    
    .welcome-section {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .chart-container {
        height: 300px;
    }

    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -35px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #667eea;
    }

    .welcome-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        {% trans "Welcome back" %}, {{ user.first_name|default:user.username }}!
                    </h1>
                    <p class="lead mb-0">
                        {% if user.user_type == 'admin' %}
                            {% trans "Manage your school efficiently with our comprehensive ERP system." %}
                        {% elif user.user_type == 'teacher' %}
                            {% trans "Track your classes, students, and academic progress." %}
                        {% elif user.user_type == 'student' %}
                            {% trans "View your grades, attendance, and school information." %}
                        {% elif user.user_type == 'parent' %}
                            {% trans "Monitor your children's academic progress and school activities." %}
                        {% else %}
                            {% trans "Access your personalized dashboard and manage your tasks." %}
                        {% endif %}
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <i class="fas fa-user-circle fa-5x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Content Based on User Type -->
{% if user.user_type == 'admin' %}
    <!-- Admin Dashboard -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card dashboard-card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <div class="stat-number">{{ dashboard_data.total_students|default:0 }}</div>
                    <div class="stat-label">{% trans "Total Students" %}</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-chalkboard-teacher fa-3x mb-3"></i>
                    <div class="stat-number">{{ dashboard_data.total_teachers|default:0 }}</div>
                    <div class="stat-label">{% trans "Total Teachers" %}</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-school fa-3x mb-3"></i>
                    <div class="stat-number">{{ stats.total_classes|default:0 }}</div>
                    <div class="stat-label">{% trans "Classes" %}</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill fa-3x mb-3"></i>
                    <div class="stat-number">${{ stats.monthly_revenue|floatformat:0|default:0 }}</div>
                    <div class="stat-label">{% trans "Monthly Revenue" %}</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Recent Activities" %}</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="activityChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Quick Actions" %}</h5>
                </div>
                <div class="card-body">
                    <a href="{% url 'students:create' %}" class="quick-action-btn bg-primary text-white">
                        <i class="fas fa-user-plus me-2"></i>{% trans "Add New Student" %}
                    </a>
                    <a href="{% url 'academics:teacher_create' %}" class="quick-action-btn bg-success text-white">
                        <i class="fas fa-chalkboard-teacher me-2"></i>{% trans "Add New Teacher" %}
                    </a>
                    <a href="{% url 'students:class_create' %}" class="quick-action-btn bg-info text-white">
                        <i class="fas fa-school me-2"></i>{% trans "Create New Class" %}
                    </a>
                    <a href="{% url 'reports:dashboard' %}" class="quick-action-btn bg-warning text-white">
                        <i class="fas fa-file-alt me-2"></i>{% trans "Generate Report" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

{% elif user.user_type == 'teacher' %}
    <!-- Teacher Dashboard -->
    <div class="row">
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "My Classes" %}</h5>
                </div>
                <div class="card-body">
                    {% if dashboard_data.my_classes %}
                        {% for class_subject in dashboard_data.my_classes %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ class_subject.class_obj }} - {{ class_subject.subject }}</span>
                                <span class="badge bg-primary">{{ class_subject.weekly_hours }}h/week</span>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">{% trans "No classes assigned yet." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Quick Actions" %}</h5>
                </div>
                <div class="card-body">
                    <a href="{% url 'academics:take_attendance' %}" class="quick-action-btn bg-primary text-white">
                        <i class="fas fa-calendar-check me-2"></i>{% trans "Take Attendance" %}
                    </a>
                    <a href="{% url 'academics:grade_entry' %}" class="quick-action-btn bg-success text-white">
                        <i class="fas fa-graduation-cap me-2"></i>{% trans "Enter Grades" %}
                    </a>
                    <a href="{% url 'academics:reports' %}" class="quick-action-btn bg-info text-white">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "View Reports" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

{% elif user.user_type == 'student' %}
    <!-- Student Dashboard -->
    <div class="row">
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "My Information" %}</h5>
                </div>
                <div class="card-body">
                    {% if dashboard_data.current_class %}
                        <p><strong>{% trans "Class:" %}</strong> {{ dashboard_data.current_class }}</p>
                        <p><strong>{% trans "Student ID:" %}</strong> {{ user.student_profile.student_id }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Unpaid Fees" %}</h5>
                </div>
                <div class="card-body">
                    {% if dashboard_data.unpaid_fees %}
                        {% for fee in dashboard_data.unpaid_fees %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ fee.grade_fee.fee_type }}</span>
                                <span class="badge bg-warning">${{ fee.net_amount }}</span>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-success">{% trans "All fees are paid!" %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

{% elif user.user_type == 'parent' %}
    <!-- Parent Dashboard -->
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "My Children" %}</h5>
                </div>
                <div class="card-body">
                    {% if dashboard_data.children %}
                        <div class="row">
                            {% for child in dashboard_data.children %}
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ child.full_name }}</h6>
                                            <p class="card-text">
                                                <strong>{% trans "Class:" %}</strong> {{ child.current_class }}<br>
                                                <strong>{% trans "Student ID:" %}</strong> {{ child.student_id }}
                                            </p>
                                            <a href="#" class="btn btn-sm btn-primary">{% trans "View Details" %}</a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">{% trans "No children registered." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

{% else %}
    <!-- Default Dashboard -->
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <i class="fas fa-cog fa-5x text-muted mb-3"></i>
                    <h4>{% trans "Dashboard Under Construction" %}</h4>
                    <p class="text-muted">{% trans "Your personalized dashboard is being prepared." %}</p>
                </div>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Sample chart for admin dashboard
    {% if user.user_type == 'admin' %}
    const ctx = document.getElementById('activityChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: '{% trans "Student Enrollments" %}',
                    data: [12, 19, 3, 5, 2, 3],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    {% endif %}
</script>
{% endblock %}
