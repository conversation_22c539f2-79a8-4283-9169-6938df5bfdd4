"""
Pytest tests for the Student Discipline System
"""
import pytest
from datetime import date, timedelta
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from core.models import School, AcademicYear
from students.models import (
    Student, StudentInfraction, StudentSuspension, 
    DisciplinaryAction, DisciplineReport
)

User = get_user_model()


@pytest.fixture
def school():
    """Create a test school"""
    return School.objects.create(
        name="Test School",
        code="TS001",
        address="123 Test St",
        phone="************",
        email="<EMAIL>",
        established_date=date(2020, 1, 1)
    )


@pytest.fixture
def academic_year(school):
    """Create a test academic year"""
    return AcademicYear.objects.create(
        school=school,
        name="2024-2025",
        start_date=date(2024, 9, 1),
        end_date=date(2025, 6, 30),
        is_current=True
    )


@pytest.fixture
def parent_user():
    """Create a test parent user"""
    return User.objects.create_user(
        username='parent1',
        email='<EMAIL>',
        password='testpass123',
        user_type='parent'
    )


@pytest.fixture
def parent(school, parent_user):
    """Create a test parent"""
    from students.models import Parent
    return Parent.objects.create(
        school=school,
        user=parent_user,
        father_name="<PERSON> Doe Sr.",
        mother_name="Jane Doe",
        father_phone="************",
        mother_phone="************",
        home_address="123 Test Street, Test City"
    )


@pytest.fixture
def student_user():
    """Create a test student user"""
    return User.objects.create_user(
        username='student1',
        email='<EMAIL>',
        password='testpass123',
        user_type='student'
    )


@pytest.fixture
def student(school, parent, student_user):
    """Create a test student"""
    return Student.objects.create(
        school=school,
        user=student_user,
        parent=parent,
        student_id="TS2024001",
        first_name="John",
        last_name="Doe",
        date_of_birth=date(2010, 5, 15),
        gender='M',
        nationality="Test Country",
        admission_date=date(2024, 9, 1)
    )


@pytest.fixture
def teacher_user():
    """Create a test teacher user"""
    return User.objects.create_user(
        username='teacher1',
        email='<EMAIL>',
        password='testpass123'
    )


@pytest.fixture
def infraction(school, student, teacher_user):
    """Create a test infraction"""
    return StudentInfraction.objects.create(
        school=school,
        student=student,
        incident_date=date.today(),
        description="Test infraction",
        severity='moderate',
        action_taken='warning',
        reported_by=teacher_user
    )


@pytest.mark.django_db
class TestStudentSuspension:
    """Test cases for StudentSuspension model"""

    def test_suspension_creation(self, school, student, infraction, teacher_user):
        """Test creating a student suspension"""
        suspension = StudentSuspension.objects.create(
            school=school,
            student=student,
            suspension_type='in_school',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=3),
            reason="Repeated violations",
            issued_by=teacher_user
        )
        
        assert suspension.student == student
        assert suspension.suspension_type == 'in_school'
        assert suspension.status == 'active'
        assert suspension.is_active

    def test_suspension_duration_calculation(self, school, student, infraction, teacher_user):
        """Test suspension duration calculation"""
        suspension = StudentSuspension.objects.create(
            school=school,
            student=student,
            suspension_type='out_of_school',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=5),
            reason="Serious violation",
            issued_by=teacher_user
        )
        
        assert suspension.duration_days == 6

    def test_suspension_completion(self, school, student, infraction, teacher_user):
        """Test suspension completion"""
        suspension = StudentSuspension.objects.create(
            school=school,
            student=student,
            suspension_type='in_school',
            start_date=date.today() - timedelta(days=3),
            end_date=date.today() - timedelta(days=1),
            reason="Test suspension",
            issued_by=teacher_user
        )
        
        suspension.complete_suspension()
        suspension.refresh_from_db()
        
        assert suspension.status == 'completed'
        assert not suspension.is_active

    def test_suspension_validation(self, school, student, infraction, teacher_user):
        """Test suspension validation"""
        # Test end date before start date
        with pytest.raises(ValidationError):
            suspension = StudentSuspension(
                school=school,
                student=student,
                suspension_type='in_school',
                start_date=date.today(),
                end_date=date.today() - timedelta(days=1),
                reason="Invalid dates",
                issued_by=teacher_user
            )
            suspension.full_clean()

    def test_suspension_notification(self, school, student, infraction, teacher_user):
        """Test suspension notification creation"""
        suspension = StudentSuspension.objects.create(
            school=school,
            student=student,
            suspension_type='out_of_school',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=3),
            reason="Test notification",
            issued_by=teacher_user
        )
        
        # Test that notification method exists and can be called
        assert hasattr(suspension, 'notify_parent')
        suspension.notify_parent()
        suspension.refresh_from_db()
        assert suspension.parent_notified
        assert suspension.parent_notified_at is not None


@pytest.mark.django_db
class TestDisciplinaryAction:
    """Test cases for DisciplinaryAction model"""

    def test_disciplinary_action_creation(self, school, student, infraction, teacher_user):
        """Test creating a disciplinary action"""
        action = DisciplinaryAction.objects.create(
            school=school,
            student=student,
            infraction=infraction,
            action_type='counseling',
            description="Mandatory counseling session",
            assigned_to=teacher_user,
            due_date=date.today() + timedelta(days=7)
        )
        
        assert action.student == student
        assert action.action_type == 'counseling'
        assert action.status == 'pending'
        assert not action.is_completed

    def test_disciplinary_action_completion(self, school, student, infraction, teacher_user):
        """Test disciplinary action completion"""
        action = DisciplinaryAction.objects.create(
            school=school,
            student=student,
            infraction=infraction,
            action_type='community_service',
            description="Clean school grounds",
            assigned_to=teacher_user,
            due_date=date.today() + timedelta(days=3)
        )
        
        action.complete_action("Task completed successfully")
        action.refresh_from_db()
        
        assert action.status == 'completed'
        assert action.is_completed
        assert action.completion_date is not None
        assert action.completion_notes == "Task completed successfully"

    def test_disciplinary_action_overdue(self, school, student, infraction, teacher_user):
        """Test overdue disciplinary action detection"""
        action = DisciplinaryAction.objects.create(
            school=school,
            student=student,
            infraction=infraction,
            action_type='detention',
            description="After school detention",
            assigned_to=teacher_user,
            due_date=date.today() - timedelta(days=1)
        )
        
        assert action.is_overdue

    def test_disciplinary_action_validation(self, school, student, infraction, teacher_user):
        """Test disciplinary action validation"""
        # Test due date in the past
        with pytest.raises(ValidationError):
            action = DisciplinaryAction(
                school=school,
                student=student,
                infraction=infraction,
                action_type='counseling',
                description="Test action",
                assigned_to=teacher_user,
                due_date=date.today() - timedelta(days=1)
            )
            action.full_clean()


@pytest.mark.django_db
class TestDisciplineReport:
    """Test cases for DisciplineReport model"""

    def test_discipline_report_creation(self, school, teacher_user):
        """Test creating a discipline report"""
        report = DisciplineReport.objects.create(
            school=school,
            report_type='student_summary',
            title="Monthly Discipline Report",
            start_date=date(2024, 10, 1),
            end_date=date(2024, 10, 31),
            generated_by=teacher_user
        )
        
        assert report.report_type == 'student_summary'
        assert report.title == "Monthly Discipline Report"
        assert report.report_data == {}

    def test_discipline_report_generation(self, school, student, teacher_user):
        """Test discipline report data generation"""
        # Create some test infractions
        StudentInfraction.objects.create(
            school=school,
            student=student,
            incident_date=date(2024, 10, 15),
            description="Test infraction 1",
            severity='minor',
            action_taken='warning',
            reported_by=teacher_user
        )
        
        StudentInfraction.objects.create(
            school=school,
            student=student,
            incident_date=date(2024, 10, 20),
            description="Test infraction 2",
            severity='moderate',
            action_taken='detention',
            reported_by=teacher_user
        )
        
        report = DisciplineReport.objects.create(
            school=school,
            report_type='student_summary',
            title="Monthly Discipline Report",
            start_date=date(2024, 10, 1),
            end_date=date(2024, 10, 31),
            generated_by=teacher_user
        )
        
        report.generate_report_data()
        report.refresh_from_db()
        
        assert report.report_data is not None
        assert 'total_infractions' in report.report_data
        assert report.report_data['total_infractions'] == 2

    def test_discipline_report_analytics(self, school, student, teacher_user):
        """Test discipline report analytics"""
        # Create infractions with different severities
        for severity in ['minor', 'moderate', 'severe']:
            StudentInfraction.objects.create(
                school=school,
                student=student,
                incident_date=date(2024, 10, 15),
                description=f"Test {severity} infraction",
                severity=severity,
                action_taken='warning',
                reported_by=teacher_user
            )
        
        report = DisciplineReport.objects.create(
            school=school,
            report_type='infraction_trends',
            title="Discipline Analytics Report",
            start_date=date(2024, 10, 1),
            end_date=date(2024, 10, 31),
            generated_by=teacher_user
        )
        
        report.generate_report_data()
        report.refresh_from_db()
        
        assert 'severity_stats' in report.report_data
        assert report.report_data['severity_stats']['minor']['count'] == 1
        assert report.report_data['severity_stats']['moderate']['count'] == 1
        assert report.report_data['severity_stats']['severe']['count'] == 1


@pytest.mark.django_db
class TestDisciplineSystemIntegration:
    """Integration tests for the complete discipline system"""

    def test_complete_discipline_workflow(self, school, student, teacher_user):
        """Test the complete discipline workflow from infraction to resolution"""
        # Step 1: Create an infraction
        infraction = StudentInfraction.objects.create(
            school=school,
            student=student,
            incident_date=date.today(),
            description="Disruptive behavior in class",
            severity='moderate',
            action_taken='warning',
            reported_by=teacher_user
        )
        
        # Step 2: Create a disciplinary action
        action = DisciplinaryAction.objects.create(
            school=school,
            student=student,
            infraction=infraction,
            action_type='counseling',
            description="Meet with school counselor",
            assigned_to=teacher_user,
            due_date=date.today() + timedelta(days=7)
        )
        
        # Step 3: If severe, create a suspension (skip for moderate)
        suspension = None
        if infraction.severity == 'severe':
            suspension = StudentSuspension.objects.create(
                school=school,
                student=student,
                infraction=infraction,
                suspension_type='in_school',
                start_date=date.today(),
                end_date=date.today() + timedelta(days=3),
                reason="Severe behavioral issue",
                suspended_by=teacher_user
            )
        
        # Step 4: Complete the disciplinary action
        action.complete_action("Student attended counseling session")
        
        # Step 5: Generate a discipline report
        report = DisciplineReport.objects.create(
            school=school,
            report_type='student_summary',
            title="Student Discipline Workflow Report",
            start_date=date.today() - timedelta(days=30),
            end_date=date.today(),
            generated_by=teacher_user
        )
        report.generate_report_data()
        
        # Verify the workflow
        assert infraction.student == student
        assert action.status == 'completed'
        assert action.is_completed
        assert report.report_data is not None

    def test_discipline_escalation_workflow(self, school, student, teacher_user):
        """Test discipline escalation for repeat offenders"""
        # Create multiple infractions for the same student
        infractions = []
        for i in range(3):
            infraction = StudentInfraction.objects.create(
                school=school,
                student=student,
                incident_date=date.today() - timedelta(days=i*7),
                description=f"Infraction {i+1}",
                severity='moderate',
                action_taken='warning' if i < 2 else 'suspension',
                reported_by=teacher_user
            )
            infractions.append(infraction)
        
        # Create escalating disciplinary actions
        for i, infraction in enumerate(infractions):
            if i == 0:
                action_type = 'warning'
            elif i == 1:
                action_type = 'detention'
            else:
                action_type = 'counseling'
            
            DisciplinaryAction.objects.create(
                school=school,
                student=student,
                infraction=infraction,
                action_type=action_type,
                description=f"Escalated action {i+1}",
                assigned_to=teacher_user,
                due_date=date.today() + timedelta(days=7)
            )
        
        # For the third infraction, create a suspension
        suspension = StudentSuspension.objects.create(
            school=school,
            student=student,
            infraction=infractions[2],
            suspension_type='out_of_school',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=3),
            reason="Repeated violations - escalation required",
            suspended_by=teacher_user
        )
        
        # Verify escalation
        student_infractions = StudentInfraction.objects.filter(student=student)
        assert student_infractions.count() == 3
        
        student_actions = DisciplinaryAction.objects.filter(student=student)
        assert student_actions.count() == 3
        
        student_suspensions = StudentSuspension.objects.filter(student=student)
        assert student_suspensions.count() == 1
        assert suspension.suspension_type == 'out_of_school'