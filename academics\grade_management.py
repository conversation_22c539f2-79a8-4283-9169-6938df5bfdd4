"""
Grade Management System for Task 4.3: Build Grade Management System

This module provides:
- Grade entry and calculation system
- GPA calculation algorithms
- Transcript generation features
- Grade analytics and reporting
- Parent/student grade access
"""

from datetime import datetime, date
from typing import List, Dict, Tuple, Optional
from decimal import Decimal, ROUND_HALF_UP
from django.db.models import Q, Avg, Sum, Count, Max, Min
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from .models import (
    StudentGrade, GradingScale, StudentGPA, Transcript, GradeReport,
    ClassSubject, Exam, Student, Subject
)
from core.models import AcademicYear, Semester
from students.models import Class


class GradeCalculator:
    """
    Advanced grade calculation system with multiple algorithms
    """
    
    def __init__(self, school):
        self.school = school
        self.default_grading_scale = self._get_default_grading_scale()
    
    def _get_default_grading_scale(self):
        """Get school's default grading scale"""
        scale = GradingScale.objects.filter(
            school=self.school,
            is_default=True,
            is_active=True
        ).first()
        
        if scale:
            return scale.scale_definition
        
        # Fallback to standard 4.0 scale
        return {
            'A+': {'min': 97, 'max': 100, 'points': 4.0},
            'A': {'min': 93, 'max': 96, 'points': 4.0},
            'A-': {'min': 90, 'max': 92, 'points': 3.7},
            'B+': {'min': 87, 'max': 89, 'points': 3.3},
            'B': {'min': 83, 'max': 86, 'points': 3.0},
            'B-': {'min': 80, 'max': 82, 'points': 2.7},
            'C+': {'min': 77, 'max': 79, 'points': 2.3},
            'C': {'min': 73, 'max': 76, 'points': 2.0},
            'C-': {'min': 70, 'max': 72, 'points': 1.7},
            'D+': {'min': 67, 'max': 69, 'points': 1.3},
            'D': {'min': 65, 'max': 66, 'points': 1.0},
            'F': {'min': 0, 'max': 64, 'points': 0.0}
        }
    
    def calculate_percentage(self, marks_obtained: float, total_marks: float) -> float:
        """Calculate percentage from marks"""
        if total_marks <= 0:
            return 0.0
        
        percentage = (marks_obtained / total_marks) * 100
        return round(percentage, 2)
    
    def get_grade_letter(self, percentage: float, grading_scale: Dict = None) -> str:
        """Get grade letter based on percentage"""
        if grading_scale is None:
            grading_scale = self.default_grading_scale
        
        for grade, criteria in grading_scale.items():
            if criteria['min'] <= percentage <= criteria['max']:
                return grade
        
        return 'F'  # Default to F if no match
    
    def get_grade_points(self, grade_letter: str, grading_scale: Dict = None) -> float:
        """Get grade points for a letter grade"""
        if grading_scale is None:
            grading_scale = self.default_grading_scale
        
        if grade_letter in grading_scale:
            return grading_scale[grade_letter]['points']
        
        return 0.0
    
    def calculate_weighted_average(self, grades: List[Dict]) -> float:
        """
        Calculate weighted average of grades
        grades: List of dicts with 'percentage', 'weight', 'credit_hours'
        """
        total_weighted_score = 0
        total_weight = 0
        
        for grade in grades:
            weight = grade.get('weight', 1.0)
            credit_hours = grade.get('credit_hours', 1)
            percentage = grade.get('percentage', 0)
            
            # Use credit hours as additional weight factor
            effective_weight = weight * credit_hours
            total_weighted_score += percentage * effective_weight
            total_weight += effective_weight
        
        if total_weight == 0:
            return 0.0
        
        return round(total_weighted_score / total_weight, 2)
    
    def calculate_subject_grade(self, student: Student, class_subject: ClassSubject, 
                              academic_year: AcademicYear, semester: Semester = None) -> Dict:
        """Calculate overall grade for a student in a subject"""
        
        # Get all exams for this class subject
        exams_query = Exam.objects.filter(
            class_subject=class_subject,
            academic_year=academic_year
        )
        
        if semester:
            exams_query = exams_query.filter(semester=semester)
        
        exams = exams_query.all()
        
        # Get student grades for these exams
        student_grades = StudentGrade.objects.filter(
            student=student,
            exam__in=exams
        ).select_related('exam')
        
        if not student_grades.exists():
            return {
                'overall_percentage': 0.0,
                'grade_letter': 'F',
                'grade_points': 0.0,
                'total_exams': len(exams),
                'completed_exams': 0,
                'status': 'incomplete'
            }
        
        # Calculate weighted average based on exam weights
        grade_data = []
        for grade in student_grades:
            grade_data.append({
                'percentage': float(grade.percentage or 0),
                'weight': float(grade.exam.weightage),
                'credit_hours': class_subject.subject.credit_hours
            })
        
        overall_percentage = self.calculate_weighted_average(grade_data)
        grade_letter = self.get_grade_letter(overall_percentage)
        grade_points = self.get_grade_points(grade_letter)
        
        return {
            'overall_percentage': overall_percentage,
            'grade_letter': grade_letter,
            'grade_points': grade_points,
            'total_exams': len(exams),
            'completed_exams': len(student_grades),
            'status': 'complete' if len(student_grades) == len(exams) else 'incomplete',
            'individual_grades': list(student_grades.values(
                'exam__name', 'marks_obtained', 'percentage', 'grade_letter'
            ))
        }


class GPACalculator:
    """
    Comprehensive GPA calculation system
    """
    
    def __init__(self, school):
        self.school = school
        self.grade_calculator = GradeCalculator(school)
    
    def calculate_semester_gpa(self, student: Student, semester: Semester) -> Dict:
        """Calculate GPA for a specific semester"""
        
        # Get all class subjects for the student in this semester
        class_subjects = ClassSubject.objects.filter(
            class_obj=student.current_class,
            semester=semester,
            is_active=True
        ).select_related('subject')
        
        total_quality_points = Decimal('0.00')
        total_credit_hours = 0
        subject_grades = []
        
        for class_subject in class_subjects:
            subject_grade = self.grade_calculator.calculate_subject_grade(
                student, class_subject, semester.academic_year, semester
            )
            
            if subject_grade['status'] == 'complete':
                credit_hours = class_subject.subject.credit_hours
                grade_points = Decimal(str(subject_grade['grade_points']))
                quality_points = grade_points * credit_hours
                
                total_quality_points += quality_points
                total_credit_hours += credit_hours
                
                subject_grades.append({
                    'subject': class_subject.subject.name,
                    'credit_hours': credit_hours,
                    'grade_letter': subject_grade['grade_letter'],
                    'grade_points': float(grade_points),
                    'quality_points': float(quality_points)
                })
        
        # Calculate GPA
        if total_credit_hours > 0:
            gpa = total_quality_points / total_credit_hours
            gpa = gpa.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        else:
            gpa = Decimal('0.00')
        
        return {
            'gpa': float(gpa),
            'total_credit_hours': total_credit_hours,
            'earned_credit_hours': total_credit_hours,  # Assuming all completed
            'quality_points': float(total_quality_points),
            'subject_grades': subject_grades,
            'semester': semester.name,
            'academic_year': semester.academic_year.name
        }
    
    def calculate_cumulative_gpa(self, student: Student, up_to_semester: Semester = None) -> Dict:
        """Calculate cumulative GPA up to a specific semester"""
        
        # Get all semesters for the student
        semesters_query = Semester.objects.filter(
            school=self.school,
            academic_year__in=student.academic_years.all()
        ).order_by('academic_year__start_date', 'start_date')
        
        if up_to_semester:
            semesters_query = semesters_query.filter(
                Q(academic_year__start_date__lt=up_to_semester.academic_year.start_date) |
                Q(academic_year=up_to_semester.academic_year, start_date__lte=up_to_semester.start_date)
            )
        
        semesters = semesters_query.all()
        
        total_quality_points = Decimal('0.00')
        total_credit_hours = 0
        semester_gpas = []
        
        for semester in semesters:
            semester_gpa_data = self.calculate_semester_gpa(student, semester)
            
            if semester_gpa_data['total_credit_hours'] > 0:
                total_quality_points += Decimal(str(semester_gpa_data['quality_points']))
                total_credit_hours += semester_gpa_data['total_credit_hours']
                semester_gpas.append(semester_gpa_data)
        
        # Calculate cumulative GPA
        if total_credit_hours > 0:
            cumulative_gpa = total_quality_points / total_credit_hours
            cumulative_gpa = cumulative_gpa.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        else:
            cumulative_gpa = Decimal('0.00')
        
        return {
            'cumulative_gpa': float(cumulative_gpa),
            'total_credit_hours': total_credit_hours,
            'total_quality_points': float(total_quality_points),
            'semester_gpas': semester_gpas,
            'semesters_completed': len(semester_gpas)
        }
    
    def calculate_class_rank(self, student: Student, academic_year: AcademicYear) -> Dict:
        """Calculate student's rank within their class"""
        
        # Get all students in the same class
        classmates = Student.objects.filter(
            current_class=student.current_class,
            is_active=True
        )
        
        # Calculate GPA for each student
        student_gpas = []
        for classmate in classmates:
            gpa_data = self.calculate_cumulative_gpa(classmate)
            student_gpas.append({
                'student_id': classmate.id,
                'student_name': classmate.full_name,
                'gpa': gpa_data['cumulative_gpa']
            })
        
        # Sort by GPA (descending)
        student_gpas.sort(key=lambda x: x['gpa'], reverse=True)
        
        # Find student's rank
        student_rank = None
        for i, gpa_data in enumerate(student_gpas, 1):
            if gpa_data['student_id'] == student.id:
                student_rank = i
                break
        
        return {
            'rank': student_rank,
            'total_students': len(student_gpas),
            'percentile': ((len(student_gpas) - student_rank + 1) / len(student_gpas)) * 100 if student_rank else 0,
            'class_gpa_average': sum(s['gpa'] for s in student_gpas) / len(student_gpas) if student_gpas else 0
        }
    
    def save_gpa_record(self, student: Student, gpa_data: Dict, gpa_type: str, 
                       academic_year: AcademicYear, semester: Semester = None):
        """Save GPA record to database"""
        
        # Calculate class rank
        rank_data = self.calculate_class_rank(student, academic_year)
        
        gpa_record, created = StudentGPA.objects.update_or_create(
            student=student,
            academic_year=academic_year,
            semester=semester,
            gpa_type=gpa_type,
            defaults={
                'school': self.school,
                'gpa_value': Decimal(str(gpa_data.get('gpa', gpa_data.get('cumulative_gpa', 0)))),
                'total_credit_hours': gpa_data.get('total_credit_hours', 0),
                'earned_credit_hours': gpa_data.get('earned_credit_hours', gpa_data.get('total_credit_hours', 0)),
                'quality_points': Decimal(str(gpa_data.get('quality_points', gpa_data.get('total_quality_points', 0)))),
                'class_rank': rank_data['rank'],
                'total_students': rank_data['total_students']
            }
        )
        
        return gpa_record


class TranscriptGenerator:
    """
    Comprehensive transcript generation system
    """
    
    def __init__(self, school):
        self.school = school
        self.gpa_calculator = GPACalculator(school)
    
    def generate_transcript(self, student: Student, transcript_type: str = 'unofficial',
                          start_year: AcademicYear = None, end_year: AcademicYear = None,
                          generated_by = None) -> Transcript:
        """Generate comprehensive transcript for student"""
        
        # Determine date range
        if not start_year:
            start_year = student.academic_years.order_by('start_date').first()
        if not end_year:
            end_year = student.academic_years.order_by('-start_date').first()
        
        # Get all academic years in range
        academic_years = AcademicYear.objects.filter(
            school=self.school,
            start_date__gte=start_year.start_date,
            start_date__lte=end_year.start_date
        ).order_by('start_date')
        
        transcript_data = {
            'student_info': {
                'name': student.full_name,
                'student_id': student.student_id,
                'date_of_birth': student.date_of_birth.isoformat() if student.date_of_birth else None,
                'admission_date': student.admission_date.isoformat() if student.admission_date else None,
                'current_class': str(student.current_class) if student.current_class else None
            },
            'academic_records': [],
            'summary': {}
        }
        
        total_credit_hours = 0
        overall_gpa_data = self.gpa_calculator.calculate_cumulative_gpa(student)
        
        # Process each academic year
        for year in academic_years:
            year_data = {
                'academic_year': year.name,
                'semesters': []
            }
            
            # Get semesters for this year
            semesters = Semester.objects.filter(
                academic_year=year,
                school=self.school
            ).order_by('start_date')
            
            for semester in semesters:
                semester_gpa_data = self.gpa_calculator.calculate_semester_gpa(student, semester)
                
                semester_data = {
                    'semester_name': semester.name,
                    'gpa': semester_gpa_data['gpa'],
                    'credit_hours': semester_gpa_data['total_credit_hours'],
                    'subjects': semester_gpa_data['subject_grades']
                }
                
                year_data['semesters'].append(semester_data)
                total_credit_hours += semester_gpa_data['total_credit_hours']
            
            transcript_data['academic_records'].append(year_data)
        
        # Add summary information
        rank_data = self.gpa_calculator.calculate_class_rank(student, end_year)
        
        transcript_data['summary'] = {
            'cumulative_gpa': overall_gpa_data['cumulative_gpa'],
            'total_credit_hours': total_credit_hours,
            'class_rank': rank_data['rank'],
            'total_students': rank_data['total_students'],
            'academic_standing': self._get_academic_standing(overall_gpa_data['cumulative_gpa']),
            'graduation_eligible': self._check_graduation_eligibility(student, overall_gpa_data)
        }
        
        # Create transcript record
        transcript = Transcript.objects.create(
            school=self.school,
            student=student,
            transcript_type=transcript_type,
            academic_year_start=start_year,
            academic_year_end=end_year,
            generated_by=generated_by,
            transcript_data=transcript_data,
            cumulative_gpa=Decimal(str(overall_gpa_data['cumulative_gpa'])),
            total_credit_hours=total_credit_hours,
            class_rank=rank_data['rank']
        )
        
        return transcript
    
    def _get_academic_standing(self, gpa: float) -> str:
        """Determine academic standing based on GPA"""
        if gpa >= 3.75:
            return 'Summa Cum Laude'
        elif gpa >= 3.5:
            return 'Magna Cum Laude'
        elif gpa >= 3.25:
            return 'Cum Laude'
        elif gpa >= 3.0:
            return 'Good Standing'
        elif gpa >= 2.0:
            return 'Satisfactory'
        else:
            return 'Academic Probation'
    
    def _check_graduation_eligibility(self, student: Student, gpa_data: Dict) -> bool:
        """Check if student is eligible for graduation"""
        # Basic eligibility criteria
        min_gpa = 2.0
        min_credit_hours = 120  # This should be configurable per curriculum
        
        return (gpa_data['cumulative_gpa'] >= min_gpa and 
                gpa_data['total_credit_hours'] >= min_credit_hours)


class GradeReportGenerator:
    """
    Grade report generation for different periods and purposes
    """
    
    def __init__(self, school):
        self.school = school
        self.gpa_calculator = GPACalculator(school)
        self.grade_calculator = GradeCalculator(school)
    
    def generate_progress_report(self, student: Student, report_period_start: date,
                               report_period_end: date, generated_by = None) -> GradeReport:
        """Generate progress report for a specific period"""
        
        # Get current semester
        current_semester = Semester.objects.filter(
            school=self.school,
            start_date__lte=report_period_end,
            end_date__gte=report_period_start
        ).first()
        
        if not current_semester:
            raise ValidationError(_('No semester found for the specified period'))
        
        # Get student's class subjects
        class_subjects = ClassSubject.objects.filter(
            class_obj=student.current_class,
            semester=current_semester,
            is_active=True
        ).select_related('subject', 'teacher')
        
        report_data = {
            'student_info': {
                'name': student.full_name,
                'student_id': student.student_id,
                'class': str(student.current_class),
                'semester': current_semester.name
            },
            'subjects': [],
            'summary': {}
        }
        
        total_subjects = 0
        subjects_passed = 0
        total_percentage = 0
        
        for class_subject in class_subjects:
            subject_grade = self.grade_calculator.calculate_subject_grade(
                student, class_subject, current_semester.academic_year, current_semester
            )
            
            subject_data = {
                'subject_name': class_subject.subject.name,
                'teacher': str(class_subject.teacher),
                'credit_hours': class_subject.subject.credit_hours,
                'overall_percentage': subject_grade['overall_percentage'],
                'grade_letter': subject_grade['grade_letter'],
                'status': subject_grade['status'],
                'individual_assessments': subject_grade['individual_grades']
            }
            
            report_data['subjects'].append(subject_data)
            total_subjects += 1
            
            if subject_grade['grade_letter'] != 'F':
                subjects_passed += 1
            
            total_percentage += subject_grade['overall_percentage']
        
        # Calculate summary
        semester_gpa_data = self.gpa_calculator.calculate_semester_gpa(student, current_semester)
        
        report_data['summary'] = {
            'overall_gpa': semester_gpa_data['gpa'],
            'total_subjects': total_subjects,
            'subjects_passed': subjects_passed,
            'average_percentage': total_percentage / total_subjects if total_subjects > 0 else 0,
            'attendance_percentage': self._get_attendance_percentage(student, current_semester)
        }
        
        # Create report record
        report = GradeReport.objects.create(
            school=self.school,
            student=student,
            report_type='progress',
            academic_year=current_semester.academic_year,
            semester=current_semester,
            report_period_start=report_period_start,
            report_period_end=report_period_end,
            generated_by=generated_by,
            report_data=report_data,
            overall_gpa=Decimal(str(semester_gpa_data['gpa'])),
            total_subjects=total_subjects,
            subjects_passed=subjects_passed,
            attendance_percentage=report_data['summary']['attendance_percentage']
        )
        
        return report
    
    def _get_attendance_percentage(self, student: Student, semester: Semester) -> Decimal:
        """Calculate attendance percentage for the semester"""
        # This would integrate with the attendance system
        # For now, return a placeholder
        return Decimal('95.00')


class GradeAnalytics:
    """
    Grade analytics and reporting system
    """
    
    def __init__(self, school):
        self.school = school
    
    def get_class_performance_analytics(self, class_obj: Class, academic_year: AcademicYear) -> Dict:
        """Get performance analytics for a class"""
        
        students = Student.objects.filter(
            current_class=class_obj,
            is_active=True
        )
        
        if not students.exists():
            return {'error': 'No students found in class'}
        
        # Get GPA data for all students
        gpa_records = StudentGPA.objects.filter(
            student__in=students,
            academic_year=academic_year,
            gpa_type='cumulative'
        )
        
        if not gpa_records.exists():
            return {'error': 'No GPA records found'}
        
        gpa_values = [float(record.gpa_value) for record in gpa_records]
        
        analytics = {
            'class_info': {
                'class_name': str(class_obj),
                'total_students': len(students),
                'academic_year': academic_year.name
            },
            'gpa_statistics': {
                'average_gpa': sum(gpa_values) / len(gpa_values),
                'highest_gpa': max(gpa_values),
                'lowest_gpa': min(gpa_values),
                'median_gpa': sorted(gpa_values)[len(gpa_values) // 2]
            },
            'grade_distribution': self._calculate_grade_distribution(gpa_values),
            'performance_trends': self._analyze_performance_trends(students, academic_year),
            'subject_performance': self._analyze_subject_performance(class_obj, academic_year)
        }
        
        return analytics
    
    def _calculate_grade_distribution(self, gpa_values: List[float]) -> Dict:
        """Calculate grade distribution"""
        distribution = {
            'A (3.5-4.0)': 0,
            'B (3.0-3.49)': 0,
            'C (2.5-2.99)': 0,
            'D (2.0-2.49)': 0,
            'F (0.0-1.99)': 0
        }
        
        for gpa in gpa_values:
            if gpa >= 3.5:
                distribution['A (3.5-4.0)'] += 1
            elif gpa >= 3.0:
                distribution['B (3.0-3.49)'] += 1
            elif gpa >= 2.5:
                distribution['C (2.5-2.99)'] += 1
            elif gpa >= 2.0:
                distribution['D (2.0-2.49)'] += 1
            else:
                distribution['F (0.0-1.99)'] += 1
        
        return distribution
    
    def _analyze_performance_trends(self, students, academic_year: AcademicYear) -> Dict:
        """Analyze performance trends over time"""
        # This would analyze semester-by-semester performance
        # Placeholder implementation
        return {
            'improving_students': 0,
            'declining_students': 0,
            'stable_students': len(students)
        }
    
    def _analyze_subject_performance(self, class_obj: Class, academic_year: AcademicYear) -> Dict:
        """Analyze performance by subject"""
        
        class_subjects = ClassSubject.objects.filter(
            class_obj=class_obj,
            academic_year=academic_year,
            is_active=True
        ).select_related('subject')
        
        subject_performance = {}
        
        for class_subject in class_subjects:
            # Get all grades for this subject
            grades = StudentGrade.objects.filter(
                exam__class_subject=class_subject
            ).values_list('percentage', flat=True)
            
            if grades:
                subject_performance[class_subject.subject.name] = {
                    'average_percentage': sum(grades) / len(grades),
                    'highest_percentage': max(grades),
                    'lowest_percentage': min(grades),
                    'total_assessments': len(grades)
                }
        
        return subject_performance