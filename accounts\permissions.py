from django.contrib.auth.decorators import user_passes_test
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from functools import wraps


def user_type_required(user_types):
    """
    Decorator to check if user has required user type
    """
    if isinstance(user_types, str):
        user_types = [user_types]
    
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('accounts:login')
            
            if request.user.user_type not in user_types:
                messages.error(request, _('You do not have permission to access this page.'))
                return redirect('accounts:dashboard')
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def admin_required(view_func):
    """
    Decorator to require admin user type
    """
    return user_type_required('admin')(view_func)


def teacher_required(view_func):
    """
    Decorator to require teacher user type
    """
    return user_type_required('teacher')(view_func)


def student_required(view_func):
    """
    Decorator to require student user type
    """
    return user_type_required('student')(view_func)


def parent_required(view_func):
    """
    Decorator to require parent user type
    """
    return user_type_required('parent')(view_func)


def staff_required(view_func):
    """
    Decorator to require staff user type (admin, teacher, or staff)
    """
    return user_type_required(['admin', 'teacher', 'staff'])(view_func)


def accountant_required(view_func):
    """
    Decorator to require accountant user type
    """
    return user_type_required(['admin', 'accountant'])(view_func)


class UserTypeMixin:
    """
    Mixin to check user type in class-based views
    """
    required_user_types = []
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts:login')
        
        if self.required_user_types and request.user.user_type not in self.required_user_types:
            messages.error(request, _('You do not have permission to access this page.'))
            return redirect('accounts:dashboard')
        
        return super().dispatch(request, *args, **kwargs)


class AdminRequiredMixin(UserTypeMixin):
    """
    Mixin to require admin user type
    """
    required_user_types = ['admin']


class TeacherRequiredMixin(UserTypeMixin):
    """
    Mixin to require teacher user type
    """
    required_user_types = ['teacher']


class StudentRequiredMixin(UserTypeMixin):
    """
    Mixin to require student user type
    """
    required_user_types = ['student']


class ParentRequiredMixin(UserTypeMixin):
    """
    Mixin to require parent user type
    """
    required_user_types = ['parent']


class StaffRequiredMixin(UserTypeMixin):
    """
    Mixin to require staff user type (admin, teacher, or staff)
    """
    required_user_types = ['admin', 'teacher', 'staff']


class AccountantRequiredMixin(UserTypeMixin):
    """
    Mixin to require accountant user type
    """
    required_user_types = ['admin', 'accountant']


def has_permission(user, permission_name):
    """
    Check if user has specific permission
    """
    if user.is_superuser:
        return True
    
    return user.has_perm(permission_name)


def can_view_student_data(user, student=None):
    """
    Check if user can view student data
    """
    if user.user_type == 'admin':
        return True
    
    if user.user_type == 'teacher':
        # Teachers can view students in their classes
        if student and hasattr(user, 'teacher_profile'):
            teacher = user.teacher_profile
            return teacher.class_subjects.filter(
                class_obj=student.current_class,
                is_active=True
            ).exists()
    
    if user.user_type == 'parent':
        # Parents can view their own children
        if student and hasattr(user, 'parent_profile'):
            parent = user.parent_profile
            return parent.children.filter(id=student.id).exists()
    
    if user.user_type == 'student':
        # Students can view their own data
        if student and hasattr(user, 'student_profile'):
            return user.student_profile.id == student.id
    
    return False


def can_edit_student_data(user, student=None):
    """
    Check if user can edit student data
    """
    if user.user_type == 'admin':
        return True
    
    if user.user_type == 'parent':
        # Parents can edit their own children's data (limited fields)
        if student and hasattr(user, 'parent_profile'):
            parent = user.parent_profile
            return parent.children.filter(id=student.id).exists()
    
    return False


def can_view_financial_data(user, student=None):
    """
    Check if user can view financial data
    """
    if user.user_type in ['admin', 'accountant']:
        return True
    
    if user.user_type == 'parent':
        # Parents can view their children's financial data
        if student and hasattr(user, 'parent_profile'):
            parent = user.parent_profile
            return parent.children.filter(id=student.id).exists()
    
    return False


def can_manage_users(user):
    """
    Check if user can manage other users
    """
    return user.user_type == 'admin' or user.is_superuser


def can_access_reports(user):
    """
    Check if user can access reports
    """
    return user.user_type in ['admin', 'teacher', 'accountant']


def get_user_dashboard_url(user):
    """
    Get appropriate dashboard URL based on user type
    """
    dashboard_urls = {
        'admin': 'accounts:dashboard',
        'teacher': 'accounts:dashboard',
        'student': 'accounts:dashboard',
        'parent': 'accounts:dashboard',
        'staff': 'accounts:dashboard',
        'accountant': 'accounts:dashboard',
    }
    
    return dashboard_urls.get(user.user_type, 'accounts:dashboard')


def get_accessible_menu_items(user):
    """
    Get menu items accessible to the user based on their type
    """
    menu_items = []
    
    # Common items for all authenticated users
    menu_items.append({
        'name': _('Dashboard'),
        'url': 'accounts:dashboard',
        'icon': 'fas fa-tachometer-alt'
    })
    
    if user.user_type == 'admin':
        menu_items.extend([
            {'name': _('Students'), 'url': 'students:list', 'icon': 'fas fa-users'},
            {'name': _('Teachers'), 'url': 'academics:teacher_list', 'icon': 'fas fa-chalkboard-teacher'},
            {'name': _('Classes'), 'url': 'academics:class_list', 'icon': 'fas fa-school'},
            {'name': _('Finance'), 'url': 'finance:dashboard', 'icon': 'fas fa-money-bill'},
            {'name': _('Reports'), 'url': 'reports:dashboard', 'icon': 'fas fa-chart-bar'},
            {'name': _('Settings'), 'url': 'core:settings', 'icon': 'fas fa-cog'},
        ])
    
    elif user.user_type == 'teacher':
        menu_items.extend([
            {'name': _('My Classes'), 'url': 'academics:my_classes', 'icon': 'fas fa-school'},
            {'name': _('Students'), 'url': 'students:my_students', 'icon': 'fas fa-users'},
            {'name': _('Attendance'), 'url': 'academics:attendance', 'icon': 'fas fa-calendar-check'},
            {'name': _('Grades'), 'url': 'academics:grades', 'icon': 'fas fa-graduation-cap'},
        ])
    
    elif user.user_type == 'student':
        menu_items.extend([
            {'name': _('My Grades'), 'url': 'academics:my_grades', 'icon': 'fas fa-graduation-cap'},
            {'name': _('My Attendance'), 'url': 'academics:my_attendance', 'icon': 'fas fa-calendar-check'},
            {'name': _('My Fees'), 'url': 'finance:my_fees', 'icon': 'fas fa-money-bill'},
        ])
    
    elif user.user_type == 'parent':
        menu_items.extend([
            {'name': _('My Children'), 'url': 'students:my_children', 'icon': 'fas fa-child'},
            {'name': _('Fees & Payments'), 'url': 'finance:family_fees', 'icon': 'fas fa-money-bill'},
            {'name': _('Reports'), 'url': 'reports:family_reports', 'icon': 'fas fa-chart-bar'},
        ])
    
    elif user.user_type == 'accountant':
        menu_items.extend([
            {'name': _('Finance'), 'url': 'finance:dashboard', 'icon': 'fas fa-money-bill'},
            {'name': _('Payments'), 'url': 'finance:payments', 'icon': 'fas fa-credit-card'},
            {'name': _('Reports'), 'url': 'reports:financial_reports', 'icon': 'fas fa-chart-bar'},
        ])
    
    # Profile and logout for all users
    menu_items.extend([
        {'name': _('Profile'), 'url': 'accounts:profile', 'icon': 'fas fa-user'},
        {'name': _('Logout'), 'url': 'accounts:logout', 'icon': 'fas fa-sign-out-alt'},
    ])
    
    return menu_items
