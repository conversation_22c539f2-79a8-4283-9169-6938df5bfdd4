{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Payroll Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .payroll-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .payroll-card:hover {
        transform: translateY(-2px);
    }
    .payroll-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .salary-amount {
        font-size: 1.25rem;
        font-weight: bold;
        color: #28a745;
    }
    .deduction-amount {
        font-size: 1.1rem;
        font-weight: 600;
        color: #dc3545;
    }
    .net-salary {
        font-size: 1.5rem;
        font-weight: bold;
        color: #007bff;
    }
    .payroll-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .status-processed {
        background-color: #d4edda;
        color: #155724;
    }
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }
    .status-draft {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    .employee-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
    }
    .payroll-summary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
    }
    .summary-item {
        text-align: center;
        padding: 1rem;
    }
    .summary-amount {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .payslip-btn {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        color: white;
        border-radius: 20px;
        padding: 0.5rem 1rem;
        font-weight: 500;
    }
    .payslip-btn:hover {
        background: linear-gradient(135deg, #0f8a7e 0%, #32d96b 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-money-bill-wave text-primary me-2"></i>{% trans "Payroll Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage employee salaries, deductions, and payroll processing" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#generatePayrollModal">
                        <i class="fas fa-plus me-2"></i>{% trans "Generate Payroll" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export Payroll" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Payroll Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="payroll-summary">
                <h5 class="mb-4 text-center">
                    <i class="fas fa-chart-bar me-2"></i>{% trans "Payroll Summary - Current Month" %}
                </h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-amount">${{ total_gross|floatformat:0|default:"0" }}</div>
                            <p class="mb-0">{% trans "Total Gross Salary" %}</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-amount">${{ total_deductions|floatformat:0|default:"0" }}</div>
                            <p class="mb-0">{% trans "Total Deductions" %}</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-amount">${{ total_net|floatformat:0|default:"0" }}</div>
                            <p class="mb-0">{% trans "Total Net Salary" %}</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-amount">{{ total_employees|default:0 }}</div>
                            <p class="mb-0">{% trans "Employees Processed" %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <h5 class="mb-3">{% trans "Filter Payroll Records" %}</h5>
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="period" class="form-label">{% trans "Payroll Period" %}</label>
                <select class="form-select" id="period" name="period">
                    <option value="">{% trans "Select Period" %}</option>
                    <option value="2024-07">{% trans "July 2024" %}</option>
                    <option value="2024-06">{% trans "June 2024" %}</option>
                    <option value="2024-05">{% trans "May 2024" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">{% trans "Department" %}</label>
                <select class="form-select" id="department" name="department">
                    <option value="">{% trans "All Departments" %}</option>
                    <option value="teaching">{% trans "Teaching" %}</option>
                    <option value="administration">{% trans "Administration" %}</option>
                    <option value="support">{% trans "Support Staff" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">{% trans "Status" %}</label>
                <select class="form-select" id="status" name="status">
                    <option value="">{% trans "All Status" %}</option>
                    <option value="processed">{% trans "Processed" %}</option>
                    <option value="pending">{% trans "Pending" %}</option>
                    <option value="draft">{% trans "Draft" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Payroll Records -->
    <div class="row">
        <div class="col-12">
            <div class="card payroll-card">
                <div class="payroll-header card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "Payroll Records" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Employee" %}</th>
                                    <th>{% trans "Period" %}</th>
                                    <th>{% trans "Basic Salary" %}</th>
                                    <th>{% trans "Allowances" %}</th>
                                    <th>{% trans "Deductions" %}</th>
                                    <th>{% trans "Net Salary" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                            <div>
                                                <h6 class="mb-1">أحمد محمد علي</h6>
                                                <small class="text-muted">EMP001 - {% trans "Senior Teacher" %}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{% trans "July 2024" %}</td>
                                    <td class="salary-amount">$2,500</td>
                                    <td class="text-success">$300</td>
                                    <td class="deduction-amount">$450</td>
                                    <td class="net-salary">$2,350</td>
                                    <td><span class="payroll-status status-processed">{% trans "Processed" %}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn payslip-btn" title="{% trans 'View Payslip' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-primary" title="{% trans 'Download' %}">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Send Email' %}">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                            <div>
                                                <h6 class="mb-1">فاطمة أحمد حسن</h6>
                                                <small class="text-muted">EMP002 - {% trans "Math Teacher" %}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{% trans "July 2024" %}</td>
                                    <td class="salary-amount">$2,200</td>
                                    <td class="text-success">$250</td>
                                    <td class="deduction-amount">$380</td>
                                    <td class="net-salary">$2,070</td>
                                    <td><span class="payroll-status status-processed">{% trans "Processed" %}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn payslip-btn" title="{% trans 'View Payslip' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-primary" title="{% trans 'Download' %}">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Send Email' %}">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                            <div>
                                                <h6 class="mb-1">محمد عبدالله سالم</h6>
                                                <small class="text-muted">EMP003 - {% trans "Science Teacher" %}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{% trans "July 2024" %}</td>
                                    <td class="salary-amount">$2,300</td>
                                    <td class="text-success">$200</td>
                                    <td class="deduction-amount">$400</td>
                                    <td class="net-salary">$2,100</td>
                                    <td><span class="payroll-status status-pending">{% trans "Pending" %}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-warning" title="{% trans 'Process' %}">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button class="btn btn-outline-primary" title="{% trans 'Edit' %}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="{% trans 'Delete' %}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                            <div>
                                                <h6 class="mb-1">سارة محمد أحمد</h6>
                                                <small class="text-muted">EMP004 - {% trans "English Teacher" %}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{% trans "July 2024" %}</td>
                                    <td class="salary-amount">$2,100</td>
                                    <td class="text-success">$180</td>
                                    <td class="deduction-amount">$350</td>
                                    <td class="net-salary">$1,930</td>
                                    <td><span class="payroll-status status-draft">{% trans "Draft" %}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" title="{% trans 'Review' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-primary" title="{% trans 'Edit' %}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Approve' %}">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Payroll pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <span class="page-link">{% trans "Previous" %}</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">{% trans "Next" %}</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Generate Payroll Modal -->
<div class="modal fade" id="generatePayrollModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Generate Payroll" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="payrollPeriod" class="form-label">{% trans "Payroll Period" %}</label>
                            <select class="form-select" id="payrollPeriod" required>
                                <option value="">{% trans "Select Period" %}</option>
                                <option value="2024-07">{% trans "July 2024" %}</option>
                                <option value="2024-08">{% trans "August 2024" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="payrollDepartment" class="form-label">{% trans "Department" %}</label>
                            <select class="form-select" id="payrollDepartment">
                                <option value="">{% trans "All Departments" %}</option>
                                <option value="teaching">{% trans "Teaching" %}</option>
                                <option value="administration">{% trans "Administration" %}</option>
                                <option value="support">{% trans "Support Staff" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="payDate" class="form-label">{% trans "Pay Date" %}</label>
                            <input type="date" class="form-control" id="payDate" required>
                        </div>
                        <div class="col-md-6">
                            <label for="payrollType" class="form-label">{% trans "Payroll Type" %}</label>
                            <select class="form-select" id="payrollType" required>
                                <option value="regular">{% trans "Regular Payroll" %}</option>
                                <option value="bonus">{% trans "Bonus Payroll" %}</option>
                                <option value="adjustment">{% trans "Adjustment" %}</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeAllowances" checked>
                                <label class="form-check-label" for="includeAllowances">
                                    {% trans "Include allowances and bonuses" %}
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeDeductions" checked>
                                <label class="form-check-label" for="includeDeductions">
                                    {% trans "Include deductions and taxes" %}
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <label for="notes" class="form-label">{% trans "Notes" %}</label>
                            <textarea class="form-control" id="notes" rows="3" placeholder="{% trans 'Additional notes for this payroll...' %}"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Generate Payroll" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Payslip Modal -->
<div class="modal fade" id="payslipModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Employee Payslip" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="payslip-content">
                    <!-- Payslip content would be loaded here -->
                    <div class="text-center">
                        <h4>{% trans "School Management System" %}</h4>
                        <h5>{% trans "Employee Payslip" %}</h5>
                        <hr>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <strong>{% trans "Employee Name:" %}</strong> أحمد محمد علي<br>
                            <strong>{% trans "Employee ID:" %}</strong> EMP001<br>
                            <strong>{% trans "Position:" %}</strong> {% trans "Senior Teacher" %}<br>
                            <strong>{% trans "Department:" %}</strong> {% trans "Teaching" %}
                        </div>
                        <div class="col-md-6 text-end">
                            <strong>{% trans "Pay Period:" %}</strong> {% trans "July 2024" %}<br>
                            <strong>{% trans "Pay Date:" %}</strong> July 31, 2024<br>
                            <strong>{% trans "Days Worked:" %}</strong> 22<br>
                            <strong>{% trans "Total Days:" %}</strong> 22
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6>{% trans "Earnings" %}</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td>{% trans "Basic Salary" %}</td>
                                    <td class="text-end">$2,500.00</td>
                                </tr>
                                <tr>
                                    <td>{% trans "Teaching Allowance" %}</td>
                                    <td class="text-end">$200.00</td>
                                </tr>
                                <tr>
                                    <td>{% trans "Transport Allowance" %}</td>
                                    <td class="text-end">$100.00</td>
                                </tr>
                                <tr class="table-success">
                                    <td><strong>{% trans "Total Earnings" %}</strong></td>
                                    <td class="text-end"><strong>$2,800.00</strong></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>{% trans "Deductions" %}</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td>{% trans "Income Tax" %}</td>
                                    <td class="text-end">$280.00</td>
                                </tr>
                                <tr>
                                    <td>{% trans "Social Security" %}</td>
                                    <td class="text-end">$140.00</td>
                                </tr>
                                <tr>
                                    <td>{% trans "Health Insurance" %}</td>
                                    <td class="text-end">$30.00</td>
                                </tr>
                                <tr class="table-danger">
                                    <td><strong>{% trans "Total Deductions" %}</strong></td>
                                    <td class="text-end"><strong>$450.00</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-primary text-center">
                                <h5>{% trans "Net Salary: $2,350.00" %}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-download me-2"></i>{% trans "Download PDF" %}
                </button>
                <button type="button" class="btn btn-success">
                    <i class="fas fa-envelope me-2"></i>{% trans "Send Email" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit filters
    const filterSelects = document.querySelectorAll('select[name="period"], select[name="department"], select[name="status"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // View payslip
    document.querySelectorAll('.payslip-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const payslipModal = new bootstrap.Modal(document.getElementById('payslipModal'));
            payslipModal.show();
        });
    });

    // Generate payroll
    document.querySelector('#generatePayrollModal .btn-primary').addEventListener('click', function() {
        const period = document.getElementById('payrollPeriod').value;
        const department = document.getElementById('payrollDepartment').value;
        const payDate = document.getElementById('payDate').value;
        
        if (!period || !payDate) {
            alert('{% trans "Please fill in all required fields." %}');
            return;
        }
        
        // Here you would make an AJAX call to generate payroll
        alert('{% trans "Payroll generation started. You will be notified when complete." %}');
        bootstrap.Modal.getInstance(document.getElementById('generatePayrollModal')).hide();
    });
});
</script>
{% endblock %}
