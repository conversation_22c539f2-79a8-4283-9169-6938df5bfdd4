# Generated by Django 5.2.4 on 2025-07-30 17:24

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0005_alter_curriculumsubject_options_and_more"),
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="TeacherAvailability",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "day_of_week",
                    models.CharField(
                        choices=[
                            ("monday", "Monday"),
                            ("tuesday", "Tuesday"),
                            ("wednesday", "Wednesday"),
                            ("thursday", "Thursday"),
                            ("friday", "Friday"),
                            ("saturday", "Saturday"),
                            ("sunday", "Sunday"),
                        ],
                        max_length=10,
                        verbose_name="Day of Week",
                    ),
                ),
                ("start_time", models.TimeField(verbose_name="Available From")),
                ("end_time", models.TimeField(verbose_name="Available Until")),
                (
                    "is_preferred",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this is a preferred time slot for the teacher",
                        verbose_name="Preferred Time",
                    ),
                ),
                (
                    "max_consecutive_hours",
                    models.PositiveIntegerField(
                        default=4,
                        help_text="Maximum consecutive hours teacher can teach",
                        verbose_name="Max Consecutive Hours",
                    ),
                ),
                (
                    "break_duration",
                    models.PositiveIntegerField(
                        default=15,
                        help_text="Minimum break time required between classes",
                        verbose_name="Required Break (minutes)",
                    ),
                ),
            ],
            options={
                "verbose_name": "Teacher Availability",
                "verbose_name_plural": "Teacher Availabilities",
                "ordering": ["teacher", "day_of_week", "start_time"],
            },
        ),
        migrations.CreateModel(
            name="TimetableTemplate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Template Name"),
                ),
                (
                    "periods_per_day",
                    models.PositiveIntegerField(
                        default=8, verbose_name="Periods per Day"
                    ),
                ),
                (
                    "period_duration",
                    models.PositiveIntegerField(
                        default=45, verbose_name="Period Duration (minutes)"
                    ),
                ),
                (
                    "break_duration",
                    models.PositiveIntegerField(
                        default=15, verbose_name="Break Duration (minutes)"
                    ),
                ),
                (
                    "lunch_break_duration",
                    models.PositiveIntegerField(
                        default=45, verbose_name="Lunch Break Duration (minutes)"
                    ),
                ),
                (
                    "school_start_time",
                    models.TimeField(verbose_name="School Start Time"),
                ),
                ("school_end_time", models.TimeField(verbose_name="School End Time")),
                (
                    "working_days",
                    models.JSONField(
                        default=list,
                        help_text='List of working days (e.g., ["monday", "tuesday", ...])',
                        verbose_name="Working Days",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
            ],
            options={
                "verbose_name": "Timetable Template",
                "verbose_name_plural": "Timetable Templates",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="schedule",
            name="effective_date",
            field=models.DateField(
                blank=True,
                help_text="Date from which this schedule is effective",
                null=True,
                verbose_name="Effective Date",
            ),
        ),
        migrations.AddField(
            model_name="schedule",
            name="end_date",
            field=models.DateField(
                blank=True,
                help_text="Date until which this schedule is effective",
                null=True,
                verbose_name="End Date",
            ),
        ),
        migrations.AddField(
            model_name="schedule",
            name="is_recurring",
            field=models.BooleanField(
                default=True,
                help_text="Whether this schedule repeats weekly",
                verbose_name="Is Recurring",
            ),
        ),
        migrations.AddField(
            model_name="schedule",
            name="notes",
            field=models.TextField(blank=True, null=True, verbose_name="Notes"),
        ),
        migrations.AddField(
            model_name="schedule",
            name="period_number",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Period number in the daily schedule",
                null=True,
                verbose_name="Period Number",
            ),
        ),
        migrations.AddField(
            model_name="schedule",
            name="status",
            field=models.CharField(
                choices=[
                    ("active", "Active"),
                    ("cancelled", "Cancelled"),
                    ("rescheduled", "Rescheduled"),
                    ("completed", "Completed"),
                ],
                default="active",
                max_length=15,
                verbose_name="Status",
            ),
        ),
        migrations.AlterField(
            model_name="schedule",
            name="room_number",
            field=models.CharField(
                blank=True,
                help_text="Alternative room identifier if Room model not used",
                max_length=20,
                null=True,
                verbose_name="Room Number",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="schedule",
            unique_together={("class_subject", "day_of_week", "start_time")},
        ),
        migrations.CreateModel(
            name="Room",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("name", models.CharField(max_length=50, verbose_name="Room Name")),
                (
                    "room_number",
                    models.CharField(max_length=20, verbose_name="Room Number"),
                ),
                (
                    "room_type",
                    models.CharField(
                        choices=[
                            ("classroom", "Regular Classroom"),
                            ("laboratory", "Laboratory"),
                            ("computer_lab", "Computer Laboratory"),
                            ("library", "Library"),
                            ("auditorium", "Auditorium"),
                            ("gymnasium", "Gymnasium"),
                            ("art_room", "Art Room"),
                            ("music_room", "Music Room"),
                            ("conference", "Conference Room"),
                        ],
                        default="classroom",
                        max_length=20,
                        verbose_name="Room Type",
                    ),
                ),
                (
                    "capacity",
                    models.PositiveIntegerField(
                        help_text="Maximum number of students this room can accommodate",
                        verbose_name="Capacity",
                    ),
                ),
                (
                    "floor",
                    models.CharField(
                        blank=True, max_length=10, null=True, verbose_name="Floor"
                    ),
                ),
                (
                    "building",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="Building"
                    ),
                ),
                (
                    "equipment",
                    models.TextField(
                        blank=True,
                        help_text="List of equipment available in this room",
                        null=True,
                        verbose_name="Available Equipment",
                    ),
                ),
                (
                    "has_projector",
                    models.BooleanField(default=False, verbose_name="Has Projector"),
                ),
                (
                    "has_computer",
                    models.BooleanField(default=False, verbose_name="Has Computer"),
                ),
                (
                    "has_internet",
                    models.BooleanField(default=True, verbose_name="Has Internet"),
                ),
                (
                    "has_whiteboard",
                    models.BooleanField(default=True, verbose_name="Has Whiteboard"),
                ),
                (
                    "is_accessible",
                    models.BooleanField(
                        default=True, verbose_name="Wheelchair Accessible"
                    ),
                ),
                (
                    "is_available",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this room is available for scheduling",
                        verbose_name="Is Available",
                    ),
                ),
                (
                    "maintenance_notes",
                    models.TextField(
                        blank=True, null=True, verbose_name="Maintenance Notes"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Room",
                "verbose_name_plural": "Rooms",
                "ordering": ["building", "floor", "room_number"],
            },
        ),
        migrations.AddField(
            model_name="schedule",
            name="room",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="schedules",
                to="academics.room",
                verbose_name="Room",
            ),
        ),
        migrations.AddIndex(
            model_name="schedule",
            index=models.Index(
                fields=["school", "day_of_week", "start_time"],
                name="academics_s_school__8e9fb6_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="schedule",
            index=models.Index(
                fields=["class_subject", "status"],
                name="academics_s_class_s_c84507_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="schedule",
            index=models.Index(
                fields=["room", "day_of_week"], name="academics_s_room_id_62b00b_idx"
            ),
        ),
        migrations.AddField(
            model_name="teacheravailability",
            name="academic_year",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="teacher_availabilities",
                to="core.academicyear",
                verbose_name="Academic Year",
            ),
        ),
        migrations.AddField(
            model_name="teacheravailability",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="teacheravailability",
            name="school",
            field=models.ForeignKey(
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
        ),
        migrations.AddField(
            model_name="teacheravailability",
            name="teacher",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="availability_slots",
                to="academics.teacher",
                verbose_name="Teacher",
            ),
        ),
        migrations.AddField(
            model_name="teacheravailability",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="timetabletemplate",
            name="academic_year",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="timetable_templates",
                to="core.academicyear",
                verbose_name="Academic Year",
            ),
        ),
        migrations.AddField(
            model_name="timetabletemplate",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="timetabletemplate",
            name="school",
            field=models.ForeignKey(
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
        ),
        migrations.AddField(
            model_name="timetabletemplate",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddIndex(
            model_name="room",
            index=models.Index(
                fields=["school", "room_type"], name="academics_r_school__8760f0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="room",
            index=models.Index(
                fields=["school", "is_available"], name="academics_r_school__25ba0d_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="room",
            unique_together={("school", "room_number")},
        ),
        migrations.AddIndex(
            model_name="teacheravailability",
            index=models.Index(
                fields=["school", "academic_year"],
                name="academics_t_school__875efc_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="teacheravailability",
            index=models.Index(
                fields=["teacher", "day_of_week"], name="academics_t_teacher_e89221_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="teacheravailability",
            unique_together={("teacher", "day_of_week", "start_time", "academic_year")},
        ),
        migrations.AlterUniqueTogether(
            name="timetabletemplate",
            unique_together={("school", "name", "academic_year")},
        ),
    ]
