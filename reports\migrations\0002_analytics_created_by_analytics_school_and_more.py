# Generated by Django 5.2.4 on 2025-07-30 12:17

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("reports", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="analytics",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="analytics",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="analytics",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="dashboard",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="dashboard",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="dashboard",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="reportexecution",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="reportexecution",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="reportexecution",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="reporttemplate",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="reporttemplate",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AlterField(
            model_name="analytics",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="dashboard",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="reportexecution",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="reporttemplate",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
    ]
