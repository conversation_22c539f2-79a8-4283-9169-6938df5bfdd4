{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير المصروفات{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">تقرير المصروفات</h1>
                    <p class="text-muted">تفاصيل جميع المصروفات والنفقات</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2">
                        <i class="fas fa-download me-2"></i>تصدير
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Expenses Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-credit-card text-danger mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-danger">${{ total_expenses|default:85000 }}</h4>
                    <p class="mb-0">إجمالي المصروفات</p>
                    <small class="text-muted">هذا الشهر</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-users text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">${{ salaries|default:55000 }}</h4>
                    <p class="mb-0">الرواتب</p>
                    <small class="text-muted">65% من المصروفات</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-bolt text-warning mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-warning">${{ utilities|default:15000 }}</h4>
                    <p class="mb-0">المرافق</p>
                    <small class="text-muted">18% من المصروفات</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-tools text-info mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-info">${{ maintenance|default:15000 }}</h4>
                    <p class="mb-0">الصيانة</p>
                    <small class="text-muted">17% من المصروفات</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Expenses Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">تفاصيل المصروفات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الفئة</th>
                                    <th>الوصف</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2025-01-15</td>
                                    <td>الرواتب</td>
                                    <td>رواتب المعلمين</td>
                                    <td class="text-danger">-$35,000</td>
                                    <td><span class="badge bg-success">مدفوع</span></td>
                                </tr>
                                <tr>
                                    <td>2025-01-14</td>
                                    <td>المرافق</td>
                                    <td>فاتورة الكهرباء</td>
                                    <td class="text-danger">-$2,500</td>
                                    <td><span class="badge bg-success">مدفوع</span></td>
                                </tr>
                                <tr>
                                    <td>2025-01-13</td>
                                    <td>الصيانة</td>
                                    <td>صيانة أجهزة الكمبيوتر</td>
                                    <td class="text-danger">-$1,200</td>
                                    <td><span class="badge bg-warning">معلق</span></td>
                                </tr>
                                <tr>
                                    <td>2025-01-12</td>
                                    <td>المواد</td>
                                    <td>مواد تعليمية</td>
                                    <td class="text-danger">-$800</td>
                                    <td><span class="badge bg-success">مدفوع</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">توزيع المصروفات</h5>
                </div>
                <div class="card-body">
                    <canvas id="expensesChart" style="height: 300px;"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Expenses Distribution Chart
    const ctx = document.getElementById('expensesChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['الرواتب', 'المرافق', 'الصيانة', 'أخرى'],
            datasets: [{
                data: [55000, 15000, 15000, 5000],
                backgroundColor: [
                    '#dc3545',
                    '#ffc107',
                    '#17a2b8',
                    '#6c757d'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
</script>
{% endblock %}
