from django.contrib import admin
from .models import (
    Subject, Teacher, ClassSubject, Schedule, Exam, StudentGrade,
    StudentAttendance, AttendanceSession, BiometricDevice, AttendanceRule,
    Curriculum, CurriculumSubject, GradeCapacityManagement, CurriculumPlan
)

# Note: AcademicYear is registered in core/admin.py since it's defined in core/models.py


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    """
    Subject admin
    """
    list_display = ('name', 'code', 'credit_hours', 'is_active')
    list_filter = ('credit_hours', 'is_active')
    search_fields = ('name', 'name_ar', 'code')
    ordering = ('name',)


@admin.register(Teacher)
class TeacherAdmin(admin.ModelAdmin):
    """
    Teacher admin
    """
    list_display = ('user', 'employee_id', 'department', 'hire_date', 'experience_years', 'is_active')
    list_filter = ('department', 'hire_date', 'is_active')
    search_fields = ('user__first_name', 'user__last_name', 'employee_id', 'qualification')
    raw_id_fields = ('user',)
    filter_horizontal = ('subjects',)
    date_hierarchy = 'hire_date'


@admin.register(ClassSubject)
class ClassSubjectAdmin(admin.ModelAdmin):
    """
    Class Subject admin
    """
    list_display = ('class_obj', 'subject', 'teacher', 'academic_year', 'weekly_hours', 'is_active')
    list_filter = ('academic_year', 'subject', 'is_active')
    search_fields = ('class_obj__name', 'subject__name', 'teacher__user__first_name', 'teacher__user__last_name')
    raw_id_fields = ('class_obj', 'subject', 'teacher', 'academic_year')


@admin.register(Schedule)
class ScheduleAdmin(admin.ModelAdmin):
    """
    Schedule admin
    """
    list_display = ('class_subject', 'day_of_week', 'start_time', 'end_time', 'room_number', 'is_active')
    list_filter = ('day_of_week', 'class_subject__academic_year', 'is_active')
    search_fields = ('class_subject__class_name__name', 'class_subject__subject__name', 'room_number')
    raw_id_fields = ('class_subject',)


@admin.register(Exam)
class ExamAdmin(admin.ModelAdmin):
    """
    Exam admin
    """
    list_display = ('name', 'class_subject', 'exam_type', 'exam_date', 'total_marks', 'is_published', 'is_completed')
    list_filter = ('exam_type', 'exam_date', 'is_published', 'class_subject__academic_year')
    search_fields = ('name', 'name_ar', 'class_subject__class_name__name', 'class_subject__subject__name')
    raw_id_fields = ('class_subject', 'created_by')
    date_hierarchy = 'exam_date'
    readonly_fields = ('is_completed',)

    def is_completed(self, obj):
        return obj.is_completed
    is_completed.boolean = True
    is_completed.short_description = 'Is Completed'


@admin.register(StudentGrade)
class StudentGradeAdmin(admin.ModelAdmin):
    """
    Student Grade admin
    """
    list_display = ('student', 'exam', 'marks_obtained', 'total_marks', 'percentage', 'grade_letter', 'graded_by')
    list_filter = ('grade_letter', 'exam__exam_type', 'exam__exam_date', 'graded_at')
    search_fields = ('student__first_name', 'student__last_name', 'exam__name')
    raw_id_fields = ('student', 'exam', 'graded_by')
    readonly_fields = ('percentage', 'grade_letter', 'graded_at')

    def total_marks(self, obj):
        return obj.exam.total_marks
    total_marks.short_description = 'Total Marks'


@admin.register(AttendanceSession)
class AttendanceSessionAdmin(admin.ModelAdmin):
    """
    Attendance Session admin
    """
    list_display = ('class_subject', 'session_date', 'start_time', 'end_time', 'attendance_method', 'status', 'total_students', 'present_count')
    list_filter = ('status', 'attendance_method', 'session_date', 'class_subject__subject', 'class_subject__academic_year')
    search_fields = ('class_subject__class_obj__name', 'class_subject__subject__name', 'session_topic')
    raw_id_fields = ('class_subject',)
    date_hierarchy = 'session_date'
    readonly_fields = ('qr_code_token', 'total_students', 'present_count', 'absent_count', 'late_count', 'excused_count')

    def get_readonly_fields(self, request, obj=None):
        if obj and obj.status == 'completed':
            return self.readonly_fields + ('status',)
        return self.readonly_fields


@admin.register(StudentAttendance)
class StudentAttendanceAdmin(admin.ModelAdmin):
    """
    Student Attendance admin
    """
    list_display = ('student', 'session', 'status', 'marking_method', 'marked_at', 'marked_by')
    list_filter = ('status', 'marking_method', 'session__session_date', 'class_subject__subject', 'parent_notified')
    search_fields = ('student__first_name', 'student__last_name', 'session__class_subject__subject__name')
    raw_id_fields = ('session', 'student', 'class_subject', 'marked_by')
    date_hierarchy = 'session__session_date'
    readonly_fields = ('marked_at', 'notification_sent_at')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'student', 'session', 'class_subject', 'marked_by'
        )


@admin.register(BiometricDevice)
class BiometricDeviceAdmin(admin.ModelAdmin):
    """
    Biometric Device admin
    """
    list_display = ('name', 'device_type', 'device_id', 'location', 'status', 'last_sync')
    list_filter = ('device_type', 'status', 'last_sync')
    search_fields = ('name', 'device_id', 'location')
    readonly_fields = ('last_sync',)

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return self.readonly_fields + ('device_id',)
        return self.readonly_fields


@admin.register(AttendanceRule)
class AttendanceRuleAdmin(admin.ModelAdmin):
    """
    Attendance Rule admin
    """
    list_display = ('name', 'rule_type', 'applicable_to', 'rule_value', 'is_active', 'effective_from', 'effective_to')
    list_filter = ('rule_type', 'applicable_to', 'is_active', 'effective_from')
    search_fields = ('name', 'description')
    filter_horizontal = ('target_grades', 'target_classes', 'target_subjects')
    date_hierarchy = 'effective_from'

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if obj:
            # Show relevant target fields based on applicable_to
            if obj.applicable_to == 'grade':
                form.base_fields['target_classes'].widget = admin.widgets.FilteredSelectMultiple('Classes', False)
                form.base_fields['target_subjects'].widget = admin.widgets.FilteredSelectMultiple('Subjects', False)
            elif obj.applicable_to == 'class':
                form.base_fields['target_grades'].widget = admin.widgets.FilteredSelectMultiple('Grades', False)
                form.base_fields['target_subjects'].widget = admin.widgets.FilteredSelectMultiple('Subjects', False)
            elif obj.applicable_to == 'subject':
                form.base_fields['target_grades'].widget = admin.widgets.FilteredSelectMultiple('Grades', False)
                form.base_fields['target_classes'].widget = admin.widgets.FilteredSelectMultiple('Classes', False)
        return form


@admin.register(Curriculum)
class CurriculumAdmin(admin.ModelAdmin):
    """
    Curriculum admin
    """
    list_display = ('name', 'grade', 'academic_year', 'is_active', 'created_by')
    list_filter = ('grade', 'academic_year', 'is_active')
    search_fields = ('name', 'name_ar', 'grade__name')
    raw_id_fields = ('grade', 'academic_year', 'created_by')


@admin.register(GradeCapacityManagement)
class GradeCapacityManagementAdmin(admin.ModelAdmin):
    """
    Grade Capacity Management admin
    """
    list_display = ('grade', 'academic_year', 'total_capacity', 'current_enrollment', 'available_capacity', 'is_enrollment_open')
    list_filter = ('academic_year', 'is_enrollment_open', 'auto_create_sections')
    search_fields = ('grade__name', 'academic_year__name')
    raw_id_fields = ('grade', 'academic_year')
    readonly_fields = ('available_capacity', 'enrollment_percentage', 'is_full')


@admin.register(CurriculumPlan)
class CurriculumPlanAdmin(admin.ModelAdmin):
    """
    Curriculum Plan admin
    """
    list_display = ('name', 'code', 'curriculum_type', 'academic_year', 'is_active', 'created_by')
    list_filter = ('curriculum_type', 'academic_year', 'is_active')
    search_fields = ('name', 'name_ar', 'code')
    raw_id_fields = ('academic_year', 'created_by', 'approved_by')
    filter_horizontal = ('grades',)
    readonly_fields = ('is_current',)


@admin.register(CurriculumSubject)
class CurriculumSubjectAdmin(admin.ModelAdmin):
    """
    Curriculum Subject admin
    """
    list_display = ('curriculum', 'subject', 'credit_hours', 'weekly_hours', 'semester', 'is_mandatory', 'sequence_order')
    list_filter = ('semester', 'is_mandatory', 'curriculum__curriculum_type', 'is_active')
    search_fields = ('curriculum__name', 'subject__name')
    raw_id_fields = ('curriculum', 'subject')
    filter_horizontal = ('applicable_grades',)
