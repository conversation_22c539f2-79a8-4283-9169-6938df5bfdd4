{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Student Attachments" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-paperclip text-primary me-2"></i>{% trans "Student Attachments" %}
                            </h2>
                            <p class="text-muted">{% trans "Manage student attachments and additional files" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:add_attachment' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add Attachment" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter options -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <label for="student" class="form-label">{% trans "Student" %}</label>
                                    <select class="form-select" id="student" name="student">
                                        <option value="">{% trans "All Students" %}</option>
                                        <!-- Add student options here -->
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="type" class="form-label">{% trans "Attachment Type" %}</label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="">{% trans "All Types" %}</option>
                                        <option value="photo">{% trans "Photo" %}</option>
                                        <option value="document">{% trans "Document" %}</option>
                                        <option value="certificate">{% trans "Certificate" %}</option>
                                        <option value="report">{% trans "Report" %}</option>
                                        <option value="medical">{% trans "Medical Document" %}</option>
                                        <option value="other">{% trans "Other" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter me-2"></i>{% trans "Apply Filters" %}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attachments List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Attachments" %}</h5>
                        </div>
                        <div class="card-body">
                            {% if attachments %}
                                <div class="row">
                                    {% for attachment in attachments %}
                                        <div class="col-md-6 col-lg-4 mb-4">
                                            <div class="card h-100">
                                                <div class="card-body">
                                                    <div class="d-flex align-items-start mb-3">
                                                        <div class="me-3">
                                                            {% if attachment.attachment_type == 'photo' %}
                                                                <i class="fas fa-image fa-2x text-info"></i>
                                                            {% elif attachment.attachment_type == 'document' %}
                                                                <i class="fas fa-file-alt fa-2x text-primary"></i>
                                                            {% elif attachment.attachment_type == 'certificate' %}
                                                                <i class="fas fa-certificate fa-2x text-warning"></i>
                                                            {% elif attachment.attachment_type == 'report' %}
                                                                <i class="fas fa-chart-line fa-2x text-success"></i>
                                                            {% elif attachment.attachment_type == 'medical' %}
                                                                <i class="fas fa-heartbeat fa-2x text-danger"></i>
                                                            {% else %}
                                                                <i class="fas fa-file fa-2x text-secondary"></i>
                                                            {% endif %}
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <h6 class="card-title">{{ attachment.title }}</h6>
                                                            <p class="card-text">
                                                                <small class="text-muted">
                                                                    {{ attachment.student.full_name }}<br>
                                                                    {{ attachment.get_attachment_type_display }}
                                                                </small>
                                                            </p>
                                                        </div>
                                                    </div>
                                                    
                                                    {% if attachment.description %}
                                                        <p class="card-text">{{ attachment.description|truncatechars:100 }}</p>
                                                    {% endif %}
                                                    
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <small class="text-muted">
                                                            {{ attachment.created_at|date:"d/m/Y" }}
                                                        </small>
                                                        {% if attachment.is_public %}
                                                            <span class="badge bg-success">{% trans "Public" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">{% trans "Private" %}</span>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="btn-group w-100">
                                                        <a href="{{ attachment.file.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{{ attachment.file.url }}" download class="btn btn-sm btn-outline-success">
                                                            <i class="fas fa-download"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-sm btn-outline-secondary">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>{% trans "No attachments found." %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}