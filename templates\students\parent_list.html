{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Parents" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-user-friends text-primary me-2"></i>{% trans "Parents" %}
                            </h2>
                            <p class="text-muted">{% trans "Manage student parents and guardians" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:add_parent' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add New Parent" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h3 class="mb-1">{{ total_parents }}</h3>
                            <p class="mb-0">{% trans "Total Parents" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-user-tie fa-2x mb-2"></i>
                            <h3 class="mb-1">{{ fathers_count }}</h3>
                            <p class="mb-0">{% trans "Fathers" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-female fa-2x mb-2"></i>
                            <h3 class="mb-1">{{ mothers_count }}</h3>
                            <p class="mb-0">{% trans "Mothers" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <i class="fas fa-user-shield fa-2x mb-2"></i>
                            <h3 class="mb-1">{{ guardians_count }}</h3>
                            <p class="mb-0">{% trans "Guardians" %}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and filters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-6">
                                    <label for="searchParents" class="form-label">{% trans "Search Parents" %}</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="searchParents" name="search" placeholder="{% trans 'Search by name, phone, or ID' %}" value="{{ request.GET.search|default:'' }}">
                                        <button class="btn btn-outline-secondary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="childrenFilter" class="form-label">{% trans "Children Count" %}</label>
                                    <select class="form-select" id="childrenFilter" name="children_count">
                                        <option value="">{% trans "All" %}</option>
                                        <option value="1" {% if request.GET.children_count == '1' %}selected{% endif %}>{% trans "1 Child" %}</option>
                                        <option value="2" {% if request.GET.children_count == '2' %}selected{% endif %}>{% trans "2 Children" %}</option>
                                        <option value="3+" {% if request.GET.children_count == '3+' %}selected{% endif %}>{% trans "3+ Children" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter me-2"></i>{% trans "Filter" %}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Parents List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">{% trans "Parents List" %}</h5>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary active">
                                        <i class="fas fa-list"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-th"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            {% if parents %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Parent" %}</th>
                                                <th>{% trans "Relation" %}</th>
                                                <th>{% trans "Contact" %}</th>
                                                <th>{% trans "Children" %}</th>
                                                <th>{% trans "Address" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for parent in parents %}
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                                <span class="text-white fw-bold">{{ parent.father_name|slice:":1" }}{{ parent.father_name.split|last|slice:":1" }}</span>
                                                            </div>
                                                            <div>
                                                                <h6 class="mb-0">{{ parent.father_name }}</h6>
                                                                <small class="text-muted">ID: {{ parent.id }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{% trans "Father/Mother" %}</td>
                                                    <td>
                                                        {% if parent.father_phone %}
                                                            {{ parent.father_phone }}
                                                        {% elif parent.mother_phone %}
                                                            {{ parent.mother_phone }}
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">{{ parent.children_count }}</span>
                                                        {% if parent.children.all %}
                                                            <small class="d-block mt-1">
                                                                {% for child in parent.children.all|slice:":2" %}
                                                                    {{ child.first_name }}{% if not forloop.last %}, {% endif %}
                                                                {% endfor %}
                                                                {% if parent.children.count > 2 %}
                                                                    {% trans "and" %} {{ parent.children.count|add:"-2" }} {% trans "more" %}
                                                                {% endif %}
                                                            </small>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ parent.home_address|truncatechars:30 }}</td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="{% url 'students:parent_detail' parent.id %}" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="{% url 'students:parent_edit' parent.id %}" class="btn btn-sm btn-outline-secondary">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#messageModal{{ parent.id }}">
                                                                <i class="fas fa-envelope"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Pagination -->
                                {% if parents.has_other_pages %}
                                    <nav aria-label="Page navigation" class="mt-4">
                                        <ul class="pagination justify-content-center">
                                            {% if parents.has_previous %}
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                                    </a>
                                                </li>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page={{ parents.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                                        <span aria-hidden="true">&laquo;</span>
                                                    </a>
                                                </li>
                                            {% else %}
                                                <li class="page-item disabled">
                                                    <a class="page-link" href="#" aria-label="First">
                                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                                    </a>
                                                </li>
                                                <li class="page-item disabled">
                                                    <a class="page-link" href="#" aria-label="Previous">
                                                        <span aria-hidden="true">&laquo;</span>
                                                    </a>
                                                </li>
                                            {% endif %}
                                            
                                            {% for i in parents.paginator.page_range %}
                                                {% if parents.number == i %}
                                                    <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                                                {% elif i > parents.number|add:'-3' and i < parents.number|add:'3' %}
                                                    <li class="page-item">
                                                        <a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a>
                                                    </li>
                                                {% endif %}
                                            {% endfor %}
                                            
                                            {% if parents.has_next %}
                                                <li class="page-item">
                                                    <a class="page-link" href="?page={{ parents.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                                        <span aria-hidden="true">&raquo;</span>
                                                    </a>
                                                </li>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page={{ parents.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                                    </a>
                                                </li>
                                            {% else %}
                                                <li class="page-item disabled">
                                                    <a class="page-link" href="#" aria-label="Next">
                                                        <span aria-hidden="true">&raquo;</span>
                                                    </a>
                                                </li>
                                                <li class="page-item disabled">
                                                    <a class="page-link" href="#" aria-label="Last">
                                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                                    </a>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </nav>
                                {% endif %}
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>{% trans "No parents found matching your criteria." %}
                                </div>
                                <div class="text-center mt-4">
                                    <a href="{% url 'students:add_parent' %}" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>{% trans "Add New Parent" %}
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Message Modals -->
{% for parent in parents %}
    <div class="modal fade" id="messageModal{{ parent.id }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{% trans "Send Message to" %} {{ parent.father_name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="messageSubject{{ parent.id }}" class="form-label">{% trans "Subject" %}</label>
                            <input type="text" class="form-control" id="messageSubject{{ parent.id }}">
                        </div>
                        <div class="mb-3">
                            <label for="messageContent{{ parent.id }}" class="form-label">{% trans "Message" %}</label>
                            <textarea class="form-control" id="messageContent{{ parent.id }}" rows="5"></textarea>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="sendSMS{{ parent.id }}">
                            <label class="form-check-label" for="sendSMS{{ parent.id }}">{% trans "Also send as SMS" %}</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="button" class="btn btn-primary">{% trans "Send Message" %}</button>
                </div>
            </div>
        </div>
    </div>
{% endfor %}
{% endblock %}