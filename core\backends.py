"""
Authentication backends for School ERP system
"""
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied
from .models import School
from .utils import SecurityUtils
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class SchoolAuthBackend(ModelBackend):
    """
    Custom authentication backend that supports school-based authentication
    """
    
    def authenticate(self, request, username=None, password=None, school_code=None, **kwargs):
        """
        Authenticate user with optional school context
        """
        if username is None or password is None:
            return None
        
        try:
            # Try to get user by username or email
            try:
                user = User.objects.get(username=username)
            except User.DoesNotExist:
                try:
                    user = User.objects.get(email=username)
                except User.DoesNotExist:
                    return None
            
            # Check password
            if not user.check_password(password):
                return None
            
            # If school code is provided, validate user belongs to that school
            if school_code:
                if not self._validate_user_school(user, school_code):
                    logger.warning(f"User {username} attempted to login to unauthorized school {school_code}")
                    return None
            
            # Check if user is active
            if not user.is_active:
                return None
            
            return user
            
        except Exception as e:
            logger.error(f"Authentication error for user {username}: {e}")
            return None
    
    def _validate_user_school(self, user, school_code):
        """
        Validate that user belongs to the specified school
        """
        try:
            school = School.objects.get(code=school_code, is_active=True)
        except School.DoesNotExist:
            return False
        
        # Check employee relationship
        if hasattr(user, 'employee') and user.employee:
            return user.employee.school == school
        
        # Check student relationship
        if hasattr(user, 'student') and user.student:
            return user.student.school == school
        
        # Check parent relationship
        if hasattr(user, 'parent') and user.parent:
            return user.parent.school == school
        
        # Superusers can access any school
        if user.is_superuser:
            return True
        
        return False
    
    def get_user(self, user_id):
        """
        Get user by ID
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None


class MultiFactorAuthBackend(ModelBackend):
    """
    Multi-factor authentication backend
    """
    
    def authenticate(self, request, username=None, password=None, mfa_token=None, **kwargs):
        """
        Authenticate with multi-factor authentication
        """
        # First, authenticate with regular credentials
        user = super().authenticate(request, username, password, **kwargs)
        if not user:
            return None
        
        # Check if MFA is required for this user
        if self._is_mfa_required(user):
            if not mfa_token:
                # Store partial authentication state
                from django.utils import timezone
                request.session['partial_auth_user_id'] = user.id
                request.session['partial_auth_timestamp'] = timezone.now().timestamp()
                raise PermissionDenied("MFA token required")
            
            # Validate MFA token
            if not self._validate_mfa_token(user, mfa_token):
                return None
        
        return user
    
    def _is_mfa_required(self, user):
        """
        Check if MFA is required for this user
        """
        # MFA required for superusers and admins
        if user.is_superuser:
            return True
        
        if hasattr(user, 'employee') and user.employee:
            return user.employee.role in ['ADMIN', 'FINANCE_MANAGER', 'HR_MANAGER']
        
        return False
    
    def _validate_mfa_token(self, user, token):
        """
        Validate MFA token (TOTP)
        """
        try:
            import pyotp
            
            # Get user's MFA secret (you'd store this in user profile)
            if hasattr(user, 'profile') and user.profile.mfa_secret:
                totp = pyotp.TOTP(user.profile.mfa_secret)
                return totp.verify(token, valid_window=1)
            
            return False
        except ImportError:
            logger.warning("pyotp not installed, MFA validation skipped")
            return True
        except Exception as e:
            logger.error(f"MFA validation error: {e}")
            return False


class TokenAuthBackend(ModelBackend):
    """
    Token-based authentication backend for API access
    """
    
    def authenticate(self, request, token=None, **kwargs):
        """
        Authenticate using API token
        """
        if not token:
            return None
        
        try:
            # Validate token format and signature
            if not self._validate_token_format(token):
                return None
            
            # Extract user ID from token
            user_id = self._extract_user_from_token(token)
            if not user_id:
                return None
            
            # Get user
            user = User.objects.get(pk=user_id, is_active=True)
            
            # Validate token hasn't expired
            if not self._validate_token_expiry(token):
                return None
            
            return user
            
        except User.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"Token authentication error: {e}")
            return None
    
    def _validate_token_format(self, token):
        """
        Validate token format
        """
        # Simple validation - in practice, use JWT or similar
        parts = token.split('.')
        return len(parts) == 3
    
    def _extract_user_from_token(self, token):
        """
        Extract user ID from token
        """
        try:
            import base64
            import json
            
            # Decode payload (second part of token)
            payload = token.split('.')[1]
            # Add padding if needed
            payload += '=' * (4 - len(payload) % 4)
            decoded = base64.urlsafe_b64decode(payload)
            data = json.loads(decoded)
            
            return data.get('user_id')
        except Exception:
            return None
    
    def _validate_token_expiry(self, token):
        """
        Validate token hasn't expired
        """
        try:
            import base64
            import json
            from django.utils import timezone
            
            payload = token.split('.')[1]
            payload += '=' * (4 - len(payload) % 4)
            decoded = base64.urlsafe_b64decode(payload)
            data = json.loads(decoded)
            
            exp = data.get('exp')
            if exp:
                return timezone.now().timestamp() < exp
            
            return True
        except Exception:
            return False


class LDAPAuthBackend(ModelBackend):
    """
    LDAP authentication backend for enterprise integration
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authenticate against LDAP server
        """
        if not username or not password:
            return None
        
        try:
            import ldap3
            from django.conf import settings
            
            # Get LDAP settings
            ldap_settings = getattr(settings, 'LDAP_CONFIG', {})
            if not ldap_settings:
                return None
            
            server = ldap3.Server(ldap_settings['SERVER'])
            
            # Bind with service account
            conn = ldap3.Connection(
                server,
                user=ldap_settings['BIND_DN'],
                password=ldap_settings['BIND_PASSWORD'],
                auto_bind=True
            )
            
            # Search for user
            search_filter = f"({ldap_settings['USERNAME_ATTR']}={username})"
            conn.search(
                ldap_settings['BASE_DN'],
                search_filter,
                attributes=['*']
            )
            
            if not conn.entries:
                return None
            
            user_dn = conn.entries[0].entry_dn
            
            # Try to bind with user credentials
            user_conn = ldap3.Connection(server, user=user_dn, password=password)
            if not user_conn.bind():
                return None
            
            # Get or create Django user
            user_data = conn.entries[0]
            user = self._get_or_create_user_from_ldap(username, user_data)
            
            return user if user.is_active else None
            
        except ImportError:
            logger.warning("ldap3 not installed, LDAP authentication skipped")
            return None
        except Exception as e:
            logger.error(f"LDAP authentication error: {e}")
            return None
    
    def _get_or_create_user_from_ldap(self, username, ldap_data):
        """
        Get or create Django user from LDAP data
        """
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            # Create new user
            user = User.objects.create_user(
                username=username,
                email=getattr(ldap_data, 'mail', [''])[0],
                first_name=getattr(ldap_data, 'givenName', [''])[0],
                last_name=getattr(ldap_data, 'sn', [''])[0]
            )
        
        # Update user data from LDAP
        user.email = getattr(ldap_data, 'mail', [''])[0] or user.email
        user.first_name = getattr(ldap_data, 'givenName', [''])[0] or user.first_name
        user.last_name = getattr(ldap_data, 'sn', [''])[0] or user.last_name
        user.save()
        
        return user