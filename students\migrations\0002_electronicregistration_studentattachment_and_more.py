# Generated by Django 5.2.4 on 2025-07-13 16:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('students', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ElectronicRegistration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('first_name', models.CharField(max_length=50, verbose_name='First Name')),
                ('last_name', models.Char<PERSON><PERSON>(max_length=50, verbose_name='Last Name')),
                ('first_name_ar', models.CharField(blank=True, max_length=50, null=True, verbose_name='First Name (Arabic)')),
                ('last_name_ar', models.CharField(blank=True, max_length=50, null=True, verbose_name='Last Name (Arabic)')),
                ('date_of_birth', models.DateField(verbose_name='Date of Birth')),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female')], max_length=1, verbose_name='Gender')),
                ('nationality', models.CharField(max_length=50, verbose_name='Nationality')),
                ('father_name', models.CharField(max_length=100, verbose_name='Father Name')),
                ('father_phone', models.CharField(max_length=20, verbose_name='Father Phone')),
                ('father_email', models.EmailField(max_length=254, verbose_name='Father Email')),
                ('mother_name', models.CharField(max_length=100, verbose_name='Mother Name')),
                ('mother_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Mother Phone')),
                ('home_address', models.TextField(verbose_name='Home Address')),
                ('previous_school', models.CharField(blank=True, max_length=200, null=True, verbose_name='Previous School')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('submitted', 'Submitted'), ('under_review', 'Under Review'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('completed', 'Completed')], default='draft', max_length=20, verbose_name='Status')),
                ('submitted_at', models.DateTimeField(blank=True, null=True, verbose_name='Submitted At')),
                ('reviewed_at', models.DateTimeField(blank=True, null=True, verbose_name='Reviewed At')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('desired_grade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.grade', verbose_name='Desired Grade')),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_registrations', to=settings.AUTH_USER_MODEL, verbose_name='Reviewed By')),
                ('student', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='electronic_registration', to='students.student', verbose_name='Created Student')),
            ],
            options={
                'verbose_name': 'Electronic Registration',
                'verbose_name_plural': 'Electronic Registrations',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StudentAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('title', models.CharField(max_length=200, verbose_name='Title')),
                ('attachment_type', models.CharField(choices=[('photo', 'Photo'), ('document', 'Document'), ('certificate', 'Certificate'), ('report', 'Report'), ('medical', 'Medical Document'), ('other', 'Other')], max_length=20, verbose_name='Attachment Type')),
                ('file', models.FileField(upload_to='students/attachments/', verbose_name='File')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_public', models.BooleanField(default=False, verbose_name='Is Public')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='students.student', verbose_name='Student')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_attachments', to=settings.AUTH_USER_MODEL, verbose_name='Uploaded By')),
            ],
            options={
                'verbose_name': 'Student Attachment',
                'verbose_name_plural': 'Student Attachments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StudentDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('document_type', models.CharField(choices=[('birth_certificate', 'Birth Certificate'), ('passport', 'Passport'), ('medical_record', 'Medical Record'), ('previous_school_record', 'Previous School Record'), ('photo', 'Photo'), ('vaccination_record', 'Vaccination Record'), ('other', 'Other')], max_length=30, verbose_name='Document Type')),
                ('title', models.CharField(max_length=200, verbose_name='Document Title')),
                ('file', models.FileField(upload_to='students/documents/', verbose_name='Document File')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_verified', models.BooleanField(default=False, verbose_name='Is Verified')),
                ('verified_at', models.DateTimeField(blank=True, null=True, verbose_name='Verified At')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='students.student', verbose_name='Student')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_student_documents', to=settings.AUTH_USER_MODEL, verbose_name='Uploaded By')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_student_documents', to=settings.AUTH_USER_MODEL, verbose_name='Verified By')),
            ],
            options={
                'verbose_name': 'Student Document',
                'verbose_name_plural': 'Student Documents',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StudentInfraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('incident_date', models.DateField(verbose_name='Incident Date')),
                ('description', models.TextField(verbose_name='Description')),
                ('severity', models.CharField(choices=[('minor', 'Minor'), ('moderate', 'Moderate'), ('major', 'Major'), ('severe', 'Severe')], max_length=20, verbose_name='Severity')),
                ('action_taken', models.CharField(choices=[('warning', 'Warning'), ('detention', 'Detention'), ('suspension', 'Suspension'), ('expulsion', 'Expulsion'), ('community_service', 'Community Service'), ('parent_meeting', 'Parent Meeting')], max_length=30, verbose_name='Action Taken')),
                ('parent_notified', models.BooleanField(default=False, verbose_name='Parent Notified')),
                ('parent_notified_at', models.DateTimeField(blank=True, null=True, verbose_name='Parent Notified At')),
                ('follow_up_required', models.BooleanField(default=False, verbose_name='Follow-up Required')),
                ('follow_up_date', models.DateField(blank=True, null=True, verbose_name='Follow-up Date')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Additional Notes')),
                ('handled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='handled_infractions', to=settings.AUTH_USER_MODEL, verbose_name='Handled By')),
                ('reported_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reported_infractions', to=settings.AUTH_USER_MODEL, verbose_name='Reported By')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='infractions', to='students.student', verbose_name='Student')),
            ],
            options={
                'verbose_name': 'Student Infraction',
                'verbose_name_plural': 'Student Infractions',
                'ordering': ['-incident_date'],
            },
        ),
        migrations.CreateModel(
            name='StudentTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('transfer_type', models.CharField(choices=[('incoming', 'Incoming Transfer'), ('outgoing', 'Outgoing Transfer'), ('internal', 'Internal Transfer')], max_length=20, verbose_name='Transfer Type')),
                ('from_school', models.CharField(blank=True, max_length=200, null=True, verbose_name='From School')),
                ('to_school', models.CharField(blank=True, max_length=200, null=True, verbose_name='To School')),
                ('transfer_date', models.DateField(verbose_name='Transfer Date')),
                ('reason', models.TextField(verbose_name='Reason for Transfer')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('completed', 'Completed'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')], default='pending', max_length=20, verbose_name='Status')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='Approved At')),
                ('documents_transferred', models.BooleanField(default=False, verbose_name='Documents Transferred')),
                ('fees_cleared', models.BooleanField(default=False, verbose_name='Fees Cleared')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to=settings.AUTH_USER_MODEL, verbose_name='Approved By')),
                ('from_class', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='outgoing_transfers', to='students.class', verbose_name='From Class')),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requested_transfers', to=settings.AUTH_USER_MODEL, verbose_name='Requested By')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers', to='students.student', verbose_name='Student')),
                ('to_class', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='incoming_transfers', to='students.class', verbose_name='To Class')),
            ],
            options={
                'verbose_name': 'Student Transfer',
                'verbose_name_plural': 'Student Transfers',
                'ordering': ['-transfer_date'],
            },
        ),
        migrations.CreateModel(
            name='VacationRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('reason', models.TextField(verbose_name='Reason')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')], default='pending', max_length=20, verbose_name='Status')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='Approved At')),
                ('rejection_reason', models.TextField(blank=True, null=True, verbose_name='Rejection Reason')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_vacations', to=settings.AUTH_USER_MODEL, verbose_name='Approved By')),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requested_vacations', to=settings.AUTH_USER_MODEL, verbose_name='Requested By')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vacation_requests', to='students.student', verbose_name='Student')),
            ],
            options={
                'verbose_name': 'Vacation Request',
                'verbose_name_plural': 'Vacation Requests',
                'ordering': ['-created_at'],
            },
        ),
    ]
