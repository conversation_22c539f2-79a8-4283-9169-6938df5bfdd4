"""
Test cases for Task 4.4: Implement Examination System

This test file validates the implementation of:
- Exam scheduling and management
- Exam room allocation system
- Invigilation assignment
- Exam result processing
- Exam analytics and reporting

Requirements: 3.4
"""

import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from datetime import date, time, timedelta, datetime
from decimal import Decimal

from core.models import School, AcademicYear, Semester
from students.models import Grade, Class, Student
from academics.models import (
    Subject, Teacher, ClassSubject, Exam, Room, StudentGrade,
    ExamSession, ExamInvigilator, ExamSeating, ExamResult, ExamAnalytics
)
from academics.examination_system import (
    ExamScheduler, InvigilationManager, ExamResultProcessor,
    ExamSeatingManager, ExamAnalyticsEngine, ExamSchedulingConflict
)

User = get_user_model()


class Task44ExaminationSystemTest(TestCase):
    """
    Comprehensive test cases for Task 4.4 Examination System
    """
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.semester = Semester.objects.create(
            school=self.school,
            academic_year=self.academic_year,
            name="First Semester",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1,
            max_capacity=60
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )
        
        # Create teacher
        self.teacher_user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        self.teacher = Teacher.objects.create(
            school=self.school,
            user=self.teacher_user,
            employee_id="T001",
            hire_date=date.today(),
            qualification="Master's in Mathematics",
            experience_years=5
        )
        
        # Create subject
        self.subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH101",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        
        self.teacher.subjects.add(self.subject)
        
        # Create class
        self.class_obj = Class.objects.create(
            school=self.school,
            name="Section A",
            grade=self.grade,
            academic_year=self.academic_year,
            class_teacher=self.teacher_user,
            max_students=30
        )
        
        # Create class subject
        self.class_subject = ClassSubject.objects.create(
            school=self.school,
            class_obj=self.class_obj,
            subject=self.subject,
            teacher=self.teacher,
            academic_year=self.academic_year,
            semester=self.semester,
            weekly_hours=4
        )
        
        # Create room
        self.room = Room.objects.create(
            school=self.school,
            name="Exam Hall 1",
            room_number="EH001",
            room_type="classroom",
            capacity=50,
            is_available=True
        )
        
        # Create parent
        parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        from students.models import Parent
        self.parent = Parent.objects.create(
            school=self.school,
            user=parent_user,
            father_name="Test Father",
            father_phone="+1234567890"
        )
        
        # Create students
        self.students = []
        for i in range(25):
            student_user = User.objects.create_user(
                username=f"student{i+1}",
                email=f"student{i+1}@school.com",
                password="testpass123",
                user_type="student"
            )
            
            student = Student.objects.create(
                school=self.school,
                user=student_user,
                student_id=f"STU{i+1:03d}",
                admission_number=f"ADM{i+1:03d}",
                first_name=f"Student{i+1}",
                last_name="Test",
                date_of_birth=date(2010, 1, 1),
                gender="M" if i % 2 == 0 else "F",
                nationality="US",
                admission_date=date.today(),
                parent=self.parent,
                current_class=self.class_obj
            )
            self.students.append(student)
            self.class_obj.students.add(student)

    def test_exam_session_model(self):
        """Test ExamSession model functionality"""
        # Create exam session
        session = ExamSession.objects.create(
            school=self.school,
            name="Midterm Exams 2024",
            session_type="midterm",
            academic_year=self.academic_year,
            semester=self.semester,
            start_date=date(2024, 11, 1),
            end_date=date(2024, 11, 15),
            registration_start=date(2024, 10, 15),
            registration_end=date(2024, 10, 30)
        )
        
        # Test session properties
        self.assertEqual(session.name, "Midterm Exams 2024")
        self.assertEqual(session.session_type, "midterm")
        self.assertEqual(session.academic_year, self.academic_year)
        
        # Test validation
        with self.assertRaises(ValidationError):
            invalid_session = ExamSession(
                school=self.school,
                name="Invalid Session",
                session_type="midterm",
                academic_year=self.academic_year,
                semester=self.semester,
                start_date=date(2024, 11, 15),
                end_date=date(2024, 11, 1)  # End before start
            )
            invalid_session.full_clean()
        
        # Test methods
        self.assertEqual(session.get_exam_count(), 0)
        self.assertEqual(session.get_completion_percentage(), 0)

    def test_enhanced_exam_model(self):
        """Test enhanced Exam model with room and session relationships"""
        # Create exam session
        session = ExamSession.objects.create(
            school=self.school,
            name="Midterm Exams 2024",
            session_type="midterm",
            academic_year=self.academic_year,
            semester=self.semester,
            start_date=date(2024, 11, 1),
            end_date=date(2024, 11, 15)
        )
        
        # Create exam
        exam = Exam.objects.create(
            school=self.school,
            name="Mathematics Midterm",
            class_subject=self.class_subject,
            exam_type="midterm",
            exam_date=date.today() + timedelta(days=10),
            start_time=time(9, 0),
            end_time=time(11, 0),
            duration_minutes=120,
            total_marks=100,
            passing_marks=40,
            room=self.room,
            exam_session=session,
            created_by=self.user
        )
        
        # Test exam properties
        self.assertEqual(exam.name, "Mathematics Midterm")
        self.assertEqual(exam.room, self.room)
        self.assertEqual(exam.exam_session, session)
        self.assertEqual(exam.class_subject, self.class_subject)
        
        # Test is_completed property
        self.assertFalse(exam.is_completed)

    def test_exam_invigilator_model(self):
        """Test ExamInvigilator model functionality"""
        # Create exam
        exam = Exam.objects.create(
            school=self.school,
            name="Mathematics Test",
            class_subject=self.class_subject,
            exam_type="quiz",
            exam_date=date.today() + timedelta(days=1),
            start_time=time(9, 0),
            end_time=time(10, 0),
            duration_minutes=60,
            total_marks=50,
            passing_marks=20,
            room=self.room,
            created_by=self.user
        )
        
        # Create invigilator assignment
        invigilator = ExamInvigilator.objects.create(
            school=self.school,
            exam=exam,
            teacher=self.teacher,
            role="chief",
            assigned_by=self.user
        )
        
        # Test invigilator properties
        self.assertEqual(invigilator.exam, exam)
        self.assertEqual(invigilator.teacher, self.teacher)
        self.assertEqual(invigilator.role, "chief")
        self.assertFalse(invigilator.is_confirmed)
        
        # Test confirmation
        invigilator.confirm_assignment(self.user)
        self.assertTrue(invigilator.is_confirmed)
        self.assertIsNotNone(invigilator.confirmed_at)
        
        # Test duration calculation
        self.assertEqual(invigilator.get_duration_minutes(), 60)

    def test_exam_seating_model(self):
        """Test ExamSeating model functionality"""
        # Create exam
        exam = Exam.objects.create(
            school=self.school,
            name="Mathematics Test",
            class_subject=self.class_subject,
            exam_type="quiz",
            exam_date=date.today() + timedelta(days=1),
            start_time=time(9, 0),
            end_time=time(10, 0),
            duration_minutes=60,
            total_marks=50,
            passing_marks=20,
            room=self.room,
            created_by=self.user
        )
        
        # Create seating arrangement
        seating = ExamSeating.objects.create(
            school=self.school,
            exam=exam,
            student=self.students[0],
            room=self.room,
            seat_number="R1C1",
            row_number=1,
            column_number=1
        )
        
        # Test seating properties
        self.assertEqual(seating.exam, exam)
        self.assertEqual(seating.student, self.students[0])
        self.assertEqual(seating.room, self.room)
        self.assertEqual(seating.seat_number, "R1C1")
        
        # Test validation - duplicate seat
        with self.assertRaises(ValidationError):
            duplicate_seating = ExamSeating(
                school=self.school,
                exam=exam,
                student=self.students[1],
                room=self.room,
                seat_number="R1C1",  # Same seat
                row_number=1,
                column_number=1
            )
            duplicate_seating.full_clean()

    def test_exam_result_model(self):
        """Test ExamResult model functionality"""
        # Create exam
        exam = Exam.objects.create(
            school=self.school,
            name="Mathematics Test",
            class_subject=self.class_subject,
            exam_type="quiz",
            exam_date=date.today() - timedelta(days=1),
            start_time=time(9, 0),
            end_time=time(10, 0),
            duration_minutes=60,
            total_marks=100,
            passing_marks=40,
            room=self.room,
            created_by=self.user
        )
        
        # Create student grades
        for i, student in enumerate(self.students[:10]):
            StudentGrade.objects.create(
                school=self.school,
                student=student,
                exam=exam,
                marks_obtained=50 + (i * 5),  # Varying marks
                graded_by=self.user
            )
        
        # Create exam result
        result = ExamResult.objects.create(
            school=self.school,
            exam=exam,
            generated_by=self.user
        )
        
        # Calculate statistics
        result.calculate_statistics()
        
        # Test calculated values
        self.assertEqual(result.total_students, 25)  # Total students in class
        self.assertEqual(result.students_appeared, 10)  # Students with grades
        self.assertGreater(result.average_marks, 0)
        self.assertGreater(result.pass_percentage, 0)
        
        # Test insights
        insights = result.get_performance_insights()
        self.assertIsInstance(insights, list)
        self.assertGreater(len(insights), 0)

    def test_exam_scheduler_basic_functionality(self):
        """Test ExamScheduler basic functionality"""
        scheduler = ExamScheduler(self.school, self.academic_year)
        
        # Create exam
        exam = Exam.objects.create(
            school=self.school,
            name="Mathematics Test",
            class_subject=self.class_subject,
            exam_type="quiz",
            exam_date=date.today() + timedelta(days=7),
            start_time=time(9, 0),
            end_time=time(10, 0),
            duration_minutes=60,
            total_marks=50,
            passing_marks=20,
            created_by=self.user
        )
        
        # Test conflict detection
        conflicts = scheduler.detect_exam_conflicts(exam)
        self.assertIsInstance(conflicts, list)
        
        # Test room allocation
        allocated_room = scheduler.allocate_exam_room(exam)
        self.assertIsNotNone(allocated_room)
        self.assertEqual(allocated_room, self.room)
        
        # Test scheduling
        result = scheduler.schedule_exam(exam)
        self.assertIn('success', result)
        self.assertIn('allocated_room', result)
        self.assertIn('conflicts', result)

    def test_exam_scheduler_conflict_detection(self):
        """Test ExamScheduler conflict detection"""
        scheduler = ExamScheduler(self.school, self.academic_year)
        
        # Create first exam
        exam1 = Exam.objects.create(
            school=self.school,
            name="Mathematics Test 1",
            class_subject=self.class_subject,
            exam_type="quiz",
            exam_date=date.today() + timedelta(days=7),
            start_time=time(9, 0),
            end_time=time(10, 0),
            duration_minutes=60,
            total_marks=50,
            passing_marks=20,
            room=self.room,
            created_by=self.user
        )
        
        # Create conflicting exam (same room, overlapping time)
        exam2 = Exam.objects.create(
            school=self.school,
            name="Mathematics Test 2",
            class_subject=self.class_subject,
            exam_type="quiz",
            exam_date=date.today() + timedelta(days=7),
            start_time=time(9, 30),
            end_time=time(10, 30),
            duration_minutes=60,
            total_marks=50,
            passing_marks=20,
            room=self.room,
            created_by=self.user
        )
        
        # Test conflict detection
        conflicts = scheduler.detect_exam_conflicts(exam2)
        self.assertGreater(len(conflicts), 0)
        
        # Check for room conflict
        room_conflicts = [c for c in conflicts if c.conflict_type == 'room']
        self.assertGreater(len(room_conflicts), 0)

    def test_invigilation_manager(self):
        """Test InvigilationManager functionality"""
        manager = InvigilationManager(self.school, self.academic_year)
        
        # Create additional teachers
        teacher2_user = User.objects.create_user(
            username="teacher2",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        teacher2 = Teacher.objects.create(
            school=self.school,
            user=teacher2_user,
            employee_id="T002",
            hire_date=date.today(),
            qualification="Master's in Science",
            experience_years=3
        )
        
        # Create exam
        exam = Exam.objects.create(
            school=self.school,
            name="Mathematics Test",
            class_subject=self.class_subject,
            exam_type="quiz",
            exam_date=date.today() + timedelta(days=7),
            start_time=time(9, 0),
            end_time=time(10, 0),
            duration_minutes=60,
            total_marks=50,
            passing_marks=20,
            room=self.room,
            created_by=self.user
        )
        
        # Test finding available teachers
        available_teachers = manager.find_available_teachers(exam)
        self.assertIn(self.teacher, available_teachers)
        self.assertIn(teacher2, available_teachers)
        
        # Test teacher availability check
        is_available = manager.is_teacher_available(self.teacher, exam)
        self.assertTrue(is_available)
        
        # Test invigilator assignment
        result = manager.assign_invigilators(exam, num_invigilators=2)
        self.assertTrue(result['success'])
        self.assertEqual(len(result['assigned_invigilators']), 2)
        
        # Check that invigilators were created
        invigilators = ExamInvigilator.objects.filter(exam=exam)
        self.assertEqual(invigilators.count(), 2)
        
        # Check roles
        chief_invigilator = invigilators.filter(role='chief').first()
        self.assertIsNotNone(chief_invigilator)

    def test_exam_result_processor(self):
        """Test ExamResultProcessor functionality"""
        processor = ExamResultProcessor(self.school, self.academic_year)
        
        # Create exam
        exam = Exam.objects.create(
            school=self.school,
            name="Mathematics Test",
            class_subject=self.class_subject,
            exam_type="quiz",
            exam_date=date.today() - timedelta(days=1),
            start_time=time(9, 0),
            end_time=time(10, 0),
            duration_minutes=60,
            total_marks=100,
            passing_marks=40,
            room=self.room,
            created_by=self.user
        )
        
        # Create student grades with varying performance
        marks_list = [85, 92, 78, 65, 45, 38, 72, 88, 55, 95]
        for i, student in enumerate(self.students[:10]):
            StudentGrade.objects.create(
                school=self.school,
                student=student,
                exam=exam,
                marks_obtained=marks_list[i],
                graded_by=self.user
            )
        
        # Test result processing
        result = processor.process_exam_results(exam)
        
        # Verify result structure
        self.assertIn('exam', result)
        self.assertIn('total_students', result)
        self.assertIn('students_appeared', result)
        self.assertIn('students_passed', result)
        self.assertIn('pass_percentage', result)
        self.assertIn('average_marks', result)
        self.assertIn('analytics', result)
        
        # Verify calculations
        self.assertEqual(result['total_students'], 25)
        self.assertEqual(result['students_appeared'], 10)
        self.assertEqual(result['students_passed'], 9)  # Marks >= 40
        self.assertEqual(result['pass_percentage'], 90.0)
        self.assertAlmostEqual(float(result['average_marks']), 71.3, places=1)
        self.assertEqual(result['highest_marks'], 95)
        self.assertEqual(result['lowest_marks'], 38)

    def test_exam_seating_manager(self):
        """Test ExamSeatingManager functionality"""
        manager = ExamSeatingManager(self.school, self.academic_year)
        
        # Create exam
        exam = Exam.objects.create(
            school=self.school,
            name="Mathematics Test",
            class_subject=self.class_subject,
            exam_type="quiz",
            exam_date=date.today() + timedelta(days=7),
            start_time=time(9, 0),
            end_time=time(10, 0),
            duration_minutes=60,
            total_marks=50,
            passing_marks=20,
            room=self.room,
            created_by=self.user
        )
        
        # Test seating arrangement generation
        result = manager.generate_seating_arrangement(exam, self.room)
        
        # Verify result
        self.assertTrue(result['success'])
        self.assertIn('seating_arrangement', result)
        self.assertIn('total_students', result)
        self.assertIn('room_capacity', result)
        self.assertIn('utilization', result)
        
        # Verify seating arrangements were created
        seatings = ExamSeating.objects.filter(exam=exam)
        self.assertEqual(seatings.count(), 25)  # All students
        
        # Test seating chart retrieval
        chart_result = manager.get_seating_chart(exam)
        self.assertTrue(chart_result['success'])
        self.assertIn('seating_chart', chart_result)
        self.assertIn('total_students', chart_result)

    def test_exam_analytics_engine(self):
        """Test ExamAnalyticsEngine functionality"""
        engine = ExamAnalyticsEngine(self.school, self.academic_year)
        
        # Create multiple exams with results
        for i in range(3):
            exam = Exam.objects.create(
                school=self.school,
                name=f"Test {i+1}",
                class_subject=self.class_subject,
                exam_type="quiz",
                exam_date=date.today() - timedelta(days=i*7),
                start_time=time(9, 0),
                end_time=time(10, 0),
                duration_minutes=60,
                total_marks=100,
                passing_marks=40,
                room=self.room,
                created_by=self.user
            )
            
            # Create grades for each exam
            for j, student in enumerate(self.students[:10]):
                StudentGrade.objects.create(
                    school=self.school,
                    student=student,
                    exam=exam,
                    marks_obtained=50 + (j * 3) + (i * 5),
                    graded_by=self.user
                )
        
        # Test comprehensive analytics
        analytics = engine.generate_comprehensive_analytics()
        
        # Verify analytics structure
        self.assertIn('overview', analytics)
        self.assertIn('performance_trends', analytics)
        self.assertIn('subject_analysis', analytics)
        self.assertIn('teacher_effectiveness', analytics)
        self.assertIn('room_utilization', analytics)
        self.assertIn('scheduling_efficiency', analytics)
        
        # Test individual analytics components
        overview = engine.get_exam_overview()
        self.assertIn('total_exams', overview)
        self.assertIn('completed_exams', overview)
        self.assertIn('average_performance', overview)
        
        trends = engine.get_performance_trends()
        self.assertIn('monthly_performance', trends)
        self.assertIn('exam_type_performance', trends)

    def test_exam_analytics_model(self):
        """Test ExamAnalytics model functionality"""
        # Create analytics record
        analytics = ExamAnalytics.objects.create(
            school=self.school,
            analytics_type="subject",
            academic_year=self.academic_year,
            semester=self.semester,
            subject=self.subject,
            analytics_data={
                'average_performance': 75.5,
                'pass_rate': 85.0,
                'standard_deviation': 12.3,
                'performance_trends': [70, 73, 75.5]
            },
            generated_by=self.user
        )
        
        # Test analytics properties
        self.assertEqual(analytics.analytics_type, "subject")
        self.assertEqual(analytics.subject, self.subject)
        self.assertIn('average_performance', analytics.analytics_data)
        
        # Test insight generation
        analytics.generate_insights()
        self.assertIsInstance(analytics.insights, list)
        self.assertGreater(len(analytics.insights), 0)
        
        # Test recommendation generation
        analytics.generate_recommendations()
        self.assertIsInstance(analytics.recommendations, list)

    def test_complete_examination_workflow(self):
        """Test complete examination workflow integration"""
        # Create exam session
        session = ExamSession.objects.create(
            school=self.school,
            name="Final Exams 2024",
            session_type="final",
            academic_year=self.academic_year,
            semester=self.semester,
            start_date=date.today() + timedelta(days=30),
            end_date=date.today() + timedelta(days=45)
        )
        
        # Create exam
        exam = Exam.objects.create(
            school=self.school,
            name="Mathematics Final",
            class_subject=self.class_subject,
            exam_type="final",
            exam_date=date.today() + timedelta(days=35),
            start_time=time(9, 0),
            end_time=time(12, 0),
            duration_minutes=180,
            total_marks=100,
            passing_marks=40,
            exam_session=session,
            created_by=self.user
        )
        
        # 1. Schedule exam
        scheduler = ExamScheduler(self.school, self.academic_year)
        schedule_result = scheduler.schedule_exam(exam)
        self.assertTrue(schedule_result['success'])
        
        # 2. Assign invigilators
        invigilation_manager = InvigilationManager(self.school, self.academic_year)
        invigilation_result = invigilation_manager.assign_invigilators(exam)
        self.assertTrue(invigilation_result['success'])
        
        # 3. Generate seating arrangement
        seating_manager = ExamSeatingManager(self.school, self.academic_year)
        seating_result = seating_manager.generate_seating_arrangement(exam)
        self.assertTrue(seating_result['success'])
        
        # 4. Simulate exam completion and grading
        for i, student in enumerate(self.students):
            StudentGrade.objects.create(
                school=self.school,
                student=student,
                exam=exam,
                marks_obtained=40 + (i * 2),  # Varying marks
                graded_by=self.user
            )
        
        # 5. Process results
        result_processor = ExamResultProcessor(self.school, self.academic_year)
        results = result_processor.process_exam_results(exam)
        self.assertGreater(results['students_appeared'], 0)
        
        # 6. Generate analytics
        analytics_engine = ExamAnalyticsEngine(self.school, self.academic_year)
        analytics = analytics_engine.generate_comprehensive_analytics()
        self.assertIn('overview', analytics)
        
        # Verify all components worked together
        self.assertIsNotNone(exam.room)
        self.assertEqual(ExamInvigilator.objects.filter(exam=exam).count(), 1)
        self.assertEqual(ExamSeating.objects.filter(exam=exam).count(), 25)
        self.assertEqual(StudentGrade.objects.filter(exam=exam).count(), 25)

    def test_validation_and_error_handling(self):
        """Test validation and error handling in examination system"""
        # Test invalid exam session dates
        with self.assertRaises(ValidationError):
            session = ExamSession(
                school=self.school,
                name="Invalid Session",
                session_type="midterm",
                academic_year=self.academic_year,
                semester=self.semester,
                start_date=date(2024, 11, 15),
                end_date=date(2024, 11, 1)  # End before start
            )
            session.full_clean()
        
        # Test exam scheduling with insufficient room capacity
        small_room = Room.objects.create(
            school=self.school,
            name="Small Room",
            room_number="SR001",
            capacity=10,  # Less than class size
            is_available=True
        )
        
        exam = Exam.objects.create(
            school=self.school,
            name="Mathematics Test",
            class_subject=self.class_subject,
            exam_type="quiz",
            exam_date=date.today() + timedelta(days=7),
            start_time=time(9, 0),
            end_time=time(10, 0),
            duration_minutes=60,
            total_marks=50,
            passing_marks=20,
            created_by=self.user
        )
        
        seating_manager = ExamSeatingManager(self.school, self.academic_year)
        result = seating_manager.generate_seating_arrangement(exam, small_room)
        self.assertFalse(result['success'])
        self.assertIn('error', result)

    def test_performance_and_scalability(self):
        """Test performance with larger datasets"""
        # Create multiple subjects and classes
        subjects = []
        class_subjects = []
        
        for i in range(5):
            subject = Subject.objects.create(
                school=self.school,
                name=f"Subject {i+1}",
                code=f"SUB{i+1}01",
                credit_hours=3,
                weekly_hours=4,
                created_by=self.user
            )
            subjects.append(subject)
            
            class_subject = ClassSubject.objects.create(
                school=self.school,
                class_obj=self.class_obj,
                subject=subject,
                teacher=self.teacher,
                academic_year=self.academic_year,
                semester=self.semester,
                weekly_hours=4
            )
            class_subjects.append(class_subject)
        
        # Create multiple exams
        exams = []
        for i, class_subject in enumerate(class_subjects):
            exam = Exam.objects.create(
                school=self.school,
                name=f"Test {i+1}",
                class_subject=class_subject,
                exam_type="quiz",
                exam_date=date.today() + timedelta(days=i+1),
                start_time=time(9, 0),
                end_time=time(10, 0),
                duration_minutes=60,
                total_marks=100,
                passing_marks=40,
                created_by=self.user
            )
            exams.append(exam)
        
        # Test scheduler performance with multiple exams
        scheduler = ExamScheduler(self.school, self.academic_year)
        
        start_time = datetime.now()
        schedule_result = scheduler.generate_exam_schedule(
            date.today(),
            date.today() + timedelta(days=10)
        )
        end_time = datetime.now()
        
        # Should complete within reasonable time (< 5 seconds)
        execution_time = (end_time - start_time).total_seconds()
        self.assertLess(execution_time, 5.0)
        
        # Verify results
        self.assertIn('total_exams', schedule_result)
        self.assertIn('scheduled_exams', schedule_result)
        self.assertEqual(schedule_result['total_exams'], 5)


@pytest.mark.django_db
class TestTask44Integration:
    """Integration tests for Task 4.4 Examination System"""
    
    def test_examination_system_integration(self):
        """Test complete examination system integration"""
        # Create school and academic structure
        school = School.objects.create(
            name="Integration Test School",
            code="ITS",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        academic_year = AcademicYear.objects.create(
            school=school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        semester = Semester.objects.create(
            school=school,
            academic_year=academic_year,
            name="First Semester",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        # Create complete academic structure
        grade = Grade.objects.create(
            school=school,
            name="Grade 10",
            level=10,
            max_capacity=120
        )
        
        user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )
        
        # Create multiple teachers
        teachers = []
        for i in range(5):
            teacher_user = User.objects.create_user(
                username=f"teacher{i+1}",
                email=f"teacher{i+1}@school.com",
                password="testpass123",
                user_type="teacher"
            )
            
            teacher = Teacher.objects.create(
                school=school,
                user=teacher_user,
                employee_id=f"T{i+1:03d}",
                hire_date=date.today(),
                qualification=f"Master's in Subject {i+1}",
                experience_years=5 + i
            )
            teachers.append(teacher)
        
        # Create subjects
        subjects = []
        subject_names = ["Mathematics", "Science", "English", "History", "Geography"]
        
        for i, name in enumerate(subject_names):
            subject = Subject.objects.create(
                school=school,
                name=name,
                code=f"{name[:3].upper()}{i+1}01",
                credit_hours=3,
                weekly_hours=4,
                created_by=user
            )
            subjects.append(subject)
            teachers[i].subjects.add(subject)
        
        # Create classes
        classes = []
        for i in range(3):
            class_obj = Class.objects.create(
                school=school,
                name=f"Section {chr(65+i)}",  # A, B, C
                grade=grade,
                academic_year=academic_year,
                class_teacher=teachers[0].user,
                max_students=40
            )
            classes.append(class_obj)
        
        # Create class subjects
        class_subjects = []
        for class_obj in classes:
            for i, subject in enumerate(subjects):
                class_subject = ClassSubject.objects.create(
                    school=school,
                    class_obj=class_obj,
                    subject=subject,
                    teacher=teachers[i],
                    academic_year=academic_year,
                    semester=semester,
                    weekly_hours=4
                )
                class_subjects.append(class_subject)
        
        # Create parent
        parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        from students.models import Parent
        parent = Parent.objects.create(
            school=school,
            user=parent_user,
            father_name="Test Father",
            father_phone="+1234567890"
        )
        
        # Create students
        students = []
        for i in range(100):
            class_index = i % 3
            student_user = User.objects.create_user(
                username=f"student{i+1}",
                email=f"student{i+1}@school.com",
                password="testpass123",
                user_type="student"
            )
            
            student = Student.objects.create(
                school=school,
                user=student_user,
                student_id=f"STU{i+1:04d}",
                admission_number=f"ADM{i+1:04d}",
                first_name=f"Student{i+1}",
                last_name="Test",
                date_of_birth=date(2008, 1, 1),
                gender="M" if i % 2 == 0 else "F",
                nationality="US",
                admission_date=date.today(),
                parent=parent,
                current_class=classes[class_index]
            )
            students.append(student)
            classes[class_index].students.add(student)
        
        # Create rooms
        rooms = []
        for i in range(10):
            room = Room.objects.create(
                school=school,
                name=f"Exam Hall {i+1}",
                room_number=f"EH{i+1:03d}",
                room_type="classroom",
                capacity=50,
                is_available=True
            )
            rooms.append(room)
        
        # Create exam session
        session = ExamSession.objects.create(
            school=school,
            name="Final Exams 2024",
            session_type="final",
            academic_year=academic_year,
            semester=semester,
            start_date=date.today() + timedelta(days=30),
            end_date=date.today() + timedelta(days=50)
        )
        
        # Create exams for all class subjects
        exams = []
        for i, class_subject in enumerate(class_subjects):
            exam = Exam.objects.create(
                school=school,
                name=f"{class_subject.subject.name} Final - {class_subject.class_obj.name}",
                class_subject=class_subject,
                exam_type="final",
                exam_date=date.today() + timedelta(days=30 + (i % 10)),
                start_time=time(9 + (i % 3) * 2, 0),
                end_time=time(11 + (i % 3) * 2, 0),
                duration_minutes=120,
                total_marks=100,
                passing_marks=40,
                exam_session=session,
                created_by=user
            )
            exams.append(exam)
        
        # Test complete workflow
        scheduler = ExamScheduler(school, academic_year)
        invigilation_manager = InvigilationManager(school, academic_year)
        seating_manager = ExamSeatingManager(school, academic_year)
        result_processor = ExamResultProcessor(school, academic_year)
        analytics_engine = ExamAnalyticsEngine(school, academic_year)
        
        # Schedule all exams
        successful_schedules = 0
        for exam in exams[:10]:  # Test with first 10 exams
            result = scheduler.schedule_exam(exam)
            if result['success']:
                successful_schedules += 1
                
                # Assign invigilators
                invigilation_result = invigilation_manager.assign_invigilators(exam)
                assert invigilation_result['success']
                
                # Generate seating
                seating_result = seating_manager.generate_seating_arrangement(exam)
                assert seating_result['success']
        
        # Verify scheduling success rate
        assert successful_schedules >= 8  # At least 80% success rate
        
        # Generate comprehensive analytics
        analytics = analytics_engine.generate_comprehensive_analytics()
        assert 'overview' in analytics
        assert 'performance_trends' in analytics
        assert 'subject_analysis' in analytics
        
        # Verify database integrity
        assert ExamSession.objects.filter(school=school).count() == 1
        assert Exam.objects.filter(school=school).count() == 15  # 3 classes * 5 subjects
        assert ExamInvigilator.objects.filter(school=school).count() >= 8
        assert ExamSeating.objects.filter(school=school).count() >= 200  # Students seated