"""
Assignment Management System

This module provides comprehensive assignment management functionality including:
- Assignment creation and distribution
- Online submission system
- Grading and feedback tools
- Plagiarism detection features
- Assignment analytics

Requirements fulfilled: 3.6
"""

from django.db import models, transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Avg, Count, Max, Min, Q
from datetime import datetime, timedelta
import logging
import json
from typing import Dict, List, Optional, Tuple
import hashlib
import difflib
import os

from .models import (
    Assignment, AssignmentSubmission, AssignmentGroup, 
    AssignmentAnalytics, ClassSubject, Student
)

logger = logging.getLogger(__name__)


class AssignmentManager:
    """
    Comprehensive assignment management system
    """
    
    def __init__(self, school):
        self.school = school
    
    def create_assignment(self, class_subject, title, description, due_date, **kwargs):
        """
        Create a new assignment with comprehensive configuration
        
        Args:
            class_subject: ClassSubject instance
            title: Assignment title
            description: Assignment description
            due_date: Due date for the assignment
            **kwargs: Additional assignment parameters
        
        Returns:
            Assignment instance
        """
        try:
            with transaction.atomic():
                assignment = Assignment.objects.create(
                    school=self.school,
                    class_subject=class_subject,
                    title=title,
                    description=description,
                    due_date=due_date,
                    **kwargs
                )
                
                logger.info(f"Assignment created: {assignment.title} for {class_subject}")
                return assignment
                
        except Exception as e:
            logger.error(f"Error creating assignment: {str(e)}")
            raise ValidationError(f"Failed to create assignment: {str(e)}")
    
    def publish_assignment(self, assignment_id):
        """
        Publish an assignment and notify students
        
        Args:
            assignment_id: ID of the assignment to publish
        
        Returns:
            bool: Success status
        """
        try:
            assignment = Assignment.objects.get(id=assignment_id, school=self.school)
            assignment.publish()
            
            # Create analytics entry
            self._create_assignment_analytics(assignment)
            
            logger.info(f"Assignment published: {assignment.title}")
            return True
            
        except Assignment.DoesNotExist:
            logger.error(f"Assignment not found: {assignment_id}")
            return False
        except Exception as e:
            logger.error(f"Error publishing assignment: {str(e)}")
            return False
    
    def get_assignment_statistics(self, assignment_id):
        """
        Get comprehensive statistics for an assignment
        
        Args:
            assignment_id: ID of the assignment
        
        Returns:
            dict: Assignment statistics
        """
        try:
            assignment = Assignment.objects.get(id=assignment_id, school=self.school)
            return assignment.get_submission_statistics()
            
        except Assignment.DoesNotExist:
            logger.error(f"Assignment not found: {assignment_id}")
            return {}
    
    def get_overdue_assignments(self, class_subject=None):
        """
        Get all overdue assignments
        
        Args:
            class_subject: Optional ClassSubject to filter by
        
        Returns:
            QuerySet: Overdue assignments
        """
        queryset = Assignment.objects.filter(
            school=self.school,
            due_date__lt=timezone.now(),
            status='published'
        )
        
        if class_subject:
            queryset = queryset.filter(class_subject=class_subject)
        
        return queryset
    
    def get_upcoming_assignments(self, days_ahead=7, class_subject=None):
        """
        Get assignments due in the next specified days
        
        Args:
            days_ahead: Number of days to look ahead
            class_subject: Optional ClassSubject to filter by
        
        Returns:
            QuerySet: Upcoming assignments
        """
        end_date = timezone.now() + timedelta(days=days_ahead)
        queryset = Assignment.objects.filter(
            school=self.school,
            due_date__range=[timezone.now(), end_date],
            status='published'
        )
        
        if class_subject:
            queryset = queryset.filter(class_subject=class_subject)
        
        return queryset.order_by('due_date')
    
    def get_assignment_workload_analysis(self, class_obj, date_range_days=30):
        """
        Analyze assignment workload for a class
        
        Args:
            class_obj: Class instance
            date_range_days: Number of days to analyze
        
        Returns:
            dict: Workload analysis
        """
        start_date = timezone.now()
        end_date = start_date + timedelta(days=date_range_days)
        
        assignments = Assignment.objects.filter(
            school=self.school,
            class_subject__class_obj=class_obj,
            due_date__range=[start_date, end_date],
            status='published'
        )
        
        total_assignments = assignments.count()
        total_estimated_hours = assignments.aggregate(
            total_hours=models.Sum('estimated_duration_hours')
        )['total_hours'] or 0
        
        # Group by week
        weekly_workload = {}
        for assignment in assignments:
            week_start = assignment.due_date.date() - timedelta(
                days=assignment.due_date.weekday()
            )
            week_key = week_start.strftime('%Y-%m-%d')
            
            if week_key not in weekly_workload:
                weekly_workload[week_key] = {
                    'assignments': 0,
                    'estimated_hours': 0,
                    'assignment_list': []
                }
            
            weekly_workload[week_key]['assignments'] += 1
            weekly_workload[week_key]['estimated_hours'] += float(
                assignment.estimated_duration_hours
            )
            weekly_workload[week_key]['assignment_list'].append({
                'title': assignment.title,
                'due_date': assignment.due_date.isoformat(),
                'estimated_hours': float(assignment.estimated_duration_hours)
            })
        
        return {
            'total_assignments': total_assignments,
            'total_estimated_hours': total_estimated_hours,
            'average_hours_per_week': total_estimated_hours / 4 if total_assignments > 0 else 0,
            'weekly_breakdown': weekly_workload
        }
    
    def _create_assignment_analytics(self, assignment):
        """Create initial analytics entry for assignment"""
        AssignmentAnalytics.objects.create(
            school=self.school,
            assignment=assignment,
            analytics_type='assignment',
            data={
                'created_at': assignment.created_at.isoformat(),
                'due_date': assignment.due_date.isoformat(),
                'max_score': float(assignment.max_score),
                'weight_percentage': float(assignment.weight_percentage)
            }
        )


class SubmissionManager:
    """
    Manages assignment submissions and grading
    """
    
    def __init__(self, school):
        self.school = school
    
    def create_submission(self, assignment_id, student_id, **kwargs):
        """
        Create a new assignment submission
        
        Args:
            assignment_id: ID of the assignment
            student_id: ID of the student
            **kwargs: Additional submission parameters
        
        Returns:
            AssignmentSubmission instance
        """
        try:
            assignment = Assignment.objects.get(id=assignment_id, school=self.school)
            student = Student.objects.get(id=student_id, school=self.school)
            
            # Check if submission already exists
            existing_submission = AssignmentSubmission.objects.filter(
                assignment=assignment,
                student=student
            ).first()
            
            if existing_submission:
                if not assignment.allow_resubmission:
                    raise ValidationError("Resubmission not allowed for this assignment")
                
                if existing_submission.submission_count >= assignment.max_resubmissions:
                    raise ValidationError("Maximum resubmissions exceeded")
            
            with transaction.atomic():
                submission = AssignmentSubmission.objects.create(
                    school=self.school,
                    assignment=assignment,
                    student=student,
                    **kwargs
                )
                
                logger.info(f"Submission created: {assignment.title} by {student}")
                return submission
                
        except (Assignment.DoesNotExist, Student.DoesNotExist) as e:
            logger.error(f"Assignment or student not found: {str(e)}")
            raise ValidationError("Assignment or student not found")
        except Exception as e:
            logger.error(f"Error creating submission: {str(e)}")
            raise ValidationError(f"Failed to create submission: {str(e)}")
    
    def submit_assignment(self, submission_id, submission_data):
        """
        Submit an assignment
        
        Args:
            submission_id: ID of the submission
            submission_data: Dictionary containing submission data
        
        Returns:
            bool: Success status
        """
        try:
            submission = AssignmentSubmission.objects.get(
                id=submission_id, 
                school=self.school
            )
            
            # Update submission data
            if 'submission_text' in submission_data:
                submission.submission_text = submission_data['submission_text']
            
            if 'submission_file' in submission_data:
                submission.submission_file = submission_data['submission_file']
            
            # Calculate word count and file size
            if submission.submission_text:
                submission.calculate_word_count()
            
            if submission.submission_file:
                submission.file_size_bytes = submission.submission_file.size
            
            # Submit the assignment
            submission.submit()
            
            logger.info(f"Assignment submitted: {submission.assignment.title} by {submission.student}")
            return True
            
        except AssignmentSubmission.DoesNotExist:
            logger.error(f"Submission not found: {submission_id}")
            return False
        except Exception as e:
            logger.error(f"Error submitting assignment: {str(e)}")
            return False
    
    def grade_submission(self, submission_id, score, feedback='', graded_by=None, rubric_scores=None):
        """
        Grade an assignment submission
        
        Args:
            submission_id: ID of the submission
            score: Score to assign
            feedback: Optional feedback text
            graded_by: User who graded the submission
            rubric_scores: Optional rubric scores dictionary
        
        Returns:
            bool: Success status
        """
        try:
            submission = AssignmentSubmission.objects.get(
                id=submission_id, 
                school=self.school
            )
            
            submission.grade(
                score=score,
                feedback=feedback,
                graded_by=graded_by,
                rubric_scores=rubric_scores
            )
            
            logger.info(f"Submission graded: {submission.assignment.title} by {submission.student}")
            return True
            
        except AssignmentSubmission.DoesNotExist:
            logger.error(f"Submission not found: {submission_id}")
            return False
        except Exception as e:
            logger.error(f"Error grading submission: {str(e)}")
            return False
    
    def get_submissions_for_grading(self, assignment_id=None, teacher=None):
        """
        Get submissions that need grading
        
        Args:
            assignment_id: Optional assignment ID to filter by
            teacher: Optional teacher to filter by their assignments
        
        Returns:
            QuerySet: Submissions needing grading
        """
        queryset = AssignmentSubmission.objects.filter(
            school=self.school,
            status__in=['submitted', 'late', 'resubmitted'],
            is_graded=False
        )
        
        if assignment_id:
            queryset = queryset.filter(assignment_id=assignment_id)
        
        if teacher:
            queryset = queryset.filter(
                assignment__class_subject__teacher=teacher
            )
        
        return queryset.order_by('submitted_at')
    
    def get_student_submissions(self, student_id, assignment_id=None):
        """
        Get submissions for a specific student
        
        Args:
            student_id: ID of the student
            assignment_id: Optional assignment ID to filter by
        
        Returns:
            QuerySet: Student submissions
        """
        queryset = AssignmentSubmission.objects.filter(
            school=self.school,
            student_id=student_id
        )
        
        if assignment_id:
            queryset = queryset.filter(assignment_id=assignment_id)
        
        return queryset.order_by('-submitted_at')
    
    def get_plagiarism_alerts(self, threshold=None):
        """
        Get submissions with high plagiarism scores
        
        Args:
            threshold: Optional custom threshold (uses assignment threshold if not provided)
        
        Returns:
            QuerySet: Submissions with high plagiarism scores
        """
        if threshold:
            queryset = AssignmentSubmission.objects.filter(
                school=self.school,
                plagiarism_score__gte=threshold
            )
        else:
            # Use assignment-specific thresholds
            queryset = AssignmentSubmission.objects.filter(
                school=self.school,
                plagiarism_score__gte=models.F('assignment__plagiarism_threshold')
            )
        
        return queryset.order_by('-plagiarism_score')


class GroupManager:
    """
    Manages assignment groups for group assignments
    """
    
    def __init__(self, school):
        self.school = school
    
    def create_group(self, assignment_id, name, leader_id, member_ids=None):
        """
        Create a new assignment group
        
        Args:
            assignment_id: ID of the assignment
            name: Group name
            leader_id: ID of the group leader
            member_ids: Optional list of member IDs
        
        Returns:
            AssignmentGroup instance
        """
        try:
            assignment = Assignment.objects.get(id=assignment_id, school=self.school)
            leader = Student.objects.get(id=leader_id, school=self.school)
            
            if not assignment.group_assignment:
                raise ValidationError("This assignment does not allow groups")
            
            with transaction.atomic():
                group = AssignmentGroup.objects.create(
                    school=self.school,
                    assignment=assignment,
                    name=name,
                    leader=leader
                )
                
                # Add leader as member
                group.members.add(leader)
                
                # Add additional members if provided
                if member_ids:
                    members = Student.objects.filter(
                        id__in=member_ids, 
                        school=self.school
                    )
                    for member in members:
                        group.add_member(member)
                
                logger.info(f"Group created: {name} for {assignment.title}")
                return group
                
        except (Assignment.DoesNotExist, Student.DoesNotExist) as e:
            logger.error(f"Assignment or student not found: {str(e)}")
            raise ValidationError("Assignment or student not found")
        except Exception as e:
            logger.error(f"Error creating group: {str(e)}")
            raise ValidationError(f"Failed to create group: {str(e)}")
    
    def join_group(self, group_id, student_id):
        """
        Add a student to an existing group
        
        Args:
            group_id: ID of the group
            student_id: ID of the student
        
        Returns:
            bool: Success status
        """
        try:
            group = AssignmentGroup.objects.get(id=group_id, school=self.school)
            student = Student.objects.get(id=student_id, school=self.school)
            
            group.add_member(student)
            
            logger.info(f"Student {student} joined group {group.name}")
            return True
            
        except (AssignmentGroup.DoesNotExist, Student.DoesNotExist) as e:
            logger.error(f"Group or student not found: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error joining group: {str(e)}")
            return False
    
    def leave_group(self, group_id, student_id):
        """
        Remove a student from a group
        
        Args:
            group_id: ID of the group
            student_id: ID of the student
        
        Returns:
            bool: Success status
        """
        try:
            group = AssignmentGroup.objects.get(id=group_id, school=self.school)
            student = Student.objects.get(id=student_id, school=self.school)
            
            if group.leader == student:
                raise ValidationError("Group leader cannot leave the group")
            
            group.remove_member(student)
            
            logger.info(f"Student {student} left group {group.name}")
            return True
            
        except (AssignmentGroup.DoesNotExist, Student.DoesNotExist) as e:
            logger.error(f"Group or student not found: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error leaving group: {str(e)}")
            return False
    
    def finalize_group(self, group_id):
        """
        Finalize a group (lock membership)
        
        Args:
            group_id: ID of the group
        
        Returns:
            bool: Success status
        """
        try:
            group = AssignmentGroup.objects.get(id=group_id, school=self.school)
            group.finalize()
            
            logger.info(f"Group finalized: {group.name}")
            return True
            
        except AssignmentGroup.DoesNotExist:
            logger.error(f"Group not found: {group_id}")
            return False
        except Exception as e:
            logger.error(f"Error finalizing group: {str(e)}")
            return False


class PlagiarismDetector:
    """
    Plagiarism detection system for assignments
    """
    
    def __init__(self, school):
        self.school = school
    
    def check_submission(self, submission_id):
        """
        Run plagiarism check on a submission
        
        Args:
            submission_id: ID of the submission
        
        Returns:
            dict: Plagiarism check results
        """
        try:
            submission = AssignmentSubmission.objects.get(
                id=submission_id, 
                school=self.school
            )
            
            if not submission.assignment.plagiarism_check_enabled:
                return {'error': 'Plagiarism check not enabled for this assignment'}
            
            # Run the plagiarism check
            submission._run_plagiarism_check()
            
            return {
                'plagiarism_score': submission.plagiarism_score,
                'threshold': submission.assignment.plagiarism_threshold,
                'is_flagged': submission.plagiarism_score >= submission.assignment.plagiarism_threshold,
                'similarity_sources': submission.similarity_sources,
                'report': submission.plagiarism_report
            }
            
        except AssignmentSubmission.DoesNotExist:
            logger.error(f"Submission not found: {submission_id}")
            return {'error': 'Submission not found'}
        except Exception as e:
            logger.error(f"Error checking plagiarism: {str(e)}")
            return {'error': f'Plagiarism check failed: {str(e)}'}
    
    def batch_check_assignment(self, assignment_id):
        """
        Run plagiarism check on all submissions for an assignment
        
        Args:
            assignment_id: ID of the assignment
        
        Returns:
            dict: Batch check results
        """
        try:
            assignment = Assignment.objects.get(id=assignment_id, school=self.school)
            
            if not assignment.plagiarism_check_enabled:
                return {'error': 'Plagiarism check not enabled for this assignment'}
            
            submissions = assignment.submissions.filter(
                status__in=['submitted', 'late', 'resubmitted']
            )
            
            results = []
            for submission in submissions:
                submission._run_plagiarism_check()
                results.append({
                    'student': str(submission.student),
                    'submission_id': submission.id,
                    'plagiarism_score': submission.plagiarism_score,
                    'is_flagged': submission.plagiarism_score >= assignment.plagiarism_threshold
                })
            
            # Generate summary
            flagged_count = len([r for r in results if r['is_flagged']])
            
            return {
                'total_checked': len(results),
                'flagged_submissions': flagged_count,
                'flagged_percentage': (flagged_count / len(results) * 100) if results else 0,
                'results': results
            }
            
        except Assignment.DoesNotExist:
            logger.error(f"Assignment not found: {assignment_id}")
            return {'error': 'Assignment not found'}
        except Exception as e:
            logger.error(f"Error in batch plagiarism check: {str(e)}")
            return {'error': f'Batch check failed: {str(e)}'}


class AnalyticsManager:
    """
    Assignment analytics and reporting system
    """
    
    def __init__(self, school):
        self.school = school
    
    def generate_assignment_analytics(self, assignment_id):
        """
        Generate comprehensive analytics for an assignment
        
        Args:
            assignment_id: ID of the assignment
        
        Returns:
            dict: Analytics data
        """
        try:
            assignment = Assignment.objects.get(id=assignment_id, school=self.school)
            
            # Create or update analytics record
            analytics, created = AssignmentAnalytics.objects.get_or_create(
                school=self.school,
                assignment=assignment,
                analytics_type='assignment',
                defaults={'data': {}}
            )
            
            analytics.generate_assignment_analytics()
            
            return analytics.data
            
        except Assignment.DoesNotExist:
            logger.error(f"Assignment not found: {assignment_id}")
            return {}
        except Exception as e:
            logger.error(f"Error generating analytics: {str(e)}")
            return {}
    
    def get_student_performance_analytics(self, student_id, class_subject_id=None):
        """
        Get performance analytics for a student
        
        Args:
            student_id: ID of the student
            class_subject_id: Optional class subject to filter by
        
        Returns:
            dict: Student performance data
        """
        try:
            student = Student.objects.get(id=student_id, school=self.school)
            
            queryset = AssignmentSubmission.objects.filter(
                school=self.school,
                student=student,
                is_graded=True
            )
            
            if class_subject_id:
                queryset = queryset.filter(assignment__class_subject_id=class_subject_id)
            
            if not queryset.exists():
                return {'error': 'No graded submissions found'}
            
            # Calculate statistics
            scores = queryset.values_list('adjusted_score', flat=True)
            percentages = queryset.values_list('percentage', flat=True)
            
            # Assignment type breakdown
            type_performance = {}
            for submission in queryset:
                assignment_type = submission.assignment.assignment_type
                if assignment_type not in type_performance:
                    type_performance[assignment_type] = {
                        'count': 0,
                        'total_score': 0,
                        'average_score': 0
                    }
                
                type_performance[assignment_type]['count'] += 1
                type_performance[assignment_type]['total_score'] += float(submission.adjusted_score)
            
            # Calculate averages
            for type_data in type_performance.values():
                type_data['average_score'] = type_data['total_score'] / type_data['count']
            
            # Submission patterns
            on_time_count = queryset.filter(is_late=False).count()
            late_count = queryset.filter(is_late=True).count()
            
            return {
                'student': str(student),
                'total_assignments': queryset.count(),
                'average_score': sum(scores) / len(scores),
                'average_percentage': sum(percentages) / len(percentages),
                'highest_score': max(scores),
                'lowest_score': min(scores),
                'on_time_submissions': on_time_count,
                'late_submissions': late_count,
                'on_time_percentage': (on_time_count / queryset.count() * 100),
                'assignment_type_performance': type_performance,
                'grade_distribution': self._calculate_grade_distribution(percentages)
            }
            
        except Student.DoesNotExist:
            logger.error(f"Student not found: {student_id}")
            return {'error': 'Student not found'}
        except Exception as e:
            logger.error(f"Error generating student analytics: {str(e)}")
            return {'error': f'Analytics generation failed: {str(e)}'}
    
    def get_class_performance_analytics(self, class_id, assignment_id=None):
        """
        Get performance analytics for a class
        
        Args:
            class_id: ID of the class
            assignment_id: Optional assignment to filter by
        
        Returns:
            dict: Class performance data
        """
        try:
            from students.models import Class
            class_obj = Class.objects.get(id=class_id, school=self.school)
            
            # Get all students in the class
            students = class_obj.students.filter(is_active=True)
            
            queryset = AssignmentSubmission.objects.filter(
                school=self.school,
                student__in=students,
                is_graded=True
            )
            
            if assignment_id:
                queryset = queryset.filter(assignment_id=assignment_id)
            
            if not queryset.exists():
                return {'error': 'No graded submissions found for this class'}
            
            # Calculate class statistics
            scores = queryset.values_list('adjusted_score', flat=True)
            percentages = queryset.values_list('percentage', flat=True)
            
            # Student performance ranking
            student_averages = []
            for student in students:
                student_submissions = queryset.filter(student=student)
                if student_submissions.exists():
                    avg_score = student_submissions.aggregate(
                        avg=Avg('adjusted_score')
                    )['avg']
                    student_averages.append({
                        'student': str(student),
                        'average_score': float(avg_score),
                        'submission_count': student_submissions.count()
                    })
            
            student_averages.sort(key=lambda x: x['average_score'], reverse=True)
            
            return {
                'class': str(class_obj),
                'total_students': students.count(),
                'students_with_submissions': len(student_averages),
                'class_average': sum(scores) / len(scores),
                'class_average_percentage': sum(percentages) / len(percentages),
                'highest_score': max(scores),
                'lowest_score': min(scores),
                'median_score': sorted(scores)[len(scores) // 2],
                'grade_distribution': self._calculate_grade_distribution(percentages),
                'student_rankings': student_averages[:10],  # Top 10
                'submission_statistics': {
                    'total_submissions': queryset.count(),
                    'on_time_submissions': queryset.filter(is_late=False).count(),
                    'late_submissions': queryset.filter(is_late=True).count()
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating class analytics: {str(e)}")
            return {'error': f'Analytics generation failed: {str(e)}'}
    
    def _calculate_grade_distribution(self, percentages):
        """Calculate grade distribution from percentages"""
        distribution = {'A': 0, 'B': 0, 'C': 0, 'D': 0, 'F': 0}
        
        for percentage in percentages:
            if percentage >= 90:
                distribution['A'] += 1
            elif percentage >= 80:
                distribution['B'] += 1
            elif percentage >= 70:
                distribution['C'] += 1
            elif percentage >= 60:
                distribution['D'] += 1
            else:
                distribution['F'] += 1
        
        return distribution


# Convenience functions for easy access
def get_assignment_manager(school):
    """Get an AssignmentManager instance for a school"""
    return AssignmentManager(school)

def get_submission_manager(school):
    """Get a SubmissionManager instance for a school"""
    return SubmissionManager(school)

def get_group_manager(school):
    """Get a GroupManager instance for a school"""
    return GroupManager(school)

def get_plagiarism_detector(school):
    """Get a PlagiarismDetector instance for a school"""
    return PlagiarismDetector(school)

def get_analytics_manager(school):
    """Get an AnalyticsManager instance for a school"""
    return AnalyticsManager(school)