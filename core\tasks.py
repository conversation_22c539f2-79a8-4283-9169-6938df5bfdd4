"""
Celery tasks for core functionality
"""
import logging
from datetime import datetime, timedelta
from celery import shared_task
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from django.core.management import call_command
from .models import AuditLog, School

logger = logging.getLogger(__name__)


@shared_task
def send_email_notification(recipient_email, subject, message, html_message=None):
    """
    Send email notification asynchronously
    """
    try:
        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[recipient_email],
            html_message=html_message,
            fail_silently=False
        )
        logger.info(f"Email sent successfully to {recipient_email}")
        return f"Email sent to {recipient_email}"
    except Exception as e:
        logger.error(f"Failed to send email to {recipient_email}: {str(e)}")
        raise


@shared_task
def send_bulk_email_notification(recipient_emails, subject, message, html_message=None):
    """
    Send bulk email notifications asynchronously
    """
    try:
        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=recipient_emails,
            html_message=html_message,
            fail_silently=False
        )
        logger.info(f"Bulk email sent successfully to {len(recipient_emails)} recipients")
        return f"Bulk email sent to {len(recipient_emails)} recipients"
    except Exception as e:
        logger.error(f"Failed to send bulk email: {str(e)}")
        raise


@shared_task
def send_sms_notification(phone_number, message):
    """
    Send SMS notification asynchronously
    """
    try:
        # Implement SMS sending logic here
        # This is a placeholder - integrate with your SMS provider
        logger.info(f"SMS would be sent to {phone_number}: {message}")
        return f"SMS sent to {phone_number}"
    except Exception as e:
        logger.error(f"Failed to send SMS to {phone_number}: {str(e)}")
        raise


@shared_task
def cleanup_old_logs():
    """
    Clean up old audit logs to prevent database bloat
    """
    try:
        # Delete audit logs older than 1 year
        cutoff_date = timezone.now() - timedelta(days=365)
        deleted_count = AuditLog.objects.filter(timestamp__lt=cutoff_date).delete()[0]
        
        logger.info(f"Cleaned up {deleted_count} old audit log entries")
        return f"Cleaned up {deleted_count} old audit log entries"
    except Exception as e:
        logger.error(f"Failed to cleanup old logs: {str(e)}")
        raise


@shared_task
def backup_database():
    """
    Create database backup
    """
    try:
        # Create database backup using Django management command
        backup_filename = f"backup_{timezone.now().strftime('%Y%m%d_%H%M%S')}.json"
        call_command('dumpdata', '--output', f'backups/{backup_filename}')
        
        logger.info(f"Database backup created: {backup_filename}")
        return f"Database backup created: {backup_filename}"
    except Exception as e:
        logger.error(f"Failed to create database backup: {str(e)}")
        raise


@shared_task
def send_daily_attendance_reports():
    """
    Send daily attendance reports to administrators
    """
    try:
        from academics.models import Attendance
        from hr.models import Employee
        
        schools = School.objects.filter(is_active=True)
        
        for school in schools:
            # Get today's attendance data
            today = timezone.now().date()
            
            # Student attendance
            student_attendance = Attendance.objects.filter(
                school=school,
                date=today,
                student__isnull=False
            )
            
            # Employee attendance
            employee_attendance = Attendance.objects.filter(
                school=school,
                date=today,
                employee__isnull=False
            )
            
            # Get school administrators
            admins = Employee.objects.filter(
                school=school,
                role='ADMIN',
                is_active=True,
                user__email__isnull=False
            )
            
            # Prepare report
            report = f"""
            Daily Attendance Report for {school.name}
            Date: {today}
            
            Student Attendance:
            - Present: {student_attendance.filter(status='present').count()}
            - Absent: {student_attendance.filter(status='absent').count()}
            - Late: {student_attendance.filter(status='late').count()}
            
            Employee Attendance:
            - Present: {employee_attendance.filter(status='present').count()}
            - Absent: {employee_attendance.filter(status='absent').count()}
            - Late: {employee_attendance.filter(status='late').count()}
            """
            
            # Send to administrators
            for admin in admins:
                send_email_notification.delay(
                    admin.user.email,
                    f"Daily Attendance Report - {school.name}",
                    report
                )
        
        logger.info("Daily attendance reports sent successfully")
        return "Daily attendance reports sent successfully"
    except Exception as e:
        logger.error(f"Failed to send daily attendance reports: {str(e)}")
        raise


@shared_task
def generate_report(report_type, school_id, user_id, parameters=None):
    """
    Generate report asynchronously
    """
    try:
        from reports.generators import ReportGenerator
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        school = School.objects.get(id=school_id)
        user = User.objects.get(id=user_id)
        
        # Generate report
        generator = ReportGenerator(report_type, school, parameters or {})
        report_path = generator.generate()
        
        # Notify user when complete
        send_email_notification.delay(
            user.email,
            f"Report Ready - {report_type}",
            f"Your {report_type} report has been generated and is ready for download."
        )
        
        logger.info(f"Report {report_type} generated successfully for school {school.name}")
        return f"Report generated: {report_path}"
    except Exception as e:
        logger.error(f"Failed to generate report {report_type}: {str(e)}")
        raise


@shared_task
def sync_external_data(school_id, data_type):
    """
    Sync data with external systems
    """
    try:
        school = School.objects.get(id=school_id)
        
        # Implement external data sync logic here
        # This is a placeholder for integration with external systems
        
        logger.info(f"External data sync completed for {school.name} - {data_type}")
        return f"External data sync completed for {school.name} - {data_type}"
    except Exception as e:
        logger.error(f"Failed to sync external data: {str(e)}")
        raise


@shared_task
def process_bulk_import(file_path, import_type, school_id, user_id):
    """
    Process bulk data import asynchronously
    """
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        school = School.objects.get(id=school_id)
        user = User.objects.get(id=user_id)
        
        # Implement bulk import logic here
        # This would depend on the specific import type
        
        # Notify user when complete
        send_email_notification.delay(
            user.email,
            f"Bulk Import Complete - {import_type}",
            f"Your {import_type} bulk import has been completed successfully."
        )
        
        logger.info(f"Bulk import {import_type} completed for school {school.name}")
        return f"Bulk import completed: {import_type}"
    except Exception as e:
        logger.error(f"Failed to process bulk import: {str(e)}")
        raise


@shared_task
def calculate_statistics(school_id, stat_type, date_range=None):
    """
    Calculate statistics asynchronously
    """
    try:
        school = School.objects.get(id=school_id)
        
        # Implement statistics calculation logic here
        # This would depend on the specific statistic type
        
        logger.info(f"Statistics calculation {stat_type} completed for school {school.name}")
        return f"Statistics calculated: {stat_type}"
    except Exception as e:
        logger.error(f"Failed to calculate statistics: {str(e)}")
        raise