# Generated by Django 5.2.4 on 2025-07-30 12:18

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "academics",
            "0003_curriculum_exam_curriculumsubject_studentattendance_and_more",
        ),
        ('core', '0002_auditlog_academicyear_created_by_and_more'),
        ('students', '0008_grade_max_age_grade_max_capacity_grade_min_age'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        # Add missing models
        migrations.CreateModel(
            name='CurriculumPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('name', models.CharField(max_length=200, verbose_name='Curriculum Plan Name')),
                ('name_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Curriculum Plan Name (Arabic)')),
                ('code', models.CharField(max_length=20, verbose_name='Curriculum Code')),
                ('curriculum_type', models.CharField(choices=[('national', 'National Curriculum'), ('international', 'International Curriculum'), ('bilingual', 'Bilingual Curriculum'), ('specialized', 'Specialized Program'), ('remedial', 'Remedial Program'), ('advanced', 'Advanced Program')], default='national', max_length=20, verbose_name='Curriculum Type')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('objectives', models.TextField(verbose_name='Learning Objectives')),
                ('total_credit_hours', models.PositiveIntegerField(default=0, verbose_name='Total Credit Hours')),
                ('minimum_credit_hours', models.PositiveIntegerField(default=0, verbose_name='Minimum Credit Hours')),
                ('core_subjects_required', models.PositiveIntegerField(default=0, verbose_name='Core Subjects Required')),
                ('elective_subjects_required', models.PositiveIntegerField(default=0, verbose_name='Elective Subjects Required')),
                ('minimum_gpa_required', models.DecimalField(decimal_places=2, default=2.00, max_digits=4, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(4.00)], verbose_name='Minimum GPA Required')),
                ('duration_years', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(12)], verbose_name='Duration (Years)')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('effective_date', models.DateField(verbose_name='Effective Date')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='Expiry Date')),
                ('prerequisites', models.TextField(blank=True, null=True, verbose_name='General Prerequisites')),
                ('assessment_policy', models.TextField(blank=True, null=True, verbose_name='Assessment Policy')),
                ('progression_rules', models.TextField(blank=True, null=True, verbose_name='Progression Rules')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='Approved At')),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='curriculum_plans', to='core.school', verbose_name='School')),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='curriculum_plans', to='core.academicyear', verbose_name='Academic Year')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_curriculum_plans', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_curriculum_plans', to=settings.AUTH_USER_MODEL, verbose_name='Approved By')),
                ('grades', models.ManyToManyField(related_name='curriculum_plans', to='students.grade', verbose_name='Applicable Grades')),
            ],
            options={
                'verbose_name': 'Curriculum Plan',
                'verbose_name_plural': 'Curriculum Plans',
                'ordering': ['name'],
            },
        ),
        
        migrations.CreateModel(
            name='GradeCapacityManagement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('total_capacity', models.PositiveIntegerField(verbose_name='Total Capacity')),
                ('current_enrollment', models.PositiveIntegerField(default=0, verbose_name='Current Enrollment')),
                ('waiting_list_capacity', models.PositiveIntegerField(default=10, verbose_name='Waiting List Capacity')),
                ('enrollment_start_date', models.DateField(blank=True, null=True, verbose_name='Enrollment Start Date')),
                ('enrollment_end_date', models.DateField(blank=True, null=True, verbose_name='Enrollment End Date')),
                ('is_enrollment_open', models.BooleanField(default=True, verbose_name='Is Enrollment Open')),
                ('minimum_class_size', models.PositiveIntegerField(default=15, verbose_name='Minimum Class Size')),
                ('maximum_class_size', models.PositiveIntegerField(default=30, verbose_name='Maximum Class Size')),
                ('auto_create_sections', models.BooleanField(default=True, verbose_name='Auto Create Sections')),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grade_capacities', to='core.school', verbose_name='School')),
                ('grade', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='capacity_management', to='students.grade', verbose_name='Grade')),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grade_capacities', to='core.academicyear', verbose_name='Academic Year')),
            ],
            options={
                'verbose_name': 'Grade Capacity Management',
                'verbose_name_plural': 'Grade Capacity Management',
            },
        ),
        
        # Add missing fields to existing models
        migrations.AddField(
            model_name='subject',
            name='school',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='subjects', to='core.school', verbose_name='School'),
            preserve_default=False,
        ),
        
        # Add unique constraints
        migrations.AlterUniqueTogether(
            name='curriculumplan',
            unique_together={('school', 'code', 'academic_year')},
        ),
        migrations.AlterUniqueTogether(
            name='gradecapacitymanagement',
            unique_together={('grade', 'academic_year')},
        ),
        migrations.AlterUniqueTogether(
            name='subject',
            unique_together={('school', 'code')},
        ),
    ]
