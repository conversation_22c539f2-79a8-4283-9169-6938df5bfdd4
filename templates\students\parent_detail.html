{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Parent Details" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-user-friends text-primary me-2"></i>{% trans "Parent Details" %}
                            </h2>
                            <p class="text-muted">{% trans "View and manage parent information" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:parent_edit' parent.id %}" class="btn btn-primary">
                                <i class="fas fa-edit me-2"></i>{% trans "Edit Parent" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Parent Information -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Parent Information" %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="fw-bold">{% trans "Father Information" %}</h6>
                                    <p><strong>{% trans "Name:" %}</strong> {{ parent.father_name }}</p>
                                    <p><strong>{% trans "Phone:" %}</strong> {{ parent.father_phone }}</p>
                                    <p><strong>{% trans "Email:" %}</strong> {{ parent.father_email|default:"-" }}</p>
                                    <p><strong>{% trans "Occupation:" %}</strong> {{ parent.father_occupation|default:"-" }}</p>
                                    <p><strong>{% trans "National ID:" %}</strong> {{ parent.father_national_id|default:"-" }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold">{% trans "Mother Information" %}</h6>
                                    <p><strong>{% trans "Name:" %}</strong> {{ parent.mother_name|default:"-" }}</p>
                                    <p><strong>{% trans "Phone:" %}</strong> {{ parent.mother_phone|default:"-" }}</p>
                                    <p><strong>{% trans "Email:" %}</strong> {{ parent.mother_email|default:"-" }}</p>
                                    <p><strong>{% trans "Occupation:" %}</strong> {{ parent.mother_occupation|default:"-" }}</p>
                                    <p><strong>{% trans "National ID:" %}</strong> {{ parent.mother_national_id|default:"-" }}</p>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="fw-bold">{% trans "Contact Information" %}</h6>
                                    <p><strong>{% trans "Home Address:" %}</strong> {{ parent.home_address|default:"-" }}</p>
                                    <p><strong>{% trans "Emergency Contact:" %}</strong> {{ parent.emergency_contact_name|default:"-" }} ({{ parent.emergency_contact_phone|default:"-" }})</p>
                                    <p><strong>{% trans "Preferred Contact Method:" %}</strong> {{ parent.get_preferred_contact_method_display|default:"-" }}</p>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="fw-bold">{% trans "Additional Information" %}</h6>
                                    <p><strong>{% trans "Notes:" %}</strong> {{ parent.notes|default:"-" }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Children List -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Children" %}</h5>
                        </div>
                        <div class="card-body">
                            {% if parent.children.all %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Student" %}</th>
                                                <th>{% trans "ID" %}</th>
                                                <th>{% trans "Class" %}</th>
                                                <th>{% trans "Status" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for child in parent.children.all %}
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                                <span class="text-white fw-bold">{{ child.first_name|slice:":1" }}{{ child.last_name|slice:":1" }}</span>
                                                            </div>
                                                            <div>
                                                                <h6 class="mb-0">{{ child.first_name }} {{ child.last_name }}</h6>
                                                                <small class="text-muted">{{ child.gender_display }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{{ child.student_id }}</td>
                                                    <td>{{ child.current_class.name }}</td>
                                                    <td>
                                                        {% if child.is_active %}
                                                            <span class="badge bg-success">{% trans "Active" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="{% url 'students:detail' child.id %}" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="{% url 'students:edit' child.id %}" class="btn btn-sm btn-outline-secondary">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>{% trans "No children associated with this parent." %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="col-md-4">
                    <!-- Contact Card -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Quick Contact" %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="tel:{{ parent.father_phone }}" class="btn btn-outline-primary">
                                    <i class="fas fa-phone me-2"></i>{% trans "Call Father" %}
                                </a>
                                {% if parent.mother_phone %}
                                    <a href="tel:{{ parent.mother_phone }}" class="btn btn-outline-primary">
                                        <i class="fas fa-phone me-2"></i>{% trans "Call Mother" %}
                                    </a>
                                {% endif %}
                                {% if parent.father_email %}
                                    <a href="mailto:{{ parent.father_email }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-envelope me-2"></i>{% trans "Email Father" %}
                                    </a>
                                {% endif %}
                                {% if parent.mother_email %}
                                    <a href="mailto:{{ parent.mother_email }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-envelope me-2"></i>{% trans "Email Mother" %}
                                    </a>
                                {% endif %}
                                <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#sendMessageModal">
                                    <i class="fas fa-comment-alt me-2"></i>{% trans "Send Message" %}
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Communications -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Recent Communications" %}</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                {% if communications %}
                                    {% for comm in communications %}
                                        <li class="list-group-item">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h6 class="mb-1">{{ comm.subject }}</h6>
                                                    <small class="text-muted">{{ comm.get_type_display }}</small>
                                                </div>
                                                <small>{{ comm.date|date:"d/m/Y" }}</small>
                                            </div>
                                        </li>
                                    {% endfor %}
                                {% else %}
                                    <li class="list-group-item">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-inbox fa-2x mb-2"></i>
                                            <p>{% trans "No recent communications" %}</p>
                                        </div>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Send Message Modal -->
<div class="modal fade" id="sendMessageModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Send Message to" %} {{ parent.father_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="messageSubject" class="form-label">{% trans "Subject" %}</label>
                        <input type="text" class="form-control" id="messageSubject">
                    </div>
                    <div class="mb-3">
                        <label for="messageContent" class="form-label">{% trans "Message" %}</label>
                        <textarea class="form-control" id="messageContent" rows="5"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="messageRecipient" class="form-label">{% trans "Send To" %}</label>
                        <select class="form-select" id="messageRecipient">
                            <option value="father">{% trans "Father" %}</option>
                            {% if parent.mother_phone or parent.mother_email %}
                                <option value="mother">{% trans "Mother" %}</option>
                            {% endif %}
                            <option value="both">{% trans "Both Parents" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="messageMethod" class="form-label">{% trans "Send Via" %}</label>
                        <select class="form-select" id="messageMethod">
                            <option value="app">{% trans "App Notification" %}</option>
                            <option value="email">{% trans "Email" %}</option>
                            <option value="sms">{% trans "SMS" %}</option>
                            <option value="all">{% trans "All Methods" %}</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Send Message" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}