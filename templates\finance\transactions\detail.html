{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Transaction Details" %} - {{ transaction.transaction_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i>
                        {% trans "Transaction Details" %} - {{ transaction.transaction_id }}
                    </h3>
                    <div>
                        <a href="{% url 'finance:transactions_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            {% trans "Back to List" %}
                        </a>
                        {% if transaction.status in 'draft,pending_approval' %}
                            <a href="{% url 'finance:edit_transaction' transaction.pk %}" class="btn btn-primary">
                                <i class="fas fa-edit"></i>
                                {% trans "Edit" %}
                            </a>
                        {% endif %}
                        {% if can_approve %}
                            <a href="{% url 'finance:approve_transaction' transaction.pk %}" class="btn btn-warning">
                                <i class="fas fa-check"></i>
                                {% trans "Approve" %}
                            </a>
                        {% endif %}
                        {% if can_post %}
                            <form method="post" action="{% url 'finance:post_transaction' transaction.pk %}" 
                                  style="display: inline;" onsubmit="return confirm('{% trans "Are you sure you want to post this transaction?" %}')">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-paper-plane"></i>
                                    {% trans "Post Transaction" %}
                                </button>
                            </form>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Transaction Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-info-circle"></i>
                                        {% trans "Transaction Information" %}
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th width="40%">{% trans "Transaction ID" %}:</th>
                                            <td>{{ transaction.transaction_id }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Date" %}:</th>
                                            <td>{{ transaction.transaction_date|date:"Y-m-d" }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Type" %}:</th>
                                            <td>
                                                <span class="badge badge-info">
                                                    {{ transaction.get_transaction_type_display }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Status" %}:</th>
                                            <td>
                                                {% if transaction.status == 'draft' %}
                                                    <span class="badge badge-secondary">{% trans "Draft" %}</span>
                                                {% elif transaction.status == 'pending_approval' %}
                                                    <span class="badge badge-warning">{% trans "Pending Approval" %}</span>
                                                {% elif transaction.status == 'approved' %}
                                                    <span class="badge badge-primary">{% trans "Approved" %}</span>
                                                {% elif transaction.status == 'posted' %}
                                                    <span class="badge badge-success">{% trans "Posted" %}</span>
                                                {% elif transaction.status == 'cancelled' %}
                                                    <span class="badge badge-danger">{% trans "Cancelled" %}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Total Amount" %}:</th>
                                            <td class="font-weight-bold">{{ transaction.total_amount|floatformat:2 }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Reference" %}:</th>
                                            <td>{{ transaction.reference|default:"-" }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Requires Approval" %}:</th>
                                            <td>
                                                {% if transaction.requires_approval %}
                                                    <i class="fas fa-check text-success"></i> {% trans "Yes" %}
                                                {% else %}
                                                    <i class="fas fa-times text-danger"></i> {% trans "No" %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-user"></i>
                                        {% trans "User Information" %}
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th width="40%">{% trans "Created By" %}:</th>
                                            <td>{{ transaction.created_by.get_full_name|default:transaction.created_by.username }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Created At" %}:</th>
                                            <td>{{ transaction.created_at|date:"Y-m-d H:i:s" }}</td>
                                        </tr>
                                        {% if transaction.approved_by %}
                                        <tr>
                                            <th>{% trans "Approved By" %}:</th>
                                            <td>{{ transaction.approved_by.get_full_name|default:transaction.approved_by.username }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Approved At" %}:</th>
                                            <td>{{ transaction.approved_at|date:"Y-m-d H:i:s" }}</td>
                                        </tr>
                                        {% endif %}
                                        {% if transaction.posted_by %}
                                        <tr>
                                            <th>{% trans "Posted By" %}:</th>
                                            <td>{{ transaction.posted_by.get_full_name|default:transaction.posted_by.username }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Posted At" %}:</th>
                                            <td>{{ transaction.posted_at|date:"Y-m-d H:i:s" }}</td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-comment"></i>
                                        {% trans "Description" %}
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p>{{ transaction.description }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Transaction Entries -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-list"></i>
                                        {% trans "Transaction Entries" %}
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>{% trans "Account" %}</th>
                                                    <th>{% trans "Description" %}</th>
                                                    <th>{% trans "Cost Center" %}</th>
                                                    <th class="text-right">{% trans "Debit" %}</th>
                                                    <th class="text-right">{% trans "Credit" %}</th>
                                                    <th>{% trans "Reference" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for entry in entries %}
                                                <tr>
                                                    <td>
                                                        <strong>{{ entry.account.code }}</strong><br>
                                                        <small class="text-muted">{{ entry.account.name }}</small>
                                                    </td>
                                                    <td>{{ entry.description }}</td>
                                                    <td>
                                                        {% if entry.cost_center %}
                                                            {{ entry.cost_center.code }} - {{ entry.cost_center.name }}
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                    <td class="text-right">
                                                        {% if entry.debit_amount > 0 %}
                                                            {{ entry.debit_amount|floatformat:2 }}
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                    <td class="text-right">
                                                        {% if entry.credit_amount > 0 %}
                                                            {{ entry.credit_amount|floatformat:2 }}
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ entry.reference|default:"-" }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                            <tfoot>
                                                <tr class="font-weight-bold">
                                                    <td colspan="3">{% trans "Total" %}</td>
                                                    <td class="text-right">{{ transaction.total_debits|floatformat:2 }}</td>
                                                    <td class="text-right">{{ transaction.total_credits|floatformat:2 }}</td>
                                                    <td></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    
                                    <!-- Balance Check -->
                                    <div class="mt-3">
                                        {% if transaction.is_balanced %}
                                            <div class="alert alert-success">
                                                <i class="fas fa-check-circle"></i>
                                                {% trans "Transaction is balanced" %} ({% trans "Debits" %} = {% trans "Credits" %})
                                            </div>
                                        {% else %}
                                            <div class="alert alert-danger">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                {% trans "Transaction is not balanced" %} ({% trans "Debits" %}: {{ transaction.total_debits|floatformat:2 }}, {% trans "Credits" %}: {{ transaction.total_credits|floatformat:2 }})
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Audit Trail -->
                    {% if audit_trail %}
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-history"></i>
                                        {% trans "Audit Trail" %}
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        {% for log in audit_trail %}
                                        <div class="timeline-item">
                                            <div class="timeline-marker"></div>
                                            <div class="timeline-content">
                                                <h6 class="timeline-title">{{ log.action }}</h6>
                                                <p class="timeline-text">
                                                    {% trans "By" %}: {{ log.user }}<br>
                                                    {% trans "Date" %}: {{ log.timestamp|date:"Y-m-d H:i:s" }}
                                                    {% if log.notes %}
                                                        <br>{% trans "Notes" %}: {{ log.notes }}
                                                    {% endif %}
                                                </p>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #007bff;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #007bff;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: -31px;
    top: 15px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #dee2e6;
}

.timeline-item:last-child:before {
    display: none;
}

.timeline-title {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 0;
    color: #6c757d;
    font-size: 0.9em;
}
</style>
{% endblock %}