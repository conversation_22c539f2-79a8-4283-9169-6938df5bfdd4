{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Journal Entry Interface" %}{% endblock %}

{% block extra_css %}
<style>
.entry-row {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #f8f9fa;
}

.entry-row.is-debit {
    border-left: 4px solid #28a745;
}

.entry-row.is-credit {
    border-left: 4px solid #dc3545;
}

.balance-display {
    font-size: 1.2em;
    font-weight: bold;
}

.balance-equal {
    color: #28a745;
}

.balance-unequal {
    color: #dc3545;
}

.account-select {
    width: 100%;
}

.amount-input {
    text-align: right;
}

.entry-controls {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 10px;
}

#entries-container {
    min-height: 200px;
}

.entry-summary {
    background-color: #e9ecef;
    padding: 15px;
    border-radius: 0.375rem;
    margin-top: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-book"></i>
                        {% trans "Journal Entry Interface" %}
                    </h3>
                    <div>
                        <a href="{% url 'finance:transactions_list' %}" class="btn btn-