from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from django.conf import settings
from django.utils import timezone
from core.models import BaseModel, AcademicYear


class Grade(BaseModel):
    """
    Grade/Level model (e.g., Grade 1, Grade 2, etc.)
    """
    name = models.CharField(
        max_length=50,
        verbose_name=_('Grade Name')
    )

    name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Grade Name (Arabic)')
    )

    level = models.PositiveIntegerField(
        verbose_name=_('Grade Level')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    max_capacity = models.PositiveIntegerField(
        default=30,
        verbose_name=_('Maximum Capacity')
    )

    min_age = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('Minimum Age')
    )

    max_age = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('Maximum Age')
    )

    class Meta:
        verbose_name = _('Grade')
        verbose_name_plural = _('Grades')
        ordering = ['level']

    def __str__(self):
        return self.name


class Class(BaseModel):
    """
    Class/Section model
    """
    name = models.CharField(
        max_length=50,
        verbose_name=_('Class Name')
    )

    grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        related_name='classes',
        verbose_name=_('Grade')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='classes',
        verbose_name=_('Academic Year')
    )

    class_teacher = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_classes',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('Class Teacher')
    )

    max_students = models.PositiveIntegerField(
        default=30,
        verbose_name=_('Maximum Students')
    )

    room_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Room Number')
    )

    class Meta:
        verbose_name = _('Class')
        verbose_name_plural = _('Classes')
        unique_together = ['name', 'grade', 'academic_year']
        ordering = ['grade__level', 'name']

    def __str__(self):
        return f"{self.grade.name} - {self.name}"

    @property
    def current_students_count(self):
        return self.students.filter(is_active=True).count()


class Parent(BaseModel):
    """
    Parent model
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='parent_profile',
        limit_choices_to={'user_type': 'parent'},
        verbose_name=_('User Account')
    )

    father_name = models.CharField(
        max_length=100,
        verbose_name=_('Father Name')
    )

    mother_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Mother Name')
    )

    father_phone = models.CharField(
        max_length=20,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Father Phone')
    )

    mother_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Mother Phone')
    )

    father_occupation = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Father Occupation')
    )

    mother_occupation = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Mother Occupation')
    )

    father_workplace = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Father Workplace')
    )

    mother_workplace = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Mother Workplace')
    )

    home_address = models.TextField(
        verbose_name=_('Home Address')
    )

    emergency_contact = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Emergency Contact')
    )

    emergency_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Emergency Phone')
    )

    class Meta:
        verbose_name = _('Parent')
        verbose_name_plural = _('Parents')

    def __str__(self):
        return f"{self.father_name} - {self.user.username}"

    def clean(self):
        from django.core.exceptions import ValidationError
        import re
        
        # Validate phone number format
        phone_pattern = r'^\+?1?\d{9,15}$'
        
        if self.father_phone and not re.match(phone_pattern, self.father_phone):
            raise ValidationError(_('Father phone number format is invalid.'))
        
        if self.mother_phone and not re.match(phone_pattern, self.mother_phone):
            raise ValidationError(_('Mother phone number format is invalid.'))
        
        if self.emergency_phone and not re.match(phone_pattern, self.emergency_phone):
            raise ValidationError(_('Emergency phone number format is invalid.'))
        
        # Validate that at least one parent name is provided
        if not self.father_name and not self.mother_name:
            raise ValidationError(_('At least one parent name must be provided.'))

    @property
    def primary_contact_name(self):
        """Get the primary contact name"""
        return self.father_name or self.mother_name

    @property
    def primary_contact_phone(self):
        """Get the primary contact phone"""
        return self.father_phone or self.mother_phone

    def get_children_count(self):
        """Get the number of children"""
        return self.children.filter(is_active=True).count()

    def get_active_children(self):
        """Get all active children"""
        return self.children.filter(is_active=True)

    def has_outstanding_fees(self):
        """Check if parent has any outstanding fees"""
        # This would integrate with the finance module
        # For now, return False as placeholder
        return False


class Student(BaseModel):
    """
    Student model
    """
    GENDER_CHOICES = (
        ('M', _('Male')),
        ('F', _('Female')),
    )

    BLOOD_TYPE_CHOICES = (
        ('A+', 'A+'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B-', 'B-'),
        ('AB+', 'AB+'),
        ('AB-', 'AB-'),
        ('O+', 'O+'),
        ('O-', 'O-'),
    )

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='student_profile',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('User Account')
    )

    student_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Student ID')
    )

    admission_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Admission Number')
    )

    first_name = models.CharField(
        max_length=50,
        verbose_name=_('First Name')
    )

    last_name = models.CharField(
        max_length=50,
        verbose_name=_('Last Name')
    )

    first_name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('First Name (Arabic)')
    )

    last_name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Last Name (Arabic)')
    )

    date_of_birth = models.DateField(
        verbose_name=_('Date of Birth')
    )

    gender = models.CharField(
        max_length=1,
        choices=GENDER_CHOICES,
        verbose_name=_('Gender')
    )

    nationality = models.CharField(
        max_length=50,
        verbose_name=_('Nationality')
    )

    national_id = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('National ID')
    )

    passport_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Passport Number')
    )

    blood_type = models.CharField(
        max_length=3,
        choices=BLOOD_TYPE_CHOICES,
        blank=True,
        null=True,
        verbose_name=_('Blood Type')
    )

    parent = models.ForeignKey(
        Parent,
        on_delete=models.CASCADE,
        related_name='children',
        verbose_name=_('Parent')
    )

    current_class = models.ForeignKey(
        Class,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='students',
        verbose_name=_('Current Class')
    )

    admission_date = models.DateField(
        verbose_name=_('Admission Date')
    )

    previous_school = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Previous School')
    )

    medical_conditions = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Medical Conditions')
    )

    allergies = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Allergies')
    )

    special_needs = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Special Needs')
    )

    photo = models.ImageField(
        upload_to='students/photos/',
        blank=True,
        null=True,
        verbose_name=_('Photo')
    )

    is_graduated = models.BooleanField(
        default=False,
        verbose_name=_('Is Graduated')
    )

    graduation_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Graduation Date')
    )

    class Meta:
        verbose_name = _('Student')
        verbose_name_plural = _('Students')
        ordering = ['first_name', 'last_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.student_id})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name_ar(self):
        if self.first_name_ar and self.last_name_ar:
            return f"{self.first_name_ar} {self.last_name_ar}"
        return self.full_name

    @property
    def age(self):
        from datetime import date
        today = date.today()
        return today.year - self.date_of_birth.year - (
            (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
        )

    def clean(self):
        from django.core.exceptions import ValidationError
        from datetime import date, timedelta
        
        # Validate date of birth is not in the future
        if self.date_of_birth and self.date_of_birth > date.today():
            raise ValidationError(_('Date of birth cannot be in the future.'))
        
        # Validate age is reasonable for a student (between 3 and 25 years)
        if self.date_of_birth:
            age = self.age
            if age < 3 or age > 25:
                raise ValidationError(
                    _('Student age must be between 3 and 25 years. Current age: {} years.').format(age)
                )
        
        # Validate admission date is not before date of birth
        if self.date_of_birth and self.admission_date:
            if self.admission_date <= self.date_of_birth:
                raise ValidationError(_('Admission date must be after date of birth.'))
        
        # Validate graduation date is after admission date
        if self.graduation_date and self.admission_date:
            if self.graduation_date <= self.admission_date:
                raise ValidationError(_('Graduation date must be after admission date.'))
        
        # Validate student ID format (should be alphanumeric)
        if self.student_id and not self.student_id.replace('-', '').replace('_', '').isalnum():
            raise ValidationError(_('Student ID must contain only letters, numbers, hyphens, and underscores.'))

    def save(self, *args, **kwargs):
        # Auto-generate student ID if not provided
        if not self.student_id:
            self.student_id = self.generate_student_id()
        
        # Auto-generate admission number if not provided
        if not self.admission_number:
            self.admission_number = self.generate_admission_number()
        
        # Set graduation status based on graduation date
        if self.graduation_date and not self.is_graduated:
            from datetime import date
            if self.graduation_date <= date.today():
                self.is_graduated = True
        
        super().save(*args, **kwargs)

    def generate_student_id(self):
        """Generate a unique student ID"""
        from datetime import date
        import random
        import string
        
        year = date.today().year
        school_code = self.school.code[:3].upper() if self.school.code else 'SCH'
        
        # Generate a random 4-digit number
        random_part = ''.join(random.choices(string.digits, k=4))
        
        base_id = f"{school_code}{year}{random_part}"
        
        # Ensure uniqueness
        counter = 1
        student_id = base_id
        while Student.objects.filter(student_id=student_id).exists():
            student_id = f"{base_id}{counter:02d}"
            counter += 1
        
        return student_id

    def generate_admission_number(self):
        """Generate a unique admission number"""
        from datetime import date
        
        year = self.admission_date.year if self.admission_date else date.today().year
        
        # Get the last admission number for this school and year
        last_student = Student.objects.filter(
            school=self.school,
            admission_date__year=year
        ).exclude(pk=self.pk).order_by('-admission_number').first()
        
        if last_student and last_student.admission_number:
            try:
                # Extract number from admission number (assuming format: YEAR-NNNN)
                last_number = int(last_student.admission_number.split('-')[-1])
                next_number = last_number + 1
            except (ValueError, IndexError):
                next_number = 1
        else:
            next_number = 1
        
        # Ensure uniqueness by checking if the generated number already exists
        admission_number = f"{year}-{next_number:04d}"
        while Student.objects.filter(admission_number=admission_number).exclude(pk=self.pk).exists():
            next_number += 1
            admission_number = f"{year}-{next_number:04d}"
        
        return admission_number

    def get_current_enrollment(self):
        """Get the current active enrollment"""
        return self.enrollments.filter(status='active').first()

    def get_academic_history(self):
        """Get complete academic history"""
        return self.enrollments.all().order_by('-academic_year__start_date')

    def is_enrolled_in_class(self, class_obj, academic_year=None):
        """Check if student is enrolled in a specific class"""
        filters = {'class_obj': class_obj, 'status': 'active'}
        if academic_year:
            filters['academic_year'] = academic_year
        return self.enrollments.filter(**filters).exists()

    def get_total_fees_due(self):
        """Calculate total fees due for the student"""
        # This would integrate with the finance module
        # For now, return 0 as placeholder
        return 0.00

    def get_attendance_percentage(self, academic_year=None):
        """Calculate attendance percentage"""
        # This would integrate with the academics module
        # For now, return 0 as placeholder
        return 0.00


class StudentDocument(BaseModel):
    """
    Student document model for managing student documents
    """
    DOCUMENT_TYPES = (
        ('birth_certificate', _('Birth Certificate')),
        ('passport', _('Passport')),
        ('medical_record', _('Medical Record')),
        ('previous_school_record', _('Previous School Record')),
        ('photo', _('Photo')),
        ('vaccination_record', _('Vaccination Record')),
        ('other', _('Other')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name=_('Student'),
        null=True,
        blank=True
    )

    document_type = models.CharField(
        max_length=30,
        choices=DOCUMENT_TYPES,
        verbose_name=_('Document Type')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('Document Title')
    )

    file = models.FileField(
        upload_to='students/documents/',
        verbose_name=_('Document File')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='uploaded_student_documents',
        verbose_name=_('Uploaded By')
    )

    is_verified = models.BooleanField(
        default=False,
        verbose_name=_('Is Verified')
    )

    verified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_student_documents',
        verbose_name=_('Verified By')
    )

    verified_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Verified At')
    )

    class Meta:
        verbose_name = _('Student Document')
        verbose_name_plural = _('Student Documents')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student.full_name} - {self.title}"

    def clean(self):
        from django.core.exceptions import ValidationError
        import os
        
        # Validate file size (max 10MB)
        if self.file and hasattr(self.file, 'size'):
            if self.file.size > 10 * 1024 * 1024:  # 10MB
                raise ValidationError(_('File size cannot exceed 10MB.'))
        
        # Validate file extension
        if self.file:
            allowed_extensions = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.gif']
            file_extension = os.path.splitext(self.file.name)[1].lower()
            if file_extension not in allowed_extensions:
                raise ValidationError(
                    _('File type not allowed. Allowed types: {}').format(', '.join(allowed_extensions))
                )

    def save(self, *args, **kwargs):
        # Set uploaded_by if not provided
        if not self.uploaded_by_id and hasattr(self, '_current_user'):
            self.uploaded_by = self._current_user
        
        super().save(*args, **kwargs)

    def verify_document(self, verified_by):
        """Mark document as verified"""
        from django.utils import timezone
        self.is_verified = True
        self.verified_by = verified_by
        self.verified_at = timezone.now()
        self.save(update_fields=['is_verified', 'verified_by', 'verified_at'])

    def get_file_size_display(self):
        """Get human-readable file size"""
        if not self.file:
            return "0 bytes"
        
        size = self.file.size
        for unit in ['bytes', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"


class VacationRequest(BaseModel):
    """
    Student vacation request model
    """
    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('cancelled', _('Cancelled')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='vacation_requests',
        verbose_name=_('Student')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    reason = models.TextField(
        verbose_name=_('Reason')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )

    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='requested_vacations',
        verbose_name=_('Requested By')
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_vacations',
        verbose_name=_('Approved By')
    )

    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Approved At')
    )

    rejection_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Rejection Reason')
    )

    class Meta:
        verbose_name = _('Vacation Request')
        verbose_name_plural = _('Vacation Requests')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student.full_name} - {self.start_date} to {self.end_date}"

    @property
    def duration_days(self):
        return (self.end_date - self.start_date).days + 1

    def clean(self):
        from django.core.exceptions import ValidationError
        from datetime import date, timedelta
        
        # Validate end date is after start date
        if self.start_date and self.end_date:
            if self.end_date <= self.start_date:
                raise ValidationError(_('End date must be after start date.'))
        
        # Validate vacation is not too long (max 30 days)
        if self.start_date and self.end_date:
            if self.duration_days > 30:
                raise ValidationError(_('Vacation cannot exceed 30 days.'))
        
        # Validate start date is not in the past (except for admin users)
        if self.start_date and self.start_date < date.today():
            if not (hasattr(self, '_current_user') and 
                   self._current_user and 
                   self._current_user.is_staff):
                raise ValidationError(_('Vacation start date cannot be in the past.'))

    def approve(self, approved_by):
        """Approve the vacation request"""
        from django.utils import timezone
        self.status = 'approved'
        self.approved_by = approved_by
        self.approved_at = timezone.now()
        self.save(update_fields=['status', 'approved_by', 'approved_at'])

    def reject(self, rejected_by, reason):
        """Reject the vacation request"""
        self.status = 'rejected'
        self.approved_by = rejected_by
        self.rejection_reason = reason
        self.save(update_fields=['status', 'approved_by', 'rejection_reason'])

    def cancel(self):
        """Cancel the vacation request"""
        if self.status in ['pending', 'approved']:
            self.status = 'cancelled'
            self.save(update_fields=['status'])
        else:
            raise ValueError(_('Cannot cancel a {} vacation request.').format(self.status))

    def is_overlapping(self, other_request):
        """Check if this vacation overlaps with another"""
        return (self.start_date <= other_request.end_date and 
                self.end_date >= other_request.start_date)

    def send_notification(self, notification_type, recipient=None):
        """Send notification for vacation request status changes"""
        from django.core.mail import send_mail
        from django.template.loader import render_to_string
        from django.conf import settings
        
        if not recipient:
            recipient = self.student.parent.user
        
        # Prepare notification context
        context = {
            'vacation_request': self,
            'student': self.student,
            'parent': self.student.parent,
        }
        
        # Determine email subject and template based on notification type
        if notification_type == 'submitted':
            subject = f"Vacation Request Submitted - {self.student.full_name}"
            template = 'students/emails/vacation_submitted.html'
        elif notification_type == 'approved':
            subject = f"Vacation Request Approved - {self.student.full_name}"
            template = 'students/emails/vacation_approved.html'
        elif notification_type == 'rejected':
            subject = f"Vacation Request Rejected - {self.student.full_name}"
            template = 'students/emails/vacation_rejected.html'
        elif notification_type == 'reminder':
            subject = f"Vacation Reminder - {self.student.full_name}"
            template = 'students/emails/vacation_reminder.html'
        else:
            return False
        
        try:
            # Render email content
            html_content = render_to_string(template, context)
            
            # Send email
            send_mail(
                subject=subject,
                message='',  # Plain text version can be added
                html_message=html_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient.email],
                fail_silently=False,
            )
            
            return True
        except Exception as e:
            # Log the error (in production, use proper logging)
            print(f"Failed to send vacation notification: {e}")
            return False

    def get_overlapping_requests(self):
        """Get other vacation requests that overlap with this one"""
        return VacationRequest.objects.filter(
            student=self.student,
            status__in=['approved', 'pending']
        ).exclude(pk=self.pk).filter(
            start_date__lte=self.end_date,
            end_date__gte=self.start_date
        )

    def get_academic_days_count(self):
        """Calculate the number of academic days in the vacation period"""
        from datetime import timedelta
        
        # This is a simplified calculation
        # In a real system, you would exclude weekends and holidays
        total_days = (self.end_date - self.start_date).days + 1
        
        # Rough estimate: exclude weekends (2 days per week)
        weeks = total_days // 7
        weekend_days = weeks * 2
        
        # Handle remaining days
        remaining_days = total_days % 7
        start_weekday = self.start_date.weekday()  # Monday is 0, Sunday is 6
        
        for i in range(remaining_days):
            day_of_week = (start_weekday + i) % 7
            if day_of_week in [5, 6]:  # Saturday and Sunday
                weekend_days += 1
        
        academic_days = total_days - weekend_days
        return max(0, academic_days)  # Ensure non-negative


class StudentInfraction(BaseModel):
    """
    Student infraction/disciplinary action model
    """
    SEVERITY_CHOICES = (
        ('minor', _('Minor')),
        ('moderate', _('Moderate')),
        ('major', _('Major')),
        ('severe', _('Severe')),
    )

    ACTION_CHOICES = (
        ('warning', _('Warning')),
        ('detention', _('Detention')),
        ('suspension', _('Suspension')),
        ('expulsion', _('Expulsion')),
        ('community_service', _('Community Service')),
        ('parent_meeting', _('Parent Meeting')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='infractions',
        verbose_name=_('Student')
    )

    incident_date = models.DateField(
        verbose_name=_('Incident Date')
    )

    description = models.TextField(
        verbose_name=_('Description')
    )

    severity = models.CharField(
        max_length=20,
        choices=SEVERITY_CHOICES,
        verbose_name=_('Severity')
    )

    action_taken = models.CharField(
        max_length=30,
        choices=ACTION_CHOICES,
        verbose_name=_('Action Taken')
    )

    reported_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='reported_infractions',
        verbose_name=_('Reported By')
    )

    handled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='handled_infractions',
        verbose_name=_('Handled By')
    )

    parent_notified = models.BooleanField(
        default=False,
        verbose_name=_('Parent Notified')
    )

    parent_notified_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Parent Notified At')
    )

    follow_up_required = models.BooleanField(
        default=False,
        verbose_name=_('Follow-up Required')
    )

    follow_up_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Follow-up Date')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Additional Notes')
    )

    class Meta:
        verbose_name = _('Student Infraction')
        verbose_name_plural = _('Student Infractions')
        ordering = ['-incident_date']

    def __str__(self):
        return f"{self.student.full_name} - {self.incident_date} ({self.severity})"

    def clean(self):
        from django.core.exceptions import ValidationError
        from datetime import date
        
        # Validate incident date is not in the future
        if self.incident_date and self.incident_date > date.today():
            raise ValidationError(_('Incident date cannot be in the future.'))
        
        # Validate follow-up date is after incident date
        if self.follow_up_date and self.incident_date:
            if self.follow_up_date <= self.incident_date:
                raise ValidationError(_('Follow-up date must be after incident date.'))

    def save(self, *args, **kwargs):
        # Set reported_by if not provided
        if not self.reported_by_id and hasattr(self, '_current_user'):
            self.reported_by = self._current_user
        
        super().save(*args, **kwargs)

    def notify_parent(self, notified_by):
        """Mark parent as notified"""
        from django.utils import timezone
        self.parent_notified = True
        self.parent_notified_at = timezone.now()
        self.save(update_fields=['parent_notified', 'parent_notified_at'])
        
        # Here you would integrate with the communication system
        # to actually send the notification

    def get_severity_color(self):
        """Get color code for severity level"""
        colors = {
            'minor': '#28a745',    # Green
            'moderate': '#ffc107', # Yellow
            'major': '#fd7e14',    # Orange
            'severe': '#dc3545',   # Red
        }
        return colors.get(self.severity, '#6c757d')

    def requires_immediate_action(self):
        """Check if infraction requires immediate action"""
        return self.severity in ['major', 'severe'] or self.action_taken in ['suspension', 'expulsion']


class StudentTransfer(BaseModel):
    """
    Student transfer model for tracking school transfers
    """
    TRANSFER_TYPES = (
        ('incoming', _('Incoming Transfer')),
        ('outgoing', _('Outgoing Transfer')),
        ('internal', _('Internal Transfer')),
    )

    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('completed', _('Completed')),
        ('rejected', _('Rejected')),
        ('cancelled', _('Cancelled')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='transfers',
        verbose_name=_('Student')
    )

    transfer_type = models.CharField(
        max_length=20,
        choices=TRANSFER_TYPES,
        verbose_name=_('Transfer Type')
    )

    from_school = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('From School')
    )

    to_school = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('To School')
    )

    from_class = models.ForeignKey(
        Class,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='outgoing_transfers',
        verbose_name=_('From Class')
    )

    to_class = models.ForeignKey(
        Class,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='incoming_transfers',
        verbose_name=_('To Class')
    )

    transfer_date = models.DateField(
        verbose_name=_('Transfer Date')
    )

    reason = models.TextField(
        verbose_name=_('Reason for Transfer')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )

    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='requested_transfers',
        verbose_name=_('Requested By')
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_transfers',
        verbose_name=_('Approved By')
    )

    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Approved At')
    )

    documents_transferred = models.BooleanField(
        default=False,
        verbose_name=_('Documents Transferred')
    )

    fees_cleared = models.BooleanField(
        default=False,
        verbose_name=_('Fees Cleared')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Student Transfer')
        verbose_name_plural = _('Student Transfers')
        ordering = ['-transfer_date']

    def __str__(self):
        return f"{self.student.full_name} - {self.transfer_type} ({self.transfer_date})"

    def clean(self):
        from django.core.exceptions import ValidationError
        from datetime import date
        
        # Validate transfer date is not in the past (except for admin users)
        if self.transfer_date and self.transfer_date < date.today():
            if not (hasattr(self, '_current_user') and 
                   self._current_user and 
                   self._current_user.is_staff):
                raise ValidationError(_('Transfer date cannot be in the past.'))
        
        # Validate transfer type specific fields
        if self.transfer_type == 'incoming' and not self.from_school:
            raise ValidationError(_('From school is required for incoming transfers.'))
        
        if self.transfer_type == 'outgoing' and not self.to_school:
            raise ValidationError(_('To school is required for outgoing transfers.'))
        
        if self.transfer_type == 'internal':
            if not self.from_class or not self.to_class:
                raise ValidationError(_('Both from and to classes are required for internal transfers.'))
            if self.from_class == self.to_class:
                raise ValidationError(_('From and to classes cannot be the same for internal transfers.'))

    def save(self, *args, **kwargs):
        # Set requested_by if not provided
        if not self.requested_by_id and hasattr(self, '_current_user'):
            self.requested_by = self._current_user
        
        super().save(*args, **kwargs)

    def approve(self, approved_by):
        """Approve the transfer request"""
        from django.utils import timezone
        self.status = 'approved'
        self.approved_by = approved_by
        self.approved_at = timezone.now()
        self.save(update_fields=['status', 'approved_by', 'approved_at'])

    def complete_transfer(self):
        """Mark transfer as completed"""
        if self.status != 'approved':
            raise ValueError(_('Transfer must be approved before it can be completed.'))
        
        self.status = 'completed'
        self.save(update_fields=['status'])
        
        # Update student's class if internal transfer
        if self.transfer_type == 'internal' and self.to_class:
            self.student.current_class = self.to_class
            self.student.save(update_fields=['current_class'])

    def can_be_completed(self):
        """Check if transfer can be completed"""
        return (self.status == 'approved' and 
                self.documents_transferred and 
                self.fees_cleared)

    def get_checklist_status(self):
        """Get transfer completion checklist status"""
        return {
            'approved': self.status == 'approved',
            'documents_transferred': self.documents_transferred,
            'fees_cleared': self.fees_cleared,
            'can_complete': self.can_be_completed()
        }

    def preserve_academic_history(self):
        """Preserve academic history before transfer"""
        if self.transfer_type in ['outgoing', 'internal']:
            # Get current enrollment
            current_enrollment = self.student.get_current_enrollment()
            
            if current_enrollment:
                # Create academic history record
                academic_history = AcademicHistory.objects.create(
                    school=self.school,
                    student=self.student,
                    academic_year=current_enrollment.academic_year,
                    class_obj=current_enrollment.class_obj,
                    enrollment_date=current_enrollment.enrollment_date,
                    completion_date=self.transfer_date,
                    status='transferred',
                    transfer_related=self
                )
                
                # Preserve current academic status
                academic_history.preserve_current_academic_status()
                
                return academic_history
        
        return None

    def generate_transfer_documents(self, document_types=None):
        """Generate official transfer documents"""
        if self.status != 'approved':
            raise ValueError(_('Transfer must be approved before generating documents.'))
        
        if document_types is None:
            document_types = ['transfer_certificate', 'academic_transcript']
        
        generated_documents = []
        
        for doc_type in document_types:
            document = TransferDocument.objects.create(
                school=self.school,
                transfer=self,
                document_type=doc_type,
                issued_by=self.approved_by
            )
            
            # Generate content
            document.generate_content()
            generated_documents.append(document)
        
        return generated_documents

    def get_transfer_timeline(self):
        """Get transfer timeline with key events"""
        timeline = [
            {
                'date': self.created_at,
                'event': _('Transfer Requested'),
                'user': self.requested_by,
                'description': f"Transfer request created for {self.student.full_name}"
            }
        ]
        
        if self.approved_at:
            timeline.append({
                'date': self.approved_at,
                'event': _('Transfer Approved'),
                'user': self.approved_by,
                'description': f"Transfer approved by {self.approved_by.get_full_name()}"
            })
        
        if self.status == 'completed':
            timeline.append({
                'date': self.transfer_date,
                'event': _('Transfer Completed'),
                'user': self.approved_by,
                'description': f"Transfer completed successfully"
            })
        
        # Add document generation events
        for document in self.documents.all():
            timeline.append({
                'date': document.issue_date,
                'event': _('Document Generated'),
                'user': document.issued_by,
                'description': f"{document.get_document_type_display()} generated"
            })
        
        return sorted(timeline, key=lambda x: x['date'])

    def get_required_approvals(self):
        """Get list of required approvals based on transfer type"""
        approvals = []
        
        if self.transfer_type == 'outgoing':
            approvals.extend([
                _('Principal Approval'),
                _('Finance Clearance'),
                _('Library Clearance'),
                _('Document Preparation'),
            ])
        elif self.transfer_type == 'internal':
            approvals.extend([
                _('Class Teacher Approval'),
                _('Academic Coordinator Approval'),
            ])
        elif self.transfer_type == 'incoming':
            approvals.extend([
                _('Admission Committee Approval'),
                _('Document Verification'),
                _('Class Assignment'),
            ])
        
        return approvals


class StudentEnrollment(BaseModel):
    """
    Student enrollment model for tracking class assignments and academic history
    """
    ENROLLMENT_STATUS = (
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('transferred', _('Transferred')),
        ('graduated', _('Graduated')),
        ('withdrawn', _('Withdrawn')),
        ('suspended', _('Suspended')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='enrollments',
        verbose_name=_('Student')
    )

    class_obj = models.ForeignKey(
        Class,
        on_delete=models.CASCADE,
        related_name='enrollments',
        verbose_name=_('Class')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='student_enrollments',
        verbose_name=_('Academic Year')
    )

    enrollment_date = models.DateField(
        verbose_name=_('Enrollment Date')
    )

    status = models.CharField(
        max_length=20,
        choices=ENROLLMENT_STATUS,
        default='active',
        verbose_name=_('Status')
    )

    roll_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Roll Number')
    )

    seat_number = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        verbose_name=_('Seat Number')
    )

    enrollment_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        verbose_name=_('Enrollment Fee')
    )

    fee_paid = models.BooleanField(
        default=False,
        verbose_name=_('Fee Paid')
    )

    fee_paid_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Fee Paid Date')
    )

    withdrawal_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Withdrawal Date')
    )

    withdrawal_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Withdrawal Reason')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    enrolled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='student_enrollments',
        verbose_name=_('Enrolled By')
    )

    class Meta:
        verbose_name = _('Student Enrollment')
        verbose_name_plural = _('Student Enrollments')
        unique_together = ['student', 'class_obj', 'academic_year']
        ordering = ['-enrollment_date']
        indexes = [
            models.Index(fields=['student', 'status']),
            models.Index(fields=['class_obj', 'status']),
            models.Index(fields=['academic_year', 'status']),
        ]

    def __str__(self):
        return f"{self.student.full_name} - {self.class_obj} ({self.academic_year})"

    def clean(self):
        from django.core.exceptions import ValidationError
        
        # Validate enrollment date is within academic year
        if self.enrollment_date:
            if (self.enrollment_date < self.academic_year.start_date or 
                self.enrollment_date > self.academic_year.end_date):
                raise ValidationError(
                    _('Enrollment date must be within the academic year period.')
                )
        
        # Validate withdrawal date is after enrollment date
        if self.withdrawal_date and self.enrollment_date:
            if self.withdrawal_date <= self.enrollment_date:
                raise ValidationError(
                    _('Withdrawal date must be after enrollment date.')
                )
        
        # Validate class capacity
        if self.status == 'active' and self.class_obj:
            active_enrollments = StudentEnrollment.objects.filter(
                class_obj=self.class_obj,
                academic_year=self.academic_year,
                status='active'
            ).exclude(pk=self.pk).count()
            
            if active_enrollments >= self.class_obj.max_students:
                raise ValidationError(
                    _('Class has reached maximum capacity of {} students.').format(
                        self.class_obj.max_students
                    )
                )

    def save(self, *args, **kwargs):
        # Auto-generate roll number if not provided
        if not self.roll_number and self.status == 'active':
            last_enrollment = StudentEnrollment.objects.filter(
                class_obj=self.class_obj,
                academic_year=self.academic_year
            ).exclude(pk=self.pk).order_by('-roll_number').first()
            
            if last_enrollment and last_enrollment.roll_number:
                try:
                    last_number = int(last_enrollment.roll_number)
                    self.roll_number = str(last_number + 1).zfill(3)
                except ValueError:
                    self.roll_number = '001'
            else:
                self.roll_number = '001'
        
        # Set withdrawal date when status changes to withdrawn
        if self.status in ['withdrawn', 'transferred'] and not self.withdrawal_date:
            from datetime import date
            self.withdrawal_date = date.today()
        
        super().save(*args, **kwargs)
        
        # Update student's current class
        if self.status == 'active':
            self.student.current_class = self.class_obj
            self.student.save(update_fields=['current_class'])

    @property
    def is_active(self):
        return self.status == 'active'

    @property
    def duration_days(self):
        from datetime import date
        end_date = self.withdrawal_date or date.today()
        return (end_date - self.enrollment_date).days


class StudentAttachment(BaseModel):
    """
    Student attachment model for additional files and documents
    """
    ATTACHMENT_TYPES = (
        ('photo', _('Photo')),
        ('document', _('Document')),
        ('certificate', _('Certificate')),
        ('report', _('Report')),
        ('medical', _('Medical Document')),
        ('other', _('Other')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='attachments',
        verbose_name=_('Student')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('Title')
    )

    attachment_type = models.CharField(
        max_length=20,
        choices=ATTACHMENT_TYPES,
        verbose_name=_('Attachment Type')
    )

    file = models.FileField(
        upload_to='students/attachments/',
        verbose_name=_('File')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='uploaded_attachments',
        verbose_name=_('Uploaded By')
    )

    is_public = models.BooleanField(
        default=False,
        verbose_name=_('Is Public')
    )

    class Meta:
        verbose_name = _('Student Attachment')
        verbose_name_plural = _('Student Attachments')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student.full_name} - {self.title}"


class AcademicHistory(BaseModel):
    """
    Academic history model for preserving student academic records during transfers
    """
    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='academic_history',
        verbose_name=_('Student')
    )
    
    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        verbose_name=_('Academic Year')
    )
    
    class_obj = models.ForeignKey(
        Class,
        on_delete=models.CASCADE,
        verbose_name=_('Class')
    )
    
    enrollment_date = models.DateField(
        verbose_name=_('Enrollment Date')
    )
    
    completion_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Completion Date')
    )
    
    status = models.CharField(
        max_length=20,
        choices=[
            ('active', _('Active')),
            ('completed', _('Completed')),
            ('transferred', _('Transferred')),
            ('withdrawn', _('Withdrawn')),
        ],
        default='active',
        verbose_name=_('Status')
    )
    
    final_grade = models.CharField(
        max_length=5,
        blank=True,
        null=True,
        verbose_name=_('Final Grade')
    )
    
    attendance_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Attendance Percentage')
    )
    
    conduct_grade = models.CharField(
        max_length=20,
        choices=[
            ('excellent', _('Excellent')),
            ('very_good', _('Very Good')),
            ('good', _('Good')),
            ('satisfactory', _('Satisfactory')),
            ('needs_improvement', _('Needs Improvement')),
        ],
        blank=True,
        null=True,
        verbose_name=_('Conduct Grade')
    )
    
    achievements = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Achievements and Awards')
    )
    
    disciplinary_actions = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Disciplinary Actions')
    )
    
    teacher_comments = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Teacher Comments')
    )
    
    transfer_related = models.ForeignKey(
        'StudentTransfer',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='academic_records',
        verbose_name=_('Related Transfer')
    )
    
    class Meta:
        verbose_name = _('Academic History')
        verbose_name_plural = _('Academic Histories')
        ordering = ['-academic_year__start_date', '-enrollment_date']
        unique_together = ['student', 'academic_year', 'class_obj']
    
    def __str__(self):
        return f"{self.student.full_name} - {self.class_obj} ({self.academic_year})"
    
    def get_duration_days(self):
        """Calculate the duration of enrollment in days"""
        end_date = self.completion_date or date.today()
        return (end_date - self.enrollment_date).days
    
    def preserve_current_academic_status(self):
        """Preserve current academic status from various sources"""
        # Get attendance data
        if hasattr(self.student, 'academic_attendance'):
            total_days = self.student.academic_attendance.filter(
                class_subject__class_obj=self.class_obj,
                date__range=[self.enrollment_date, self.completion_date or date.today()]
            ).count()
            
            present_days = self.student.academic_attendance.filter(
                class_subject__class_obj=self.class_obj,
                status='present',
                date__range=[self.enrollment_date, self.completion_date or date.today()]
            ).count()
            
            if total_days > 0:
                self.attendance_percentage = (present_days / total_days) * 100
        
        # Get disciplinary records
        infractions = self.student.infractions.filter(
            incident_date__range=[self.enrollment_date, self.completion_date or date.today()]
        )
        
        if infractions.exists():
            self.disciplinary_actions = "; ".join([
                f"{infraction.incident_date}: {infraction.description} ({infraction.get_severity_display()})"
                for infraction in infractions
            ])
        
        # Get achievements (this would be expanded based on actual achievement system)
        # For now, we'll leave it empty to be filled manually
        
        self.save()


class TransferDocument(BaseModel):
    """
    Transfer document model for generating official transfer documents
    """
    DOCUMENT_TYPES = [
        ('transfer_certificate', _('Transfer Certificate')),
        ('academic_transcript', _('Academic Transcript')),
        ('conduct_certificate', _('Conduct Certificate')),
        ('attendance_record', _('Attendance Record')),
        ('medical_record', _('Medical Record')),
        ('disciplinary_record', _('Disciplinary Record')),
    ]
    
    transfer = models.ForeignKey(
        'StudentTransfer',
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name=_('Transfer')
    )
    
    document_type = models.CharField(
        max_length=30,
        choices=DOCUMENT_TYPES,
        verbose_name=_('Document Type')
    )
    
    document_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Document Number')
    )
    
    issue_date = models.DateField(
        auto_now_add=True,
        verbose_name=_('Issue Date')
    )
    
    issued_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='issued_transfer_documents',
        verbose_name=_('Issued By')
    )
    
    content = models.JSONField(
        default=dict,
        verbose_name=_('Document Content'),
        help_text=_('JSON field containing document data')
    )
    
    file_path = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        verbose_name=_('Generated File Path')
    )
    
    is_official = models.BooleanField(
        default=True,
        verbose_name=_('Is Official Document')
    )
    
    verification_code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Verification Code')
    )
    
    class Meta:
        verbose_name = _('Transfer Document')
        verbose_name_plural = _('Transfer Documents')
        ordering = ['-issue_date']
    
    def __str__(self):
        return f"{self.get_document_type_display()} - {self.transfer.student.full_name}"
    
    def save(self, *args, **kwargs):
        if not self.document_number:
            self.document_number = self.generate_document_number()
        
        if not self.verification_code:
            self.verification_code = self.generate_verification_code()
        
        super().save(*args, **kwargs)
    
    def generate_document_number(self):
        """Generate unique document number"""
        from datetime import date
        import random
        import string
        
        year = date.today().year
        doc_type_code = self.document_type[:3].upper()
        random_part = ''.join(random.choices(string.digits, k=4))
        
        base_number = f"{doc_type_code}{year}{random_part}"
        
        # Ensure uniqueness
        counter = 1
        document_number = base_number
        while TransferDocument.objects.filter(document_number=document_number).exists():
            document_number = f"{base_number}{counter:02d}"
            counter += 1
        
        return document_number
    
    def generate_verification_code(self):
        """Generate verification code for document authenticity"""
        import random
        import string
        
        # Generate 8-character alphanumeric code
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        
        # Ensure uniqueness
        while TransferDocument.objects.filter(verification_code=code).exists():
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        
        return code
    
    def generate_content(self):
        """Generate document content based on type"""
        student = self.transfer.student
        
        base_content = {
            'student_name': student.full_name,
            'student_id': student.student_id,
            'admission_number': student.admission_number,
            'date_of_birth': student.date_of_birth.isoformat(),
            'father_name': student.parent.father_name,
            'mother_name': student.parent.mother_name,
            'transfer_date': self.transfer.transfer_date.isoformat(),
            'transfer_type': self.transfer.transfer_type,
            'issue_date': self.issue_date.isoformat(),
            'issued_by': self.issued_by.get_full_name(),
            'verification_code': self.verification_code,
        }
        
        if self.document_type == 'transfer_certificate':
            base_content.update({
                'reason_for_leaving': self.transfer.reason,
                'last_class_attended': str(self.transfer.from_class) if self.transfer.from_class else 'N/A',
                'conduct': 'Good',  # This would come from actual conduct records
                'dues_cleared': 'Yes',  # This would come from finance system
            })
        
        elif self.document_type == 'academic_transcript':
            # Get academic history
            academic_records = student.academic_history.all()
            base_content.update({
                'academic_records': [
                    {
                        'year': record.academic_year.name,
                        'class': str(record.class_obj),
                        'final_grade': record.final_grade or 'N/A',
                        'attendance': f"{record.attendance_percentage}%" if record.attendance_percentage else 'N/A',
                    }
                    for record in academic_records
                ]
            })
        
        elif self.document_type == 'conduct_certificate':
            infractions = student.infractions.all()
            base_content.update({
                'conduct_grade': 'Good',  # This would be calculated
                'disciplinary_actions': len(infractions),
                'character_remarks': 'Student has maintained good discipline throughout the academic period.',
            })
        
        self.content = base_content
        self.save()


class VacationCalendar(BaseModel):
    """
    Vacation calendar model for tracking school holidays and vacation periods
    """
    VACATION_TYPES = [
        ('school_holiday', _('School Holiday')),
        ('public_holiday', _('Public Holiday')),
        ('exam_period', _('Exam Period')),
        ('maintenance', _('Maintenance Period')),
        ('special_event', _('Special Event')),
    ]
    
    name = models.CharField(
        max_length=200,
        verbose_name=_('Vacation/Holiday Name')
    )
    
    vacation_type = models.CharField(
        max_length=20,
        choices=VACATION_TYPES,
        verbose_name=_('Type')
    )
    
    start_date = models.DateField(
        verbose_name=_('Start Date')
    )
    
    end_date = models.DateField(
        verbose_name=_('End Date')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    
    affects_attendance = models.BooleanField(
        default=True,
        verbose_name=_('Affects Attendance'),
        help_text=_('Whether this period affects student attendance calculations')
    )
    
    is_recurring = models.BooleanField(
        default=False,
        verbose_name=_('Is Recurring'),
        help_text=_('Whether this vacation repeats annually')
    )
    
    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='vacation_calendar',
        verbose_name=_('Academic Year')
    )
    
    class Meta:
        verbose_name = _('Vacation Calendar')
        verbose_name_plural = _('Vacation Calendar')
        ordering = ['start_date']
    
    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"
    
    def clean(self):
        from django.core.exceptions import ValidationError
        
        if self.start_date and self.end_date:
            if self.start_date > self.end_date:
                raise ValidationError(_('Start date cannot be after end date.'))
    
    @property
    def duration_days(self):
        """Get the duration of the vacation in days"""
        return (self.end_date - self.start_date).days + 1
    
    def is_active_on_date(self, check_date):
        """Check if this vacation is active on a specific date"""
        return self.start_date <= check_date <= self.end_date
    
    def overlaps_with_request(self, vacation_request):
        """Check if this calendar entry overlaps with a vacation request"""
        return (self.start_date <= vacation_request.end_date and 
                self.end_date >= vacation_request.start_date)


class VacationReport(BaseModel):
    """
    Vacation report model for generating vacation statistics and reports
    """
    REPORT_TYPES = [
        ('monthly', _('Monthly Report')),
        ('quarterly', _('Quarterly Report')),
        ('annual', _('Annual Report')),
        ('custom', _('Custom Period Report')),
    ]
    
    report_type = models.CharField(
        max_length=20,
        choices=REPORT_TYPES,
        verbose_name=_('Report Type')
    )
    
    start_date = models.DateField(
        verbose_name=_('Report Start Date')
    )
    
    end_date = models.DateField(
        verbose_name=_('Report End Date')
    )
    
    generated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='generated_vacation_reports',
        verbose_name=_('Generated By')
    )
    
    report_data = models.JSONField(
        default=dict,
        verbose_name=_('Report Data')
    )
    
    class Meta:
        verbose_name = _('Vacation Report')
        verbose_name_plural = _('Vacation Reports')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.get_report_type_display()} - {self.start_date} to {self.end_date}"
    
    def generate_report_data(self):
        """Generate comprehensive vacation report data"""
        vacation_requests = VacationRequest.objects.filter(
            start_date__gte=self.start_date,
            end_date__lte=self.end_date
        ).select_related('student', 'student__current_class', 'requested_by', 'approved_by')
        
        # Basic statistics
        total_requests = vacation_requests.count()
        approved_requests = vacation_requests.filter(status='approved').count()
        pending_requests = vacation_requests.filter(status='pending').count()
        rejected_requests = vacation_requests.filter(status='rejected').count()
        
        # Calculate total vacation days
        total_vacation_days = sum([
            req.duration_days for req in vacation_requests.filter(status='approved')
        ])
        
        # Group by class
        class_stats = {}
        for request in vacation_requests:
            class_name = str(request.student.current_class) if request.student.current_class else 'No Class'
            if class_name not in class_stats:
                class_stats[class_name] = {
                    'total': 0,
                    'approved': 0,
                    'pending': 0,
                    'rejected': 0,
                    'total_days': 0
                }
            
            class_stats[class_name]['total'] += 1
            class_stats[class_name][request.status] += 1
            
            if request.status == 'approved':
                class_stats[class_name]['total_days'] += request.duration_days
        
        # Group by month
        monthly_stats = {}
        for request in vacation_requests:
            month_key = request.start_date.strftime('%Y-%m')
            if month_key not in monthly_stats:
                monthly_stats[month_key] = {
                    'total': 0,
                    'approved': 0,
                    'total_days': 0
                }
            
            monthly_stats[month_key]['total'] += 1
            if request.status == 'approved':
                monthly_stats[month_key]['approved'] += 1
                monthly_stats[month_key]['total_days'] += request.duration_days
        
        # Top reasons for vacation
        reason_stats = {}
        for request in vacation_requests.filter(status='approved'):
            # Simple keyword extraction from reason
            words = request.reason.lower().split()
            for word in words:
                if len(word) > 3:  # Only consider words longer than 3 characters
                    reason_stats[word] = reason_stats.get(word, 0) + 1
        
        # Sort and get top 10 reasons
        top_reasons = sorted(reason_stats.items(), key=lambda x: x[1], reverse=True)[:10]
        
        self.report_data = {
            'summary': {
                'total_requests': total_requests,
                'approved_requests': approved_requests,
                'pending_requests': pending_requests,
                'rejected_requests': rejected_requests,
                'total_vacation_days': total_vacation_days,
                'approval_rate': (approved_requests / total_requests * 100) if total_requests > 0 else 0,
            },
            'class_breakdown': class_stats,
            'monthly_breakdown': monthly_stats,
            'top_reasons': top_reasons,
            'generated_at': timezone.now().isoformat(),
        }
        
        self.save()
        return self.report_data


class StudentSuspension(BaseModel):
    """
    Student suspension model for managing disciplinary suspensions
    """
    SUSPENSION_TYPES = [
        ('in_school', _('In-School Suspension')),
        ('out_of_school', _('Out-of-School Suspension')),
        ('indefinite', _('Indefinite Suspension')),
    ]

    STATUS_CHOICES = [
        ('active', _('Active')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    ]

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='suspensions',
        verbose_name=_('Student')
    )

    suspension_type = models.CharField(
        max_length=20,
        choices=SUSPENSION_TYPES,
        verbose_name=_('Suspension Type')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('End Date')
    )

    reason = models.TextField(
        verbose_name=_('Reason for Suspension')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name=_('Status')
    )

    issued_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='issued_suspensions',
        verbose_name=_('Issued By')
    )

    parent_notified = models.BooleanField(
        default=False,
        verbose_name=_('Parent Notified')
    )

    parent_notified_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Parent Notified At')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Additional Notes')
    )

    class Meta:
        verbose_name = _('Student Suspension')
        verbose_name_plural = _('Student Suspensions')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.student.full_name} - {self.get_suspension_type_display()} ({self.start_date})"

    @property
    def duration_days(self):
        """Calculate suspension duration in days"""
        if self.end_date:
            return (self.end_date - self.start_date).days + 1
        return None

    @property
    def is_active(self):
        """Check if suspension is currently active"""
        from datetime import date
        today = date.today()
        return (self.status == 'active' and 
                self.start_date <= today and 
                (self.end_date is None or self.end_date >= today))

    def notify_parent(self):
        """Mark parent as notified about suspension"""
        from django.utils import timezone
        self.parent_notified = True
        self.parent_notified_at = timezone.now()
        self.save(update_fields=['parent_notified', 'parent_notified_at'])

    def complete_suspension(self):
        """Mark suspension as completed"""
        self.status = 'completed'
        self.save(update_fields=['status'])

    def cancel_suspension(self, reason=None):
        """Cancel the suspension"""
        self.status = 'cancelled'
        if reason:
            self.notes = f"{self.notes}\n\nCancellation reason: {reason}" if self.notes else f"Cancellation reason: {reason}"
        self.save()


class DisciplinaryAction(BaseModel):
    """
    Model for tracking disciplinary actions and their outcomes
    """
    ACTION_TYPES = [
        ('verbal_warning', _('Verbal Warning')),
        ('written_warning', _('Written Warning')),
        ('detention', _('Detention')),
        ('community_service', _('Community Service')),
        ('parent_conference', _('Parent Conference')),
        ('counseling', _('Counseling')),
        ('suspension', _('Suspension')),
        ('expulsion', _('Expulsion')),
    ]

    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    ]

    infraction = models.ForeignKey(
        StudentInfraction,
        on_delete=models.CASCADE,
        related_name='disciplinary_actions',
        verbose_name=_('Related Infraction')
    )

    action_type = models.CharField(
        max_length=30,
        choices=ACTION_TYPES,
        verbose_name=_('Action Type')
    )

    description = models.TextField(
        verbose_name=_('Action Description')
    )

    assigned_date = models.DateField(
        verbose_name=_('Assigned Date')
    )

    due_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Due Date')
    )

    completed_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Completed Date')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )

    assigned_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='assigned_disciplinary_actions',
        verbose_name=_('Assigned By')
    )

    supervised_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='supervised_disciplinary_actions',
        verbose_name=_('Supervised By')
    )

    completion_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Completion Notes')
    )

    class Meta:
        verbose_name = _('Disciplinary Action')
        verbose_name_plural = _('Disciplinary Actions')
        ordering = ['-assigned_date']

    def __str__(self):
        return f"{self.infraction.student.full_name} - {self.get_action_type_display()}"

    def mark_completed(self, completion_notes=None):
        """Mark the disciplinary action as completed"""
        from datetime import date
        self.status = 'completed'
        self.completed_date = date.today()
        if completion_notes:
            self.completion_notes = completion_notes
        self.save()

    @property
    def is_overdue(self):
        """Check if the action is overdue"""
        from datetime import date
        return (self.due_date and 
                self.due_date < date.today() and 
                self.status not in ['completed', 'cancelled'])


class DisciplineReport(BaseModel):
    """
    Model for generating discipline reports and analytics
    """
    REPORT_TYPES = [
        ('student_summary', _('Student Discipline Summary')),
        ('class_summary', _('Class Discipline Summary')),
        ('school_summary', _('School Discipline Summary')),
        ('infraction_trends', _('Infraction Trends')),
        ('action_effectiveness', _('Action Effectiveness')),
    ]

    report_type = models.CharField(
        max_length=30,
        choices=REPORT_TYPES,
        verbose_name=_('Report Type')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('Report Title')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    filters = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Report Filters')
    )

    report_data = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Report Data')
    )

    generated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='generated_discipline_reports',
        verbose_name=_('Generated By')
    )

    class Meta:
        verbose_name = _('Discipline Report')
        verbose_name_plural = _('Discipline Reports')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} ({self.start_date} - {self.end_date})"

    def generate_report_data(self):
        """Generate comprehensive discipline report data"""
        infractions = StudentInfraction.objects.filter(
            incident_date__gte=self.start_date,
            incident_date__lte=self.end_date,
            school=self.school
        ).select_related('student', 'student__current_class', 'reported_by', 'handled_by')

        # Apply additional filters
        if self.filters.get('student_id'):
            infractions = infractions.filter(student_id=self.filters['student_id'])
        if self.filters.get('class_id'):
            infractions = infractions.filter(student__current_class_id=self.filters['class_id'])
        if self.filters.get('severity'):
            infractions = infractions.filter(severity=self.filters['severity'])

        # Basic statistics
        total_infractions = infractions.count()
        unique_students = infractions.values('student').distinct().count()

        # Severity breakdown
        severity_stats = {}
        for severity, label in StudentInfraction.SEVERITY_CHOICES:
            severity_stats[severity] = {
                'count': infractions.filter(severity=severity).count(),
                'label': label
            }

        # Action breakdown
        action_stats = {}
        for action, label in StudentInfraction.ACTION_CHOICES:
            action_stats[action] = {
                'count': infractions.filter(action_taken=action).count(),
                'label': label
            }

        # Monthly trends
        monthly_stats = {}
        for infraction in infractions:
            month_key = infraction.incident_date.strftime('%Y-%m')
            if month_key not in monthly_stats:
                monthly_stats[month_key] = {
                    'total': 0,
                    'by_severity': {s[0]: 0 for s in StudentInfraction.SEVERITY_CHOICES}
                }
            monthly_stats[month_key]['total'] += 1
            monthly_stats[month_key]['by_severity'][infraction.severity] += 1

        # Class breakdown
        class_stats = {}
        for infraction in infractions:
            class_name = str(infraction.student.current_class) if infraction.student.current_class else 'No Class'
            if class_name not in class_stats:
                class_stats[class_name] = {
                    'total': 0,
                    'unique_students': set(),
                    'by_severity': {s[0]: 0 for s in StudentInfraction.SEVERITY_CHOICES}
                }
            class_stats[class_name]['total'] += 1
            class_stats[class_name]['unique_students'].add(infraction.student.id)
            class_stats[class_name]['by_severity'][infraction.severity] += 1

        # Convert sets to counts for JSON serialization
        for class_name in class_stats:
            class_stats[class_name]['unique_students'] = len(class_stats[class_name]['unique_students'])

        # Repeat offenders
        repeat_offenders = []
        student_infraction_counts = {}
        for infraction in infractions:
            student_id = infraction.student.id
            if student_id not in student_infraction_counts:
                student_infraction_counts[student_id] = {
                    'student_name': infraction.student.full_name,
                    'count': 0,
                    'severities': []
                }
            student_infraction_counts[student_id]['count'] += 1
            student_infraction_counts[student_id]['severities'].append(infraction.severity)

        # Find students with more than 2 infractions
        for student_id, data in student_infraction_counts.items():
            if data['count'] > 2:
                repeat_offenders.append({
                    'student_name': data['student_name'],
                    'infraction_count': data['count'],
                    'most_common_severity': max(set(data['severities']), key=data['severities'].count)
                })

        repeat_offenders.sort(key=lambda x: x['infraction_count'], reverse=True)

        self.report_data = {
            'summary': {
                'total_infractions': total_infractions,
                'unique_students': unique_students,
                'average_infractions_per_student': round(total_infractions / unique_students, 2) if unique_students > 0 else 0,
            },
            'severity_breakdown': severity_stats,
            'action_breakdown': action_stats,
            'monthly_trends': monthly_stats,
            'class_breakdown': class_stats,
            'repeat_offenders': repeat_offenders[:10],  # Top 10
            'generated_at': timezone.now().isoformat(),
        }

        self.save()
        return self.report_data


class ElectronicRegistration(BaseModel):
    """
    Electronic registration model for online student registration
    """
    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('submitted', _('Submitted')),
        ('under_review', _('Under Review')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('completed', _('Completed')),
    )

    # Basic Information
    first_name = models.CharField(
        max_length=50,
        verbose_name=_('First Name')
    )

    last_name = models.CharField(
        max_length=50,
        verbose_name=_('Last Name')
    )

    first_name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('First Name (Arabic)')
    )

    last_name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Last Name (Arabic)')
    )

    date_of_birth = models.DateField(
        verbose_name=_('Date of Birth')
    )

    gender = models.CharField(
        max_length=1,
        choices=Student.GENDER_CHOICES,
        verbose_name=_('Gender')
    )

    nationality = models.CharField(
        max_length=50,
        verbose_name=_('Nationality')
    )

    # Parent Information
    father_name = models.CharField(
        max_length=100,
        verbose_name=_('Father Name')
    )

    father_phone = models.CharField(
        max_length=20,
        verbose_name=_('Father Phone')
    )

    father_email = models.EmailField(
        verbose_name=_('Father Email')
    )

    mother_name = models.CharField(
        max_length=100,
        verbose_name=_('Mother Name')
    )

    mother_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Mother Phone')
    )

    # Address Information
    home_address = models.TextField(
        verbose_name=_('Home Address')
    )

    # Academic Information
    desired_grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        verbose_name=_('Desired Grade')
    )

    previous_school = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Previous School')
    )

    # Registration Status
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('Status')
    )

    submitted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Submitted At')
    )

    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_registrations',
        verbose_name=_('Reviewed By')
    )

    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Reviewed At')
    )

    student = models.OneToOneField(
        Student,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='electronic_registration',
        verbose_name=_('Created Student')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Electronic Registration')
        verbose_name_plural = _('Electronic Registrations')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.desired_grade}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
