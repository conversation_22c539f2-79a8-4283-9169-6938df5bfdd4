{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Class" %} - {{ object.grade.name }} {{ object.name }}
    {% else %}
        {% trans "Add New Class" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-chalkboard text-primary me-2"></i>
                        {% if object %}
                            {% trans "Edit Class" %} - {{ object.grade.name }} {{ object.name }}
                        {% else %}
                            {% trans "Add New Class" %}
                        {% endif %}
                    </h2>
                    <p class="text-muted">
                        {% if object %}
                            {% trans "Update class information" %}
                        {% else %}
                            {% trans "Create a new class in the system" %}
                        {% endif %}
                    </p>
                </div>
            </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.name|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.grade|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.academic_year|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.class_teacher|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.max_students|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.room_number|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="id_is_active" name="is_active" {% if not object or object.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="id_is_active">
                                {% trans "Active" %}
                            </label>
                            <div class="form-text">{% trans "Inactive classes won't appear in most lists and selections" %}</div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% trans "Save Class" %}
                            </button>
                            <a href="{% url 'students:class_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Information" %}</h5>
                </div>
                <div class="card-body">
                    <p>{% trans "Classes represent the different sections within a grade level." %}</p>
                    <p>{% trans "Each class should have:" %}</p>
                    <ul>
                        <li>{% trans "A unique name within its grade" %}</li>
                        <li>{% trans "An assigned grade level" %}</li>
                        <li>{% trans "An academic year" %}</li>
                        <li>{% trans "Optionally, a class teacher" %}</li>
                    </ul>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "After creating a class, you can assign subjects and create a timetable." %}
                    </div>
                </div>
            </div>
            
            {% if object %}
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{% trans "Quick Actions" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{% url 'academics:timetable' %}?class={{ object.id }}" class="btn btn-outline-primary">
                                <i class="fas fa-calendar-week me-2"></i>{% trans "View Timetable" %}
                            </a>
                            <a href="{% url 'students:class_detail' object.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-eye me-2"></i>{% trans "View Class Details" %}
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}