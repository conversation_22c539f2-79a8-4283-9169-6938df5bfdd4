from django import template

register = template.Library()

@register.filter
def split(value, arg):
    """Split a string by the given separator"""
    return value.split(arg)

@register.filter
def get(dictionary, key):
    """Get an item from a dictionary"""
    return dictionary.get(key, [])

@register.filter
def filter_by_time(schedules, time_slot):
    """Filter schedules by time slot"""
    for schedule in schedules:
        start_time_str = schedule.start_time.strftime('%H:%M')
        end_time_str = schedule.end_time.strftime('%H:%M')
        if f"{start_time_str}-{end_time_str}" == time_slot:
            return schedule
    return None