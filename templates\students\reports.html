{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Student Reports" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-chart-bar text-primary me-2"></i>{% trans "Student Reports" %}
                    </h2>
                    <p class="text-muted">{% trans "Generate and view reports about students" %}</p>
                </div>
            </div>

            <!-- Report Categories -->
            <div class="row mb-4">
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-users text-primary me-2"></i>{% trans "Enrollment Reports" %}
                            </h5>
                            <p class="card-text">{% trans "Reports about student enrollment, admissions, and demographics." %}</p>
                            <ul class="list-group list-group-flush mb-3">
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Enrollment by Grade" %}</a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Enrollment by Class" %}</a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Enrollment Trends" %}</a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Demographics Report" %}</a>
                                </li>
                            </ul>
                            <button class="btn btn-primary">
                                <i class="fas fa-file-export me-2"></i>{% trans "Generate Report" %}
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-graduation-cap text-success me-2"></i>{% trans "Academic Reports" %}
                            </h5>
                            <p class="card-text">{% trans "Reports about student academic performance and grades." %}</p>
                            <ul class="list-group list-group-flush mb-3">
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Grade Distribution" %}</a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Subject Performance" %}</a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Class Rankings" %}</a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Progress Reports" %}</a>
                                </li>
                            </ul>
                            <button class="btn btn-success">
                                <i class="fas fa-file-export me-2"></i>{% trans "Generate Report" %}
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-clipboard-check text-info me-2"></i>{% trans "Attendance Reports" %}
                            </h5>
                            <p class="card-text">{% trans "Reports about student attendance and absences." %}</p>
                            <ul class="list-group list-group-flush mb-3">
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Attendance Summary" %}</a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Absence Report" %}</a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Tardiness Report" %}</a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#" class="text-decoration-none">{% trans "Attendance Trends" %}</a>
                                </li>
                            </ul>
                            <button class="btn btn-info">
                                <i class="fas fa-file-export me-2"></i>{% trans "Generate Report" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}  
          <!-- Custom Report Generator -->
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-cogs me-2"></i>{% trans "Custom Report Generator" %}
                            </h5>
                        </div>
                        <div class="card-body">
                            <form>
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="reportType" class="form-label">{% trans "Report Type" %}</label>
                                        <select class="form-select" id="reportType">
                                            <option value="">{% trans "Select Report Type" %}</option>
                                            <option value="enrollment">{% trans "Enrollment Report" %}</option>
                                            <option value="academic">{% trans "Academic Report" %}</option>
                                            <option value="attendance">{% trans "Attendance Report" %}</option>
                                            <option value="behavior">{% trans "Behavior Report" %}</option>
                                            <option value="custom">{% trans "Custom Report" %}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="reportFormat" class="form-label">{% trans "Format" %}</label>
                                        <select class="form-select" id="reportFormat">
                                            <option value="pdf">PDF</option>
                                            <option value="excel">Excel</option>
                                            <option value="csv">CSV</option>
                                            <option value="html">HTML</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="reportPeriod" class="form-label">{% trans "Time Period" %}</label>
                                        <select class="form-select" id="reportPeriod">
                                            <option value="current_year">{% trans "Current Academic Year" %}</option>
                                            <option value="current_semester">{% trans "Current Semester" %}</option>
                                            <option value="last_month">{% trans "Last Month" %}</option>
                                            <option value="custom_range">{% trans "Custom Date Range" %}</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row mb-3" id="dateRangeFields" style="display: none;">
                                    <div class="col-md-6">
                                        <label for="startDate" class="form-label">{% trans "Start Date" %}</label>
                                        <input type="date" class="form-control" id="startDate">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="endDate" class="form-label">{% trans "End Date" %}</label>
                                        <input type="date" class="form-control" id="endDate">
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="gradeFilter" class="form-label">{% trans "Grade" %}</label>
                                        <select class="form-select" id="gradeFilter">
                                            <option value="">{% trans "All Grades" %}</option>
                                            <option value="1">{% trans "Grade 1" %}</option>
                                            <option value="2">{% trans "Grade 2" %}</option>
                                            <option value="3">{% trans "Grade 3" %}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="classFilter" class="form-label">{% trans "Class" %}</label>
                                        <select class="form-select" id="classFilter">
                                            <option value="">{% trans "All Classes" %}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="studentFilter" class="form-label">{% trans "Student" %}</label>
                                        <select class="form-select" id="studentFilter">
                                            <option value="">{% trans "All Students" %}</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-12 text-end">
                                        <button type="button" class="btn btn-secondary me-2">
                                            <i class="fas fa-redo me-2"></i>{% trans "Reset" %}
                                        </button>
                                        <button type="button" class="btn btn-primary">
                                            <i class="fas fa-file-export me-2"></i>{% trans "Generate Report" %}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Reports -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Recent Reports" %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Report Name" %}</th>
                                            <th>{% trans "Type" %}</th>
                                            <th>{% trans "Generated On" %}</th>
                                            <th>{% trans "Generated By" %}</th>
                                            <th>{% trans "Format" %}</th>
                                            <th>{% trans "Actions" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Enrollment Summary 2025</td>
                                            <td>Enrollment</td>
                                            <td>2025-07-15</td>
                                            <td>Admin User</td>
                                            <td>PDF</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Academic Performance Q2</td>
                                            <td>Academic</td>
                                            <td>2025-07-10</td>
                                            <td>Admin User</td>
                                            <td>Excel</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Attendance Summary June</td>
                                            <td>Attendance</td>
                                            <td>2025-07-05</td>
                                            <td>Admin User</td>
                                            <td>PDF</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide date range fields based on period selection
    const reportPeriod = document.getElementById('reportPeriod');
    const dateRangeFields = document.getElementById('dateRangeFields');
    
    reportPeriod.addEventListener('change', function() {
        if (this.value === 'custom_range') {
            dateRangeFields.style.display = 'flex';
        } else {
            dateRangeFields.style.display = 'none';
        }
    });
    
    // Update class options based on selected grade
    const gradeFilter = document.getElementById('gradeFilter');
    const classFilter = document.getElementById('classFilter');
    
    gradeFilter.addEventListener('change', function() {
        // Clear current options
        classFilter.innerHTML = '<option value="">{% trans "All Classes" %}</option>';
        
        // Add classes based on selected grade
        if (this.value) {
            // This would be populated with actual data in a real application
            const classes = {
                '1': ['1-A', '1-B', '1-C'],
                '2': ['2-A', '2-B'],
                '3': ['3-A', '3-B', '3-C']
            };
            
            const gradeClasses = classes[this.value] || [];
            gradeClasses.forEach(function(className, index) {
                const option = document.createElement('option');
                option.value = `${this.value}-${index+1}`;
                option.textContent = className;
                classFilter.appendChild(option);
            }, this);
        }
    });
});
</script>
{% endblock %}