from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML
from crispy_forms.bootstrap import FormActions
from core.models import AcademicYear
from .models import (
    Subject, Teacher, ClassSubject, Schedule, Exam, StudentGrade,
    StudentAttendance, AttendanceSession, Curriculum, CurriculumSubject
)
from students.models import Student, Grade, Class


class AcademicYearForm(forms.ModelForm):
    """
    Academic year form
    """
    class Meta:
        model = AcademicYear
        fields = ['name', 'start_date', 'end_date', 'is_current']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError(_('Start date must be before end date.'))

        return cleaned_data


class SubjectForm(forms.ModelForm):
    """
    Subject form
    """
    class Meta:
        model = Subject
        fields = ['name', 'name_ar', 'code', 'description', 'credit_hours']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class TeacherForm(forms.ModelForm):
    """
    Teacher form
    """
    class Meta:
        model = Teacher
        fields = [
            'user', 'employee_id', 'qualification', 'hire_date',
            'experience_years', 'salary', 'department', 'subjects'
        ]
        widgets = {
            'hire_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'qualification': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'subjects': forms.CheckboxSelectMultiple(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class ClassSubjectForm(forms.ModelForm):
    """
    Class subject form
    """
    class Meta:
        model = ClassSubject
        fields = ['class_obj', 'subject', 'teacher', 'academic_year', 'weekly_hours']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class ScheduleForm(forms.ModelForm):
    """
    Schedule form
    """
    class Meta:
        model = Schedule
        fields = ['class_subject', 'day_of_week', 'start_time', 'end_time', 'room_number']
        widgets = {
            'start_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'end_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        # Filter class_subject to only show active records
        self.fields['class_subject'].queryset = ClassSubject.objects.filter(
            is_active=True
        ).select_related('class_obj', 'subject', 'teacher').order_by(
            'class_obj__grade__level', 'class_obj__name', 'subject__name'
        )

    def clean(self):
        cleaned_data = super().clean()
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')

        if start_time and end_time:
            if start_time >= end_time:
                raise ValidationError(_('Start time must be before end time.'))

        return cleaned_data


class ExamForm(forms.ModelForm):
    """
    Exam form
    """
    class Meta:
        model = Exam
        fields = [
            'name', 'name_ar', 'class_subject', 'exam_type', 'exam_date',
            'start_time', 'end_time', 'duration_minutes', 'total_marks',
            'passing_marks', 'room', 'exam_session', 'instructions'
        ]
        widgets = {
            'exam_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'start_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'end_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'instructions': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Basic Information'),
                Row(
                    Column('name', css_class='form-group col-md-6 mb-3'),
                    Column('name_ar', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('class_subject', css_class='form-group col-md-6 mb-3'),
                    Column('exam_type', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Schedule'),
                Row(
                    Column('exam_date', css_class='form-group col-md-4 mb-3'),
                    Column('start_time', css_class='form-group col-md-4 mb-3'),
                    Column('end_time', css_class='form-group col-md-4 mb-3'),
                ),
                Row(
                    Column('duration_minutes', css_class='form-group col-md-6 mb-3'),
                    Column('room_number', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Grading'),
                Row(
                    Column('total_marks', css_class='form-group col-md-6 mb-3'),
                    Column('passing_marks', css_class='form-group col-md-6 mb-3'),
                ),
                'instructions',
            ),
            FormActions(
                Submit('submit', _('Save Exam'), css_class='btn btn-primary'),
            )
        )


class StudentGradeForm(forms.ModelForm):
    """
    Student grade form
    """
    class Meta:
        model = StudentGrade
        fields = ['student', 'exam', 'marks_obtained', 'remarks']
        widgets = {
            'marks_obtained': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'remarks': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean_marks_obtained(self):
        marks_obtained = self.cleaned_data.get('marks_obtained')
        exam = self.cleaned_data.get('exam')
        
        if marks_obtained and exam and marks_obtained > exam.total_marks:
            raise ValidationError(_('Marks obtained cannot exceed total marks.'))
        
        return marks_obtained


class StudentAttendanceForm(forms.ModelForm):
    """
    Student attendance form
    """
    class Meta:
        model = StudentAttendance
        fields = ['session', 'student', 'class_subject', 'status', 'notes']
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        # Filter class_subject to only show active records
        self.fields['class_subject'].queryset = ClassSubject.objects.filter(
            is_active=True
        ).select_related('class_obj', 'subject', 'teacher').order_by(
            'class_obj__grade__level', 'class_obj__name', 'subject__name'
        )
        
        # Filter students to only show active records
        self.fields['student'].queryset = Student.objects.filter(
            is_active=True
        ).order_by('first_name', 'last_name')
        
        # Filter sessions to only show active sessions
        self.fields['session'].queryset = AttendanceSession.objects.filter(
            status__in=['scheduled', 'active']
        ).select_related('class_subject').order_by('-session_date', '-start_time')


class CurriculumForm(forms.ModelForm):
    """
    Curriculum form
    """
    class Meta:
        model = Curriculum
        fields = ['name', 'name_ar', 'grade', 'academic_year', 'description', 'objectives']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'objectives': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class CurriculumSubjectForm(forms.ModelForm):
    """
    Curriculum subject form
    """
    class Meta:
        model = CurriculumSubject
        fields = [
            'curriculum', 'subject', 'weekly_hours', 'semester',
            'is_mandatory', 'learning_outcomes', 'assessment_criteria'
        ]
        widgets = {
            'learning_outcomes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'assessment_criteria': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        # Filter class_subject to only show active records
        self.fields['class_subject'].queryset = ClassSubject.objects.filter(
            is_active=True
        ).select_related('class_obj', 'subject', 'teacher').order_by(
            'class_obj__grade__level', 'class_obj__name', 'subject__name'
        )


# Bulk attendance form
class BulkAttendanceForm(forms.Form):
    """
    Bulk attendance form for marking attendance for multiple students
    """
    class_subject = forms.ModelChoiceField(
        queryset=ClassSubject.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Class Subject')
    )
    
    date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('Date')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


# Grade entry form for multiple students
class BulkGradeEntryForm(forms.Form):
    """
    Bulk grade entry form for entering grades for multiple students
    """
    exam = forms.ModelChoiceField(
        queryset=Exam.objects.filter(is_published=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Exam')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
