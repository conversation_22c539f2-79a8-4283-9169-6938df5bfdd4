# Generated by Django 5.2.4 on 2025-07-13 17:08

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('finance', '0001_initial'),
        ('students', '0002_electronicregistration_studentattachment_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FinancialYear',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.Char<PERSON>ield(max_length=50, verbose_name='Financial Year Name')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('is_current', models.BooleanField(default=False, verbose_name='Is Current Year')),
                ('is_closed', models.BooleanField(default=False, verbose_name='Is Closed')),
            ],
            options={
                'verbose_name': 'Financial Year',
                'verbose_name_plural': 'Financial Years',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Bank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=200, verbose_name='Bank Name')),
                ('name_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Bank Name (Arabic)')),
                ('account_number', models.CharField(max_length=50, verbose_name='Account Number')),
                ('account_name', models.CharField(max_length=200, verbose_name='Account Name')),
                ('branch', models.CharField(blank=True, max_length=200, null=True, verbose_name='Branch')),
                ('swift_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='SWIFT Code')),
                ('iban', models.CharField(blank=True, max_length=50, null=True, verbose_name='IBAN')),
                ('opening_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Opening Balance')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Current Balance')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bank_accounts', to='finance.account', verbose_name='Linked Account')),
            ],
            options={
                'verbose_name': 'Bank Account',
                'verbose_name_plural': 'Bank Accounts',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CostCenter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Cost Center Code')),
                ('name', models.CharField(max_length=200, verbose_name='Cost Center Name')),
                ('name_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Cost Center Name (Arabic)')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('budget_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Budget Amount')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_cost_centers', to=settings.AUTH_USER_MODEL, verbose_name='Manager')),
            ],
            options={
                'verbose_name': 'Cost Center',
                'verbose_name_plural': 'Cost Centers',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('invoice_number', models.CharField(max_length=50, unique=True, verbose_name='Invoice Number')),
                ('invoice_type', models.CharField(choices=[('tuition', 'Tuition Invoice'), ('fees', 'Fees Invoice'), ('other', 'Other Invoice')], max_length=20, verbose_name='Invoice Type')),
                ('invoice_date', models.DateField(verbose_name='Invoice Date')),
                ('due_date', models.DateField(verbose_name='Due Date')),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Subtotal')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Tax Amount')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Discount Amount')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Total Amount')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent'), ('paid', 'Paid'), ('overdue', 'Overdue'), ('cancelled', 'Cancelled')], default='draft', max_length=20, verbose_name='Status')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_invoices', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='students.student', verbose_name='Student')),
            ],
            options={
                'verbose_name': 'Invoice',
                'verbose_name_plural': 'Invoices',
                'ordering': ['-invoice_date'],
            },
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('description', models.CharField(max_length=200, verbose_name='Description')),
                ('quantity', models.DecimalField(decimal_places=2, default=1, max_digits=10, verbose_name='Quantity')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Unit Price')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Total Price')),
                ('fee_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='finance.feetype', verbose_name='Fee Type')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='finance.invoice', verbose_name='Invoice')),
            ],
            options={
                'verbose_name': 'Invoice Item',
                'verbose_name_plural': 'Invoice Items',
                'ordering': ['invoice', 'id'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('reference_number', models.CharField(max_length=50, unique=True, verbose_name='Reference Number')),
                ('entry_date', models.DateField(verbose_name='Entry Date')),
                ('entry_type', models.CharField(choices=[('manual', 'Manual Entry'), ('automatic', 'Automatic Entry'), ('adjustment', 'Adjustment Entry'), ('closing', 'Closing Entry')], default='manual', max_length=20, verbose_name='Entry Type')),
                ('description', models.TextField(verbose_name='Description')),
                ('debit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Debit Amount')),
                ('credit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Credit Amount')),
                ('is_posted', models.BooleanField(default=False, verbose_name='Is Posted')),
                ('posted_at', models.DateTimeField(blank=True, null=True, verbose_name='Posted At')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='journal_entries', to='finance.account', verbose_name='Account')),
                ('cost_center', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='journal_entries', to='finance.costcenter', verbose_name='Cost Center')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_entries', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posted_entries', to=settings.AUTH_USER_MODEL, verbose_name='Posted By')),
            ],
            options={
                'verbose_name': 'Journal Entry',
                'verbose_name_plural': 'Journal Entries',
                'ordering': ['-entry_date', '-created_at'],
            },
        ),
    ]
