from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML
from crispy_forms.bootstrap import FormActions
from .models import (
    Department, Position, Employee, AttendanceRecord, LeaveType, LeaveRequest,
    PayrollPeriod, Payroll, PerformanceEvaluation, EmployeeDocument
)
from accounts.models import User


class DepartmentForm(forms.ModelForm):
    """
    Department creation and update form
    """
    class Meta:
        model = Department
        fields = ['name', 'name_ar', 'code', 'description', 'head']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class PositionForm(forms.ModelForm):
    """
    Position creation and update form
    """
    class Meta:
        model = Position
        fields = ['title', 'title_ar', 'department', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class EmployeeForm(forms.ModelForm):
    """
    Employee creation and update form
    """
    class Meta:
        model = Employee
        fields = [
            'user', 'employee_id', 'position', 'hire_date', 'termination_date',
            'employment_status', 'salary', 'emergency_contact_name',
            'emergency_contact_phone', 'emergency_contact_relationship'
        ]
        widgets = {
            'hire_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'termination_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'salary': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Basic Information'),
                Row(
                    Column('user', css_class='form-group col-md-6 mb-3'),
                    Column('employee_id', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('position', css_class='form-group col-md-6 mb-3'),
                    Column('employment_status', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Employment Details'),
                Row(
                    Column('hire_date', css_class='form-group col-md-6 mb-3'),
                    Column('termination_date', css_class='form-group col-md-6 mb-3'),
                ),
                'salary',
            ),
            Fieldset(
                _('Emergency Contact'),
                'emergency_contact_name',
                Row(
                    Column('emergency_contact_phone', css_class='form-group col-md-6 mb-3'),
                    Column('emergency_contact_relationship', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            FormActions(
                Submit('submit', _('Save Employee'), css_class='btn btn-primary'),
            )
        )


class AttendanceRecordForm(forms.ModelForm):
    """
    Attendance record form
    """
    class Meta:
        model = AttendanceRecord
        fields = [
            'employee', 'date', 'check_in_time', 'check_out_time',
            'break_start_time', 'break_end_time', 'status', 'notes'
        ]
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'check_in_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'check_out_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'break_start_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'break_end_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class LeaveRequestForm(forms.ModelForm):
    """
    Leave request form
    """
    class Meta:
        model = LeaveRequest
        fields = ['employee', 'leave_type', 'start_date', 'end_date', 'reason', 'attachment']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'attachment': forms.FileInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date > end_date:
                raise ValidationError(_('Start date cannot be after end date.'))

        return cleaned_data


class PayrollForm(forms.ModelForm):
    """
    Payroll form
    """
    class Meta:
        model = Payroll
        fields = [
            'employee', 'period', 'basic_salary', 'allowances', 'overtime_hours',
            'overtime_rate', 'deductions', 'tax_deduction', 'insurance_deduction', 'notes'
        ]
        widgets = {
            'basic_salary': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'allowances': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'overtime_hours': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'overtime_rate': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'deductions': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'tax_deduction': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'insurance_deduction': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class PerformanceEvaluationForm(forms.ModelForm):
    """
    Performance evaluation form
    """
    class Meta:
        model = PerformanceEvaluation
        fields = [
            'employee', 'evaluation_period_start', 'evaluation_period_end',
            'quality_of_work', 'productivity', 'communication', 'teamwork',
            'punctuality', 'initiative', 'overall_rating', 'strengths',
            'areas_for_improvement', 'goals_for_next_period', 'employee_comments'
        ]
        widgets = {
            'evaluation_period_start': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'evaluation_period_end': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'strengths': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'areas_for_improvement': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'goals_for_next_period': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'employee_comments': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class EmployeeDocumentForm(forms.ModelForm):
    """
    Employee document form
    """
    class Meta:
        model = EmployeeDocument
        fields = ['employee', 'document_type', 'title', 'file', 'description', 'is_confidential', 'expiry_date']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'file': forms.FileInput(attrs={'class': 'form-control'}),
            'expiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'


class LeaveTypeForm(forms.ModelForm):
    """
    Leave type form
    """
    class Meta:
        model = LeaveType
        fields = ['name', 'name_ar', 'code', 'max_days_per_year', 'is_paid', 'requires_approval', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class PayrollPeriodForm(forms.ModelForm):
    """
    Payroll period form
    """
    class Meta:
        model = PayrollPeriod
        fields = ['name', 'start_date', 'end_date']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError(_('Start date must be before end date.'))

        return cleaned_data


# Quick attendance form for bulk entry
class QuickAttendanceForm(forms.Form):
    """
    Quick attendance form for marking attendance
    """
    date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('Date')
    )
    
    employees = forms.ModelMultipleChoiceField(
        queryset=Employee.objects.filter(employment_status='active'),
        widget=forms.CheckboxSelectMultiple,
        label=_('Employees')
    )
    
    status = forms.ChoiceField(
        choices=AttendanceRecord._meta.get_field('status').choices,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Status')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
