# Generated by Django 5.2.4 on 2025-07-30 12:23

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0004_auto_20250730_1518"),
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("students", "0008_grade_max_age_grade_max_capacity_grade_min_age"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="curriculumsubject",
            options={
                "ordering": ["curriculum", "sequence_order", "subject"],
                "verbose_name": "Curriculum Subject",
                "verbose_name_plural": "Curriculum Subjects",
            },
        ),
        migrations.AlterUniqueTogether(
            name="classsubject",
            unique_together=set(),
        ),
        migrations.AlterUniqueTogether(
            name="curriculumsubject",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="classsubject",
            name="assistant_teachers",
            field=models.ManyToManyField(
                blank=True,
                help_text="Additional teachers assisting with this subject",
                related_name="assisted_class_subjects",
                to="academics.teacher",
                verbose_name="Assistant Teachers",
            ),
        ),
        migrations.AddField(
            model_name="classsubject",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="classsubject",
            name="end_date",
            field=models.DateField(blank=True, null=True, verbose_name="End Date"),
        ),
        migrations.AddField(
            model_name="classsubject",
            name="grading_scale",
            field=models.JSONField(
                blank=True,
                default=dict,
                help_text="Custom grading scale for this class-subject combination",
                verbose_name="Grading Scale",
            ),
        ),
        migrations.AddField(
            model_name="classsubject",
            name="max_students",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Override default subject capacity for this class",
                null=True,
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(100),
                ],
                verbose_name="Maximum Students",
            ),
        ),
        migrations.AddField(
            model_name="classsubject",
            name="requires_special_setup",
            field=models.BooleanField(
                default=False,
                help_text="Whether this class requires special room setup",
                verbose_name="Requires Special Setup",
            ),
        ),
        migrations.AddField(
            model_name="classsubject",
            name="room_preference",
            field=models.CharField(
                blank=True,
                help_text="Preferred room for this subject",
                max_length=50,
                null=True,
                verbose_name="Room Preference",
            ),
        ),
        migrations.AddField(
            model_name="classsubject",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="classsubject",
            name="semester",
            field=models.ForeignKey(
                default="",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="class_subjects",
                to="core.semester",
                verbose_name="Semester",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="classsubject",
            name="setup_requirements",
            field=models.TextField(
                blank=True,
                help_text="Special setup requirements for this class",
                null=True,
                verbose_name="Setup Requirements",
            ),
        ),
        migrations.AddField(
            model_name="classsubject",
            name="start_date",
            field=models.DateField(blank=True, null=True, verbose_name="Start Date"),
        ),
        migrations.AddField(
            model_name="classsubject",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="curriculum",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="curriculum",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="curriculumplan",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="applicable_grades",
            field=models.ManyToManyField(
                help_text="Grades where this subject is taught in this curriculum",
                related_name="curriculum_subject_mappings",
                to="students.grade",
                verbose_name="Applicable Grades",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="assessment_breakdown",
            field=models.JSONField(
                blank=True,
                default=dict,
                help_text='Breakdown of assessment components (e.g., {"midterm": 30, "final": 40, "assignments": 30})',
                verbose_name="Assessment Breakdown",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="credit_hours",
            field=models.PositiveIntegerField(
                default=1,
                help_text="Credit hours for this subject in this curriculum",
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(10),
                ],
                verbose_name="Credit Hours",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="digital_resources",
            field=models.TextField(
                blank=True,
                help_text="Digital resources and online materials",
                null=True,
                verbose_name="Digital Resources",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="effective_date",
            field=models.DateField(
                blank=True, null=True, verbose_name="Effective Date"
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="minimum_passing_grade",
            field=models.DecimalField(
                decimal_places=2,
                default=60.0,
                help_text="Minimum grade required to pass this subject in this curriculum",
                max_digits=5,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
                verbose_name="Minimum Passing Grade",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="notes",
            field=models.TextField(
                blank=True,
                help_text="Additional notes or special instructions",
                null=True,
                verbose_name="Notes",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="required_resources",
            field=models.TextField(
                blank=True,
                help_text="Resources required to teach this subject effectively",
                null=True,
                verbose_name="Required Resources",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="sequence_order",
            field=models.PositiveIntegerField(
                default=1,
                help_text="Order in which this subject should be taught",
                verbose_name="Sequence Order",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="teacher_qualifications",
            field=models.TextField(
                blank=True,
                help_text="Required qualifications for teachers of this subject",
                null=True,
                verbose_name="Teacher Qualifications",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="textbooks",
            field=models.TextField(
                blank=True,
                help_text="Required and recommended textbooks",
                null=True,
                verbose_name="Textbooks",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="curriculumsubject",
            name="weight_in_gpa",
            field=models.DecimalField(
                decimal_places=2,
                default=1.0,
                help_text="Weight of this subject in overall GPA calculation",
                max_digits=4,
                validators=[
                    django.core.validators.MinValueValidator(0.1),
                    django.core.validators.MaxValueValidator(5.0),
                ],
                verbose_name="Weight in GPA",
            ),
        ),
        migrations.AddField(
            model_name="exam",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="exam",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="gradecapacitymanagement",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="gradecapacitymanagement",
            name="is_active",
            field=models.BooleanField(default=True, verbose_name="Is Active"),
        ),
        migrations.AddField(
            model_name="gradecapacitymanagement",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="schedule",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="schedule",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="schedule",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="assessment_methods",
            field=models.TextField(
                blank=True,
                help_text="Methods used to assess student performance in this subject",
                null=True,
                verbose_name="Assessment Methods",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="corequisites",
            field=models.ManyToManyField(
                blank=True,
                help_text="Subjects that must be taken simultaneously with this subject",
                related_name="corequisite_subjects",
                to="academics.subject",
                verbose_name="Corequisites",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="equipment_requirements",
            field=models.TextField(
                blank=True,
                help_text="List of special equipment or facilities required",
                null=True,
                verbose_name="Equipment Requirements",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="learning_objectives",
            field=models.TextField(
                blank=True,
                help_text="Key learning objectives for this subject",
                null=True,
                verbose_name="Learning Objectives",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="max_students_per_class",
            field=models.PositiveIntegerField(
                default=30,
                help_text="Maximum number of students allowed in a class for this subject",
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(100),
                ],
                verbose_name="Maximum Students per Class",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="minimum_grade_required",
            field=models.DecimalField(
                decimal_places=2,
                default=60.0,
                help_text="Minimum grade percentage required to pass this subject",
                max_digits=5,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
                verbose_name="Minimum Grade Required",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="prerequisites",
            field=models.ManyToManyField(
                blank=True,
                help_text="Subjects that must be completed before taking this subject",
                related_name="dependent_subjects",
                to="academics.subject",
                verbose_name="Prerequisites",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="reference_materials",
            field=models.TextField(
                blank=True,
                help_text="Additional reference materials for this subject",
                null=True,
                verbose_name="Reference Materials",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="requires_lab",
            field=models.BooleanField(
                default=False,
                help_text="Whether this subject requires laboratory facilities",
                verbose_name="Requires Laboratory",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="requires_special_equipment",
            field=models.BooleanField(
                default=False,
                help_text="Whether this subject requires special equipment or facilities",
                verbose_name="Requires Special Equipment",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="subject_type",
            field=models.CharField(
                choices=[
                    ("core", "Core Subject"),
                    ("elective", "Elective"),
                    ("language", "Language"),
                    ("science", "Science"),
                    ("mathematics", "Mathematics"),
                    ("arts", "Arts"),
                    ("physical_education", "Physical Education"),
                    ("religious", "Religious Studies"),
                ],
                default="core",
                max_length=20,
                verbose_name="Subject Type",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="textbook_required",
            field=models.CharField(
                blank=True,
                help_text="Required textbook for this subject",
                max_length=200,
                null=True,
                verbose_name="Required Textbook",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="weekly_hours",
            field=models.PositiveIntegerField(
                default=1,
                help_text="Number of hours per week for this subject",
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(20),
                ],
                verbose_name="Weekly Hours",
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="teacher",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AlterField(
            model_name="classsubject",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="classsubject",
            name="teacher",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="class_subjects",
                to="academics.teacher",
                verbose_name="Primary Teacher",
            ),
        ),
        migrations.AlterField(
            model_name="classsubject",
            name="weekly_hours",
            field=models.PositiveIntegerField(
                default=1,
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(20),
                ],
                verbose_name="Weekly Hours",
            ),
        ),
        migrations.AlterField(
            model_name="curriculum",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="assessment_policy",
            field=models.TextField(
                blank=True,
                help_text="Assessment and grading policy for this curriculum",
                null=True,
                verbose_name="Assessment Policy",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="core_subjects_required",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Number of core subjects that must be completed",
                verbose_name="Core Subjects Required",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="duration_years",
            field=models.PositiveIntegerField(
                default=1,
                help_text="Expected duration to complete this curriculum",
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(12),
                ],
                verbose_name="Duration (Years)",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="effective_date",
            field=models.DateField(
                help_text="Date when this curriculum becomes effective",
                verbose_name="Effective Date",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="elective_subjects_required",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Number of elective subjects that must be completed",
                verbose_name="Elective Subjects Required",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="expiry_date",
            field=models.DateField(
                blank=True,
                help_text="Date when this curriculum expires (optional)",
                null=True,
                verbose_name="Expiry Date",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="minimum_credit_hours",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Minimum credit hours required for progression",
                verbose_name="Minimum Credit Hours",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="minimum_gpa_required",
            field=models.DecimalField(
                decimal_places=2,
                default=2.0,
                help_text="Minimum GPA required for curriculum completion",
                max_digits=4,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(4.0),
                ],
                verbose_name="Minimum GPA Required",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="objectives",
            field=models.TextField(
                help_text="Overall learning objectives for this curriculum",
                verbose_name="Learning Objectives",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="prerequisites",
            field=models.TextField(
                blank=True,
                help_text="General prerequisites for enrollment in this curriculum",
                null=True,
                verbose_name="General Prerequisites",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="progression_rules",
            field=models.TextField(
                blank=True,
                help_text="Rules for student progression through the curriculum",
                null=True,
                verbose_name="Progression Rules",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="school",
            field=models.ForeignKey(
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumplan",
            name="total_credit_hours",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Total credit hours required for completion",
                verbose_name="Total Credit Hours",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumsubject",
            name="assessment_criteria",
            field=models.TextField(
                default="",
                help_text="Assessment criteria and methods for this subject",
                verbose_name="Assessment Criteria",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="curriculumsubject",
            name="curriculum",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="curriculum_subjects",
                to="academics.curriculumplan",
                verbose_name="Curriculum Plan",
            ),
        ),
        migrations.AlterField(
            model_name="curriculumsubject",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="curriculumsubject",
            name="learning_outcomes",
            field=models.TextField(
                default="",
                help_text="Specific learning outcomes for this subject in this curriculum",
                verbose_name="Learning Outcomes",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="curriculumsubject",
            name="weekly_hours",
            field=models.PositiveIntegerField(
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(20),
                ],
                verbose_name="Weekly Hours",
            ),
        ),
        migrations.AlterField(
            model_name="exam",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="gradecapacitymanagement",
            name="auto_create_sections",
            field=models.BooleanField(
                default=True,
                help_text="Automatically create new sections when capacity is reached",
                verbose_name="Auto Create Sections",
            ),
        ),
        migrations.AlterField(
            model_name="gradecapacitymanagement",
            name="current_enrollment",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Current number of enrolled students",
                verbose_name="Current Enrollment",
            ),
        ),
        migrations.AlterField(
            model_name="gradecapacitymanagement",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="gradecapacitymanagement",
            name="maximum_class_size",
            field=models.PositiveIntegerField(
                default=30,
                help_text="Maximum number of students allowed per class section",
                verbose_name="Maximum Class Size",
            ),
        ),
        migrations.AlterField(
            model_name="gradecapacitymanagement",
            name="minimum_class_size",
            field=models.PositiveIntegerField(
                default=15,
                help_text="Minimum number of students required to form a class",
                verbose_name="Minimum Class Size",
            ),
        ),
        migrations.AlterField(
            model_name="gradecapacitymanagement",
            name="school",
            field=models.ForeignKey(
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
        ),
        migrations.AlterField(
            model_name="gradecapacitymanagement",
            name="total_capacity",
            field=models.PositiveIntegerField(
                help_text="Maximum number of students that can be enrolled in this grade",
                verbose_name="Total Capacity",
            ),
        ),
        migrations.AlterField(
            model_name="gradecapacitymanagement",
            name="waiting_list_capacity",
            field=models.PositiveIntegerField(
                default=10,
                help_text="Maximum number of students allowed on waiting list",
                verbose_name="Waiting List Capacity",
            ),
        ),
        migrations.AlterField(
            model_name="schedule",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="studentattendance",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="studentgrade",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="subject",
            name="code",
            field=models.CharField(max_length=20, verbose_name="Subject Code"),
        ),
        migrations.AlterField(
            model_name="subject",
            name="credit_hours",
            field=models.PositiveIntegerField(
                default=1,
                help_text="Number of credit hours for this subject (1-10)",
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(10),
                ],
                verbose_name="Credit Hours",
            ),
        ),
        migrations.AlterField(
            model_name="subject",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="subject",
            name="school",
            field=models.ForeignKey(
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterUniqueTogether(
            name="classsubject",
            unique_together={("class_obj", "subject", "academic_year", "semester")},
        ),
        migrations.AlterUniqueTogether(
            name="curriculumsubject",
            unique_together={("curriculum", "subject", "semester")},
        ),
        migrations.AddIndex(
            model_name="classsubject",
            index=models.Index(
                fields=["school", "academic_year", "semester"],
                name="academics_c_school__239ef9_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="classsubject",
            index=models.Index(
                fields=["teacher", "academic_year"],
                name="academics_c_teacher_621f92_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="classsubject",
            index=models.Index(
                fields=["subject", "is_active"], name="academics_c_subject_289749_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="curriculumplan",
            index=models.Index(
                fields=["school", "curriculum_type"],
                name="academics_c_school__b2066b_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="curriculumplan",
            index=models.Index(
                fields=["school", "is_active"], name="academics_c_school__e90327_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="curriculumplan",
            index=models.Index(
                fields=["academic_year", "is_active"],
                name="academics_c_academi_677d4d_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="curriculumsubject",
            index=models.Index(
                fields=["curriculum", "is_mandatory"],
                name="academics_c_curricu_2986c1_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="curriculumsubject",
            index=models.Index(
                fields=["curriculum", "semester"], name="academics_c_curricu_3e2db0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="curriculumsubject",
            index=models.Index(
                fields=["subject", "is_active"], name="academics_c_subject_e30444_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="gradecapacitymanagement",
            index=models.Index(
                fields=["school", "academic_year"],
                name="academics_g_school__4add02_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="gradecapacitymanagement",
            index=models.Index(
                fields=["grade", "is_enrollment_open"],
                name="academics_g_grade_i_ab27a0_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="subject",
            index=models.Index(
                fields=["school", "subject_type"], name="academics_s_school__a5fa1a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="subject",
            index=models.Index(
                fields=["school", "is_mandatory"], name="academics_s_school__40f7b3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="subject",
            index=models.Index(fields=["code"], name="academics_s_code_3689e5_idx"),
        ),
    ]
