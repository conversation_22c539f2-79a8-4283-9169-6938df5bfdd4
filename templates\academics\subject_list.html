{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Subjects" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .subject-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .subject-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .subject-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
    }
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1.5rem;
    }
    .search-box:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'academics:dashboard' %}">{% trans "Academics" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Subjects" %}</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-book text-primary me-2"></i>{% trans "Subject Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage academic subjects and curriculum" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:subject_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Subject" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="input-group">
                <input type="text" class="form-control search-box" placeholder="{% trans 'Search subjects...' %}" id="searchInput">
                <button class="btn btn-outline-primary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="d-flex gap-2">
                <select class="form-select" id="statusFilter">
                    <option value="">{% trans "All Status" %}</option>
                    <option value="active">{% trans "Active" %}</option>
                    <option value="inactive">{% trans "Inactive" %}</option>
                </select>
                <button class="btn btn-outline-secondary" id="resetFilters">
                    <i class="fas fa-undo me-2"></i>{% trans "Reset" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card subject-card">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ total_subjects }}</h4>
                    <p class="text-muted mb-0">{% trans "Total Subjects" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card subject-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ active_subjects }}</h4>
                    <p class="text-muted mb-0">{% trans "Active Subjects" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card subject-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ total_teachers }}</h4>
                    <p class="text-muted mb-0">{% trans "Assigned Teachers" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card subject-card">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ total_classes }}</h4>
                    <p class="text-muted mb-0">{% trans "Classes Assigned" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Subjects List -->
    <div class="card subject-card">
        <div class="subject-header">
            <div class="card-body">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>{% trans "All Subjects" %}
                </h5>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="subjectsTable">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Subject Code" %}</th>
                            <th>{% trans "Subject Name" %}</th>
                            <th>{% trans "Credit Hours" %}</th>
                            <th>{% trans "Teachers" %}</th>
                            <th>{% trans "Classes" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for subject in subjects %}
                        <tr>
                            <td>
                                <span class="badge bg-primary">{{ subject.code }}</span>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ subject.name }}</strong>
                                    {% if subject.name_ar %}
                                        <br><small class="text-muted">{{ subject.name_ar }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if subject.credit_hours %}
                                    <span class="badge bg-info">{{ subject.credit_hours }} {% trans "hrs" %}</span>
                                {% else %}
                                    <span class="text-muted">{% trans "Not set" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ subject.teacher_count }} {% trans "teacher(s)" %}</span>
                            </td>
                            <td>
                                <span class="badge bg-warning">{{ subject.class_count }} {% trans "class(es)" %}</span>
                            </td>
                            <td>
                                {% if subject.is_active %}
                                    <span class="badge bg-success">{% trans "Active" %}</span>
                                {% else %}
                                    <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'academics:subject_detail' subject.pk %}" class="btn btn-sm btn-outline-info" title="{% trans 'View Details' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'academics:subject_edit' subject.pk %}" class="btn btn-sm btn-outline-primary" title="{% trans 'Edit' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" title="{% trans 'Delete' %}" onclick="confirmDelete({{ subject.pk }}, '{{ subject.name }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-book fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">{% trans "No subjects found" %}</h5>
                                <p class="text-muted">{% trans "Start by adding your first subject to the curriculum." %}</p>
                                <a href="{% url 'academics:subject_add' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>{% trans "Add First Subject" %}
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Confirm Delete" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{% trans "Are you sure you want to delete the subject" %} "<span id="subjectName"></span>"?</p>
                <p class="text-danger"><small>{% trans "This action cannot be undone." %}</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <form method="post" id="deleteForm" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">{% trans "Delete" %}</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const resetFilters = document.getElementById('resetFilters');
    const table = document.getElementById('subjectsTable');
    const rows = table.querySelectorAll('tbody tr');

    // Search functionality
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        filterTable();
    });

    // Status filter
    statusFilter.addEventListener('change', function() {
        filterTable();
    });

    // Reset filters
    resetFilters.addEventListener('click', function() {
        searchInput.value = '';
        statusFilter.value = '';
        filterTable();
    });

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;

        rows.forEach(row => {
            if (row.cells.length === 1) return; // Skip empty state row
            
            const subjectName = row.cells[1].textContent.toLowerCase();
            const subjectCode = row.cells[0].textContent.toLowerCase();
            const status = row.cells[5].textContent.toLowerCase().includes('active') ? 'active' : 'inactive';
            
            const matchesSearch = subjectName.includes(searchTerm) || subjectCode.includes(searchTerm);
            const matchesStatus = !statusValue || status === statusValue;
            
            row.style.display = matchesSearch && matchesStatus ? '' : 'none';
        });
    }
});

function confirmDelete(subjectId, subjectName) {
    document.getElementById('subjectName').textContent = subjectName;
    document.getElementById('deleteForm').action = `/academics/subjects/${subjectId}/delete/`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
