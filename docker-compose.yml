version: '3.8'

services:
  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      POSTGRES_DB: school_erp
      POSTGRES_USER: school_user
      POSTGRES_PASSWORD: school_password
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  web:
    build: .
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=True
      - DATABASE_URL=************************************************/school_erp
      - REDIS_URL=redis://redis:6379/0

  celery:
    build: .
    command: celery -A school_erp worker -l info
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=True
      - DATABASE_URL=************************************************/school_erp
      - REDIS_URL=redis://redis:6379/0

  celery-beat:
    build: .
    command: celery -A school_erp beat -l info
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=True
      - DATABASE_URL=************************************************/school_erp
      - REDIS_URL=redis://redis:6379/0

volumes:
  postgres_data:
