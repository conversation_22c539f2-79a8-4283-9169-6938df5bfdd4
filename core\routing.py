"""
WebSocket routing for School ERP system
"""
from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    re_path(r'ws/notifications/(?P<school_code>\w+)/$', consumers.NotificationConsumer.as_asgi()),
    re_path(r'ws/chat/(?P<room_name>\w+)/$', consumers.ChatConsumer.as_asgi()),
    re_path(r'ws/attendance/(?P<school_code>\w+)/$', consumers.AttendanceConsumer.as_asgi()),
]