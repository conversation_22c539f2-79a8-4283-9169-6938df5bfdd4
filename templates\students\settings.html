{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Student Settings" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-cog text-primary me-2"></i>{% trans "Student Settings" %}
                    </h2>
                    <p class="text-muted">{% trans "Configure student module settings and preferences" %}</p>
                </div>
            </div>

            <!-- Settings Sections -->
            <div class="row">
                {% for section in settings_sections %}
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 settings-card">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="{{ section.icon }} fa-3x text-primary"></i>
                                </div>
                                <h5 class="card-title">{{ section.title }}</h5>
                                <p class="card-text">{{ section.description }}</p>
                                <a href="{% url section.url %}" class="btn btn-primary">
                                    {% trans "Configure" %}
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Quick Settings -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Quick Settings" %}</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                                            <label class="form-check-label" for="enableNotifications">
                                                {% trans "Enable Notifications" %}
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="autoAssignIds" checked>
                                            <label class="form-check-label" for="autoAssignIds">
                                                {% trans "Auto-assign Student IDs" %}
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="requirePhotos">
                                            <label class="form-check-label" for="requirePhotos">
                                                {% trans "Require Student Photos" %}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="enableBulkOperations" checked>
                                            <label class="form-check-label" for="enableBulkOperations">
                                                {% trans "Enable Bulk Operations" %}
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="showInactiveStudents">
                                            <label class="form-check-label" for="showInactiveStudents">
                                                {% trans "Show Inactive Students" %}
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="enableAdvancedSearch" checked>
                                            <label class="form-check-label" for="enableAdvancedSearch">
                                                {% trans "Enable Advanced Search" %}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="defaultPageSize" class="form-label">{% trans "Default Page Size" %}</label>
                                            <select class="form-select" id="defaultPageSize">
                                                <option value="10">10</option>
                                                <option value="20" selected>20</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="defaultSortOrder" class="form-label">{% trans "Default Sort Order" %}</label>
                                            <select class="form-select" id="defaultSortOrder">
                                                <option value="first_name">{% trans "First Name" %}</option>
                                                <option value="last_name">{% trans "Last Name" %}</option>
                                                <option value="student_id">{% trans "Student ID" %}</option>
                                                <option value="admission_date">{% trans "Admission Date" %}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>{% trans "Save Settings" %}
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary ms-2">
                                        <i class="fas fa-undo me-2"></i>{% trans "Reset to Defaults" %}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    transition: transform 0.2s;
}
.settings-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}
</style>
{% endblock %}