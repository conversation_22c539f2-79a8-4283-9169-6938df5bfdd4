from django.contrib import admin
from .models import (
    AccountType, Account, FeeType, GradeFee, StudentFee, Payment, PaymentItem,
    CostCenter, JournalEntry, FinancialYear, Bank, Invoice, InvoiceItem
)


@admin.register(AccountType)
class AccountTypeAdmin(admin.ModelAdmin):
    """
    Account Type admin
    """
    list_display = ('name', 'type', 'is_active')
    list_filter = ('type', 'is_active')
    search_fields = ('name', 'name_ar')
    ordering = ('name',)


@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    """
    Account admin
    """
    list_display = ('code', 'name', 'account_type', 'parent', 'is_header', 'is_active')
    list_filter = ('account_type', 'is_header', 'is_active')
    search_fields = ('code', 'name', 'name_ar')
    raw_id_fields = ('parent',)
    ordering = ('code',)


@admin.register(CostCenter)
class CostCenterAdmin(admin.ModelAdmin):
    """
    Cost Center admin
    """
    list_display = ('code', 'name', 'manager', 'budget_amount', 'total_expenses', 'is_active')
    list_filter = ('is_active', 'manager')
    search_fields = ('code', 'name', 'name_ar')
    raw_id_fields = ('manager',)
    readonly_fields = ('total_expenses', 'budget_utilization')

    def total_expenses(self, obj):
        return obj.total_expenses
    total_expenses.short_description = 'Total Expenses'

    def budget_utilization(self, obj):
        return f"{obj.budget_utilization:.1f}%"
    budget_utilization.short_description = 'Budget Utilization'


@admin.register(JournalEntry)
class JournalEntryAdmin(admin.ModelAdmin):
    """
    Journal Entry admin
    """
    list_display = ('reference_number', 'entry_date', 'account', 'debit_amount', 'credit_amount', 'is_posted', 'created_by')
    list_filter = ('entry_type', 'is_posted', 'entry_date', 'account__account_type')
    search_fields = ('reference_number', 'description', 'account__name')
    raw_id_fields = ('account', 'cost_center', 'posted_by', 'created_by')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'entry_date'


@admin.register(FinancialYear)
class FinancialYearAdmin(admin.ModelAdmin):
    """
    Financial Year admin
    """
    list_display = ('name', 'start_date', 'end_date', 'is_current', 'is_closed', 'is_active')
    list_filter = ('is_current', 'is_closed', 'is_active')
    search_fields = ('name',)
    ordering = ('-start_date',)


@admin.register(Bank)
class BankAdmin(admin.ModelAdmin):
    """
    Bank admin
    """
    list_display = ('name', 'account_number', 'account_name', 'current_balance', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'name_ar', 'account_number', 'account_name')
    raw_id_fields = ('account',)


@admin.register(FeeType)
class FeeTypeAdmin(admin.ModelAdmin):
    """
    Fee Type admin
    """
    list_display = ('name', 'is_mandatory', 'is_active')
    list_filter = ('is_mandatory', 'is_active')
    search_fields = ('name', 'name_ar')
    ordering = ('name',)


@admin.register(GradeFee)
class GradeFeeAdmin(admin.ModelAdmin):
    """
    Grade Fee admin
    """
    list_display = ('grade', 'fee_type', 'amount', 'academic_year', 'is_active')
    list_filter = ('grade', 'fee_type', 'academic_year', 'is_active')
    search_fields = ('grade__name', 'fee_type__name')
    raw_id_fields = ('grade', 'fee_type', 'academic_year')


@admin.register(StudentFee)
class StudentFeeAdmin(admin.ModelAdmin):
    """
    Student Fee admin
    """
    list_display = ('student', 'grade_fee', 'amount', 'due_date', 'is_paid', 'paid_date')
    list_filter = ('is_paid', 'due_date', 'grade_fee__fee_type')
    search_fields = ('student__first_name', 'student__last_name', 'grade_fee__fee_type__name')
    raw_id_fields = ('student', 'grade_fee')
    date_hierarchy = 'due_date'


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """
    Payment admin
    """
    list_display = ('receipt_number', 'student', 'amount', 'payment_date', 'payment_method', 'received_by')
    list_filter = ('payment_method', 'payment_date')
    search_fields = ('receipt_number', 'student__first_name', 'student__last_name')
    raw_id_fields = ('student', 'received_by')
    date_hierarchy = 'payment_date'


class InvoiceItemInline(admin.TabularInline):
    model = InvoiceItem
    extra = 1


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    """
    Invoice admin
    """
    list_display = ('invoice_number', 'student', 'invoice_type', 'invoice_date', 'due_date', 'total_amount', 'status')
    list_filter = ('invoice_type', 'status', 'invoice_date')
    search_fields = ('invoice_number', 'student__first_name', 'student__last_name')
    raw_id_fields = ('student', 'created_by')
    date_hierarchy = 'invoice_date'
    inlines = [InvoiceItemInline]
    readonly_fields = ('total_amount', 'is_overdue')

    def is_overdue(self, obj):
        return obj.is_overdue
    is_overdue.boolean = True
    is_overdue.short_description = 'Is Overdue'


@admin.register(PaymentItem)
class PaymentItemAdmin(admin.ModelAdmin):
    """
    Payment Item admin
    """
    list_display = ('payment', 'student_fee', 'amount')
    search_fields = ('payment__receipt_number', 'student_fee__student__first_name')
    raw_id_fields = ('payment', 'student_fee')
