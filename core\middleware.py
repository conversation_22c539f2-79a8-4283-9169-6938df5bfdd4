"""
Middleware for School ERP system
"""
import json
import logging
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth import get_user_model
from django.http import JsonResponse
from django.core.exceptions import PermissionDenied
from django.utils import timezone
from .models import School, AuditLog
from .utils import security_utils

logger = logging.getLogger(__name__)
User = get_user_model()


class SchoolMiddleware(MiddlewareMixin):
    """
    Middleware to handle multi-tenancy by setting school context
    """
    
    def process_request(self, request):
        """
        Set school context for the request
        """
        school = None
        
        # Try to get school from various sources
        if request.user.is_authenticated:
            # Get school from user's employee record
            if hasattr(request.user, 'employee') and request.user.employee:
                school = request.user.employee.school
            # Get school from user's student record
            elif hasattr(request.user, 'student') and request.user.student:
                school = request.user.student.school
            # Get school from user's parent record
            elif hasattr(request.user, 'parent') and request.user.parent:
                school = request.user.parent.school
        
        # Fallback: get school from header or subdomain
        if not school:
            school_code = request.META.get('HTTP_X_SCHOOL_CODE')
            if not school_code:
                # Extract from subdomain (e.g., school1.example.com)
                host = request.get_host().split(':')[0]  # Remove port
                if '.' in host:
                    subdomain = host.split('.')[0]
                    if subdomain != 'www':
                        school_code = subdomain.upper()
            
            if school_code:
                try:
                    school = School.objects.get(code=school_code, is_active=True)
                except School.DoesNotExist:
                    pass
        
        # Set school in request
        request.school = school
        
        # Set school in thread local for model access
        if school:
            from django.db import models
            # Store in thread local storage for model access
            import threading
            if not hasattr(threading.current_thread(), 'school'):
                threading.current_thread().school = school
    
    def process_response(self, request, response):
        """
        Clean up thread local storage
        """
        import threading
        if hasattr(threading.current_thread(), 'school'):
            delattr(threading.current_thread(), 'school')
        
        return response


class AuditMiddleware(MiddlewareMixin):
    """
    Middleware to log user actions for audit trail
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Store request start time and user info
        """
        request._audit_start_time = timezone.now()
        request._audit_ip = security_utils.get_client_ip(request)
        request._audit_user_agent = request.META.get('HTTP_USER_AGENT', '')
    
    def process_response(self, request, response):
        """
        Log the request if it's a modification
        """
        # Only log authenticated users
        if not request.user.is_authenticated:
            return response
        
        # Only log modification requests
        if request.method not in ['POST', 'PUT', 'PATCH', 'DELETE']:
            return response
        
        # Skip certain paths
        skip_paths = ['/admin/jsi18n/', '/static/', '/media/']
        if any(request.path.startswith(path) for path in skip_paths):
            return response
        
        try:
            self._create_audit_log(request, response)
        except Exception as e:
            logger.error(f"Failed to create audit log: {e}")
        
        return response
    
    def _create_audit_log(self, request, response):
        """
        Create audit log entry
        """
        # Get school from request
        school = getattr(request, 'school', None)
        if not school:
            return
        
        # Determine action based on method and response status
        action = self._get_action(request.method, response.status_code)
        if not action:
            return
        
        # Extract model info from URL
        model_name, object_id = self._extract_model_info(request)
        
        # Get object representation
        object_repr = self._get_object_repr(model_name, object_id, school)
        
        # Extract changes from request body
        changes = self._extract_changes(request)
        
        # Create audit log
        AuditLog.objects.create(
            school=school,
            user=request.user,
            action=action,
            model_name=model_name,
            object_id=str(object_id) if object_id else '',
            object_repr=object_repr,
            changes=changes,
            ip_address=request._audit_ip,
            user_agent=request._audit_user_agent[:500]  # Limit length
        )
    
    def _get_action(self, method, status_code):
        """
        Determine action based on HTTP method and status code
        """
        if status_code >= 400:
            return None  # Don't log failed requests
        
        action_map = {
            'POST': 'CREATE',
            'PUT': 'UPDATE',
            'PATCH': 'UPDATE',
            'DELETE': 'DELETE'
        }
        
        return action_map.get(method)
    
    def _extract_model_info(self, request):
        """
        Extract model name and object ID from URL
        """
        path_parts = request.path.strip('/').split('/')
        
        # Try to identify model from URL patterns
        model_name = 'Unknown'
        object_id = None
        
        # Common patterns: /api/students/123/, /students/123/edit/
        if len(path_parts) >= 2:
            if path_parts[0] == 'api':
                model_name = path_parts[1].title().rstrip('s')  # Remove 's' and capitalize
                if len(path_parts) >= 3 and path_parts[2].isdigit():
                    object_id = path_parts[2]
            else:
                model_name = path_parts[0].title().rstrip('s')
                if len(path_parts) >= 2 and path_parts[1].isdigit():
                    object_id = path_parts[1]
        
        return model_name, object_id
    
    def _get_object_repr(self, model_name, object_id, school):
        """
        Get string representation of the object
        """
        if not object_id:
            return f"New {model_name}"
        
        try:
            # Try to get the object and its string representation
            # This is a simplified approach - in practice, you'd have a registry
            # of models and their managers
            from django.apps import apps
            
            # Try to find the model
            for app_config in apps.get_app_configs():
                try:
                    model = app_config.get_model(model_name.lower())
                    if hasattr(model, 'objects'):
                        obj = model.objects.filter(pk=object_id).first()
                        if obj:
                            return str(obj)
                except:
                    continue
            
            return f"{model_name} #{object_id}"
        except Exception:
            return f"{model_name} #{object_id}"
    
    def _extract_changes(self, request):
        """
        Extract changes from request body
        """
        changes = {}
        
        try:
            if request.content_type == 'application/json':
                body = request.body.decode('utf-8')
                if body:
                    changes = json.loads(body)
            elif hasattr(request, 'POST') and request.POST:
                changes = dict(request.POST)
                # Remove sensitive fields
                sensitive_fields = ['password', 'password1', 'password2', 'csrfmiddlewaretoken']
                for field in sensitive_fields:
                    changes.pop(field, None)
        except Exception as e:
            logger.debug(f"Could not extract changes: {e}")
        
        return changes


class ErrorHandlingMiddleware(MiddlewareMixin):
    """
    Middleware to handle errors gracefully
    """
    
    def process_exception(self, request, exception):
        """
        Handle exceptions and return appropriate responses
        """
        logger.exception(f"Unhandled exception: {exception}")
        
        # Handle specific exception types
        if isinstance(exception, PermissionDenied):
            if request.path.startswith('/api/'):
                return JsonResponse({
                    'error': {
                        'code': 'PERMISSION_DENIED',
                        'message': 'You do not have permission to perform this action',
                        'timestamp': timezone.now().isoformat()
                    }
                }, status=403)
        
        # For API requests, return JSON error response
        if request.path.startswith('/api/'):
            return JsonResponse({
                'error': {
                    'code': 'INTERNAL_ERROR',
                    'message': 'An internal error occurred',
                    'timestamp': timezone.now().isoformat()
                }
            }, status=500)
        
        # Let Django handle other exceptions normally
        return None


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Middleware to add security headers
    """
    
    def process_response(self, request, response):
        """
        Add security headers to response
        """
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Add CSP header for non-admin pages
        if not request.path.startswith('/admin/'):
            response['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self';"
            )
        
        return response