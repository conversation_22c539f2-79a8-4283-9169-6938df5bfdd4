{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load dashboard_tags %}

{% block title %}{% trans "Class Performance Report" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .performance-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .performance-card:hover {
        transform: translateY(-2px);
    }
    .performance-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .grade-excellent { background-color: #d4edda; color: #155724; }
    .grade-good { background-color: #d1ecf1; color: #0c5460; }
    .grade-average { background-color: #fff3cd; color: #856404; }
    .grade-poor { background-color: #f8d7da; color: #721c24; }
    .subject-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
        margin-right: 10px;
    }
    .math-icon { background-color: #007bff; }
    .science-icon { background-color: #28a745; }
    .english-icon { background-color: #fd7e14; }
    .arabic-icon { background-color: #e83e8c; }
    .art-icon { background-color: #6f42c1; }
    .pe-icon { background-color: #20c997; }
    .chart-container {
        position: relative;
        height: 300px;
    }
    .performance-meter {
        height: 20px;
        background-color: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
    }
    .performance-bar {
        height: 100%;
        border-radius: 10px;
        transition: width 0.5s ease;
    }
    .student-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        object-fit: cover;
    }
    .rank-badge {
        font-size: 0.8em;
        padding: 4px 8px;
        border-radius: 15px;
        font-weight: bold;
    }
    .rank-1 { background-color: #ffd700; color: #000; }
    .rank-2 { background-color: #c0c0c0; color: #000; }
    .rank-3 { background-color: #cd7f32; color: #fff; }
    .rank-other { background-color: #6c757d; color: #fff; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-line me-2"></i>{% trans "Class Performance Report" %}
                    </h1>
                    <p class="text-muted">{% trans "Comprehensive analysis of class academic performance" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>{% trans "Export Report" %}
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printReport()">
                        <i class="fas fa-print me-2"></i>{% trans "Print" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="class_filter" class="form-label">{% trans "Class" %}</label>
                            <select name="class" id="class_filter" class="form-select">
                                <option value="">{% trans "Select Class" %}</option>
                                {% for class in classes %}
                                    <option value="{{ class.id }}" {% if selected_class and class.id == selected_class.id %}selected{% endif %}>
                                        {{ class.grade.name }} - {{ class.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="subject_filter" class="form-label">{% trans "Subject" %}</label>
                            <select name="subject" id="subject_filter" class="form-select">
                                <option value="">{% trans "All Subjects" %}</option>
                                {% for subject in subjects %}
                                    <option value="{{ subject.id }}" {% if selected_subject and subject.id == selected_subject.id %}selected{% endif %}>
                                        {{ subject.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="semester_filter" class="form-label">{% trans "Semester" %}</label>
                            <select name="semester" id="semester_filter" class="form-select">
                                <option value="">{% trans "Current Semester" %}</option>
                                <option value="first" {% if selected_semester == 'first' %}selected{% endif %}>{% trans "First Semester" %}</option>
                                <option value="second" {% if selected_semester == 'second' %}selected{% endif %}>{% trans "Second Semester" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>{% trans "Generate Report" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if selected_class %}
        <!-- Class Overview Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card performance-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users text-primary mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-primary">{{ class_stats.total_students|default:0 }}</h4>
                        <p class="mb-0">{% trans "Total Students" %}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card performance-card">
                    <div class="card-body text-center">
                        <i class="fas fa-star text-warning mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-warning">{{ class_stats.average_grade|floatformat:1|default:"0.0" }}%</h4>
                        <p class="mb-0">{% trans "Class Average" %}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card performance-card">
                    <div class="card-body text-center">
                        <i class="fas fa-trophy text-success mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-success">{{ class_stats.excellent_count|default:0 }}</h4>
                        <p class="mb-0">{% trans "Excellent (90%+)" %}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card performance-card">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle text-danger mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-danger">{{ class_stats.failing_count|default:0 }}</h4>
                        <p class="mb-0">{% trans "Below 60%" %}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Charts -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card performance-card">
                    <div class="card-header">
                        <h5 class="mb-0">{% trans "Grade Distribution" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="gradeDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card performance-card">
                    <div class="card-header">
                        <h5 class="mb-0">{% trans "Subject Performance" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="subjectPerformanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subject Performance Breakdown -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card performance-card">
                    <div class="card-header performance-header">
                        <h5 class="mb-0">
                            <i class="fas fa-book me-2"></i>{% trans "Subject Performance Breakdown" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if subject_performance %}
                            {% for subject in subject_performance %}
                                <div class="row align-items-center mb-3">
                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <div class="subject-icon 
                                                {% if 'math' in subject.name|lower %}math-icon
                                                {% elif 'science' in subject.name|lower %}science-icon
                                                {% elif 'english' in subject.name|lower %}english-icon
                                                {% elif 'arabic' in subject.name|lower %}arabic-icon
                                                {% elif 'art' in subject.name|lower %}art-icon
                                                {% elif 'pe' in subject.name|lower or 'physical' in subject.name|lower %}pe-icon
                                                {% else %}math-icon{% endif %}">
                                                <i class="fas fa-book"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ subject.name }}</h6>
                                                <small class="text-muted">{{ subject.teacher|default:"No teacher assigned" }}</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="performance-meter">
                                            <div class="performance-bar 
                                                {% if subject.average >= 90 %}bg-success
                                                {% elif subject.average >= 80 %}bg-primary
                                                {% elif subject.average >= 70 %}bg-warning
                                                {% else %}bg-danger{% endif %}"
                                                style="width: {{ subject.average|default:0 }}%">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <h5 class="mb-0">{{ subject.average|floatformat:1|default:"0.0" }}%</h5>
                                        <small class="text-muted">{{ subject.student_count|default:0 }} {% trans "students" %}</small>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{% trans "No performance data available for this class." %}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performers -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card performance-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-medal me-2"></i>{% trans "Top Performers" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if top_performers %}
                            {% for student in top_performers %}
                                <div class="d-flex align-items-center mb-3">
                                    <span class="rank-badge 
                                        {% if forloop.counter == 1 %}rank-1
                                        {% elif forloop.counter == 2 %}rank-2
                                        {% elif forloop.counter == 3 %}rank-3
                                        {% else %}rank-other{% endif %} me-3">
                                        #{{ forloop.counter }}
                                    </span>
                                    {% if student.user.profile_picture %}
                                        <img src="{{ student.user.profile_picture.url }}" alt="{{ student.user.get_full_name }}" class="student-avatar me-3">
                                    {% else %}
                                        <div class="student-avatar bg-secondary d-flex align-items-center justify-content-center me-3">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">{{ student.user.first_name }} {{ student.user.last_name }}</h6>
                                        <small class="text-muted">{{ student.student_id }}</small>
                                    </div>
                                    <div class="text-end">
                                        <h6 class="mb-0 text-success">{{ student.average_grade|floatformat:1 }}%</h6>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{% trans "No performance data available." %}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card performance-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Students Needing Support" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if struggling_students %}
                            {% for student in struggling_students %}
                                <div class="d-flex align-items-center mb-3">
                                    {% if student.user.profile_picture %}
                                        <img src="{{ student.user.profile_picture.url }}" alt="{{ student.user.get_full_name }}" class="student-avatar me-3">
                                    {% else %}
                                        <div class="student-avatar bg-secondary d-flex align-items-center justify-content-center me-3">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">{{ student.user.first_name }} {{ student.user.last_name }}</h6>
                                        <small class="text-muted">{{ student.student_id }}</small>
                                    </div>
                                    <div class="text-end">
                                        <h6 class="mb-0 text-danger">{{ student.average_grade|floatformat:1 }}%</h6>
                                        <small class="text-muted">{% trans "Needs attention" %}</small>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <p class="text-muted">{% trans "All students are performing well!" %}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Student Performance Table -->
        <div class="row">
            <div class="col-12">
                <div class="card performance-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>{% trans "Detailed Student Performance" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "Rank" %}</th>
                                        <th>{% trans "Student" %}</th>
                                        <th>{% trans "Student ID" %}</th>
                                        <th>{% trans "Overall Average" %}</th>
                                        <th>{% trans "Highest Subject" %}</th>
                                        <th>{% trans "Lowest Subject" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if student_performance %}
                                        {% for student in student_performance %}
                                            <tr>
                                                <td>
                                                    <span class="rank-badge 
                                                        {% if forloop.counter <= 3 %}rank-{{ forloop.counter }}
                                                        {% else %}rank-other{% endif %}">
                                                        #{{ forloop.counter }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        {% if student.user.profile_picture %}
                                                            <img src="{{ student.user.profile_picture.url }}" alt="{{ student.user.get_full_name }}" class="student-avatar me-2">
                                                        {% else %}
                                                            <div class="student-avatar bg-secondary d-flex align-items-center justify-content-center me-2">
                                                                <i class="fas fa-user text-white"></i>
                                                            </div>
                                                        {% endif %}
                                                        <div>
                                                            <h6 class="mb-0">{{ student.user.first_name }} {{ student.user.last_name }}</h6>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>{{ student.student_id }}</td>
                                                <td>
                                                    <span class="fw-bold 
                                                        {% if student.average_grade >= 90 %}text-success
                                                        {% elif student.average_grade >= 80 %}text-primary
                                                        {% elif student.average_grade >= 70 %}text-warning
                                                        {% else %}text-danger{% endif %}">
                                                        {{ student.average_grade|floatformat:1 }}%
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="text-success">
                                                        {{ student.highest_subject|default:"N/A" }}
                                                        {% if student.highest_grade %}({{ student.highest_grade|floatformat:1 }}%){% endif %}
                                                    </small>
                                                </td>
                                                <td>
                                                    <small class="text-danger">
                                                        {{ student.lowest_subject|default:"N/A" }}
                                                        {% if student.lowest_grade %}({{ student.lowest_grade|floatformat:1 }}%){% endif %}
                                                    </small>
                                                </td>
                                                <td>
                                                    {% if student.average_grade >= 90 %}
                                                        <span class="badge bg-success">{% trans "Excellent" %}</span>
                                                    {% elif student.average_grade >= 80 %}
                                                        <span class="badge bg-primary">{% trans "Good" %}</span>
                                                    {% elif student.average_grade >= 70 %}
                                                        <span class="badge bg-warning">{% trans "Average" %}</span>
                                                    {% elif student.average_grade >= 60 %}
                                                        <span class="badge bg-orange">{% trans "Below Average" %}</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">{% trans "Needs Support" %}</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <a href="{% url 'reports:student_performance' student.id %}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <div class="alert alert-info mb-0">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    {% trans "No student performance data available for this class." %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="row">
            <div class="col-12">
                <div class="card performance-card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-chart-line fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted">{% trans "Select a Class to View Performance Report" %}</h4>
                        <p class="text-muted">{% trans "Choose a class from the filter above to generate a comprehensive performance analysis." %}</p>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    {% if selected_class %}
        // Grade Distribution Chart
        const gradeDistCtx = document.getElementById('gradeDistributionChart').getContext('2d');
        const gradeDistChart = new Chart(gradeDistCtx, {
            type: 'doughnut',
            data: {
                labels: ['{% trans "Excellent (90%+)" %}', '{% trans "Good (80-89%)" %}', '{% trans "Average (70-79%)" %}', '{% trans "Below Average (60-69%)" %}', '{% trans "Failing (<60%)" %}'],
                datasets: [{
                    data: [
                        {{ class_stats.excellent_count|default:0 }},
                        {{ class_stats.good_count|default:0 }},
                        {{ class_stats.average_count|default:0 }},
                        {{ class_stats.below_average_count|default:0 }},
                        {{ class_stats.failing_count|default:0 }}
                    ],
                    backgroundColor: [
                        '#28a745',
                        '#007bff',
                        '#ffc107',
                        '#fd7e14',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Subject Performance Chart
        const subjectPerfCtx = document.getElementById('subjectPerformanceChart').getContext('2d');
        const subjectPerfChart = new Chart(subjectPerfCtx, {
            type: 'bar',
            data: {
                labels: {{ subject_names|safe|default:"[]" }},
                datasets: [{
                    label: '{% trans "Average Grade %" %}',
                    data: {{ subject_averages|safe|default:"[]" }},
                    backgroundColor: [
                        '#007bff', '#28a745', '#ffc107', '#dc3545', 
                        '#17a2b8', '#6f42c1', '#fd7e14', '#20c997'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    {% endif %}

    function exportReport() {
        window.location.href = '{% url "reports:class_performance_export" %}?class={{ selected_class.id|default:"" }}';
    }

    function printReport() {
        window.print();
    }

    // Auto-submit form on class change
    document.getElementById('class_filter').addEventListener('change', function() {
        this.form.submit();
    });
</script>
{% endblock %}
