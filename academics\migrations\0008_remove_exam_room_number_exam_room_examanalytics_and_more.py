# Generated by Django 5.2.4 on 2025-07-31 07:37

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0007_gradereport_gradingscale_studentgpa_transcript"),
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("students", "0008_grade_max_age_grade_max_capacity_grade_min_age"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name="exam",
            name="room_number",
        ),
        migrations.AddField(
            model_name="exam",
            name="room",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="exams",
                to="academics.room",
                verbose_name="Room",
            ),
        ),
        migrations.CreateModel(
            name="ExamAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "analytics_type",
                    models.CharField(
                        choices=[
                            ("subject", "Subject Analytics"),
                            ("teacher", "Teacher Analytics"),
                            ("class", "Class Analytics"),
                            ("school", "School Analytics"),
                            ("comparative", "Comparative Analytics"),
                        ],
                        max_length=20,
                        verbose_name="Analytics Type",
                    ),
                ),
                (
                    "analytics_data",
                    models.JSONField(default=dict, verbose_name="Analytics Data"),
                ),
                (
                    "insights",
                    models.JSONField(default=list, verbose_name="Generated Insights"),
                ),
                (
                    "recommendations",
                    models.JSONField(default=list, verbose_name="Recommendations"),
                ),
                (
                    "generated_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Generated At"
                    ),
                ),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_analytics",
                        to="core.academicyear",
                        verbose_name="Academic Year",
                    ),
                ),
                (
                    "class_obj",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="students.class",
                        verbose_name="Class",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="generated_analytics",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Generated By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "semester",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_analytics",
                        to="core.semester",
                        verbose_name="Semester",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="academics.subject",
                        verbose_name="Subject",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="academics.teacher",
                        verbose_name="Teacher",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Exam Analytics",
                "verbose_name_plural": "Exam Analytics",
                "ordering": ["-generated_at"],
            },
        ),
        migrations.CreateModel(
            name="ExamResult",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "total_students",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Total Students"
                    ),
                ),
                (
                    "students_appeared",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Students Appeared"
                    ),
                ),
                (
                    "students_passed",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Students Passed"
                    ),
                ),
                (
                    "pass_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        verbose_name="Pass Percentage",
                    ),
                ),
                (
                    "average_marks",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=6,
                        verbose_name="Average Marks",
                    ),
                ),
                (
                    "highest_marks",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=6,
                        verbose_name="Highest Marks",
                    ),
                ),
                (
                    "lowest_marks",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=6,
                        verbose_name="Lowest Marks",
                    ),
                ),
                (
                    "standard_deviation",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=6,
                        verbose_name="Standard Deviation",
                    ),
                ),
                (
                    "grade_distribution",
                    models.JSONField(default=dict, verbose_name="Grade Distribution"),
                ),
                (
                    "performance_analysis",
                    models.JSONField(default=dict, verbose_name="Performance Analysis"),
                ),
                (
                    "generated_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Generated At"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "exam",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="result_summary",
                        to="academics.exam",
                        verbose_name="Exam",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="generated_exam_results",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Generated By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Exam Result",
                "verbose_name_plural": "Exam Results",
                "ordering": ["-generated_at"],
            },
        ),
        migrations.CreateModel(
            name="ExamSession",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=200, verbose_name="Session Name")),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Session Name (Arabic)",
                    ),
                ),
                (
                    "session_type",
                    models.CharField(
                        choices=[
                            ("midterm", "Midterm Exams"),
                            ("final", "Final Exams"),
                            ("monthly", "Monthly Tests"),
                            ("quarterly", "Quarterly Exams"),
                            ("annual", "Annual Exams"),
                        ],
                        max_length=20,
                        verbose_name="Session Type",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="Start Date")),
                ("end_date", models.DateField(verbose_name="End Date")),
                (
                    "registration_start",
                    models.DateField(
                        blank=True, null=True, verbose_name="Registration Start Date"
                    ),
                ),
                (
                    "registration_end",
                    models.DateField(
                        blank=True, null=True, verbose_name="Registration End Date"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "instructions",
                    models.TextField(
                        blank=True, null=True, verbose_name="General Instructions"
                    ),
                ),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_sessions",
                        to="core.academicyear",
                        verbose_name="Academic Year",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "semester",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_sessions",
                        to="core.semester",
                        verbose_name="Semester",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Exam Session",
                "verbose_name_plural": "Exam Sessions",
                "ordering": ["-start_date"],
                "unique_together": {("school", "name", "academic_year")},
            },
        ),
        migrations.AddField(
            model_name="exam",
            name="exam_session",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="exams",
                to="academics.examsession",
                verbose_name="Exam Session",
            ),
        ),
        migrations.CreateModel(
            name="ExamInvigilator",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("chief", "Chief Invigilator"),
                            ("assistant", "Assistant Invigilator"),
                            ("observer", "Observer"),
                            ("special_needs", "Special Needs Support"),
                        ],
                        default="assistant",
                        max_length=20,
                        verbose_name="Role",
                    ),
                ),
                (
                    "assigned_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Assigned At"),
                ),
                (
                    "is_confirmed",
                    models.BooleanField(default=False, verbose_name="Is Confirmed"),
                ),
                (
                    "confirmed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Confirmed At"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="Notes"),
                ),
                (
                    "assigned_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assigned_invigilations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Assigned By",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invigilators",
                        to="academics.exam",
                        verbose_name="Exam",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invigilation_duties",
                        to="academics.teacher",
                        verbose_name="Teacher",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Exam Invigilator",
                "verbose_name_plural": "Exam Invigilators",
                "ordering": ["exam__exam_date", "exam__start_time", "role"],
                "unique_together": {("exam", "teacher")},
            },
        ),
        migrations.CreateModel(
            name="ExamSeating",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "seat_number",
                    models.CharField(max_length=20, verbose_name="Seat Number"),
                ),
                ("row_number", models.PositiveIntegerField(verbose_name="Row Number")),
                (
                    "column_number",
                    models.PositiveIntegerField(verbose_name="Column Number"),
                ),
                (
                    "is_special_needs",
                    models.BooleanField(
                        default=False, verbose_name="Special Needs Accommodation"
                    ),
                ),
                (
                    "special_requirements",
                    models.TextField(
                        blank=True, null=True, verbose_name="Special Requirements"
                    ),
                ),
                (
                    "assigned_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Assigned At"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="seating_arrangements",
                        to="academics.exam",
                        verbose_name="Exam",
                    ),
                ),
                (
                    "room",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_seatings",
                        to="academics.room",
                        verbose_name="Room",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_seats",
                        to="students.student",
                        verbose_name="Student",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Exam Seating",
                "verbose_name_plural": "Exam Seatings",
                "ordering": ["exam", "row_number", "column_number"],
                "unique_together": {("exam", "student")},
            },
        ),
    ]
