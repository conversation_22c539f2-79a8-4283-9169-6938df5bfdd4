{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "New Vacation Request" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-calendar-plus text-primary me-2"></i>{% trans "New Vacation Request" %}
                    </h2>
                    <p class="text-muted">{% trans "Submit a vacation request for a student" %}</p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                {{ form|crispy }}
                                
                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i>{% trans "Submit Request" %}
                                    </button>
                                    <a href="{% url 'students:vacation_requests' %}" class="btn btn-secondary ms-2">
                                        <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Information" %}</h5>
                        </div>
                        <div class="card-body">
                            <p>{% trans "Vacation requests should be submitted at least 3 days in advance." %}</p>
                            <p>{% trans "Please provide a clear reason for the vacation request." %}</p>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                {% trans "All vacation requests require approval from the administration." %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}