{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Subject Details" %} - {{ subject.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="row align-items-center">
            <div class="col">
                <h2 class="page-title">
                    {{ subject.name }}
                </h2>
                <div class="text-muted mt-1">{% trans "Subject Code" %}: {{ subject.code }}</div>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="{% url 'academics:subject_edit' subject.id %}" class="btn btn-primary d-none d-sm-inline-block">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M9 7h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3" />
                            <path d="M9 15h3l8.5 -8.5a1.5 1.5 0 0 0 -3 -3l-8.5 8.5v3" />
                            <line x1="16" y1="5" x2="19" y2="8" />
                        </svg>
                        {% trans "Edit Subject" %}
                    </a>
                    <a href="{% url 'academics:subjects' %}" class="btn btn-secondary d-none d-sm-inline-block">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <line x1="5" y1="12" x2="19" y2="12" />
                            <line x1="5" y1="12" x2="11" y2="18" />
                            <line x1="5" y1="12" x2="11" y2="6" />
                        </svg>
                        {% trans "Back to Subjects" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject details -->
    <div class="row mt-3">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Subject Information" %}</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">{% trans "Subject Name" %}:</div>
                        <div class="col-md-8">{{ subject.name }}</div>
                    </div>
                    {% if subject.name_ar %}
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">{% trans "Subject Name (Arabic)" %}:</div>
                        <div class="col-md-8">{{ subject.name_ar }}</div>
                    </div>
                    {% endif %}
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">{% trans "Subject Code" %}:</div>
                        <div class="col-md-8">{{ subject.code }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">{% trans "Credit Hours" %}:</div>
                        <div class="col-md-8">{{ subject.credit_hours }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">{% trans "Is Mandatory" %}:</div>
                        <div class="col-md-8">
                            {% if subject.is_mandatory %}
                                <span class="badge bg-success">{% trans "Yes" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "No" %}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">{% trans "Status" %}:</div>
                        <div class="col-md-8">
                            {% if subject.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-danger">{% trans "Inactive" %}</span>
                            {% endif %}
                        </div>
                    </div>
                    {% if subject.description %}
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">{% trans "Description" %}:</div>
                        <div class="col-md-8">{{ subject.description }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Classes teaching this subject -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Classes Teaching This Subject" %}</h3>
                </div>
                <div class="card-body">
                    {% if class_subjects %}
                        <div class="table-responsive">
                            <table class="table table-vcenter card-table">
                                <thead>
                                    <tr>
                                        <th>{% trans "Class" %}</th>
                                        <th>{% trans "Teacher" %}</th>
                                        <th>{% trans "Academic Year" %}</th>
                                        <th>{% trans "Weekly Hours" %}</th>
                                        <th class="w-1"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for class_subject in class_subjects %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'students:class_detail' class_subject.class_obj.id %}">
                                                {{ class_subject.class_obj }}
                                            </a>
                                        </td>
                                        <td>
                                            <a href="{% url 'academics:teacher_detail' class_subject.teacher.id %}">
                                                {{ class_subject.teacher }}
                                            </a>
                                        </td>
                                        <td>{{ class_subject.academic_year }}</td>
                                        <td>{{ class_subject.weekly_hours }}</td>
                                        <td>
                                            <a href="#" class="btn btn-sm btn-outline-primary">
                                                {% trans "View Schedule" %}
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty">
                            <div class="empty-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <circle cx="12" cy="12" r="9" />
                                    <line x1="9" y1="10" x2="9.01" y2="10" />
                                    <line x1="15" y1="10" x2="15.01" y2="10" />
                                    <path d="M9.5 15.25a3.5 3.5 0 0 1 5 0" />
                                </svg>
                            </div>
                            <p class="empty-title">{% trans "No classes teaching this subject yet" %}</p>
                            <p class="empty-subtitle text-muted">
                                {% trans "This subject is not currently assigned to any class." %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent exams -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Recent Exams" %}</h3>
                </div>
                <div class="card-body">
                    {% if recent_exams %}
                        <div class="table-responsive">
                            <table class="table table-vcenter card-table">
                                <thead>
                                    <tr>
                                        <th>{% trans "Exam Name" %}</th>
                                        <th>{% trans "Class" %}</th>
                                        <th>{% trans "Date" %}</th>
                                        <th>{% trans "Type" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th class="w-1"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for exam in recent_exams %}
                                    <tr>
                                        <td>{{ exam.name }}</td>
                                        <td>{{ exam.class_subject.class_obj }}</td>
                                        <td>{{ exam.exam_date }}</td>
                                        <td>{{ exam.get_exam_type_display }}</td>
                                        <td>
                                            {% if exam.is_completed %}
                                                <span class="badge bg-success">{% trans "Completed" %}</span>
                                            {% else %}
                                                <span class="badge bg-primary">{% trans "Upcoming" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'academics:exam_detail' exam.id %}" class="btn btn-sm btn-outline-primary">
                                                {% trans "View" %}
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty">
                            <div class="empty-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <circle cx="12" cy="12" r="9" />
                                    <line x1="9" y1="10" x2="9.01" y2="10" />
                                    <line x1="15" y1="10" x2="15.01" y2="10" />
                                    <path d="M9.5 15.25a3.5 3.5 0 0 1 5 0" />
                                </svg>
                            </div>
                            <p class="empty-title">{% trans "No exams found" %}</p>
                            <p class="empty-subtitle text-muted">
                                {% trans "There are no exams for this subject yet." %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Subject statistics -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Statistics" %}</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col">{% trans "Classes" %}</div>
                        <div class="col-auto">
                            <span class="badge bg-primary">{{ class_subjects|length }}</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col">{% trans "Teachers" %}</div>
                        <div class="col-auto">
                            <span class="badge bg-primary">{{ subject.teachers.count }}</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col">{% trans "Grades" %}</div>
                        <div class="col-auto">
                            <span class="badge bg-primary">{{ subject.grades.count }}</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col">{% trans "Exams" %}</div>
                        <div class="col-auto">
                            <span class="badge bg-primary">{{ recent_exams|length }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Grades teaching this subject -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Grades Teaching This Subject" %}</h3>
                </div>
                <div class="card-body">
                    {% if subject.grades.all %}
                        <div class="list-group list-group-flush">
                            {% for grade in subject.grades.all %}
                                <div class="list-group-item">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <div class="d-flex align-items-center">
                                                <span class="avatar bg-blue-lt me-2">{{ grade.level }}</span>
                                                <div>{{ grade.name }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty">
                            <p class="empty-title h6">{% trans "No grades assigned" %}</p>
                            <p class="empty-subtitle text-muted">
                                {% trans "This subject is not assigned to any grade yet." %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Teachers teaching this subject -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Teachers" %}</h3>
                </div>
                <div class="card-body">
                    {% if subject.teachers.all %}
                        <div class="list-group list-group-flush">
                            {% for teacher in subject.teachers.all %}
                                <div class="list-group-item">
                                    <div class="row align-items-center">
                                        <div class="col-auto">
                                            <span class="avatar">
                                                {% if teacher.user.profile_picture %}
                                                    <img src="{{ teacher.user.profile_picture.url }}" alt="{{ teacher }}">
                                                {% else %}
                                                    {{ teacher.user.first_name|first }}{{ teacher.user.last_name|first }}
                                                {% endif %}
                                            </span>
                                        </div>
                                        <div class="col">
                                            <div class="d-block">
                                                <a href="{% url 'academics:teacher_detail' teacher.id %}">{{ teacher }}</a>
                                            </div>
                                            <div class="text-muted">{{ teacher.employee_id }}</div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty">
                            <p class="empty-title h6">{% trans "No teachers assigned" %}</p>
                            <p class="empty-subtitle text-muted">
                                {% trans "This subject is not assigned to any teacher yet." %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}