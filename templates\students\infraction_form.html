{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Infraction" %}
    {% else %}
        {% trans "Record Infraction" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        {% if object %}
                            {% trans "Edit Infraction" %}
                        {% else %}
                            {% trans "Record Student Infraction" %}
                        {% endif %}
                    </h2>
                    <p class="text-muted">
                        {% if object %}
                            {% trans "Update disciplinary record information" %}
                        {% else %}
                            {% trans "Record a new disciplinary action or infraction" %}
                        {% endif %}
                    </p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        {{ form.student|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        {{ form.incident_date|as_crispy_field }}
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        {{ form.severity|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        {{ form.action_taken|as_crispy_field }}
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.description|as_crispy_field }}
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            {{ form.follow_up_required }}
                                            <label class="form-check-label" for="{{ form.follow_up_required.id_for_label }}">
                                                {% trans "Follow-up Required" %}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        {{ form.follow_up_date|as_crispy_field }}
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.notes|as_crispy_field }}
                                </div>
                                
                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>{% trans "Save Infraction" %}
                                    </button>
                                    <a href="{% url 'students:infractions' %}" class="btn btn-secondary ms-2">
                                        <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Information" %}</h5>
                        </div>
                        <div class="card-body">
                            <p>{% trans "Student infractions are used to track disciplinary issues and actions taken." %}</p>
                            <p>{% trans "Each infraction should include:" %}</p>
                            <ul>
                                <li>{% trans "The student involved" %}</li>
                                <li>{% trans "Date of the incident" %}</li>
                                <li>{% trans "Description of what happened" %}</li>
                                <li>{% trans "Severity level" %}</li>
                                <li>{% trans "Action taken" %}</li>
                            </ul>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                {% trans "Parents will be automatically notified of infractions if the system is configured to do so." %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Severity Levels" %}</h5>
                        </div>
                        <div class="card-body p-0">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <span class="badge bg-success me-2">{% trans "Minor" %}</span>
                                    {% trans "Small infractions that require minimal intervention" %}
                                </li>
                                <li class="list-group-item">
                                    <span class="badge bg-warning text-dark me-2">{% trans "Moderate" %}</span>
                                    {% trans "Repeated minor infractions or more serious issues" %}
                                </li>
                                <li class="list-group-item">
                                    <span class="badge bg-danger me-2">{% trans "Major" %}</span>
                                    {% trans "Serious infractions requiring significant intervention" %}
                                </li>
                                <li class="list-group-item">
                                    <span class="badge bg-dark me-2">{% trans "Severe" %}</span>
                                    {% trans "Very serious infractions that may require external intervention" %}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide follow-up date based on checkbox
    const followUpRequired = document.getElementById('id_follow_up_required');
    const followUpDateField = document.getElementById('div_id_follow_up_date');
    
    function toggleFollowUpDate() {
        if (followUpRequired.checked) {
            followUpDateField.style.display = 'block';
        } else {
            followUpDateField.style.display = 'none';
        }
    }
    
    // Initial state
    toggleFollowUpDate();
    
    // Add event listener
    followUpRequired.addEventListener('change', toggleFollowUpDate);
});
</script>
{% endblock %}