{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Teachers Management" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>
                        <i class="fas fa-chalkboard-teacher text-primary me-2"></i>{% trans "Teachers Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage school teachers and staff" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:teacher_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add New Teacher" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ total_teachers }}</h3>
                    <p class="mb-0">{% trans "Total Teachers" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ active_teachers }}</h3>
                    <p class="mb-0">{% trans "Active Teachers" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ subjects_taught }}</h3>
                    <p class="mb-0">{% trans "Subjects Taught" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-day fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ classes_today }}</h3>
                    <p class="mb-0">{% trans "Classes Today" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="searchTeachers" class="form-label">{% trans "Search Teachers" %}</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchTeachers" placeholder="{% trans 'Search by name, ID, or subject' %}">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label for="departmentFilter" class="form-label">{% trans "Department" %}</label>
                            <select class="form-select" id="departmentFilter">
                                <option value="">{% trans "All Departments" %}</option>
                                {% for department in departments %}
                                    <option value="{{ department }}">{{ department }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="statusFilter" class="form-label">{% trans "Status" %}</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">{% trans "All Status" %}</option>
                                <option value="active">{% trans "Active" %}</option>
                                <option value="inactive">{% trans "Inactive" %}</option>
                                <option value="on_leave">{% trans "On Leave" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="experienceFilter" class="form-label">{% trans "Experience" %}</label>
                            <select class="form-select" id="experienceFilter">
                                <option value="">{% trans "All Levels" %}</option>
                                <option value="0-2">{% trans "0-2 years" %}</option>
                                <option value="3-5">{% trans "3-5 years" %}</option>
                                <option value="6-10">{% trans "6-10 years" %}</option>
                                <option value="10+">{% trans "10+ years" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary d-block w-100" id="filterButton">
                                <i class="fas fa-filter me-2"></i>{% trans "Filter" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Teachers List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{% trans "Teachers List" %}</h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-secondary active">
                                <i class="fas fa-list"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-th"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Teacher" %}</th>
                                    <th>{% trans "ID" %}</th>
                                    <th>{% trans "Subjects" %}</th>
                                    <th>{% trans "Experience" %}</th>
                                    <th>{% trans "Classes" %}</th>
                                    <th>{% trans "Students" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if teachers %}
                                    {% for teacher in teachers %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                    <span class="text-white fw-bold">{{ teacher.user.first_name|slice:":1" }}{{ teacher.user.last_name|slice:":1" }}</span>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ teacher.user.first_name }} {{ teacher.user.last_name }}</h6>
                                                    <small class="text-muted">ID: {{ teacher.employee_id }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ teacher.employee_id }}</td>
                                        <td>
                                            {% for subject in teacher.subjects.all %}
                                                <span class="badge bg-primary me-1">{{ subject.name }}</span>
                                            {% endfor %}
                                        </td>
                                        <td>{{ teacher.experience_years }} years</td>
                                        <td>{{ teacher.class_subjects.count }}</td>
                                        <td>{{ teacher.student_count }}</td>
                                        <td>
                                            {% if teacher.is_active %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% else %}
                                                <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{% url 'academics:teacher_detail' teacher.id %}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'academics:teacher_edit' teacher.id %}" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-calendar"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center">{% trans "No teachers found" %}</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter button click handler
    document.getElementById('filterButton').addEventListener('click', function() {
        const department = document.getElementById('departmentFilter').value;
        const status = document.getElementById('statusFilter').value;
        const experience = document.getElementById('experienceFilter').value;
        const search = document.getElementById('searchTeachers').value;
        
        // Create the query string
        let queryParams = [];
        if (department) queryParams.push(`department=${encodeURIComponent(department)}`);
        if (status) queryParams.push(`status=${encodeURIComponent(status)}`);
        if (experience) queryParams.push(`experience=${encodeURIComponent(experience)}`);
        if (search) queryParams.push(`search=${encodeURIComponent(search)}`);
        
        // Redirect with the query parameters
        if (queryParams.length > 0) {
            window.location.href = `${window.location.pathname}?${queryParams.join('&')}`;
        } else {
            window.location.href = window.location.pathname;
        }
    });
});
</script>
{% endblock %}