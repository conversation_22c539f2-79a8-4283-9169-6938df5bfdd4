{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Grade Report" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .report-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .report-card:hover {
        transform: translateY(-2px);
    }
    .report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .grade-summary-card {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        text-align: center;
        padding: 20px;
    }
    .chart-container {
        position: relative;
        height: 300px;
    }
    .grade-distribution {
        display: flex;
        justify-content: space-around;
        margin: 20px 0;
    }
    .grade-item {
        text-align: center;
        padding: 15px;
        border-radius: 10px;
        margin: 5px;
        flex: 1;
    }
    .grade-a { background-color: #d4edda; color: #155724; }
    .grade-b { background-color: #d1ecf1; color: #0c5460; }
    .grade-c { background-color: #fff3cd; color: #856404; }
    .grade-d { background-color: #f8d7da; color: #721c24; }
    .grade-f { background-color: #f5c6cb; color: #721c24; }
    .filter-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Grade Report" %}
                    </h1>
                    <p class="text-muted">{% trans "Comprehensive grade analysis and statistics" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>{% trans "Export Report" %}
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printReport()">
                        <i class="fas fa-print me-2"></i>{% trans "Print" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filter-card">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="class_filter" class="form-label">{% trans "Class" %}</label>
                        <select name="class" id="class_filter" class="form-select">
                            <option value="">{% trans "All Classes" %}</option>
                            {% for class in classes %}
                                <option value="{{ class.id }}" {% if selected_class and class.id == selected_class.id %}selected{% endif %}>
                                    {{ class.grade.name }} - {{ class.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="subject_filter" class="form-label">{% trans "Subject" %}</label>
                        <select name="subject" id="subject_filter" class="form-select">
                            <option value="">{% trans "All Subjects" %}</option>
                            {% for subject in subjects %}
                                <option value="{{ subject.id }}" {% if selected_subject and subject.id == selected_subject.id %}selected{% endif %}>
                                    {{ subject.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="exam_filter" class="form-label">{% trans "Exam" %}</label>
                        <select name="exam" id="exam_filter" class="form-select">
                            <option value="">{% trans "All Exams" %}</option>
                            {% for exam in exams %}
                                <option value="{{ exam.id }}" {% if selected_exam and exam.id == selected_exam.id %}selected{% endif %}>
                                    {{ exam.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>{% trans "Generate Report" %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card report-card">
                <div class="card-body text-center">
                    <i class="fas fa-users text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">{{ total_students|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Students" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card">
                <div class="card-body text-center">
                    <i class="fas fa-star text-warning mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-warning">{{ average_grade|floatformat:1|default:0 }}%</h4>
                    <p class="mb-0">{% trans "Average Grade" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card">
                <div class="card-body text-center">
                    <i class="fas fa-trophy text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">{{ highest_grade|floatformat:1|default:0 }}%</h4>
                    <p class="mb-0">{% trans "Highest Grade" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line text-info mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-info">{{ pass_rate|floatformat:1|default:0 }}%</h4>
                    <p class="mb-0">{% trans "Pass Rate" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Grade Distribution -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>{% trans "Grade Distribution" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="grade-distribution">
                        <div class="grade-item grade-a">
                            <h3>{{ grade_a_count|default:0 }}</h3>
                            <p class="mb-0">{% trans "A Grade" %}</p>
                            <small>90% - 100%</small>
                        </div>
                        <div class="grade-item grade-b">
                            <h3>{{ grade_b_count|default:0 }}</h3>
                            <p class="mb-0">{% trans "B Grade" %}</p>
                            <small>80% - 89%</small>
                        </div>
                        <div class="grade-item grade-c">
                            <h3>{{ grade_c_count|default:0 }}</h3>
                            <p class="mb-0">{% trans "C Grade" %}</p>
                            <small>70% - 79%</small>
                        </div>
                        <div class="grade-item grade-d">
                            <h3>{{ grade_d_count|default:0 }}</h3>
                            <p class="mb-0">{% trans "D Grade" %}</p>
                            <small>60% - 69%</small>
                        </div>
                        <div class="grade-item grade-f">
                            <h3>{{ grade_f_count|default:0 }}</h3>
                            <p class="mb-0">{% trans "F Grade" %}</p>
                            <small>Below 60%</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Grade Trends" %}</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="gradeTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Subject Performance" %}</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="subjectPerformanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Grade Table -->
    <div class="row">
        <div class="col-12">
            <div class="card report-card">
                <div class="card-header report-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>{% trans "Detailed Grade Records" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Class" %}</th>
                                    <th>{% trans "Subject" %}</th>
                                    <th>{% trans "Exam" %}</th>
                                    <th>{% trans "Marks" %}</th>
                                    <th>{% trans "Percentage" %}</th>
                                    <th>{% trans "Grade" %}</th>
                                    <th>{% trans "Date" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if grades %}
                                    {% for grade in grades %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {% if grade.student.user.profile_picture %}
                                                        <img src="{{ grade.student.user.profile_picture.url }}" alt="{{ grade.student.user.get_full_name }}" class="rounded-circle me-2" style="width: 30px; height: 30px;">
                                                    {% else %}
                                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <h6 class="mb-0">{{ grade.student.user.first_name }} {{ grade.student.user.last_name }}</h6>
                                                        <small class="text-muted">{{ grade.student.student_id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ grade.student.current_class.grade.name }} - {{ grade.student.current_class.name }}</td>
                                            <td>{{ grade.exam.class_subject.subject.name }}</td>
                                            <td>{{ grade.exam.name }}</td>
                                            <td>{{ grade.marks_obtained }}/{{ grade.total_marks }}</td>
                                            <td>
                                                <span class="fw-bold 
                                                    {% if grade.percentage >= 90 %}text-success
                                                    {% elif grade.percentage >= 80 %}text-primary
                                                    {% elif grade.percentage >= 70 %}text-warning
                                                    {% else %}text-danger{% endif %}">
                                                    {{ grade.percentage|floatformat:1 }}%
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge 
                                                    {% if grade.grade == 'A' %}bg-success
                                                    {% elif grade.grade == 'B' %}bg-primary
                                                    {% elif grade.grade == 'C' %}bg-warning
                                                    {% elif grade.grade == 'D' %}bg-orange
                                                    {% else %}bg-danger{% endif %}">
                                                    {{ grade.grade }}
                                                </span>
                                            </td>
                                            <td>{{ grade.graded_at|date:"M d, Y" }}</td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No grade records found matching your criteria." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Grade Trends Chart
    const gradeTrendsCtx = document.getElementById('gradeTrendsChart').getContext('2d');
    const gradeTrendsChart = new Chart(gradeTrendsCtx, {
        type: 'line',
        data: {
            labels: {{ grade_trend_labels|safe|default:"[]" }},
            datasets: [{
                label: '{% trans "Average Grade %" %}',
                data: {{ grade_trend_data|safe|default:"[]" }},
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Subject Performance Chart
    const subjectPerfCtx = document.getElementById('subjectPerformanceChart').getContext('2d');
    const subjectPerfChart = new Chart(subjectPerfCtx, {
        type: 'bar',
        data: {
            labels: {{ subject_names|safe|default:"[]" }},
            datasets: [{
                label: '{% trans "Average Grade %" %}',
                data: {{ subject_averages|safe|default:"[]" }},
                backgroundColor: [
                    '#007bff', '#28a745', '#ffc107', '#dc3545', 
                    '#17a2b8', '#6f42c1', '#fd7e14', '#20c997'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    function exportReport() {
        window.location.href = '{% url "academics:grade_export" %}?' + window.location.search.substring(1);
    }

    function printReport() {
        window.print();
    }

    // Auto-submit form on filter change
    document.getElementById('class_filter').addEventListener('change', function() {
        this.form.submit();
    });
</script>
{% endblock %}
