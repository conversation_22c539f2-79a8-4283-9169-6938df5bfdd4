{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Profile" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <!-- Profile Card -->
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>{% trans "Profile Information" %}
                </h5>
            </div>
            <div class="card-body text-center">
                {% if user.profile_picture %}
                    <img src="{{ user.profile_picture.url }}" class="rounded-circle mb-3" width="120" height="120" alt="Profile Picture">
                {% else %}
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 120px; height: 120px;">
                        <i class="fas fa-user fa-3x text-white"></i>
                    </div>
                {% endif %}
                
                <h4>{{ user.first_name }} {{ user.last_name }}</h4>
                <p class="text-muted">{{ user.get_user_type_display }}</p>
                <p class="text-muted">
                    <i class="fas fa-envelope me-2"></i>{{ user.email }}
                </p>
                {% if user.phone %}
                    <p class="text-muted">
                        <i class="fas fa-phone me-2"></i>{{ user.phone }}
                    </p>
                {% endif %}
                
                <div class="mt-3">
                    <span class="badge bg-success">
                        <i class="fas fa-check-circle me-1"></i>{% trans "Active" %}
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card dashboard-card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>{% trans "Quick Stats" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ user.date_joined|date:"M Y" }}</h4>
                        <small class="text-muted">{% trans "Member Since" %}</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ user.last_login|date:"M d" }}</h4>
                        <small class="text-muted">{% trans "Last Login" %}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <!-- Profile Form -->
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>{% trans "Edit Profile" %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <h6 class="text-primary mb-3">{% trans "Basic Information" %}</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">{% trans "First Name" %}</label>
                            <input type="text" class="form-control" value="{{ user.first_name }}" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">{% trans "Last Name" %}</label>
                            <input type="text" class="form-control" value="{{ user.last_name }}" readonly>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">{% trans "Email" %}</label>
                            <input type="email" class="form-control" value="{{ user.email }}" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">{% trans "User Type" %}</label>
                            <input type="text" class="form-control" value="{{ user.get_user_type_display }}" readonly>
                        </div>
                    </div>
                    
                    <!-- Extended Profile Information -->
                    <hr>
                    <h6 class="text-primary mb-3">{% trans "Extended Information" %}</h6>
                    
                    {% if form %}
                        {{ form|crispy }}
                    {% else %}
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">{% trans "National ID" %}</label>
                                <input type="text" class="form-control" name="national_id" 
                                       value="{{ object.national_id|default:'' }}" placeholder="{% trans 'Enter National ID' %}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">{% trans "Blood Type" %}</label>
                                <input type="text" class="form-control" name="blood_type" 
                                       value="{{ object.blood_type|default:'' }}" placeholder="{% trans 'Enter Blood Type' %}">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">{% trans "Emergency Contact" %}</label>
                                <input type="text" class="form-control" name="emergency_contact" 
                                       value="{{ object.emergency_contact|default:'' }}" placeholder="{% trans 'Emergency Contact Name' %}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">{% trans "Emergency Phone" %}</label>
                                <input type="text" class="form-control" name="emergency_phone" 
                                       value="{{ object.emergency_phone|default:'' }}" placeholder="{% trans 'Emergency Phone Number' %}">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">{% trans "Medical Conditions" %}</label>
                            <textarea class="form-control" name="medical_conditions" rows="3" 
                                      placeholder="{% trans 'Any medical conditions or allergies' %}">{{ object.medical_conditions|default:'' }}</textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">{% trans "Notes" %}</label>
                            <textarea class="form-control" name="notes" rows="3" 
                                      placeholder="{% trans 'Additional notes' %}">{{ object.notes|default:'' }}</textarea>
                        </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'accounts:dashboard' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Dashboard" %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>{% trans "Save Changes" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Change Password -->
        <div class="card dashboard-card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-key me-2"></i>{% trans "Change Password" %}
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">{% trans "For security reasons, please change your password regularly." %}</p>
                <a href="#" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                    <i class="fas fa-key me-2"></i>{% trans "Change Password" %}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Change Password" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">{% trans "Current Password" %}</label>
                        <input type="password" class="form-control" name="old_password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{% trans "New Password" %}</label>
                        <input type="password" class="form-control" name="new_password1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{% trans "Confirm New Password" %}</label>
                        <input type="password" class="form-control" name="new_password2" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="submit" form="changePasswordForm" class="btn btn-primary">{% trans "Change Password" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // Add password change logic here
    alert('{% trans "Password change functionality will be implemented" %}');
});
</script>
{% endblock %}
