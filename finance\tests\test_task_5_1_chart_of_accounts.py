"""
Tests for Task 5.1: Create Chart of Accounts
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date

from core.models import School
from finance.models import Account, AccountType, JournalEntry
from finance.services import ChartOfAccountsService, AccountValidationService

User = get_user_model()


class TestChartOfAccountsTask(TestCase):
    """Test Chart of Accounts implementation for Task 5.1"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="+**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
    
    def test_account_model_hierarchy(self):
        """Test Account model with hierarchy support"""
        # Create account type
        asset_type = AccountType.objects.create(
            school=self.school,
            name="Assets",
            type="asset"
        )
        
        # Create parent account
        parent_account = Account.objects.create(
            school=self.school,
            code="1000",
            name="Assets",
            account_type=asset_type,
            is_header=True
        )
        
        # Create child account
        child_account = Account.objects.create(
            school=self.school,
            code="1100",
            name="Current Assets",
            account_type=asset_type,
            parent=parent_account,
            is_header=True
        )
        
        # Test hierarchy properties
        self.assertEqual(child_account.level, 1)
        self.assertEqual(child_account.path, "1000/1100")
        self.assertEqual(child_account.parent, parent_account)
        self.assertIn(child_account, parent_account.children.all())
    
    def test_account_validation_controls(self):
        """Test account validation and controls"""
        asset_type = AccountType.objects.create(
            school=self.school,
            name="Assets",
            type="asset"
        )
        
        # Test valid account creation
        account = Account.objects.create(
            school=self.school,
            code="1100",
            name="Current Assets",
            account_type=asset_type,
            opening_balance=Decimal('1000.00')
        )
        
        # Test account properties
        self.assertEqual(account.opening_balance, Decimal('1000.00'))
        self.assertTrue(account.allow_manual_entries)
        self.assertFalse(account.is_system_account)
        
        # Test account code validation
        is_valid, error = AccountValidationService.validate_account_code(
            self.school, "ABC123"
        )
        self.assertFalse(is_valid)
        self.assertIn("digits", str(error))
    
    def test_account_balance_calculation(self):
        """Test account balance calculation"""
        asset_type = AccountType.objects.create(
            school=self.school,
            name="Assets",
            type="asset"
        )
        
        account = Account.objects.create(
            school=self.school,
            code="1110",
            name="Cash",
            account_type=asset_type,
            opening_balance=Decimal('1000.00')
        )
        
        # Create journal entries
        JournalEntry.objects.create(
            school=self.school,
            reference_number="JE001",
            entry_date=date.today(),
            description="Test debit entry",
            account=account,
            debit_amount=Decimal('500.00'),
            is_posted=True,
            created_by=self.user
        )
        
        JournalEntry.objects.create(
            school=self.school,
            reference_number="JE002",
            entry_date=date.today(),
            description="Test credit entry",
            account=account,
            credit_amount=Decimal('200.00'),
            is_posted=True,
            created_by=self.user
        )
        
        # For asset account: opening + debits - credits
        expected_balance = Decimal('1000.00') + Decimal('500.00') - Decimal('200.00')
        self.assertEqual(account.get_balance(), expected_balance)
    
    def test_account_archiving_system(self):
        """Test account archiving functionality"""
        asset_type = AccountType.objects.create(
            school=self.school,
            name="Assets",
            type="asset"
        )
        
        account = Account.objects.create(
            school=self.school,
            code="1110",
            name="Cash",
            account_type=asset_type
        )
        
        # Test archiving
        can_archive, error = account.can_be_archived()
        self.assertTrue(can_archive)
        self.assertIsNone(error)
        
        account.archive(self.user, "Test archiving")
        
        self.assertTrue(account.is_archived)
        self.assertEqual(account.archived_by, self.user)
        self.assertEqual(account.archive_reason, "Test archiving")
        self.assertFalse(account.is_active)
        
        # Test unarchiving
        account.unarchive()
        
        self.assertFalse(account.is_archived)
        self.assertIsNone(account.archived_by)
        self.assertIsNone(account.archive_reason)
        self.assertTrue(account.is_active)
    
    def test_default_accounts_creation(self):
        """Test creation of default chart of accounts"""
        accounts = ChartOfAccountsService.create_default_accounts(self.school)
        
        # Verify main account categories were created
        self.assertIn('1000', accounts)  # Assets
        self.assertIn('2000', accounts)  # Liabilities
        self.assertIn('3000', accounts)  # Equity
        self.assertIn('4000', accounts)  # Revenue
        self.assertIn('5000', accounts)  # Expenses
        
        # Verify hierarchy
        assets_account = accounts['1000']
        current_assets = accounts['1100']
        
        self.assertEqual(current_assets.parent, assets_account)
        self.assertTrue(assets_account.is_header)
        self.assertTrue(assets_account.is_system_account)
    
    def test_account_tree_management(self):
        """Test account tree management interface"""
        ChartOfAccountsService.create_default_accounts(self.school)
        
        # Get account tree
        tree = ChartOfAccountsService.get_account_tree(self.school)
        
        # Verify tree structure
        self.assertEqual(len(tree), 5)  # 5 main account types
        
        # Find assets node
        assets_node = next(
            (node for node in tree if node['account'].code == '1000'), 
            None
        )
        self.assertIsNotNone(assets_node)
        self.assertGreater(len(assets_node['children']), 0)
    
    def test_account_reporting_capabilities(self):
        """Test account reporting capabilities"""
        ChartOfAccountsService.create_default_accounts(self.school)
        
        # Create some journal entries for testing
        cash_account = Account.objects.get(school=self.school, code='1110')
        revenue_account = Account.objects.get(school=self.school, code='4100')
        
        JournalEntry.objects.create(
            school=self.school,
            reference_number="JE001",
            entry_date=date.today(),
            description="Cash receipt",
            account=cash_account,
            debit_amount=Decimal('1000.00'),
            is_posted=True,
            created_by=self.user
        )
        
        JournalEntry.objects.create(
            school=self.school,
            reference_number="JE002",
            entry_date=date.today(),
            description="Revenue recognition",
            account=revenue_account,
            credit_amount=Decimal('1000.00'),
            is_posted=True,
            created_by=self.user
        )
        
        # Test trial balance report
        trial_balance = ChartOfAccountsService.generate_account_report(
            self.school, 'trial_balance'
        )
        
        self.assertEqual(trial_balance['title'], 'Trial Balance')
        self.assertEqual(trial_balance['total_debits'], trial_balance['total_credits'])
        self.assertGreater(len(trial_balance['accounts']), 0)
        
        # Test balance sheet report
        balance_sheet = ChartOfAccountsService.generate_account_report(
            self.school, 'balance_sheet'
        )
        
        self.assertEqual(balance_sheet['title'], 'Balance Sheet')
        self.assertGreater(balance_sheet['total_assets'], 0)
        
        # Test income statement report
        income_statement = ChartOfAccountsService.generate_account_report(
            self.school, 'income_statement'
        )
        
        self.assertEqual(income_statement['title'], 'Income Statement')
        self.assertGreater(income_statement['total_revenue'], 0)
    
    def test_account_manager_methods(self):
        """Test Account manager custom methods"""
        ChartOfAccountsService.create_default_accounts(self.school)
        
        # Test active accounts
        active_accounts = Account.objects.active()
        self.assertGreater(active_accounts.count(), 0)
        
        # Test header accounts
        header_accounts = Account.objects.header_accounts()
        self.assertGreater(header_accounts.count(), 0)
        
        # Test detail accounts
        detail_accounts = Account.objects.detail_accounts()
        self.assertGreater(detail_accounts.count(), 0)
        
        # Test accounts by type
        asset_accounts = Account.objects.by_type('asset')
        self.assertGreater(asset_accounts.count(), 0)
        
        # Test root accounts
        root_accounts = Account.objects.root_accounts()
        self.assertEqual(root_accounts.count(), 5)  # 5 main categories
    
    def test_account_validation_service(self):
        """Test Account validation service methods"""
        asset_type = AccountType.objects.create(
            school=self.school,
            name="Assets",
            type="asset"
        )
        
        # Test code validation
        is_valid, error = AccountValidationService.validate_account_code(
            self.school, "1100"
        )
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        # Create account and test uniqueness
        Account.objects.create(
            school=self.school,
            code="1100",
            name="Test Account",
            account_type=asset_type
        )
        
        is_valid, error = AccountValidationService.validate_account_code(
            self.school, "1100"
        )
        self.assertFalse(is_valid)
        self.assertIn("already exists", str(error))
        
        # Test journal entry validation
        account = Account.objects.get(school=self.school, code="1100")
        
        is_valid, error = AccountValidationService.validate_journal_entry_account(account)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        # Test header account validation
        account.is_header = True
        account.save()
        
        is_valid, error = AccountValidationService.validate_journal_entry_account(account)
        self.assertFalse(is_valid)
        self.assertIn("header accounts", str(error))