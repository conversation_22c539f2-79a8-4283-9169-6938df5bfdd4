{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Parent" %}
    {% else %}
        {% trans "Add New Parent" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-user-friends text-primary me-2"></i>
                        {% if object %}
                            {% trans "Edit Parent" %}
                        {% else %}
                            {% trans "Add New Parent" %}
                        {% endif %}
                    </h2>
                    <p class="text-muted">
                        {% if object %}
                            {% trans "Update parent information" %}
                        {% else %}
                            {% trans "Register a new parent in the system" %}
                        {% endif %}
                    </p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                {{ form|crispy }}
                                
                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        {% if object %}
                                            {% trans "Update Parent" %}
                                        {% else %}
                                            {% trans "Save Parent" %}
                                        {% endif %}
                                    </button>
                                    <a href="{% url 'students:parent_list' %}" class="btn btn-secondary ms-2">
                                        <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Information" %}</h5>
                        </div>
                        <div class="card-body">
                            <p>{% trans "Parent information is used to:" %}</p>
                            <ul>
                                <li>{% trans "Contact parents regarding their children" %}</li>
                                <li>{% trans "Send notifications about school events" %}</li>
                                <li>{% trans "Share academic progress reports" %}</li>
                                <li>{% trans "Handle emergency situations" %}</li>
                            </ul>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                {% trans "At least one parent's contact information is required." %}
                            </div>
                        </div>
                    </div>
                    
                    {% if object %}
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="card-title mb-0">{% trans "Associated Children" %}</h5>
                            </div>
                            <div class="card-body">
                                {% if object.children.all %}
                                    <ul class="list-group list-group-flush">
                                        {% for child in object.children.all %}
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                {{ child.first_name }} {{ child.last_name }}
                                                <a href="{% url 'students:detail' child.id %}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </li>
                                        {% endfor %}
                                    </ul>
                                {% else %}
                                    <div class="text-center text-muted">
                                        <p>{% trans "No children associated with this parent." %}</p>
                                        <a href="{% url 'students:add_student' %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-plus me-2"></i>{% trans "Add Student" %}
                                        </a>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Optional: Add any client-side validation or dynamic form behavior here
});
</script>
{% endblock %}