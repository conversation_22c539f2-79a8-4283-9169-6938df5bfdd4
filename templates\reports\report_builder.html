{% extends 'base.html' %}
{% load static %}

{% block title %}منشئ التقارير{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .builder-step {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s;
    }
    .builder-step.active {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #007bff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 1rem;
    }
    .field-item {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        margin-bottom: 0.5rem;
        cursor: pointer;
        transition: all 0.3s;
    }
    .field-item:hover {
        background-color: #f8f9fa;
        border-color: #007bff;
    }
    .selected-fields {
        min-height: 200px;
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 1rem;
    }
    .preview-area {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        min-height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">منشئ التقارير</h1>
                    <p class="text-muted">أداة سهلة لإنشاء تقارير مخصصة بدون برمجة</p>
                </div>
                <div>
                    <button class="btn btn-success me-2">
                        <i class="fas fa-save me-2"></i>حفظ التقرير
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-play me-2"></i>تشغيل التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Builder Steps -->
    <div class="row">
        <div class="col-lg-3">
            <!-- Step 1: Data Source -->
            <div class="builder-step active">
                <div class="d-flex align-items-center mb-3">
                    <div class="step-number">1</div>
                    <h6 class="mb-0">مصدر البيانات</h6>
                </div>
                <select class="form-select mb-2">
                    <option>اختر مصدر البيانات</option>
                    <option value="students">الطلاب</option>
                    <option value="teachers">المعلمون</option>
                    <option value="attendance">الحضور</option>
                    <option value="grades">الدرجات</option>
                    <option value="finance">المالية</option>
                </select>
                <small class="text-muted">اختر الجدول أو المصدر الذي تريد إنشاء التقرير منه</small>
            </div>

            <!-- Step 2: Fields Selection -->
            <div class="builder-step">
                <div class="d-flex align-items-center mb-3">
                    <div class="step-number">2</div>
                    <h6 class="mb-0">اختيار الحقول</h6>
                </div>
                <div class="available-fields">
                    <h6 class="small">الحقول المتاحة:</h6>
                    <div class="field-item" draggable="true">
                        <i class="fas fa-user me-2"></i>اسم الطالب
                    </div>
                    <div class="field-item" draggable="true">
                        <i class="fas fa-school me-2"></i>الفصل
                    </div>
                    <div class="field-item" draggable="true">
                        <i class="fas fa-calendar me-2"></i>تاريخ التسجيل
                    </div>
                    <div class="field-item" draggable="true">
                        <i class="fas fa-star me-2"></i>المعدل
                    </div>
                    <div class="field-item" draggable="true">
                        <i class="fas fa-phone me-2"></i>رقم الهاتف
                    </div>
                </div>
            </div>

            <!-- Step 3: Filters -->
            <div class="builder-step">
                <div class="d-flex align-items-center mb-3">
                    <div class="step-number">3</div>
                    <h6 class="mb-0">الفلاتر</h6>
                </div>
                <button class="btn btn-outline-primary btn-sm w-100">
                    <i class="fas fa-plus me-2"></i>إضافة فلتر
                </button>
            </div>

            <!-- Step 4: Sorting -->
            <div class="builder-step">
                <div class="d-flex align-items-center mb-3">
                    <div class="step-number">4</div>
                    <h6 class="mb-0">الترتيب</h6>
                </div>
                <select class="form-select mb-2">
                    <option>اختر حقل الترتيب</option>
                    <option>اسم الطالب</option>
                    <option>المعدل</option>
                    <option>تاريخ التسجيل</option>
                </select>
                <select class="form-select">
                    <option>تصاعدي</option>
                    <option>تنازلي</option>
                </select>
            </div>
        </div>

        <div class="col-lg-6">
            <!-- Selected Fields Area -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">الحقول المحددة</h6>
                </div>
                <div class="card-body">
                    <div class="selected-fields">
                        <div class="text-center text-muted">
                            <i class="fas fa-mouse-pointer fa-2x mb-2"></i>
                            <p>اسحب الحقول من القائمة الجانبية إلى هنا</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters Configuration -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">إعدادات الفلاتر</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">الحقل</label>
                            <select class="form-select">
                                <option>اختر الحقل</option>
                                <option>الفصل</option>
                                <option>المعدل</option>
                                <option>تاريخ التسجيل</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الشرط</label>
                            <select class="form-select">
                                <option>يساوي</option>
                                <option>أكبر من</option>
                                <option>أقل من</option>
                                <option>يحتوي على</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">القيمة</label>
                            <input type="text" class="form-control" placeholder="أدخل القيمة">
                        </div>
                    </div>
                    <button class="btn btn-primary btn-sm mt-2">
                        <i class="fas fa-plus me-2"></i>إضافة فلتر
                    </button>
                </div>
            </div>

            <!-- Report Settings -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">إعدادات التقرير</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">اسم التقرير</label>
                            <input type="text" class="form-control" placeholder="أدخل اسم التقرير">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تنسيق الإخراج</label>
                            <select class="form-select">
                                <option>جدول HTML</option>
                                <option>Excel</option>
                                <option>PDF</option>
                                <option>CSV</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">عدد الصفوف</label>
                            <input type="number" class="form-control" value="100" placeholder="عدد الصفوف">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تجميع البيانات</label>
                            <select class="form-select">
                                <option>بدون تجميع</option>
                                <option>حسب الفصل</option>
                                <option>حسب التاريخ</option>
                                <option>حسب المعدل</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3">
            <!-- Preview Area -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">معاينة التقرير</h6>
                </div>
                <div class="card-body">
                    <div class="preview-area">
                        <div class="text-center text-muted">
                            <i class="fas fa-eye fa-2x mb-2"></i>
                            <p>ستظهر معاينة التقرير هنا</p>
                            <button class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-refresh me-2"></i>تحديث المعاينة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Templates -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">قوالب سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-users me-2"></i>قائمة الطلاب
                        </button>
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-calendar-check me-2"></i>تقرير الحضور
                        </button>
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-star me-2"></i>تقرير الدرجات
                        </button>
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-dollar-sign me-2"></i>تقرير مالي
                        </button>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">الإجراءات</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-success">
                            <i class="fas fa-save me-2"></i>حفظ التقرير
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-play me-2"></i>تشغيل التقرير
                        </button>
                        <button class="btn btn-outline-secondary">
                            <i class="fas fa-download me-2"></i>تصدير
                        </button>
                        <button class="btn btn-outline-danger">
                            <i class="fas fa-trash me-2"></i>مسح
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Add drag and drop functionality
    document.addEventListener('DOMContentLoaded', function() {
        const fieldItems = document.querySelectorAll('.field-item');
        const selectedFields = document.querySelector('.selected-fields');

        fieldItems.forEach(item => {
            item.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('text/plain', this.innerHTML);
            });
        });

        selectedFields.addEventListener('dragover', function(e) {
            e.preventDefault();
        });

        selectedFields.addEventListener('drop', function(e) {
            e.preventDefault();
            const data = e.dataTransfer.getData('text/plain');
            
            // Clear placeholder text if it exists
            if (this.querySelector('.text-center')) {
                this.innerHTML = '';
            }
            
            // Add the dropped field
            const fieldDiv = document.createElement('div');
            fieldDiv.className = 'field-item mb-2';
            fieldDiv.innerHTML = data + ' <button class="btn btn-sm btn-outline-danger float-end"><i class="fas fa-times"></i></button>';
            this.appendChild(fieldDiv);
            
            // Add remove functionality
            fieldDiv.querySelector('button').addEventListener('click', function() {
                fieldDiv.remove();
                
                // Show placeholder if no fields left
                if (selectedFields.children.length === 0) {
                    selectedFields.innerHTML = `
                        <div class="text-center text-muted">
                            <i class="fas fa-mouse-pointer fa-2x mb-2"></i>
                            <p>اسحب الحقول من القائمة الجانبية إلى هنا</p>
                        </div>
                    `;
                }
            });
        });
    });
</script>
{% endblock %}
