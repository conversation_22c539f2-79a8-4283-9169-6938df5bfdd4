"""
Unit and Integration Tests for Attendance Management System (Task 4.5)

This test suite covers:
- Multiple attendance tracking methods
- Biometric integration support
- QR code attendance system
- Attendance analytics and reporting
- Parent notification system

Requirements: 3.5
"""

import pytest
import uuid
from datetime import date, time, datetime, timedelta
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from accounts.models import User
from core.models import School, AcademicYear, Semester
from students.models import Student, Grade, Class, Parent
from academics.models import Subject, ClassSubject, Teacher
from academics.models import (
    AttendanceSession,
    StudentAttendance,
    BiometricDevice,
    AttendanceRule
)
from academics.attendance_system import (
    AttendanceAnalytics,
    AttendanceNotificationService
)


class AttendanceSystemTestCase(TestCase):
    """Base test case with common setup for attendance system tests"""
    
    def setUp(self):
        """Set up test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2020, 1, 1)
        )
        
        # Create academic year and semester
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.semester = Semester.objects.create(
            school=self.school,
            academic_year=self.academic_year,
            name="Fall 2024",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        # Create grade and class
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 10",
            level=10,
            max_capacity=100
        )
        
        self.class_obj = Class.objects.create(
            school=self.school,
            name="10A",
            grade=self.grade,
            academic_year=self.academic_year,
            max_students=30
        )
        
        # Create subject
        self.subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH101",
            credit_hours=3,
            weekly_hours=5
        )
        self.subject.grades.add(self.grade)
        
        # Create teacher
        self.teacher_user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            first_name="John",
            last_name="Teacher"
        )
        
        self.teacher = Teacher.objects.create(
            school=self.school,
            user=self.teacher_user,
            employee_id="T001",
            hire_date=date(2024, 8, 1),
            qualification="M.Sc. Mathematics",
            experience_years=5
        )
        self.teacher.subjects.add(self.subject)
        
        # Create class-subject assignment
        self.class_subject = ClassSubject.objects.create(
            school=self.school,
            class_obj=self.class_obj,
            subject=self.subject,
            teacher=self.teacher,
            academic_year=self.academic_year,
            semester=self.semester,
            weekly_hours=5
        )
        
        # Create students
        self.students = []
        for i in range(5):
            # Create parent user first
            parent_user = User.objects.create_user(
                username=f"parent{i+1}",
                email=f"parent{i+1}@test.com",
                password="testpass123",
                user_type="parent"
            )
            
            # Create parent
            parent = Parent.objects.create(
                school=self.school,
                user=parent_user,
                father_name=f"Parent{i+1}",
                father_phone=f"123-456-78{i:02d}",
                home_address="123 Test St"
            )
            
            # Create student user
            student_user = User.objects.create_user(
                username=f"student{i+1}",
                email=f"student{i+1}@test.com",
                password="testpass123",
                user_type="student"
            )
            
            student = Student.objects.create(
                school=self.school,
                user=student_user,
                student_id=f"STU{i+1:03d}",
                admission_number=f"ADM{i+1:03d}",
                first_name=f"Student{i+1}",
                last_name="Test",
                date_of_birth=date(2008, 1, 1),
                gender="M" if i % 2 == 0 else "F",
                nationality="US",
                admission_date=date(2024, 9, 1),
                current_class=self.class_obj,
                parent=parent
            )
            self.students.append(student)


class AttendanceSessionModelTest(AttendanceSystemTestCase):
    """Test AttendanceSession model functionality"""
    
    def test_create_attendance_session(self):
        """Test creating an attendance session"""
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0),
            session_topic="Algebra Basics",
            attendance_method="manual"
        )
        
        self.assertEqual(session.class_subject, self.class_subject)
        self.assertEqual(session.session_date, date.today())
        self.assertEqual(session.status, 'scheduled')
        self.assertIsNotNone(session.qr_code_token)
    
    def test_session_validation(self):
        """Test session validation rules"""
        # Test invalid time range
        with self.assertRaises(ValidationError):
            session = AttendanceSession(
                school=self.school,
                class_subject=self.class_subject,
                session_date=date.today(),
                start_time=time(10, 0),
                end_time=time(9, 0)  # End before start
            )
            session.clean()
    
    def test_generate_qr_code(self):
        """Test QR code generation"""
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0),
            attendance_method="qr_code"
        )
        
        qr_code = session.generate_qr_code()
        self.assertIsInstance(qr_code, str)
        self.assertTrue(len(qr_code) > 0)
    
    def test_qr_code_validity(self):
        """Test QR code validity checking"""
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0),
            attendance_method="qr_code",
            qr_code_expires_at=timezone.now() + timedelta(hours=1)
        )
        
        self.assertTrue(session.is_qr_code_valid())
        
        # Test expired QR code
        session.qr_code_expires_at = timezone.now() - timedelta(hours=1)
        session.save()
        self.assertFalse(session.is_qr_code_valid())
    
    def test_start_session(self):
        """Test starting an attendance session"""
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0)
        )
        
        session.start_session()
        
        self.assertEqual(session.status, 'active')
        self.assertEqual(session.total_students, 5)  # 5 students in class
        
        # Check that attendance records were created
        attendance_records = StudentAttendance.objects.filter(session=session)
        self.assertEqual(attendance_records.count(), 5)
        
        # All should default to absent
        for record in attendance_records:
            self.assertEqual(record.status, 'absent')
    
    def test_complete_session(self):
        """Test completing an attendance session"""
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0)
        )
        
        session.start_session()
        
        # Mark some students present
        attendance_records = StudentAttendance.objects.filter(session=session)
        attendance_records[0].mark_attendance('present')
        attendance_records[1].mark_attendance('present')
        attendance_records[2].mark_attendance('late')
        # Leave 2 as absent
        
        session.complete_session()
        
        self.assertEqual(session.status, 'completed')
        self.assertEqual(session.present_count, 2)
        self.assertEqual(session.absent_count, 2)
        self.assertEqual(session.late_count, 1)
    
    def test_get_attendance_rate(self):
        """Test attendance rate calculation"""
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0),
            total_students=5,
            present_count=4
        )
        
        self.assertEqual(session.get_attendance_rate(), 80.0)
    
    def test_session_summary(self):
        """Test getting session summary"""
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0),
            total_students=5,
            present_count=3,
            absent_count=1,
            late_count=1
        )
        
        summary = session.get_session_summary()
        
        self.assertEqual(summary['total_students'], 5)
        self.assertEqual(summary['present_count'], 3)
        self.assertEqual(summary['attendance_rate'], 60.0)
        self.assertEqual(summary['session_duration'], 60)  # 1 hour


class StudentAttendanceModelTest(AttendanceSystemTestCase):
    """Test StudentAttendance model functionality"""
    
    def setUp(self):
        super().setUp()
        self.session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0)
        )
        self.session.start_session()
    
    def test_create_student_attendance(self):
        """Test creating student attendance record"""
        attendance = StudentAttendance.objects.filter(
            session=self.session,
            student=self.students[0]
        ).first()
        
        self.assertIsNotNone(attendance)
        self.assertEqual(attendance.status, 'absent')  # Default status
        self.assertEqual(attendance.class_subject, self.class_subject)
    
    def test_mark_attendance(self):
        """Test marking student attendance"""
        attendance = StudentAttendance.objects.filter(
            session=self.session,
            student=self.students[0]
        ).first()
        
        attendance.mark_attendance(
            status='present',
            marked_by=self.teacher_user,
            method='manual',
            notes='On time'
        )
        
        self.assertEqual(attendance.status, 'present')
        self.assertEqual(attendance.marked_by, self.teacher_user)
        self.assertEqual(attendance.marking_method, 'manual')
        self.assertEqual(attendance.notes, 'On time')
        self.assertIsNotNone(attendance.marked_at)
        self.assertIsNotNone(attendance.arrival_time)
    
    def test_biometric_attendance(self):
        """Test marking attendance with biometric data"""
        attendance = StudentAttendance.objects.filter(
            session=self.session,
            student=self.students[0]
        ).first()
        
        biometric_data = {
            'fingerprint_hash': 'abc123def456',
            'confidence_score': 0.95
        }
        
        attendance.mark_attendance(
            status='present',
            method='biometric',
            biometric_data=biometric_data
        )
        
        self.assertEqual(attendance.marking_method, 'biometric')
        self.assertEqual(attendance.biometric_data, biometric_data)
    
    def test_qr_code_attendance(self):
        """Test marking attendance with QR code"""
        attendance = StudentAttendance.objects.filter(
            session=self.session,
            student=self.students[0]
        ).first()
        
        location_data = {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'accuracy': 10
        }
        
        device_info = {
            'device_type': 'smartphone',
            'os': 'Android',
            'app_version': '1.0.0'
        }
        
        attendance.mark_attendance(
            status='present',
            method='qr_code',
            location_data=location_data,
            device_info=device_info
        )
        
        self.assertEqual(attendance.marking_method, 'qr_code')
        self.assertEqual(attendance.location_data, location_data)
        self.assertEqual(attendance.device_info, device_info)
    
    def test_is_late_detection(self):
        """Test late arrival detection"""
        attendance = StudentAttendance.objects.filter(
            session=self.session,
            student=self.students[0]
        ).first()
        
        # Arrive 15 minutes late
        late_time = time(9, 15)
        attendance.arrival_time = late_time
        attendance.save()
        
        self.assertTrue(attendance.is_late())
    
    def test_session_duration_calculation(self):
        """Test session duration calculation"""
        attendance = StudentAttendance.objects.filter(
            session=self.session,
            student=self.students[0]
        ).first()
        
        attendance.arrival_time = time(9, 5)
        attendance.departure_time = time(9, 55)
        attendance.save()
        
        duration = attendance.calculate_session_duration()
        self.assertEqual(duration, 50)  # 50 minutes
    
    def test_attendance_validation(self):
        """Test attendance record validation"""
        # Test student not in class
        other_student = Student.objects.create(
            school=self.school,
            student_id="STU999",
            first_name="Other",
            last_name="Student",
            date_of_birth=date(2008, 1, 1),
            gender="M",
            nationality="US",
            admission_date=date(2024, 9, 1),
            current_grade=self.grade
        )
        
        with self.assertRaises(ValidationError):
            attendance = StudentAttendance(
                school=self.school,
                session=self.session,
                student=other_student,
                class_subject=self.class_subject
            )
            attendance.clean()


class BiometricDeviceModelTest(AttendanceSystemTestCase):
    """Test BiometricDevice model functionality"""
    
    def test_create_biometric_device(self):
        """Test creating a biometric device"""
        device = BiometricDevice.objects.create(
            school=self.school,
            name="Main Entrance Scanner",
            device_type="fingerprint",
            device_id="FP001",
            location="Main Entrance",
            ip_address="*************",
            port=8080
        )
        
        self.assertEqual(device.name, "Main Entrance Scanner")
        self.assertEqual(device.device_type, "fingerprint")
        self.assertEqual(device.status, "active")
    
    def test_device_status(self):
        """Test getting device status"""
        device = BiometricDevice.objects.create(
            school=self.school,
            name="Test Scanner",
            device_type="fingerprint",
            device_id="FP002",
            location="Classroom A"
        )
        
        status = device.get_device_status()
        
        self.assertIn('status', status)
        self.assertIn('location', status)
        self.assertIn('type', status)
        self.assertIn('connection_status', status)
    
    @patch('academics.attendance_system.BiometricDevice.test_connection')
    def test_connection_test(self, mock_test):
        """Test device connection testing"""
        mock_test.return_value = True
        
        device = BiometricDevice.objects.create(
            school=self.school,
            name="Test Scanner",
            device_type="fingerprint",
            device_id="FP003",
            location="Test Location"
        )
        
        self.assertTrue(device.test_connection())


class AttendanceRuleModelTest(AttendanceSystemTestCase):
    """Test AttendanceRule model functionality"""
    
    def test_create_attendance_rule(self):
        """Test creating an attendance rule"""
        rule = AttendanceRule.objects.create(
            school=self.school,
            name="Minimum Attendance Policy",
            rule_type="minimum_attendance",
            applicable_to="all",
            rule_value=75.0,
            description="Students must maintain 75% attendance",
            effective_from=date.today()
        )
        
        self.assertEqual(rule.name, "Minimum Attendance Policy")
        self.assertEqual(rule.rule_value, 75.0)
        self.assertTrue(rule.is_active)
    
    def test_rule_applicability(self):
        """Test rule applicability checking"""
        # Rule for all classes
        rule_all = AttendanceRule.objects.create(
            school=self.school,
            name="General Rule",
            rule_type="minimum_attendance",
            applicable_to="all",
            rule_value=75.0,
            effective_from=date.today()
        )
        
        self.assertTrue(rule_all.is_applicable_to_class_subject(self.class_subject))
        
        # Rule for specific grade
        rule_grade = AttendanceRule.objects.create(
            school=self.school,
            name="Grade Rule",
            rule_type="minimum_attendance",
            applicable_to="grade",
            rule_value=80.0,
            effective_from=date.today()
        )
        rule_grade.target_grades.add(self.grade)
        
        self.assertTrue(rule_grade.is_applicable_to_class_subject(self.class_subject))
        
        # Rule for different grade
        other_grade = Grade.objects.create(
            school=self.school,
            name="Grade 11",
            level=11,
            capacity=100
        )
        rule_other = AttendanceRule.objects.create(
            school=self.school,
            name="Other Grade Rule",
            rule_type="minimum_attendance",
            applicable_to="grade",
            rule_value=85.0,
            effective_from=date.today()
        )
        rule_other.target_grades.add(other_grade)
        
        self.assertFalse(rule_other.is_applicable_to_class_subject(self.class_subject))
    
    def test_rule_effective_dates(self):
        """Test rule effective date validation"""
        # Future rule
        future_rule = AttendanceRule.objects.create(
            school=self.school,
            name="Future Rule",
            rule_type="minimum_attendance",
            applicable_to="all",
            rule_value=75.0,
            effective_from=date.today() + timedelta(days=30)
        )
        
        self.assertFalse(future_rule.is_applicable_to_class_subject(self.class_subject))
        
        # Expired rule
        expired_rule = AttendanceRule.objects.create(
            school=self.school,
            name="Expired Rule",
            rule_type="minimum_attendance",
            applicable_to="all",
            rule_value=75.0,
            effective_from=date.today() - timedelta(days=60),
            effective_to=date.today() - timedelta(days=30)
        )
        
        self.assertFalse(expired_rule.is_applicable_to_class_subject(self.class_subject))


class AttendanceAnalyticsTest(AttendanceSystemTestCase):
    """Test AttendanceAnalytics functionality"""
    
    def setUp(self):
        super().setUp()
        
        # Create multiple sessions with attendance data
        self.sessions = []
        for i in range(10):
            session = AttendanceSession.objects.create(
                school=self.school,
                class_subject=self.class_subject,
                session_date=date.today() - timedelta(days=i),
                start_time=time(9, 0),
                end_time=time(10, 0)
            )
            session.start_session()
            self.sessions.append(session)
            
            # Mark attendance for students (varying patterns)
            attendance_records = StudentAttendance.objects.filter(session=session)
            for j, record in enumerate(attendance_records):
                if j < 3:  # First 3 students always present
                    record.mark_attendance('present')
                elif j == 3 and i % 3 == 0:  # 4th student sometimes absent
                    record.mark_attendance('absent')
                else:
                    record.mark_attendance('present')
    
    def test_student_attendance_summary(self):
        """Test getting student attendance summary"""
        student = self.students[0]
        summary = AttendanceAnalytics.get_student_attendance_summary(
            student=student,
            academic_year=self.academic_year
        )
        
        self.assertIn('total_sessions', summary)
        self.assertIn('present_count', summary)
        self.assertIn('attendance_rate', summary)
        self.assertEqual(summary['total_sessions'], 10)
        self.assertEqual(summary['present_count'], 10)  # Always present
        self.assertEqual(summary['attendance_rate'], 100.0)
    
    def test_class_attendance_summary(self):
        """Test getting class attendance summary"""
        summary = AttendanceAnalytics.get_class_attendance_summary(
            class_obj=self.class_obj,
            academic_year=self.academic_year
        )
        
        self.assertIn('total_records', summary)
        self.assertIn('overall_attendance_rate', summary)
        self.assertEqual(summary['total_records'], 50)  # 10 sessions × 5 students
        self.assertGreater(summary['overall_attendance_rate'], 80.0)
    
    def test_attendance_trends(self):
        """Test getting attendance trends"""
        trends = AttendanceAnalytics.get_attendance_trends(
            school=self.school,
            period_days=15
        )
        
        self.assertIn('daily_trends', trends)
        self.assertIn('average_attendance_rate', trends)
        self.assertGreater(len(trends['daily_trends']), 0)
        self.assertGreater(trends['average_attendance_rate'], 0)
    
    def test_identify_at_risk_students(self):
        """Test identifying at-risk students"""
        # Create a student with poor attendance
        poor_student = Student.objects.create(
            school=self.school,
            student_id="STU999",
            first_name="Poor",
            last_name="Attendance",
            date_of_birth=date(2008, 1, 1),
            gender="M",
            nationality="US",
            admission_date=date(2024, 9, 1),
            current_grade=self.grade,
            is_active=True
        )
        self.class_obj.students.add(poor_student)
        
        # Create sessions with poor attendance
        for i in range(10):
            session = AttendanceSession.objects.create(
                school=self.school,
                class_subject=self.class_subject,
                session_date=date.today() - timedelta(days=i+20),
                start_time=time(10, 0),
                end_time=time(11, 0)
            )
            
            # Create attendance record with mostly absent
            StudentAttendance.objects.create(
                school=self.school,
                session=session,
                student=poor_student,
                class_subject=self.class_subject,
                status='absent' if i < 7 else 'present'  # 70% absent
            )
        
        at_risk = AttendanceAnalytics.identify_at_risk_students(
            school=self.school,
            threshold=75.0,
            academic_year=self.academic_year
        )
        
        self.assertGreater(len(at_risk), 0)
        
        # Check if our poor attendance student is identified
        poor_student_found = any(
            student['student_id'] == poor_student.id 
            for student in at_risk
        )
        self.assertTrue(poor_student_found)


class AttendanceNotificationServiceTest(AttendanceSystemTestCase):
    """Test AttendanceNotificationService functionality"""
    
    def setUp(self):
        super().setUp()
        
        # Create notification templates
        from communications.models import NotificationTemplate
        
        NotificationTemplate.objects.create(
            school=self.school,
            name='student_absence',
            subject='Student Absence Notification',
            body='Dear Parent, {student_name} was absent from {subject_name} class on {session_date}.',
            notification_type='email',
            is_active=True
        )
        
        NotificationTemplate.objects.create(
            school=self.school,
            name='attendance_summary_weekly',
            subject='Weekly Attendance Summary',
            body='Weekly attendance summary for {student_name}: {attendance_rate}% attendance rate.',
            notification_type='email',
            is_active=True
        )
    
    @patch('communications.tasks.send_notification.delay')
    def test_send_absence_notification(self, mock_send):
        """Test sending absence notification"""
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0)
        )
        
        student = self.students[0]
        
        AttendanceNotificationService.send_absence_notification(
            student=student,
            session=session
        )
        
        # Should be called for parent's email and phone
        self.assertTrue(mock_send.called)
        self.assertGreaterEqual(mock_send.call_count, 1)
    
    @patch('communications.tasks.send_notification.delay')
    def test_send_attendance_summary(self, mock_send):
        """Test sending attendance summary"""
        student = self.students[0]
        
        AttendanceNotificationService.send_attendance_summary(
            student=student,
            period='weekly'
        )
        
        # Should be called for parent's email
        self.assertTrue(mock_send.called)


class AttendanceIntegrationTest(AttendanceSystemTestCase):
    """Integration tests for complete attendance workflows"""
    
    def test_complete_attendance_workflow(self):
        """Test complete attendance taking workflow"""
        # 1. Create and start session
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0),
            session_topic="Integration Test Session",
            attendance_method="manual"
        )
        
        session.start_session()
        self.assertEqual(session.status, 'active')
        
        # 2. Mark attendance for students
        attendance_records = StudentAttendance.objects.filter(session=session)
        
        # Mark different attendance statuses
        attendance_records[0].mark_attendance('present', marked_by=self.teacher_user)
        attendance_records[1].mark_attendance('present', marked_by=self.teacher_user)
        attendance_records[2].mark_attendance('late', marked_by=self.teacher_user)
        attendance_records[3].mark_attendance('absent', marked_by=self.teacher_user)
        attendance_records[4].mark_attendance('excused', marked_by=self.teacher_user)
        
        # 3. Complete session
        session.complete_session()
        
        # 4. Verify final counts
        self.assertEqual(session.status, 'completed')
        self.assertEqual(session.present_count, 2)
        self.assertEqual(session.absent_count, 1)
        self.assertEqual(session.late_count, 1)
        self.assertEqual(session.excused_count, 1)
        
        # 5. Verify attendance rate
        self.assertEqual(session.get_attendance_rate(), 40.0)  # 2/5 = 40%
        
        # 6. Get session summary
        summary = session.get_session_summary()
        self.assertEqual(summary['total_students'], 5)
        self.assertEqual(summary['attendance_rate'], 40.0)
    
    def test_qr_code_attendance_workflow(self):
        """Test QR code attendance workflow"""
        # 1. Create QR code session
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0),
            attendance_method="qr_code"
        )
        
        session.start_session()
        
        # 2. Generate QR code
        qr_code = session.generate_qr_code()
        self.assertIsNotNone(qr_code)
        self.assertTrue(session.is_qr_code_valid())
        
        # 3. Simulate QR code scanning
        attendance = StudentAttendance.objects.filter(
            session=session,
            student=self.students[0]
        ).first()
        
        attendance.mark_attendance(
            status='present',
            method='qr_code',
            location_data={'latitude': 40.7128, 'longitude': -74.0060},
            device_info={'device_type': 'smartphone', 'os': 'iOS'}
        )
        
        # 4. Verify QR code attendance
        self.assertEqual(attendance.marking_method, 'qr_code')
        self.assertIsNotNone(attendance.location_data)
        self.assertIsNotNone(attendance.device_info)
    
    def test_biometric_attendance_workflow(self):
        """Test biometric attendance workflow"""
        # 1. Create biometric device
        device = BiometricDevice.objects.create(
            school=self.school,
            name="Test Fingerprint Scanner",
            device_type="fingerprint",
            device_id="FP001",
            location="Main Entrance"
        )
        
        # 2. Create session
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0),
            attendance_method="biometric"
        )
        
        session.start_session()
        
        # 3. Simulate biometric attendance
        attendance = StudentAttendance.objects.filter(
            session=session,
            student=self.students[0]
        ).first()
        
        biometric_data = {
            'device_id': device.device_id,
            'fingerprint_hash': 'abc123def456',
            'confidence_score': 0.95,
            'scan_time': timezone.now().isoformat()
        }
        
        attendance.mark_attendance(
            status='present',
            method='biometric',
            biometric_data=biometric_data
        )
        
        # 4. Verify biometric attendance
        self.assertEqual(attendance.marking_method, 'biometric')
        self.assertEqual(attendance.biometric_data['device_id'], device.device_id)
        self.assertEqual(attendance.biometric_data['confidence_score'], 0.95)
    
    @patch('academics.attendance_system.AttendanceNotificationService.send_absence_notification')
    def test_attendance_with_notifications(self, mock_notification):
        """Test attendance workflow with parent notifications"""
        # 1. Create session
        session = AttendanceSession.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            session_date=date.today(),
            start_time=time(9, 0),
            end_time=time(10, 0)
        )
        
        session.start_session()
        
        # 2. Mark some students absent
        attendance_records = StudentAttendance.objects.filter(session=session)
        attendance_records[0].mark_attendance('present')
        attendance_records[1].mark_attendance('absent')  # This should trigger notification
        attendance_records[2].mark_attendance('late')    # This should trigger notification
        
        # 3. Complete session (triggers notifications)
        session.complete_session()
        
        # 4. Verify notifications were triggered
        self.assertTrue(mock_notification.called)
    
    def test_attendance_analytics_integration(self):
        """Test integration with attendance analytics"""
        # 1. Create multiple sessions with varied attendance
        sessions = []
        for i in range(5):
            session = AttendanceSession.objects.create(
                school=self.school,
                class_subject=self.class_subject,
                session_date=date.today() - timedelta(days=i),
                start_time=time(9, 0),
                end_time=time(10, 0)
            )
            session.start_session()
            sessions.append(session)
            
            # Create attendance pattern
            attendance_records = StudentAttendance.objects.filter(session=session)
            for j, record in enumerate(attendance_records):
                if j == 0:  # Student 0 always present
                    record.mark_attendance('present')
                elif j == 1 and i < 2:  # Student 1 absent first 2 days
                    record.mark_attendance('absent')
                else:
                    record.mark_attendance('present')
        
        # 2. Test student analytics
        student_summary = AttendanceAnalytics.get_student_attendance_summary(
            student=self.students[0],
            academic_year=self.academic_year
        )
        
        self.assertEqual(student_summary['attendance_rate'], 100.0)
        
        student_summary_1 = AttendanceAnalytics.get_student_attendance_summary(
            student=self.students[1],
            academic_year=self.academic_year
        )
        
        self.assertEqual(student_summary_1['attendance_rate'], 60.0)  # 3/5 = 60%
        
        # 3. Test class analytics
        class_summary = AttendanceAnalytics.get_class_attendance_summary(
            class_obj=self.class_obj,
            academic_year=self.academic_year
        )
        
        self.assertEqual(class_summary['total_records'], 25)  # 5 sessions × 5 students
        self.assertGreater(class_summary['overall_attendance_rate'], 80.0)
        
        # 4. Test at-risk identification
        at_risk = AttendanceAnalytics.identify_at_risk_students(
            school=self.school,
            threshold=75.0,
            academic_year=self.academic_year
        )
        
        # Student 1 should be identified as at-risk (60% < 75%)
        at_risk_ids = [student['student_id'] for student in at_risk]
        self.assertIn(self.students[1].id, at_risk_ids)


if __name__ == '__main__':
    pytest.main([__file__])