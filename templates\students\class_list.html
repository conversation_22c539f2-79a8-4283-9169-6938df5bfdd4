{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Class Management" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-chalkboard text-primary me-2"></i>{% trans "Class Management" %}
                            </h2>
                            <p class="text-muted">{% trans "Manage school classes and sections" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:class_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add New Class" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

    <!-- Filter options -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="grade" class="form-label">{% trans "Grade" %}</label>
                            <select class="form-select" id="grade" name="grade">
                                <option value="">{% trans "All Grades" %}</option>
                                {% for grade in grades %}
                                    <option value="{{ grade.id }}" {% if request.GET.grade == grade.id|stringformat:"i" %}selected{% endif %}>
                                        {{ grade.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="academic_year" class="form-label">{% trans "Academic Year" %}</label>
                            <select class="form-select" id="academic_year" name="academic_year">
                                <option value="">{% trans "All Academic Years" %}</option>
                                {% for year in academic_years %}
                                    <option value="{{ year.id }}" {% if request.GET.academic_year == year.id|stringformat:"i" %}selected{% endif %}>
                                        {{ year.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="status" class="form-label">{% trans "Status" %}</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">{% trans "All" %}</option>
                                <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>{% trans "Active" %}</option>
                                <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>{% trans "Inactive" %}</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>{% trans "Apply Filters" %}
                            </button>
                            <a href="{% url 'students:class_list' %}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-redo me-2"></i>{% trans "Reset" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Total Classes" %}</h5>
                    <h2 class="display-4">{{ total_classes|default:"0" }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Active Classes" %}</h5>
                    <h2 class="display-4">{{ active_classes|default:"0" }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Total Students" %}</h5>
                    <h2 class="display-4">{{ total_students|default:"0" }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Total Grades" %}</h5>
                    <h2 class="display-4">{{ total_grades|default:"0" }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Class list -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{% trans "Classes" %}</h5>
                </div>
                <div class="card-body">
                    {% if classes %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Class Name" %}</th>
                                        <th>{% trans "Grade" %}</th>
                                        <th>{% trans "Academic Year" %}</th>
                                        <th>{% trans "Class Teacher" %}</th>
                                        <th>{% trans "Students" %}</th>
                                        <th>{% trans "Room" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for class_obj in classes %}
                                        <tr>
                                            <td>{{ class_obj.name }}</td>
                                            <td>{{ class_obj.grade.name }}</td>
                                            <td>{{ class_obj.academic_year }}</td>
                                            <td>
                                                {% if class_obj.class_teacher %}
                                                    {{ class_obj.class_teacher.first_name }} {{ class_obj.class_teacher.last_name }}
                                                {% else %}
                                                    <span class="text-muted">{% trans "Not assigned" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ class_obj.student_count }}</td>
                                            <td>
                                                {% if class_obj.room_number %}
                                                    {{ class_obj.room_number }}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if class_obj.is_active %}
                                                    <span class="badge bg-success">{% trans "Active" %}</span>
                                                {% else %}
                                                    <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{% url 'students:class_detail' class_obj.id %}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'students:class_edit' class_obj.id %}" class="btn btn-sm btn-outline-secondary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{% url 'academics:timetable' %}?class={{ class_obj.id }}" class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-calendar-week"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        {% if classes.has_other_pages %}
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if classes.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ classes.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for i in classes.paginator.page_range %}
                                        {% if classes.number == i %}
                                            <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                                        {% elif i > classes.number|add:'-3' and i < classes.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if classes.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ classes.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ classes.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>{% trans "No classes found matching your criteria." %}
                        </div>
                        <div class="text-center mt-4">
                            <a href="{% url 'students:class_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add New Class" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Grade filter change - could update other filters based on grade
    const gradeSelect = document.getElementById('grade');
    if (gradeSelect) {
        gradeSelect.addEventListener('change', function() {
            console.log('Grade changed:', this.value);
            // You could load related data here if needed
        });
    }
});
</script>
{% endblock %}