﻿var main_module = angular.module("SchoolApp_Controllers").controller("HistoryCtr", ["$scope", "HistoryFactory", "CulutrServices", "NotificationServices", (s, f, cl, n) => {
    s.UsersCount = "";
    s.AllCount = "";

    //// Get History Data //////

    HistoryData = () => {
        s.UsersCount = "";
        s.AllCount = "";
        f.GetAllHistory().then(function (response) {
            s.HistoryData = response.data;
            if (response.data.length > 0) {
                s.UsersCount = response.data[0].UsersCount;
                s.AllCount = response.data[0].allCount;
            }

        })

    }
    HistoryData();
}]);
