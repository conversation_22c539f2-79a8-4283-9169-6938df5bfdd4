from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML
from crispy_forms.bootstrap import FormActions
from .models import (
    Student, Parent, Class, Grade, StudentDocument, VacationRequest,
    StudentInfraction, StudentTransfer, StudentAttachment, ElectronicRegistration,
    StudentSuspension, DisciplinaryAction, DisciplineReport
)
from accounts.models import User


class StudentForm(forms.ModelForm):
    """
    Student creation and update form
    """
    class Meta:
        model = Student
        fields = [
            'student_id', 'admission_number', 'first_name', 'last_name',
            'first_name_ar', 'last_name_ar', 'date_of_birth', 'gender',
            'nationality', 'national_id', 'passport_number', 'blood_type',
            'parent', 'current_class', 'admission_date', 'previous_school',
            'medical_conditions', 'allergies', 'special_needs', 'photo'
        ]
        widgets = {
            'date_of_birth': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'admission_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'medical_conditions': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'allergies': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'special_needs': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'photo': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'
        self.helper.layout = Layout(
            Fieldset(
                _('Basic Information'),
                Row(
                    Column('student_id', css_class='form-group col-md-6 mb-3'),
                    Column('admission_number', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('first_name', css_class='form-group col-md-6 mb-3'),
                    Column('last_name', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('first_name_ar', css_class='form-group col-md-6 mb-3'),
                    Column('last_name_ar', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Personal Details'),
                Row(
                    Column('date_of_birth', css_class='form-group col-md-4 mb-3'),
                    Column('gender', css_class='form-group col-md-4 mb-3'),
                    Column('blood_type', css_class='form-group col-md-4 mb-3'),
                ),
                Row(
                    Column('nationality', css_class='form-group col-md-6 mb-3'),
                    Column('national_id', css_class='form-group col-md-6 mb-3'),
                ),
                'passport_number',
                'photo',
            ),
            Fieldset(
                _('Academic Information'),
                Row(
                    Column('parent', css_class='form-group col-md-6 mb-3'),
                    Column('current_class', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('admission_date', css_class='form-group col-md-6 mb-3'),
                    Column('previous_school', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Medical Information'),
                'medical_conditions',
                'allergies',
                'special_needs',
            ),
            FormActions(
                Submit('submit', _('Save Student'), css_class='btn btn-primary'),
                HTML('<a href="{% url "students:list" %}" class="btn btn-secondary ms-2">' + str(_('Cancel')) + '</a>'),
            )
        )


class ParentForm(forms.ModelForm):
    """
    Parent creation and update form
    """
    class Meta:
        model = Parent
        fields = [
            'user', 'father_name', 'mother_name', 'father_phone', 'mother_phone',
            'father_occupation', 'mother_occupation', 'father_workplace', 'mother_workplace',
            'home_address', 'emergency_contact', 'emergency_phone'
        ]
        widgets = {
            'home_address': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('User Account'),
                'user',
            ),
            Fieldset(
                _('Father Information'),
                Row(
                    Column('father_name', css_class='form-group col-md-6 mb-3'),
                    Column('father_phone', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('father_occupation', css_class='form-group col-md-6 mb-3'),
                    Column('father_workplace', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Mother Information'),
                Row(
                    Column('mother_name', css_class='form-group col-md-6 mb-3'),
                    Column('mother_phone', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('mother_occupation', css_class='form-group col-md-6 mb-3'),
                    Column('mother_workplace', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Contact Information'),
                'home_address',
                Row(
                    Column('emergency_contact', css_class='form-group col-md-6 mb-3'),
                    Column('emergency_phone', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            FormActions(
                Submit('submit', _('Save Parent'), css_class='btn btn-primary'),
                HTML('<a href="{% url "students:parent_list" %}" class="btn btn-secondary ms-2">' + str(_('Cancel')) + '</a>'),
            )
        )


class VacationRequestForm(forms.ModelForm):
    """
    Vacation request form
    """
    class Meta:
        model = VacationRequest
        fields = ['student', 'start_date', 'end_date', 'reason']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date > end_date:
                raise ValidationError(_('Start date cannot be after end date.'))

        return cleaned_data


class StudentInfractionForm(forms.ModelForm):
    """
    Student infraction form
    """
    class Meta:
        model = StudentInfraction
        fields = [
            'student', 'incident_date', 'description', 'severity',
            'action_taken', 'follow_up_required', 'follow_up_date', 'notes'
        ]
        widgets = {
            'incident_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'follow_up_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class StudentTransferForm(forms.ModelForm):
    """
    Student transfer form
    """
    class Meta:
        model = StudentTransfer
        fields = [
            'student', 'transfer_type', 'from_school', 'to_school',
            'from_class', 'to_class', 'transfer_date', 'reason', 'notes'
        ]
        widgets = {
            'transfer_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class StudentDocumentForm(forms.ModelForm):
    """
    Student document form
    """
    class Meta:
        model = StudentDocument
        fields = ['student', 'document_type', 'title', 'file', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'file': forms.FileInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'


class StudentAttachmentForm(forms.ModelForm):
    """
    Student attachment form
    """
    class Meta:
        model = StudentAttachment
        fields = ['student', 'title', 'attachment_type', 'file', 'description', 'is_public']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'file': forms.FileInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'


class ElectronicRegistrationForm(forms.ModelForm):
    """
    Electronic registration form for online student registration
    """
    terms_accepted = forms.BooleanField(
        label=_('I accept the terms and conditions'),
        required=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    class Meta:
        model = ElectronicRegistration
        fields = [
            'first_name', 'last_name', 'first_name_ar', 'last_name_ar',
            'date_of_birth', 'gender', 'nationality', 'father_name',
            'father_phone', 'father_email', 'mother_name', 'mother_phone',
            'home_address', 'desired_grade', 'previous_school'
        ]
        widgets = {
            'date_of_birth': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'home_address': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Student Information'),
                Row(
                    Column('first_name', css_class='form-group col-md-6 mb-3'),
                    Column('last_name', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('first_name_ar', css_class='form-group col-md-6 mb-3'),
                    Column('last_name_ar', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('date_of_birth', css_class='form-group col-md-4 mb-3'),
                    Column('gender', css_class='form-group col-md-4 mb-3'),
                    Column('nationality', css_class='form-group col-md-4 mb-3'),
                ),
            ),
            Fieldset(
                _('Parent Information'),
                Row(
                    Column('father_name', css_class='form-group col-md-6 mb-3'),
                    Column('father_phone', css_class='form-group col-md-6 mb-3'),
                ),
                'father_email',
                Row(
                    Column('mother_name', css_class='form-group col-md-6 mb-3'),
                    Column('mother_phone', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Address & Academic Information'),
                'home_address',
                Row(
                    Column('desired_grade', css_class='form-group col-md-6 mb-3'),
                    Column('previous_school', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Terms and Conditions'),
                HTML('<div class="form-check mb-3">'),
                'terms_accepted',
                HTML('</div>'),
            ),
            FormActions(
                Submit('submit', _('Submit Registration'), css_class='btn btn-primary'),
            )
        )

    def clean_father_email(self):
        email = self.cleaned_data.get('father_email')
        if email and User.objects.filter(email=email).exists():
            raise ValidationError(_('A user with this email already exists.'))
        return email

    def clean_date_of_birth(self):
        date_of_birth = self.cleaned_data.get('date_of_birth')
        if date_of_birth:
            from datetime import date, timedelta
            today = date.today()
            age = today.year - date_of_birth.year - ((today.month, today.day) < (date_of_birth.month, date_of_birth.day))
            
            if age < 3:
                raise ValidationError(_('Student must be at least 3 years old.'))
            if age > 25:
                raise ValidationError(_('Student cannot be older than 25 years.'))
        
        return date_of_birth


class AdmissionDocumentForm(forms.Form):
    """
    Form for uploading admission documents
    """
    DOCUMENT_TYPES = [
        ('birth_certificate', _('Birth Certificate')),
        ('passport', _('Passport Copy')),
        ('medical_record', _('Medical Records')),
        ('previous_school_record', _('Previous School Records')),
        ('vaccination_record', _('Vaccination Records')),
        ('photo', _('Student Photo')),
    ]
    
    registration_id = forms.CharField(widget=forms.HiddenInput())
    
    birth_certificate = forms.FileField(
        label=_('Birth Certificate'),
        required=True,
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': '.pdf,.jpg,.jpeg,.png'})
    )
    
    passport = forms.FileField(
        label=_('Passport Copy'),
        required=False,
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': '.pdf,.jpg,.jpeg,.png'})
    )
    
    medical_record = forms.FileField(
        label=_('Medical Records'),
        required=False,
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': '.pdf,.jpg,.jpeg,.png'})
    )
    
    previous_school_record = forms.FileField(
        label=_('Previous School Records'),
        required=False,
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': '.pdf,.jpg,.jpeg,.png'})
    )
    
    vaccination_record = forms.FileField(
        label=_('Vaccination Records'),
        required=False,
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': '.pdf,.jpg,.jpeg,.png'})
    )
    
    photo = forms.FileField(
        label=_('Student Photo'),
        required=True,
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'
        self.helper.layout = Layout(
            'registration_id',
            Fieldset(
                _('Required Documents'),
                'birth_certificate',
                'photo',
            ),
            Fieldset(
                _('Optional Documents'),
                'passport',
                'medical_record',
                'previous_school_record',
                'vaccination_record',
            ),
            FormActions(
                Submit('submit', _('Upload Documents'), css_class='btn btn-primary'),
            )
        )

    def clean(self):
        cleaned_data = super().clean()
        
        # Validate file sizes (max 5MB each)
        for field_name, file_field in cleaned_data.items():
            if isinstance(file_field, forms.FileField) and file_field:
                if hasattr(file_field, 'size') and file_field.size > 5 * 1024 * 1024:
                    self.add_error(field_name, _('File size cannot exceed 5MB.'))
        
        return cleaned_data


class AdmissionReviewForm(forms.Form):
    """
    Form for reviewing and approving/rejecting admission applications
    """
    STATUS_CHOICES = [
        ('approved', _('Approve')),
        ('rejected', _('Reject')),
    ]
    
    registration_id = forms.CharField(widget=forms.HiddenInput())
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
        label=_('Decision')
    )
    
    assigned_class = forms.ModelChoiceField(
        queryset=Class.objects.none(),
        required=False,
        label=_('Assign to Class'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    notes = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
        required=False,
        label=_('Review Notes')
    )
    
    rejection_reason = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        required=False,
        label=_('Rejection Reason')
    )

    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['assigned_class'].queryset = Class.objects.filter(
                school=school,
                academic_year__is_current=True
            )
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'registration_id',
            'status',
            'assigned_class',
            'notes',
            'rejection_reason',
            FormActions(
                Submit('submit', _('Submit Review'), css_class='btn btn-primary'),
            )
        )

    def clean(self):
        cleaned_data = super().clean()
        status = cleaned_data.get('status')
        assigned_class = cleaned_data.get('assigned_class')
        rejection_reason = cleaned_data.get('rejection_reason')
        
        if status == 'approved' and not assigned_class:
            self.add_error('assigned_class', _('Class assignment is required for approved applications.'))
        
        if status == 'rejected' and not rejection_reason:
            self.add_error('rejection_reason', _('Rejection reason is required for rejected applications.'))
        
        return cleaned_data


class StudentIDGenerationForm(forms.Form):
    """
    Form for generating student IDs
    """
    student_ids = forms.CharField(
        widget=forms.HiddenInput()
    )
    
    id_format = forms.ChoiceField(
        choices=[
            ('auto', _('Auto Generate')),
            ('custom', _('Custom Format')),
        ],
        initial='auto',
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
        label=_('ID Generation Method')
    )
    
    custom_prefix = forms.CharField(
        max_length=10,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., STU'}),
        label=_('Custom Prefix')
    )
    
    starting_number = forms.IntegerField(
        min_value=1,
        required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': '1'}),
        label=_('Starting Number')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        id_format = cleaned_data.get('id_format')
        custom_prefix = cleaned_data.get('custom_prefix')
        starting_number = cleaned_data.get('starting_number')
        
        if id_format == 'custom':
            if not custom_prefix:
                self.add_error('custom_prefix', _('Custom prefix is required for custom format.'))
            if not starting_number:
                self.add_error('starting_number', _('Starting number is required for custom format.'))
        
        return cleaned_data


class DocumentVerificationForm(forms.Form):
    """
    Form for verifying uploaded documents
    """
    document_id = forms.CharField(widget=forms.HiddenInput())
    
    verification_status = forms.ChoiceField(
        choices=[
            ('verified', _('Verified')),
            ('rejected', _('Rejected')),
            ('needs_resubmission', _('Needs Resubmission')),
        ],
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
        label=_('Verification Status')
    )
    
    verification_notes = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        required=False,
        label=_('Verification Notes')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class StudentSuspensionForm(forms.ModelForm):
    """
    Form for creating and managing student suspensions
    """
    class Meta:
        model = StudentSuspension
        fields = [
            'student', 'suspension_type', 'start_date', 'end_date',
            'reason', 'notes'
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'student',
            'suspension_type',
            Row(
                Column('start_date', css_class='form-group col-md-6 mb-3'),
                Column('end_date', css_class='form-group col-md-6 mb-3'),
            ),
            'reason',
            'notes',
            FormActions(
                Submit('submit', _('Create Suspension'), css_class='btn btn-primary'),
                HTML('<a href="{% url "students:suspension" %}" class="btn btn-secondary ms-2">' + str(_('Cancel')) + '</a>'),
            )
        )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        suspension_type = cleaned_data.get('suspension_type')

        if start_date and end_date:
            if start_date > end_date:
                raise ValidationError(_('Start date cannot be after end date.'))

        # Indefinite suspensions don't need an end date
        if suspension_type != 'indefinite' and not end_date:
            self.add_error('end_date', _('End date is required for this suspension type.'))

        return cleaned_data


class DisciplinaryActionForm(forms.ModelForm):
    """
    Form for creating disciplinary actions
    """
    class Meta:
        model = DisciplinaryAction
        fields = [
            'infraction', 'action_type', 'description', 'assigned_date',
            'due_date', 'supervised_by'
        ]
        widgets = {
            'assigned_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'due_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'infraction',
            'action_type',
            'description',
            Row(
                Column('assigned_date', css_class='form-group col-md-6 mb-3'),
                Column('due_date', css_class='form-group col-md-6 mb-3'),
            ),
            'supervised_by',
            FormActions(
                Submit('submit', _('Create Action'), css_class='btn btn-primary'),
            )
        )


class DisciplineReportForm(forms.ModelForm):
    """
    Form for generating discipline reports
    """
    class Meta:
        model = DisciplineReport
        fields = ['report_type', 'title', 'start_date', 'end_date']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

    # Additional filter fields
    student = forms.ModelChoiceField(
        queryset=Student.objects.none(),
        required=False,
        label=_('Filter by Student'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    class_filter = forms.ModelChoiceField(
        queryset=Class.objects.none(),
        required=False,
        label=_('Filter by Class'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    severity_filter = forms.ChoiceField(
        choices=[('', _('All Severities'))] + list(StudentInfraction.SEVERITY_CHOICES),
        required=False,
        label=_('Filter by Severity'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['student'].queryset = Student.objects.filter(
                school=school, is_active=True
            ).order_by('first_name', 'last_name')
            self.fields['class_filter'].queryset = Class.objects.filter(
                school=school, is_active=True
            ).order_by('grade__level', 'name')

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'report_type',
            'title',
            Row(
                Column('start_date', css_class='form-group col-md-6 mb-3'),
                Column('end_date', css_class='form-group col-md-6 mb-3'),
            ),
            Fieldset(
                _('Filters (Optional)'),
                'student',
                'class_filter',
                'severity_filter',
            ),
            FormActions(
                Submit('submit', _('Generate Report'), css_class='btn btn-primary'),
            )
        )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date > end_date:
                raise ValidationError(_('Start date cannot be after end date.'))

        return cleaned_data


class ClassForm(forms.ModelForm):
    """
    Class creation and update form
    """
    class Meta:
        model = Class
        fields = ['name', 'grade', 'academic_year', 'class_teacher', 'max_students', 'room_number']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class GradeForm(forms.ModelForm):
    """
    Grade creation and update form
    """
    class Meta:
        model = Grade
        fields = ['name', 'name_ar', 'level', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
