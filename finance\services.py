"""
Finance services for double-entry bookkeeping system
"""
from django.db import transaction
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from decimal import Decimal
from datetime import date, datetime
from typing import List, Dict, Optional, Tuple
import logging

from .models import (
    Account, AccountType, JournalEntry, Payment, Invoice, CostCenter, FinancialYear,
    Transaction, TransactionEntry, TransactionAuditLog
)

logger = logging.getLogger(__name__)


class ChartOfAccountsService:
    """Service class for Chart of Accounts operations"""
    
    @staticmethod
    def create_default_accounts(school):
        """Create default chart of accounts for a new school"""
        try:
            with transaction.atomic():
                # Create account types if they don't exist
                account_types = {
                    'asset': AccountType.objects.get_or_create(
                        school=school,
                        name='Assets',
                        name_ar='الأصول',
                        type='asset'
                    )[0],
                    'liability': AccountType.objects.get_or_create(
                        school=school,
                        name='Liabilities',
                        name_ar='الخصوم',
                        type='liability'
                    )[0],
                    'equity': AccountType.objects.get_or_create(
                        school=school,
                        name='Equity',
                        name_ar='حقوق الملكية',
                        type='equity'
                    )[0],
                    'revenue': AccountType.objects.get_or_create(
                        school=school,
                        name='Revenue',
                        name_ar='الإيرادات',
                        type='revenue'
                    )[0],
                    'expense': AccountType.objects.get_or_create(
                        school=school,
                        name='Expenses',
                        name_ar='المصروفات',
                        type='expense'
                    )[0],
                }
                
                # Default account structure
                default_accounts = [
                    # Assets
                    {'code': '1000', 'name': 'Assets', 'name_ar': 'الأصول', 'type': 'asset', 'is_header': True, 'is_system': True},
                    {'code': '1100', 'name': 'Current Assets', 'name_ar': 'الأصول المتداولة', 'type': 'asset', 'parent': '1000', 'is_header': True, 'is_system': True},
                    {'code': '1110', 'name': 'Cash', 'name_ar': 'النقدية', 'type': 'asset', 'parent': '1100', 'is_system': True},
                    {'code': '1120', 'name': 'Bank Accounts', 'name_ar': 'الحسابات البنكية', 'type': 'asset', 'parent': '1100', 'is_system': True},
                    {'code': '1130', 'name': 'Accounts Receivable', 'name_ar': 'الذمم المدينة', 'type': 'asset', 'parent': '1100', 'is_system': True},
                    {'code': '1200', 'name': 'Fixed Assets', 'name_ar': 'الأصول الثابتة', 'type': 'asset', 'parent': '1000', 'is_header': True, 'is_system': True},
                    {'code': '1210', 'name': 'Buildings', 'name_ar': 'المباني', 'type': 'asset', 'parent': '1200', 'is_system': True},
                    {'code': '1220', 'name': 'Equipment', 'name_ar': 'المعدات', 'type': 'asset', 'parent': '1200', 'is_system': True},
                    
                    # Liabilities
                    {'code': '2000', 'name': 'Liabilities', 'name_ar': 'الخصوم', 'type': 'liability', 'is_header': True, 'is_system': True},
                    {'code': '2100', 'name': 'Current Liabilities', 'name_ar': 'الخصوم المتداولة', 'type': 'liability', 'parent': '2000', 'is_header': True, 'is_system': True},
                    {'code': '2110', 'name': 'Accounts Payable', 'name_ar': 'الذمم الدائنة', 'type': 'liability', 'parent': '2100', 'is_system': True},
                    {'code': '2120', 'name': 'Accrued Expenses', 'name_ar': 'المصروفات المستحقة', 'type': 'liability', 'parent': '2100', 'is_system': True},
                    
                    # Equity
                    {'code': '3000', 'name': 'Equity', 'name_ar': 'حقوق الملكية', 'type': 'equity', 'is_header': True, 'is_system': True},
                    {'code': '3100', 'name': 'Capital', 'name_ar': 'رأس المال', 'type': 'equity', 'parent': '3000', 'is_system': True},
                    {'code': '3200', 'name': 'Retained Earnings', 'name_ar': 'الأرباح المحتجزة', 'type': 'equity', 'parent': '3000', 'is_system': True},
                    
                    # Revenue
                    {'code': '4000', 'name': 'Revenue', 'name_ar': 'الإيرادات', 'type': 'revenue', 'is_header': True, 'is_system': True},
                    {'code': '4100', 'name': 'Tuition Revenue', 'name_ar': 'إيرادات الرسوم الدراسية', 'type': 'revenue', 'parent': '4000', 'is_system': True},
                    {'code': '4200', 'name': 'Other Revenue', 'name_ar': 'إيرادات أخرى', 'type': 'revenue', 'parent': '4000', 'is_system': True},
                    
                    # Expenses
                    {'code': '5000', 'name': 'Expenses', 'name_ar': 'المصروفات', 'type': 'expense', 'is_header': True, 'is_system': True},
                    {'code': '5100', 'name': 'Salaries and Wages', 'name_ar': 'الرواتب والأجور', 'type': 'expense', 'parent': '5000', 'is_system': True},
                    {'code': '5200', 'name': 'Utilities', 'name_ar': 'المرافق', 'type': 'expense', 'parent': '5000', 'is_system': True},
                    {'code': '5300', 'name': 'Office Supplies', 'name_ar': 'اللوازم المكتبية', 'type': 'expense', 'parent': '5000', 'is_system': True},
                ]
                
                # Create accounts
                created_accounts = {}
                for account_data in default_accounts:
                    parent = None
                    if 'parent' in account_data:
                        parent = created_accounts.get(account_data['parent'])
                    
                    account = Account.objects.create(
                        school=school,
                        code=account_data['code'],
                        name=account_data['name'],
                        name_ar=account_data.get('name_ar', ''),
                        account_type=account_types[account_data['type']],
                        parent=parent,
                        is_header=account_data.get('is_header', False),
                        is_system_account=account_data.get('is_system', False),
                        allow_manual_entries=not account_data.get('is_header', False)
                    )
                    created_accounts[account_data['code']] = account
                
                logger.info(f"Created default chart of accounts for school {school.name}")
                return created_accounts
                
        except Exception as e:
            logger.error(f"Error creating default accounts for school {school.name}: {str(e)}")
            raise
    
    @staticmethod
    def validate_account_hierarchy(account: Account) -> Tuple[bool, Optional[str]]:
        """Validate account hierarchy rules"""
        try:
            # Check if parent is a header account
            if account.parent and not account.parent.is_header:
                return False, _('Parent account must be a header account')
            
            # Check account type consistency
            if account.parent and account.account_type != account.parent.account_type:
                return False, _('Account type must match parent account type')
            
            # Check maximum hierarchy depth (e.g., 5 levels)
            if account.level > 4:  # 0-based, so 5 levels max
                return False, _('Maximum account hierarchy depth exceeded')
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating account hierarchy: {str(e)}")
            return False, str(e)
    
    @staticmethod
    def get_account_tree(school, include_archived=False) -> List[Dict]:
        """Get hierarchical account tree"""
        try:
            if not include_archived:
                queryset = Account.objects.filter(school=school).filter(is_active=True, archived_at__isnull=True)
            else:
                queryset = Account.objects.filter(school=school)
            
            accounts = list(queryset.select_related('account_type', 'parent').order_by('code'))
            
            # Build tree structure
            account_dict = {acc.id: {
                'account': acc,
                'children': []
            } for acc in accounts}
            
            root_accounts = []
            
            for account in accounts:
                account_data = account_dict[account.id]
                if account.parent_id and account.parent_id in account_dict:
                    account_dict[account.parent_id]['children'].append(account_data)
                else:
                    root_accounts.append(account_data)
            
            return root_accounts
            
        except Exception as e:
            logger.error(f"Error building account tree: {str(e)}")
            raise
    
    @staticmethod
    def get_account_balances(school, as_of_date=None) -> Dict[str, Decimal]:
        """Get balances for all accounts"""
        try:
            if as_of_date is None:
                as_of_date = date.today()
            
            accounts = Account.objects.filter(school=school).active()
            balances = {}
            
            for account in accounts:
                balances[account.code] = account.get_balance(as_of_date)
            
            return balances
            
        except Exception as e:
            logger.error(f"Error getting account balances: {str(e)}")
            raise
    
    @staticmethod
    def archive_account(account: Account, user, reason: str = None) -> bool:
        """Archive an account with validation"""
        try:
            with transaction.atomic():
                can_archive, error_msg = account.can_be_archived()
                if not can_archive:
                    raise ValidationError(error_msg)
                
                account.archive(user, reason)
                
                logger.info(f"Account {account.code} archived by {user.username}")
                return True
                
        except Exception as e:
            logger.error(f"Error archiving account {account.code}: {str(e)}")
            raise
    
    @staticmethod
    def generate_account_report(school, report_type='trial_balance', as_of_date=None) -> Dict:
        """Generate various account reports"""
        try:
            if as_of_date is None:
                as_of_date = date.today()
            
            accounts = Account.objects.filter(school=school, is_active=True, archived_at__isnull=True).select_related('account_type')
            
            if report_type == 'trial_balance':
                return ChartOfAccountsService._generate_trial_balance(accounts, as_of_date)
            elif report_type == 'balance_sheet':
                return ChartOfAccountsService._generate_balance_sheet(accounts, as_of_date)
            elif report_type == 'income_statement':
                return ChartOfAccountsService._generate_income_statement(accounts, as_of_date)
            else:
                raise ValueError(f"Unknown report type: {report_type}")
                
        except Exception as e:
            logger.error(f"Error generating account report: {str(e)}")
            raise
    
    @staticmethod
    def _generate_trial_balance(accounts, as_of_date) -> Dict:
        """Generate trial balance report"""
        trial_balance = {
            'title': 'Trial Balance',
            'as_of_date': as_of_date,
            'accounts': [],
            'total_debits': Decimal('0'),
            'total_credits': Decimal('0')
        }
        
        for account in accounts:
            if account.is_header:
                continue
                
            balance = account.get_balance(as_of_date)
            if balance != 0:
                if account.balance_type == 'debit':
                    debit_balance = balance if balance > 0 else Decimal('0')
                    credit_balance = abs(balance) if balance < 0 else Decimal('0')
                else:
                    credit_balance = balance if balance > 0 else Decimal('0')
                    debit_balance = abs(balance) if balance < 0 else Decimal('0')
                
                trial_balance['accounts'].append({
                    'code': account.code,
                    'name': account.name,
                    'debit_balance': debit_balance,
                    'credit_balance': credit_balance
                })
                
                trial_balance['total_debits'] += debit_balance
                trial_balance['total_credits'] += credit_balance
        
        return trial_balance
    
    @staticmethod
    def _generate_balance_sheet(accounts, as_of_date) -> Dict:
        """Generate balance sheet report"""
        balance_sheet = {
            'title': 'Balance Sheet',
            'as_of_date': as_of_date,
            'assets': [],
            'liabilities': [],
            'equity': [],
            'total_assets': Decimal('0'),
            'total_liabilities': Decimal('0'),
            'total_equity': Decimal('0')
        }
        
        for account in accounts:
            if account.is_header:
                continue
                
            balance = account.get_balance(as_of_date)
            if balance != 0:
                account_data = {
                    'code': account.code,
                    'name': account.name,
                    'balance': balance
                }
                
                if account.account_type.type == 'asset':
                    balance_sheet['assets'].append(account_data)
                    balance_sheet['total_assets'] += balance
                elif account.account_type.type == 'liability':
                    balance_sheet['liabilities'].append(account_data)
                    balance_sheet['total_liabilities'] += balance
                elif account.account_type.type == 'equity':
                    balance_sheet['equity'].append(account_data)
                    balance_sheet['total_equity'] += balance
        
        return balance_sheet
    
    @staticmethod
    def _generate_income_statement(accounts, as_of_date) -> Dict:
        """Generate income statement report"""
        income_statement = {
            'title': 'Income Statement',
            'as_of_date': as_of_date,
            'revenue': [],
            'expenses': [],
            'total_revenue': Decimal('0'),
            'total_expenses': Decimal('0'),
            'net_income': Decimal('0')
        }
        
        for account in accounts:
            if account.is_header:
                continue
                
            balance = account.get_balance(as_of_date)
            if balance != 0:
                account_data = {
                    'code': account.code,
                    'name': account.name,
                    'balance': balance
                }
                
                if account.account_type.type == 'revenue':
                    income_statement['revenue'].append(account_data)
                    income_statement['total_revenue'] += balance
                elif account.account_type.type == 'expense':
                    income_statement['expenses'].append(account_data)
                    income_statement['total_expenses'] += balance
        
        income_statement['net_income'] = income_statement['total_revenue'] - income_statement['total_expenses']
        
        return income_statement


class AccountValidationService:
    """Service for account validation and controls"""
    
    @staticmethod
    def validate_account_code(school, code: str, exclude_account_id=None) -> Tuple[bool, Optional[str]]:
        """Validate account code uniqueness and format"""
        try:
            # Check format
            if not code.isdigit():
                return False, _('Account code must contain only digits')
            
            if len(code) < 3 or len(code) > 10:
                return False, _('Account code must be between 3 and 10 digits')
            
            # Check uniqueness
            queryset = Account.objects.filter(school=school, code=code)
            if exclude_account_id:
                queryset = queryset.exclude(id=exclude_account_id)
            
            if queryset.exists():
                return False, _('Account code already exists')
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating account code: {str(e)}")
            return False, str(e)
    
    @staticmethod
    def validate_account_deletion(account: Account) -> Tuple[bool, Optional[str]]:
        """Validate if account can be deleted"""
        return account.can_be_deleted()
    
    @staticmethod
    def validate_journal_entry_account(account: Account) -> Tuple[bool, Optional[str]]:
        """Validate if journal entries can be posted to account"""
        if account.is_header:
            return False, _('Cannot post entries to header accounts')
        
        if not account.allow_manual_entries:
            return False, _('Manual entries not allowed for this account')
        
        if account.is_archived:
            return False, _('Cannot post entries to archived accounts')
        
        return True, None

class DoubleEntryBookkeepingService:
    """Service class for double-entry bookkeeping operations"""
    
    @staticmethod
    def create_transaction(school, user, transaction_data: Dict, entries_data: List[Dict]) -> Transaction:
        """Create a new transaction with entries"""
        try:
            with transaction.atomic():
                # Create transaction
                transaction_obj = Transaction.objects.create(
                    school=school,
                    transaction_date=transaction_data['transaction_date'],
                    transaction_type=transaction_data.get('transaction_type', 'manual'),
                    description=transaction_data['description'],
                    reference=transaction_data.get('reference', ''),
                    total_amount=transaction_data['total_amount'],
                    requires_approval=transaction_data.get('requires_approval', False),
                    created_by=user
                )
                
                # Create transaction entries
                total_debits = Decimal('0')
                total_credits = Decimal('0')
                
                for entry_data in entries_data:
                    account = Account.objects.get(
                        school=school,
                        id=entry_data['account_id']
                    )
                    
                    # Validate account can accept entries
                    can_post, error_msg = AccountValidationService.validate_journal_entry_account(account)
                    if not can_post:
                        raise ValidationError(f"Account {account.code}: {error_msg}")
                    
                    entry = TransactionEntry.objects.create(
                        transaction=transaction_obj,
                        account=account,
                        entry_date=transaction_obj.transaction_date,
                        description=entry_data.get('description', transaction_obj.description),
                        debit_amount=entry_data.get('debit_amount', 0),
                        credit_amount=entry_data.get('credit_amount', 0),
                        cost_center_id=entry_data.get('cost_center_id'),
                        reference=entry_data.get('reference', ''),
                        school=school,
                        created_by=user
                    )
                    
                    total_debits += entry.debit_amount
                    total_credits += entry.credit_amount
                
                # Validate double-entry balance
                if abs(total_debits - total_credits) > 0.01:
                    raise ValidationError(
                        f"Transaction is not balanced: Debits={total_debits}, Credits={total_credits}"
                    )
                
                # Update transaction total amount
                transaction_obj.total_amount = total_debits  # or total_credits, they should be equal
                transaction_obj.save(update_fields=['total_amount'])
                
                # Set status based on approval requirement
                if transaction_obj.requires_approval:
                    transaction_obj.status = 'pending_approval'
                    transaction_obj.save(update_fields=['status'])
                
                # Create audit log
                transaction_obj.create_audit_log('created', user)
                
                logger.info(f"Transaction {transaction_obj.transaction_id} created by {user.username}")
                return transaction_obj
                
        except Exception as e:
            logger.error(f"Error creating transaction: {str(e)}")
            raise
    
    @staticmethod
    def validate_transaction_balance(transaction_obj: Transaction) -> Tuple[bool, Optional[str]]:
        """Validate that transaction is balanced"""
        try:
            if not transaction_obj.entries.exists():
                return False, _('Transaction has no entries')
            
            total_debits = transaction_obj.total_debits
            total_credits = transaction_obj.total_credits
            
            if abs(total_debits - total_credits) > 0.01:
                return False, _(
                    'Transaction is not balanced: Debits={}, Credits={}'
                ).format(total_debits, total_credits)
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating transaction balance: {str(e)}")
            return False, str(e)
    
    @staticmethod
    def post_transaction(transaction_obj: Transaction, user) -> bool:
        """Post transaction to accounts"""
        try:
            with transaction.atomic():
                can_post, error_msg = transaction_obj.can_be_posted()
                if not can_post:
                    raise ValidationError(error_msg)
                
                # Post the transaction
                transaction_obj.post(user)
                
                logger.info(f"Transaction {transaction_obj.transaction_id} posted by {user.username}")
                return True
                
        except Exception as e:
            logger.error(f"Error posting transaction {transaction_obj.transaction_id}: {str(e)}")
            raise
    
    @staticmethod
    def approve_transaction(transaction_obj: Transaction, user, notes: str = None) -> bool:
        """Approve a transaction"""
        try:
            transaction_obj.approve(user, notes)
            
            logger.info(f"Transaction {transaction_obj.transaction_id} approved by {user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Error approving transaction {transaction_obj.transaction_id}: {str(e)}")
            raise
    
    @staticmethod
    def cancel_transaction(transaction_obj: Transaction, user, reason: str = None) -> bool:
        """Cancel a transaction"""
        try:
            transaction_obj.cancel(user, reason)
            
            logger.info(f"Transaction {transaction_obj.transaction_id} cancelled by {user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling transaction {transaction_obj.transaction_id}: {str(e)}")
            raise
    
    @staticmethod
    def create_journal_entry(school, user, entry_data: Dict) -> JournalEntry:
        """Create a simple journal entry (legacy support)"""
        try:
            account = Account.objects.get(
                school=school,
                id=entry_data['account_id']
            )
            
            # Validate account can accept entries
            can_post, error_msg = AccountValidationService.validate_journal_entry_account(account)
            if not can_post:
                raise ValidationError(f"Account {account.code}: {error_msg}")
            
            entry = JournalEntry.objects.create(
                school=school,
                reference_number=entry_data.get('reference_number', 
                    DoubleEntryBookkeepingService._generate_journal_reference(school)),
                entry_date=entry_data['entry_date'],
                entry_type=entry_data.get('entry_type', 'manual'),
                description=entry_data['description'],
                account=account,
                cost_center_id=entry_data.get('cost_center_id'),
                debit_amount=entry_data.get('debit_amount', 0),
                credit_amount=entry_data.get('credit_amount', 0),
                created_by=user
            )
            
            logger.info(f"Journal entry {entry.reference_number} created by {user.username}")
            return entry
            
        except Exception as e:
            logger.error(f"Error creating journal entry: {str(e)}")
            raise
    
    @staticmethod
    def _generate_journal_reference(school) -> str:
        """Generate unique journal entry reference"""
        from datetime import datetime
        
        year = datetime.now().year
        prefix = f"JE-{year}-"
        
        # Get the last journal entry for this year
        last_entry = JournalEntry.objects.filter(
            school=school,
            reference_number__startswith=prefix
        ).order_by('-reference_number').first()
        
        if last_entry:
            try:
                last_number = int(last_entry.reference_number.split('-')[-1])
                next_number = last_number + 1
            except (ValueError, IndexError):
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{next_number:06d}"
    
    @staticmethod
    def get_transaction_audit_trail(transaction_obj: Transaction) -> List[Dict]:
        """Get audit trail for a transaction"""
        try:
            audit_logs = transaction_obj.audit_logs.select_related('user').order_by('timestamp')
            
            trail = []
            for log in audit_logs:
                trail.append({
                    'action': log.get_action_display(),
                    'user': log.user.get_full_name() or log.user.username,
                    'timestamp': log.timestamp,
                    'notes': log.notes,
                    'ip_address': log.ip_address,
                    'changes': log.changes
                })
            
            return trail
            
        except Exception as e:
            logger.error(f"Error getting audit trail: {str(e)}")
            raise
    
    @staticmethod
    def get_account_ledger(account: Account, start_date: date = None, end_date: date = None) -> Dict:
        """Get account ledger with all transactions"""
        try:
            if start_date is None:
                start_date = date(date.today().year, 1, 1)  # Start of current year
            
            if end_date is None:
                end_date = date.today()
            
            # Get opening balance
            opening_balance = account.get_balance(start_date - timezone.timedelta(days=1))
            
            # Get all entries for the period
            entries = TransactionEntry.objects.filter(
                account=account,
                entry_date__gte=start_date,
                entry_date__lte=end_date,
                is_posted=True
            ).select_related('transaction').order_by('entry_date', 'created_at')
            
            ledger_entries = []
            running_balance = opening_balance
            
            for entry in entries:
                if account.balance_type == 'debit':
                    running_balance += entry.debit_amount - entry.credit_amount
                else:
                    running_balance += entry.credit_amount - entry.debit_amount
                
                ledger_entries.append({
                    'date': entry.entry_date,
                    'transaction_id': entry.transaction.transaction_id,
                    'description': entry.description,
                    'reference': entry.reference or entry.transaction.reference,
                    'debit_amount': entry.debit_amount,
                    'credit_amount': entry.credit_amount,
                    'balance': running_balance
                })
            
            return {
                'account': account,
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'opening_balance': opening_balance,
                'closing_balance': running_balance,
                'entries': ledger_entries
            }
            
        except Exception as e:
            logger.error(f"Error generating account ledger: {str(e)}")
            raise


class TransactionApprovalService:
    """Service class for transaction approval workflow"""
    
    @staticmethod
    def submit_for_approval(transaction_obj: Transaction, user) -> bool:
        """Submit transaction for approval"""
        try:
            if transaction_obj.status != 'draft':
                raise ValidationError(_('Only draft transactions can be submitted for approval'))
            
            # Validate transaction is balanced
            is_balanced, error_msg = DoubleEntryBookkeepingService.validate_transaction_balance(transaction_obj)
            if not is_balanced:
                raise ValidationError(error_msg)
            
            transaction_obj.status = 'pending_approval'
            transaction_obj.requires_approval = True
            transaction_obj.save(update_fields=['status', 'requires_approval'])
            
            # Create audit log
            transaction_obj.create_audit_log('modified', user, 'Submitted for approval')
            
            logger.info(f"Transaction {transaction_obj.transaction_id} submitted for approval by {user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Error submitting transaction for approval: {str(e)}")
            raise
    
    @staticmethod
    def get_pending_approvals(school, user=None) -> List[Transaction]:
        """Get transactions pending approval"""
        try:
            queryset = Transaction.objects.filter(
                school=school,
                status='pending_approval'
            ).select_related('created_by').order_by('-created_at')
            
            # If user is specified, filter by permissions or other criteria
            if user:
                # Add permission-based filtering here if needed
                pass
            
            return list(queryset)
            
        except Exception as e:
            logger.error(f"Error getting pending approvals: {str(e)}")
            raise
    
    @staticmethod
    def bulk_approve_transactions(transaction_ids: List[int], user, notes: str = None) -> Dict:
        """Bulk approve multiple transactions"""
        try:
            results = {
                'approved': [],
                'failed': []
            }
            
            transactions = Transaction.objects.filter(
                id__in=transaction_ids,
                status='pending_approval'
            )
            
            for transaction_obj in transactions:
                try:
                    DoubleEntryBookkeepingService.approve_transaction(transaction_obj, user, notes)
                    results['approved'].append(transaction_obj.transaction_id)
                except Exception as e:
                    results['failed'].append({
                        'transaction_id': transaction_obj.transaction_id,
                        'error': str(e)
                    })
            
            logger.info(f"Bulk approval completed by {user.username}: {len(results['approved'])} approved, {len(results['failed'])} failed")
            return results
            
        except Exception as e:
            logger.error(f"Error in bulk approval: {str(e)}")
            raise


class FinancialReportingService:
    """Service class for financial reporting"""
    
    @staticmethod
    def generate_general_ledger(school, start_date: date = None, end_date: date = None) -> Dict:
        """Generate general ledger report"""
        try:
            if start_date is None:
                start_date = date(date.today().year, 1, 1)
            
            if end_date is None:
                end_date = date.today()
            
            accounts = Account.objects.filter(
                school=school,
                is_active=True,
                is_header=False
            ).select_related('account_type').order_by('code')
            
            ledger_data = []
            
            for account in accounts:
                ledger = DoubleEntryBookkeepingService.get_account_ledger(
                    account, start_date, end_date
                )
                
                if ledger['entries'] or ledger['opening_balance'] != 0:
                    ledger_data.append(ledger)
            
            return {
                'title': 'General Ledger',
                'school': school,
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'accounts': ledger_data
            }
            
        except Exception as e:
            logger.error(f"Error generating general ledger: {str(e)}")
            raise
    
    @staticmethod
    def generate_journal_register(school, start_date: date = None, end_date: date = None) -> Dict:
        """Generate journal register report"""
        try:
            if start_date is None:
                start_date = date(date.today().year, 1, 1)
            
            if end_date is None:
                end_date = date.today()
            
            transactions = Transaction.objects.filter(
                school=school,
                transaction_date__gte=start_date,
                transaction_date__lte=end_date,
                status='posted'
            ).prefetch_related('entries__account').order_by('transaction_date', 'transaction_id')
            
            journal_entries = []
            
            for transaction_obj in transactions:
                entries_data = []
                for entry in transaction_obj.entries.all():
                    entries_data.append({
                        'account_code': entry.account.code,
                        'account_name': entry.account.name,
                        'description': entry.description,
                        'debit_amount': entry.debit_amount,
                        'credit_amount': entry.credit_amount
                    })
                
                journal_entries.append({
                    'transaction_id': transaction_obj.transaction_id,
                    'date': transaction_obj.transaction_date,
                    'description': transaction_obj.description,
                    'reference': transaction_obj.reference,
                    'total_amount': transaction_obj.total_amount,
                    'entries': entries_data
                })
            
            return {
                'title': 'Journal Register',
                'school': school,
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'transactions': journal_entries
            }
            
        except Exception as e:
            logger.error(f"Error generating journal register: {str(e)}")
            raise