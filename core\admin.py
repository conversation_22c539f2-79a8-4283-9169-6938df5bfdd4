from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.contrib.admin import SimpleListFilter
from django.db.models import Count
from .models import School, AcademicYear, Semester, AuditLog


class SchoolFilter(SimpleListFilter):
    """
    Filter for school-based models
    """
    title = _('School')
    parameter_name = 'school'
    
    def lookups(self, request, model_admin):
        schools = School.objects.filter(is_active=True)
        return [(school.id, school.name) for school in schools]
    
    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(school_id=self.value())
        return queryset


@admin.register(School)
class SchoolAdmin(admin.ModelAdmin):
    """
    Enhanced School admin with comprehensive management features
    """
    list_display = (
        'name', 'code', 'phone', 'email', 'principal_name', 
        'student_count', 'employee_count', 'is_active', 'created_at'
    )
    list_filter = ('is_active', 'established_date', 'currency', 'timezone')
    search_fields = ('name', 'name_ar', 'code', 'email', 'principal_name', 'address')
    readonly_fields = ('id', 'created_at', 'updated_at')
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': (
                'name', 'name_ar', 'code', 'address', 'phone', 'email', 'website'
            )
        }),
        (_('Administration'), {
            'fields': (
                'principal_name', 'established_date', 'license_number', 'tax_number'
            )
        }),
        (_('Configuration'), {
            'fields': (
                'logo', 'timezone', 'currency', 'academic_year_start_month', 'settings'
            )
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
        (_('System Information'), {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['activate_schools', 'deactivate_schools']
    
    def get_queryset(self, request):
        """
        Add annotations for counts
        """
        queryset = super().get_queryset(request)
        return queryset.annotate(
            student_count=Count('student', distinct=True),
            employee_count=Count('employee', distinct=True)
        )
    
    def student_count(self, obj):
        """Display student count"""
        return obj.student_count
    student_count.short_description = _('Students')
    student_count.admin_order_field = 'student_count'
    
    def employee_count(self, obj):
        """Display employee count"""
        return obj.employee_count
    employee_count.short_description = _('Employees')
    employee_count.admin_order_field = 'employee_count'
    
    def activate_schools(self, request, queryset):
        """Activate selected schools"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} schools activated.')
    activate_schools.short_description = _('Activate selected schools')
    
    def deactivate_schools(self, request, queryset):
        """Deactivate selected schools"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} schools deactivated.')
    deactivate_schools.short_description = _('Deactivate selected schools')


@admin.register(AcademicYear)
class AcademicYearAdmin(admin.ModelAdmin):
    """
    Enhanced Academic Year admin
    """
    list_display = (
        'name', 'school', 'start_date', 'end_date', 'is_current', 
        'registration_period', 'semester_count', 'is_active'
    )
    list_filter = (SchoolFilter, 'is_current', 'is_active', 'start_date')
    search_fields = ('name', 'school__name', 'school__code')
    readonly_fields = ('id', 'created_at', 'updated_at', 'created_by', 'updated_by')
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('school', 'name', 'start_date', 'end_date')
        }),
        (_('Registration'), {
            'fields': ('registration_start_date', 'registration_end_date')
        }),
        (_('Status'), {
            'fields': ('is_current', 'is_active')
        }),
        (_('System Information'), {
            'fields': ('id', 'created_at', 'updated_at', 'created_by', 'updated_by'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['set_as_current', 'activate_years', 'deactivate_years']
    
    def get_queryset(self, request):
        """
        Add semester count annotation
        """
        queryset = super().get_queryset(request)
        return queryset.select_related('school').annotate(
            semester_count=Count('semesters', distinct=True)
        )
    
    def registration_period(self, obj):
        """Display registration period"""
        if obj.registration_start_date and obj.registration_end_date:
            return f"{obj.registration_start_date} - {obj.registration_end_date}"
        return _('Not set')
    registration_period.short_description = _('Registration Period')
    
    def semester_count(self, obj):
        """Display semester count"""
        return obj.semester_count
    semester_count.short_description = _('Semesters')
    semester_count.admin_order_field = 'semester_count'
    
    def set_as_current(self, request, queryset):
        """Set selected academic year as current"""
        for academic_year in queryset:
            # Deactivate other current years for the same school
            AcademicYear.objects.filter(
                school=academic_year.school,
                is_current=True
            ).update(is_current=False)
            
            # Set this year as current
            academic_year.is_current = True
            academic_year.save()
        
        self.message_user(request, f'{queryset.count()} academic years set as current.')
    set_as_current.short_description = _('Set as current academic year')
    
    def activate_years(self, request, queryset):
        """Activate selected academic years"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} academic years activated.')
    activate_years.short_description = _('Activate selected academic years')
    
    def deactivate_years(self, request, queryset):
        """Deactivate selected academic years"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} academic years deactivated.')
    deactivate_years.short_description = _('Deactivate selected academic years')


@admin.register(Semester)
class SemesterAdmin(admin.ModelAdmin):
    """
    Enhanced Semester admin
    """
    list_display = (
        'name', 'academic_year', 'school', 'start_date', 'end_date', 
        'exam_period', 'is_current', 'is_active'
    )
    list_filter = (SchoolFilter, 'is_current', 'is_active', 'start_date')
    search_fields = ('name', 'name_ar', 'academic_year__name', 'school__name')
    readonly_fields = ('id', 'created_at', 'updated_at', 'created_by', 'updated_by')
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('academic_year', 'name', 'name_ar', 'start_date', 'end_date')
        }),
        (_('Examinations'), {
            'fields': ('exam_start_date', 'exam_end_date')
        }),
        (_('Status'), {
            'fields': ('is_current', 'is_active')
        }),
        (_('System Information'), {
            'fields': ('id', 'created_at', 'updated_at', 'created_by', 'updated_by'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['set_as_current', 'activate_semesters', 'deactivate_semesters']
    
    def get_queryset(self, request):
        """
        Optimize queries
        """
        return super().get_queryset(request).select_related('academic_year', 'school')
    
    def school(self, obj):
        """Display school name"""
        return obj.academic_year.school.name
    school.short_description = _('School')
    school.admin_order_field = 'academic_year__school__name'
    
    def exam_period(self, obj):
        """Display exam period"""
        if obj.exam_start_date and obj.exam_end_date:
            return f"{obj.exam_start_date} - {obj.exam_end_date}"
        return _('Not set')
    exam_period.short_description = _('Exam Period')
    
    def set_as_current(self, request, queryset):
        """Set selected semester as current"""
        for semester in queryset:
            # Deactivate other current semesters for the same academic year
            Semester.objects.filter(
                academic_year=semester.academic_year,
                is_current=True
            ).update(is_current=False)
            
            # Set this semester as current
            semester.is_current = True
            semester.save()
        
        self.message_user(request, f'{queryset.count()} semesters set as current.')
    set_as_current.short_description = _('Set as current semester')
    
    def activate_semesters(self, request, queryset):
        """Activate selected semesters"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} semesters activated.')
    activate_semesters.short_description = _('Activate selected semesters')
    
    def deactivate_semesters(self, request, queryset):
        """Deactivate selected semesters"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} semesters deactivated.')
    deactivate_semesters.short_description = _('Deactivate selected semesters')


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    """
    Audit Log admin for security monitoring
    """
    list_display = (
        'timestamp', 'user', 'school', 'action', 'model_name', 
        'object_repr', 'ip_address'
    )
    list_filter = (
        SchoolFilter, 'action', 'model_name', 'timestamp'
    )
    search_fields = (
        'user__username', 'user__email', 'object_repr', 
        'ip_address', 'model_name'
    )
    readonly_fields = (
        'id', 'school', 'user', 'action', 'model_name', 'object_id',
        'object_repr', 'changes', 'ip_address', 'user_agent', 'timestamp'
    )
    
    date_hierarchy = 'timestamp'
    
    def has_add_permission(self, request):
        """Audit logs cannot be added manually"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Audit logs cannot be changed"""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Only superusers can delete audit logs"""
        return request.user.is_superuser
    
    def get_queryset(self, request):
        """
        Optimize queries and filter by user's school if not superuser
        """
        queryset = super().get_queryset(request).select_related('user', 'school')
        
        # Non-superusers can only see their school's audit logs
        if not request.user.is_superuser:
            user_school = None
            if hasattr(request.user, 'employee') and request.user.employee:
                user_school = request.user.employee.school
            
            if user_school:
                queryset = queryset.filter(school=user_school)
            else:
                # If user has no school, show no logs
                queryset = queryset.none()
        
        return queryset


# Custom admin site configuration
admin.site.site_header = _('School ERP Administration')
admin.site.site_title = _('School ERP Admin')
admin.site.index_title = _('Welcome to School ERP Administration')
