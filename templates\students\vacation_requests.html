{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Vacation Requests" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-calendar-alt text-primary me-2"></i>{% trans "Vacation Requests" %}
                            </h2>
                            <p class="text-muted">{% trans "Manage student vacation requests" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:add_vacation_request' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "New Request" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter options -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="status" class="form-label">{% trans "Status" %}</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">{% trans "All Statuses" %}</option>
                                        <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>{% trans "Pending" %}</option>
                                        <option value="approved" {% if request.GET.status == 'approved' %}selected{% endif %}>{% trans "Approved" %}</option>
                                        <option value="rejected" {% if request.GET.status == 'rejected' %}selected{% endif %}>{% trans "Rejected" %}</option>
                                        <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>{% trans "Cancelled" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request.GET.date_from|default:'' }}">
                                </div>
                                <div class="col-md-3">
                                    <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request.GET.date_to|default:'' }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter me-2"></i>{% trans "Apply Filters" %}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vacation Requests List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Vacation Requests" %}</h5>
                        </div>
                        <div class="card-body">
                            {% if vacation_requests %}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Student" %}</th>
                                                <th>{% trans "Start Date" %}</th>
                                                <th>{% trans "End Date" %}</th>
                                                <th>{% trans "Duration" %}</th>
                                                <th>{% trans "Status" %}</th>
                                                <th>{% trans "Requested By" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for request in vacation_requests %}
                                                <tr>
                                                    <td>
                                                        <a href="{% url 'students:detail' request.student.id %}">
                                                            {{ request.student.full_name }}
                                                        </a>
                                                    </td>
                                                    <td>{{ request.start_date }}</td>
                                                    <td>{{ request.end_date }}</td>
                                                    <td>{{ request.duration_days }} {% trans "days" %}</td>
                                                    <td>
                                                        {% if request.status == 'pending' %}
                                                            <span class="badge bg-warning">{% trans "Pending" %}</span>
                                                        {% elif request.status == 'approved' %}
                                                            <span class="badge bg-success">{% trans "Approved" %}</span>
                                                        {% elif request.status == 'rejected' %}
                                                            <span class="badge bg-danger">{% trans "Rejected" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">{% trans "Cancelled" %}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ request.requested_by.get_full_name }}</td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#requestModal{{ request.id }}">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            {% if request.status == 'pending' %}
                                                                <button type="button" class="btn btn-sm btn-outline-success">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-sm btn-outline-danger">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            {% endif %}
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>{% trans "No vacation requests found." %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Request Detail Modals -->
{% for request in vacation_requests %}
    <div class="modal fade" id="requestModal{{ request.id }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{% trans "Vacation Request Details" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>{% trans "Student:" %}</strong> {{ request.student.full_name }}</p>
                            <p><strong>{% trans "Start Date:" %}</strong> {{ request.start_date }}</p>
                            <p><strong>{% trans "End Date:" %}</strong> {{ request.end_date }}</p>
                            <p><strong>{% trans "Duration:" %}</strong> {{ request.duration_days }} {% trans "days" %}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{% trans "Status:" %}</strong> {{ request.get_status_display }}</p>
                            <p><strong>{% trans "Requested By:" %}</strong> {{ request.requested_by.get_full_name }}</p>
                            <p><strong>{% trans "Requested On:" %}</strong> {{ request.created_at|date:"d/m/Y H:i" }}</p>
                            {% if request.approved_by %}
                                <p><strong>{% trans "Approved By:" %}</strong> {{ request.approved_by.get_full_name }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <p><strong>{% trans "Reason:" %}</strong></p>
                        <p>{{ request.reason }}</p>
                    </div>
                    
                    {% if request.rejection_reason %}
                        <div class="alert alert-danger">
                            <p><strong>{% trans "Rejection Reason:" %}</strong></p>
                            <p>{{ request.rejection_reason }}</p>
                        </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                </div>
            </div>
        </div>
    </div>
{% endfor %}
{% endblock %}