﻿var cluturedata = {
    ar: {
        EmployeePenaltyReport:"تقرير جزائات الموظفين ",
        PendingSuggestionReport: "تقرير الشكاوي و الاقتراحات ",
        AddingSuggetionReport: "تقرير اضافه الشكاوي و الاقتراحات ",
        AutomaticBackup: " نسخة إحتياطية تلقائية",
        OutgoingPriceTotal: " إجمالى سعر الصادر",
        StudentNotFound: "رقم القيد غير مسجل ",
        OutgoingTotal: " إجمالى الصادر",
        supplierName: "اسم المورد",
        IncomingPriceTotal: "إجمالى سعر الوارد",
        ClientName: "اسم العميل",
        IncomingTotal: "إجمالى الوارد",
        PreviousBalanceTotal: "إجمالى الرصيد السابق ",
        TotalTardinessAndAbsencesReport: "تقرير إجمالي  تأخيرات وغيابات",
        SalaryComponentsForTheEmployee: "مفرادات الراتب",
        AllCommitteMembersReport: "تقرير اعضاء اللجان مجمع ",
        NewColorTermDegreeReport: "  شهاده شكل 3 الوان",
        InvalidFormData: "هناك خطأ في البيانات التي تم ادخالها ",
        feesPaymentElectronicInvoiceRecoveryReport: "تقرير إشعار دائن للفاتورة الالكترونية ",
        DisplayOfAcademicTermAndEndOfYearResults: "عرض نتائج الفصول الدراسية ونهاية العام",

        NewColorTermDegreeReportForSeconadary:"شهاده شكل 3 درجات",
        AutoMassagesSender: "الرسائل التلقائيه ",
        //NewColorTermDegreeReport: "شهاده الدرجات شكل 3",
        quedeIsLocked: "إغلاق القيد",
        theQuedeHasBeenLocked: " تم إغلاق القيد ",
        openIsQuede: "تفعيل القيد",
        "YouDoNotHavePermissionToAccessTheStorage":"ليس لديك صلاحيه علي المخزن",
        AddLibarayUser: "اضافه مستخدمين لمكتبه  ",
        StoreClint: "حسابات العملاء",
        StoreSuppliersReport: "اجمالي ارصده الموردين ",
        ComprehensiveItemMovementReport: "تقرير حركة  صنف شاملة",
        AddStudentBus: "اشتراك الحافلات",
        creditAmount: "الرصيد",
        EmployeeCodeIsDublicate: "كود الموظف مكرر",
        PleaseAddAClientFirst: "يرجي إضافة عميل أولاً",
        PleaseAdjustTheEmployeeAffairsSettingsAndTheInsuranceAndTaxSettingsFirst: "يرجي ضبط إعدادات شؤون الموظفين وإعدادات التأمينات والضرائب أولاً ",
        ActualSubscriptionValueTotal: "  إجمالى قيمة الإشتراكات  الفعلية",
        SubscriptionValueTotal: "  إجمالى قيمة الإشتراكات ",
        DifferenceTotal: "  إجمالى الفرق",
        BusSubscriptionModificationsReport: " تقرير تعديل إشتراكات الحافلة  ",
        BusSubscriptionModifications: "تعديل إشتراكات الحافلة",
        TheBroadcastIsBeingStartedPleaseWait: "جار فتح البث انتظر من فضلك",
        NoBroadcastChannelsAreAvailablePleaseTryAgainLater: "لا توجد قنوات بث متاحة حاول فى وقت اخر",
        TodayIsDateIsAHolidayForTheBranch: " تاريخ هذا اليوم اجازة للفرع",
        NoSeasonsSelected: " لم يتم اختيار فصول",
        SaveACopyToYourDevice: " حفظ نسخة على الجهاز",
        PleaseSelectAtLeastOneStation: " يرجى اختيار محطة واحدة على الأقل",
        PleaseSelectAtLeastOnePath: " يرجى اختيار مسار واحد على الأقل",
        NoBusFound: " لا يوجد حافلة",
        NoAreaFound: " لا يوجد منطقة",
        Detailed: " مفصل",
        Concise: " مختصر",
        IsEvaluation: "(تقييم )",

        REPORTED: "تم الإرسال",
        NOT_REPORTED: "خطأ في الفاتورة",
        CodeIdentifierIsRepeated: "الرقم التعريفي مكرر",
        CertificateFinalForStudentsMasterCrossColor: "شهادة نهائية لطلاب الماجستير في الألوان المتعددة",
        purchaseInvoiceReport: "تقرير فاتورة المشتريات",
        EditData: " تعديل البيانات",
        alertForDataFeesLastYears: "لدي الطالب مديوينة علي العام السابق يرجي سدادها أولاً",
        NoStationFound: " لا يوجد محطة",
        StudentAbsenceRegistrationForBusesBackReport: " تقرير غياب وحضور الطلاب إياباً  ",
        StudentAbsenceRegistrationForBusesGoReport: " تقرير غياب وحضور الطلاب ذهاباً  ",
        StudentAbsenceRegistrationForBuses: "تسجيل غياب الطلاب للحافلات",
        CompleteTheData: "أكمل البيانات",
        ApprovalOfSupplyOrders: "إعتماد أوامر التوريد",
        SupplyRequestsMenue: " أوامر التوريد",
        chooseEmployee: "اختر موظف",
        TaxPaymentReport: "تقرير المدفوعات لحساب الضريبة",
        AStudentWhoIsInDebtCannotBeStopped: "لا يمكن إيقاف الطالب  ولدية مديونية",
        SoryThisGradeFeesHaveStudent: "لا يمكن تعديل قيمه تم إضافتها إلى طلاب",
        TotalDegreesOfEmployeesReport: "إجمالى درجات المواظفين",
        SonsFeesRequestsReport: "مطالبات الرسوم",
        PleaceEnterProcedure: "برجاء إدخال الإجراء ",
        TotalDegreesOfEmployees: "تقرير إجمالي تقييمات الموظفين ",
        NotApproved: "لم يتم الإعتماد",
        InvaildClient: "بيانات التسجيل غير صحيحة",
        EgsCodeRequired: "كود الصنف مطلوب",
        ActualDegree: "برجاء ادخال الدرجة الفعلية",
        MinorDegree: " برجاء ادخال الدرجة الصغرى",
        GreatestDegree: "برجاء ادخال الدرجة الكبرى",
        theirIsAdepitAddedOnThisGradeInThisYear: "لا يمكن اضافة رسوم على هذا الصف لوجود مديونية على طلاب فى هذا الفصل  فى هذا العام",
        threirIsApayedFees: "تم تدفيع جزء من المديونية قم بألغاء الدفع للحذف",
        theirIsOtherFeesOnTheStudent: "لا يمكن حذف المديونية الاجبارية الا اذ لم يوجد على الطالب اى مديونيات اخرى فى هذا العام",
        cannotDeleteDepit: "يوجد حركات على المديونية لا يمكن حذفها",
        theirIsMovmentsAddedOnThiesItems: "تم اضافة حركات على هذه البنود لا يمكن الحذف",
        pleaseSetTheAccountForStudentsFees: "ضع اعدادت حساب الرسوم الدراسية فى شجرة الحسابات",
        ConfirmDelete: "تاكيد عملية الحذف",
        AreYouWantToDeleteMessages: "يوجد رسائل مرسلة من خلال هذ الاسم هل تريد حذفها؟",
        ConfirmPrint: "يوجد مديونات سابقة على الطالب , هل تريد الطباعة ؟",
        SuccessfullyDeleted: "تم الحذف بنجاح",
        deleteSenderNameFirst: "يرجى حذف اسماء المرسلين اولا",
        EditGateEntry: "تعديل مدخلات البوابه ",
        DeleteGateEntry: "مسح من مدخلات بوابه ",
        accept: "قبول",
        monthlyProp: "(ترصيد شهري)",
        PaymentPermissionsReport: "تقرير إذون الدفع ",
        PaymentPermission: " إذن الدفع ",
        ThePercentShouldBeLessThan100: "يجب أن لا تزيد النسبة المئوية عن %100 ",
        PaymentPermission: " إذن الدفع ",
        sendmessage: "ارسال الرسالة",
        reject: "رفض",
        GetAdvancedEmpSearchReport:"تقرير البحث المتقدم للموظفين ",
        Conduct: "شهادة حسن سير وسلوك",
        Clarification: "طلب إستقالة",
        Acknowledgment: "إقرار",
        CaseStudiesReport: "تقرير دراسة حالة",
        Experincecetrificate: "شهادة خبرة",
        comment: "ارسال رد",
        Termination: "إخلاء الطرف",
        noResultsFound: "لا يوجد إجازة",
        EnterprodeuctionrUsername: " برجاء ادخال اسم المستخدم للاعدادات الفعلية",
        EnterPreprodeuctionrUsername: " برجاء ادخال اسم المستخدم للاعدادات التجريبية",
        EnterPreprodeuctionrPassword: " برجاء ادخال كلمة المرور للاعدادات التجريبية",
        EnterprodeuctionrPassword: " برجاء ادخال كلمة المرور للاعدادات الفعلية",
        PleaseEnterEmployeeName: "برجاء ادخاء اسم الموظف",
        ErrorSendToEta: "خطاء فى الارسال للمنظومه",
        SendToEta: "تم الارسال للمنظومه بنجاح",
        send: "ارسال",
        theNumberisfoundbefore: "رقم الشكوي أو المقترح موجود من قبل يجب ادخال رقم جديد",
        sendTosignature: "ارسال للتوقيع",
        TeachersandSubjectsManagement: "إدارة المعلمين والمواد",
        StudentsDailyPaymentsReport: "تقرير يومية مدفوعات الطلاب",
        CannotdeleteHaveStudentMonthResult: "العنصر المراد  حذفه مسجل به  نتائج شهرية يرجي  حذفها أولاً",
        CertificateFinalForStudentsMasterCross: "شهادة الدرجات شكل2",
        DailyTimeReport: "تقرير تاخير الموظفين",
        PaymentPermession: "إذن دفع",
        PaymentPermessions: "إذون الدفع",
        userIsSelected: "تم اختيار المستخدم من قبل",
        DepetsAlreadyExist: "تم  إضافة مديونية علي الطالب للمرحلة او الصف او القسم المختار لا يمكن التعديل علي تلك البيانات قبل حذف المديونية",
        SetsNumberHaveSerialNumbers: "أرقام الجلوس المحددة مربوطة بمجموعة  أرقام سريه  (***) يرجي حذفها أولاً ",
        SalaryVocabularyIsExist: "تم إعتماد صرف راتب تلك الموظف عن هذا الشهر يرجي حذفه اولاً",
        CantEditSalaryVocabularyIsExist: "لايمكن التعديل تم إعتماد صرف راتب تلك الموظف عن هذا الشهر يرجي حذفه اولاً",
        busNumberMustRelatedWithBusName: "رقم الحافلة يجب ان يكون خاص باسم الحافلة",
        NotDone: "لم تتم العملية",
        AddedSuccessfully: "تمت الاضافه بنجاح",
        UpdatedSuccessfully: "تمت التحديث بنجاح",
        DoneButNotAll: "تمت ولكن بعض الاعضاء لم تُضاف",
        dailyQuedes: "تقرير القيود اليومية",
        ColorTermDegreeReport: "تقرير نتيجة الوان",
        dailyQuedesHistoryReport: "تقرير تاريخ القيود اليومية",
        studentCallDone: "تم استدعاء طلبات الالتحاق بنجاح العدد ",
        thereisnoStudentApply: "لا يوجد طلبات التحاق فى الوقت الحالى",
        GroupNameIsRequired: "يرجي اضافة اسم المجموعه اولا",
        recovered: "المسترجع",
        PleaseSpecifyPermissions: "يرجي تحديد صلاحيات اولا",
        PleaseEnterAUsername: "يرجي ادخال اسم مستخدم ",
        PleaseEnterTheFullName: "يرجي ادخال  الاسم بالكامل",
        PleaseEnterYourPassword: "يرجي ادخال   كلمه المرور",
        ControlMembersRelatedWithCommittees: "اعضاء هذا الكنترول موجودة فى لجنة او اكثر , يرجى حذف اللجان اولا",
        dateFromMustbelessthanDateto: "التاريخ من يجب ان يكون اقل من التاريخ الى",
        EmployeebusPayment: 'مدفوعات الحافلات للموظفين',
        ApplayBusReport: "تقرير طلبات الاشتراك فى الحافلة",
        addToFavoritePages: "اضافة الى المفضلة",
        removeFromFavoritePages: "ازالة الى المفضلة",
        PasswordWeakPleaseEnterAtLeast5LettersOrNumbers: "كلمه مرور ضعيفة ,, يرجي ادخال علي الاقل 5 حروف او ارقام",
        PasswordsDoNotMatch: "كلمة المرور غير مطابقة",
        PleaseEnterAnEmailAddress: "يرجي ادخال عنوان بريد الكتروني",
        PleaseEnterTheDepartment: "يرجي ادخال القسم",
        ErrorFromPayment: "خطاء اثناء الدفع",
        AllStudentFee: "تقرير حسابات الطلاب المجمع",
        PleaseEnterTheDivision: "يرجي ادخال الشعبة",
        PleaseChooseARequest: "يرجي اختيار طلب على الاقل",
        AccountStatementWithTaxReport: "تقرير كشف حساب بالضريبة",
        CanNotAddMoreThan4Levels: "لا يمكن اضافة اكثر من 4 مستويات",
        PleaseEnterName: "يرجى ادخال الاسم",
        PleaseEnterMobile: "يرجى ادخال رقم الهاتف",
        PleaseEnterReason: "يرجى ادخال السبب",
        PleaseEnterNationID: "يرجى ادخال الرقم القومى",
        PleaseEnterDate: "يرجى ادخال التاريخ",
        PleaseEnterInvoiceNumber: "يرجى ادخال رقم الفاتورة",
        PleaseEnterTime: "يرجى ادخال الوقت",
        PleaseEnterCallerName: "يرجى ادخال اسم المتصل",
        PleaseEnterCallerMobile: "يرجى ادخال رقم المتصل",
        PleaseEnterNotes: "يرجى ادخال الملاحظات",
        PleaseEnterDateFrom: "يرجى ادخال الفترة من",
        PleaseEnterDateTo: "يرجى ادخال الفترة الى",
        ConfirmVisitEnd: "تاكيد انهاء الزيارة ؟!!",
        EndVisitSuccess: "تم انهاء الزيارة",
        MaxLength: "قيمه كبيره ",
        PleasDeleteSubjectDegree: " يرجي حذف ترصيد المادة اولاً",
        PleaseEnterGateEntriesItems: "يرجى ادخال مدخلات البوابة",
        SetdefaultNation: "هل ترغب فى تعينها جنسية افتراضية",
        AssignAVirtualEmail: "تعين كايميل افتراضية",
        PleaseEnterNationality: "يرجى ادخال اسم الجنسية",
        PleaseEnterNationalityEn: " يرجى ادخال اسم الجنسية بالانجليزى",
        WriteHere: "اكتب هنا ...",
        NumberOfTreeLevelsCalculationsIsRequired: "يرجي ادخال عدد مستويات شجرة الحسابات",
        StagesOfTreeAccountsIsRequired: "يرجي ادخال عدد مراحل شجرة الحسابات",
        MaximumValuesForLevelsAreOnly10Levels: "اقصي قيمة للمستويات هي 10 مستويات فقط",
        DigitSystemIsRequired: "يرجي اضافة نظام الـ Digit المتبع",
        PleaseEnterBookName: "يرجي ادخال  اسم الكتاب",
        PleaseEnterBookNumber: "يرجي ادخال  رقم الكتاب",
        PleaseEnterBookParcode: "يرجي ادخال  الباركود",
        PleaseEnterBookPlace: "يرجي ادخال  مكان الكتاب",
        PleaseEnterPublisher: "يرجي ادخال  اسم الناشر",
        PleaseEnterAboutBook: "يرجي ادخال  اسم المؤلف",
        FileUploadSuccessfully: "تمت اضافة الملف بنجاح",
        PleaseEnterPath: " PDF يرجى ادخال ",
        someThingWentWrong: "حصل خطأ ما",
        DoYouWantToChangeTheStatusOfIheQuede: "هل تريد تغير حالة القيد ؟",
        saved: "تم الحفظ",
        deleted: "تم الحذف",
        youmustchooseaschoolstagegradedepartment: " يجب عليك ادخال اسم المدرسه \المرحله \الفصل \القسم ",
        NoUsersHaveBeenSelected: "لم يتم اختيار اى مستخدمين",
        ThereAreIncorrectNumbers: "هذه الحقول تحتاج قيم صحيحه ",
        PleaseAddTheBeginningOfTheYear: "يرجي ادخال بداية السنة المالية",
        PleaseAddTheEndOfTheYear: "يرجي ادخال نهاية السنة المالية",
        committeeNameOrNumberIsRepeated: "اسم او رقم اللجنة تم ادخالهم مسبقا",
        makeSureToChoseAcontrolAdepartmentAndAbranche: "تأكد من اختيار كنترول و قسم ومدرسه",
        text: "نصى",
        number: "رقمي",
        checkbox: "صح او خطأ",
        bankNameArabic_Required: "يرجي اضافه اسم البنك باللغه العربيه",
        bankNameEnglish_Required: "يرجي اضافه اسم البنك باللغه الانجليزيه",
        bankAccountNumber_Required: "يرجي اضافه رقم الحساب البنكي",
        accountNumber_Required: " يرجي اضافه رقم الحساب الخاص بشجره الحسابات",
        accountOwner_Required: " يرجي اضافه اسم صاحب الحساب",
        address_Required: "يرجي اضافه العنوان",
        BankIsRequired: "يرجي اختيار بنك",
        safeNameArabic_Required: "يرجي اضافه اسم الخزينه باللغه العربيه",
        safeNameEnglish_Required: "يرجي اضافه اسم الخزينه باللغه العربيه",
        cashierIsSafe_Required: "يرجي اضافه اسم امين صندوق الخزينه ",
        Branches: "الفروع",
        branche_Required: "يرجي ربط الحساب بفرع واحد علي الاقل",
        PleaseAddTheNameOfTheBook: "يرجي اضافه اسم الدفتر",
        PleaseAddAStartDate: "يرجي اضافه بدايه الفتره",
        PleaseAddAEndtDate: "يرجي اضافه نهايه الفتره",
        branchIsRequired: "يرجي اختيار فرع",
        motherQualfication: "مؤهل الام",
        fatherQualfication: "مؤهل الاب",
        DateOfDisActive: "تاريخ الإيقاف",
        ReasonOfDisActive: "سبب الإيقاف",
        chknotactive: "غير نشط ",
        EmployeesMissionsReport: "مأموريه الموظف مجمع ",
        MissionReport: "مأوريه الموظف",
        JobApplicationSummary: "ملخص طلبات الوظائف ",
        sortingByDateOfReceipt: "ترتيب حسب تاريخ الايصال",
        DateOfReceipt: " تاريخ الايصال",

        financialYearIsRequired: "يرجي اختيار سنه ماليه",
        currencyIsRequired: "يرجي اختيار عمله",
        PaperReceiptIsRequired: "يرجي اختيار اسم الدفتر",
        historyOfTheMovementIsRequired: "يرجي اختيار تاريخ السند",
        PaperReceiptNumberIsRequired: "يرجي اضافه رقم الايصال الورقي",
        DateOfReceiptIsRequired: "يرجي اضافه تاريخ الايصال ",
        TypeOfCashReceiptsIsRequired: "يرجي اختيار نوع الحساب",
        AccountIsRequired: "يرجي اختيار حساب",
        supplementIsRequired: "يرجي اختيار الحساب المكمل",
        PaperReceiptNumber: "رقم الايصال خارج الفتره المححده",
        AccountNumberReceiptIsRequired: "يرجي اختيار نوع حساب  القبض",
        PleaseSelectAtLeastOneAccountToCompleteTheProcess: "يرجي اختيار علي الاقل حساب واحد فقط لاكمال العمليه",
        PleaseFillInAllTheDataInTheTableFirst: "يرجي ملئ جميع البيانات في الجدول اولا",
        CheckNumberIsRequired: "يرجي اضافه رقم الشيك",
        DueDateIsRequired: "يرجي اضافه تاريخ الاستحقاق",
        InvalidDateTimeFormatting: "يرجي اضافه التاريخ بشكل صحيح",
        namerepeated: "الاسم مكرر",
        coderepeated: "رقم الكود مكرر",
        nationalidRepeated: "الرقم القومى مكرر",
        TheAccountHasBeenTransferredSuccessfully: "تم نقل الحساب بنجاح",
        errorInUpdate: "خطأ  في التحديث",
        PleaseEnterDay: "يرجى ادخال اليوم",

        PleaseEnterBookdivisionName: "يرجي ادخال اسم الشعبه",
        PleaseEnterbranchName: "يرجي ادخال اسم الفرع",
        Cannotdelete: "لا يمكن الحذف",
        StudentTransferReport: "طلب تحويل طالب",
        FinalCertificateDegreeMasterReport: "شهادة درجات نهاية العام",
        Loading: "جار التحميل",
        Department: "القسم",
        TheDivision: " الشعبة",
        PleaseEnternewcount: "يرجي ادخال رصيد الكتب الجديد",

        PleaseEnterstudentName: "  يرجى ادخال الاسم الطالب ",
        studentName: "اسم الطالب",

        TheDebtorAccountValuesMustBeEqualToTheCreditorAccounts: "يجب أن تكون قيم حسابات المدين مساوية لحسابات الدئن",
        branches: "الفرع",
        Print: "طباعه",
        PleaseEnterAnotes: "يرجي ادخال الملاحظات",
        BookName: "اسم الكتاب",
        AuthorName: "اسم المؤلف",
        PublicNum: " الرقم العام",
        PrivateNum: "الرقم الخاص",
        Publisher: "الناشر",
        InPeriod: "خلال فتره",
        deleteConfimation: "متأكد من حذف السجل؟",
        commonData: "بيانات عامة",
        birthData: "بيانات الميلاد",
        addressData: "بيانات العنوان",
        medicalStateData: "بيانات الحالة الصحية",
        date: "تاريخ",
        check: "اختر",
        TypeOfEar: "نوع الاذن",
        PleaseEnterinfiractionrate: "يرجي ادخال تصنيف المخالفه",
        PleaseEnterinfiractionname: "يرجي ادخال المخالفه",
        Addedinfrationratingbefor: "تمت  اضافه هذه المخالفه  من قبل",
        CommitteeAddedBefore: "تمت  اضافه هذه اللجنة  من قبل",
        CommitteesCallingDetectionReport: "كشف مناداة اللجان",
        Attending_LeavingReport: "تقرير حضور والانصراف الموظفين",
        StockInventoryDataReport: "تقرير جرد الاصناف",
        StudentsDelaysReport: "تقرير تاخيرات الطلاب",
        StudentDelaysReport: "تقرير تاخيرات طالب",
        BusTrafficReport: "خط سير الحافلة",

        Duplicatename: "اسم مكرر",
        PleaseEnterDestructionABooknumber: "يرجي ادخال عدد الكتب المهلكه",
        Allcopiesofthebookareborrowed: "كل نسخ الكتاب مستعاره",
        Thestudentborrowedthebookbefore: "الطالب قام باستعاره الكتاب من قبل",
        nobook: "عدد الكتب غير كافي",
        show: "عرض",
        accountNameRepeated: "اسم الحساب مكرر",
        noStudentsAccountSet: "من فضلك قم بضبط اعدادت حساب الطلاب",
        PleaseAddTheQuedNumber: 'يرجي اضافه رقم القيد',
        PleaseAddAnotherComplementaryAccount: "يرجي اضافه حساب مكمل اخر",
        thisRecordIsAnItem: "هذا العنصر صنف",
        doYouWantToDelete: "هل ترغب فى الحذف؟",
        item: "البند",
        value: "القيمة",
        edit: "تعديل",
        delete: "حذف",
        noStudentsChoosen: "لم يتم اختيار اى من الطلاب",
        noFeesAddedOnThisGrade: "لم يتم اضافة رسوم على هذا الصف",
        pleaseChooseSomeItemsToPay: "لم يتم اختيار اى من البنود",
        PleaseEnterSpecialtyName: "يرجى ادخال اسم التخصص",
        PleaseEnterClinicName: "يرجى ادخال اسم العيادة",
        PleaseEnterDoctorName: "يرجى ادخال اسم الطبيب",
        PleaseEnterPhone: "يرجى ادخال رقم الهاتف",
        PleaseEnterAddress: "يرجى ادخال العنوان",
        PleaseEnterDiagnosisName: "يرجى ادخال التشخيص",
        QuedeIsLocked: "إقفال القيود",

        clinicIsRequired: "يرجى اختيار اسم عيادة",
        doctorIsRequired: "يرجى اختيار اسم دكتور",
        studentIsRequired: "يرجى اختيار اسم طالب",
        SuspensionAndBlockRequired: "يرجى اختيار نوع الايقاف او الحجب",

        EnterSurgName: "يرجى ادخال اسم العملية ",
        EnterSurgDesc: "يرجى ادخال تفاصيل العملية ",
        EnterSurgDate: "يرجى ادخال ميعاد العملية ",

        EnterMedName: "يرجى ادخال اسم الدواء ",
        EnterMedReason: "يرجى ادخال سبب تناول الدواء ",
        EnterMedDose: "يرجى ادخال جرعة الدواء ",
        SimplifiedAccountsReport:" تقرير قائمه الدخل المبسطه ",

        EntervaccName: "يرجى ادخال اسم التطعيم ",
        EntersensName: "يرجى ادخال اسم الحساسية",
        EntersensDesc: "يرجى ادخال تفاصيل الحساسية ",
        ChooseStudentFirst: "اختر اسم الطالب اولا",
        cancel: "الغاء",
        Delete: "حذف",
        TheValueMustBeGreaterThanZero: "القيمه يجب ان تكون اكبر من صفر",
        areyousurefordeleation: "هل انت متاكد من الحذف ؟",
        DeleteGateOutPut: "امكانيه حذف مخرجات بوابه ",
        EditGateOutPut: "امكانيه التعديل علي مخرجات البوابه ",
        thiskindoftypedosenotexist: "هذا النوع غير مدعوم يرجى رفع",
        dosenotExist: "لا يوجد",
        AddNew: "اضافة جديد",
        without: "بدون",
        therewasnoclasseschoosen: "لم يتم اختيار فصول",
        thereAreErrorsinSubmission: "يوجد اخطاء في الارسال .. قم باصلاحها",
        theAppSubmittedSuccessfully: "تم ارسال التطبيق بنجاح",
        reasonIsRequired: "يرجى اختيار سبب الشكوى",
        PleaseEnterSuggestion: "يجب ادخال الشكوى ",
        infoisrequired: 'يرجى اختيار البيانات',
        address: "العنوان",
        medicalNotes: "الحالة الصحية",
        SuccessfullyActivated: "تم التنشيط بنجاح",
        ConfirmArchive: "هل انت متاكد من تغير الحالة؟",
        payedValueisBiggerThanTheRemainingValue: "القيمة المدفوعة اكبر من القيمة المتبقية للبند",
        someItemsCannotBeParted: "بعض العناصر يجب ان تدفع مرة واحده",
        valueCannotBeZero: "لا يمكن ان تكون القيمة صفر",
        recieptNumberRepeated: "رقم الايصال مكرر",
        DuplicateDiagname: "تشخيص مكرر",
        PleaseEnterPhase: "يرجى اختيار المرحلة التعليمية",
        pleaseEnterSubPhase: "يرجى اختيار الصف",
        PleaseEnterSessionName: "يرجى ادخال اسم الحصة",
        PleaseEnterStartTime: "يرجى ادخال ميعاد بداية الحصة",
        PleaseEnterEndTime: "يرجى ادخال ميعاد نهاية الحصة",
        DublicatedDate: " يوجد ميعاد حصة في هذا الوقت",
        PleaseEnterClass: "يرجى اختيار الفصل",
        PleaseEnterTerm: "يرجى اختيار الترم",
        PleaseEnterSubProp: "يرجى اختيار العنصر",
        PleaseEnterorder: "يرجى اختيار الترتيب",
        Start: "تشغيل",
        PleaseEnterOutPutsItem: "يرجى ادخال مخراجات البوابه  ",

        PleaseEnterSubject: "يرجى ادخال اسم المادة",
        thisGroupIsUsedOnlyNameCanChange: "هذه المجموعة مستخدمة يمكن تغيير الاسم فقط",
        valueIsBiggerThanTheRemain: "القيمة المراد خصمها اكبر من القيمة المتبقية للبند",
        valueIsbiggerThanThePayedValue: "القيمة المراد استرجاعها اكبر من القيمة المدفوعة",
        cannotCancelRecovery: "لا يمكن الغاء الاسترجاع",
        noItemsChoosed: "لم يتم اختيار اصناف",
        PleaseEnterFDate: "يرجى ادخال من تاريخ",
        PleaseEnterTDate: " يرجى ادخال الي تاريخ",
        PleaseEnterStudyYear: "يرجى اختيار العام الدراسي",
        PleaseEnterAdminstation: "يرجي اختيار الإدارة ",
        pleaseEnterDevision: "يرجى اختيار الشعبة",
        PleaseEnterDepartment: "يرجي اختيار القسم",
        NoReturnedData: "لا يوجد نتائج لهذة البيانات",
        startGreatEnd: "بداية ميعاد الحصة اكبر من النهاية",
        pleaseUploadPhoto: "يرجى رفع صورة",
        PleaseEnterBusName: "يرجى ادخال اسم الحافلة",
        PleaseEnterBusFrom: "يرجى ادخال بداية خط الحافلة",
        PleaseEnterBusTo: "يرجى ادخال نهاية خط الحافلة",
        PleaseEnterpathName: "يرجى ادخال خط الحافلة",
        PleaseEnterBusNumber: "يرجى ادخال رقم الحافلة",
        PleaseEnterDrivName: "يرجى ادخال اسم السائق",
        PleaseEnterSuperVisor: "يرجى ادخال اسم المشرف",
        PleaseEnterQuantity: "يرجى ادخال عدد الطلاب",
        PleaseEnterbusState: "يرجى ادخال حالة الحافلة",
        PleaseEnterKeedNumber: "يرجى ادخال رقم تسلسل الطالب",
        NoStudentWithKeedNumb: "يرجى ادخال رقم تسلسل صحيح للطالب ",
        pleaseEnterReligion: "يرجى ادخال الديانة",
        pleaseEnterKinShip: "يرجى ادخال صلة القرابة",
        pleaseEnterHouse: "يرجى ادخال اسم المنزل",
        pleaseEnterSocial: "يرجى ادخال الحالة الاجتماعية",
        pleaseEnterStatusReport: "يرجى ادخال حالة القيد",
        pleaseEnterJoinYear: "يرجى ادخال عام الالتحاق",
        PleaseEnterAdminstartionName: "يرجى ادخال اسم الادارة",
        PleaseEnterAdminsDepartment: "يرجى ادخال اسم القسم",
        EmpDuplicate: "اسم و رقم الموظف مكرر ",
        confirmActive: "هل انت متاكد من تغيير حالة الموظف",
        wrongExtension: "امتداد خطأ للملف",
        download: "تحميل",
        active: "تشغيل",
        disactive: "ايقاف",
        PleaseEnterEmployee: "يرجى اختيار موظف",
        PleaseEnterAssignment: "يرجى اختيار التكليف",
        PleaseEnterEmployeeAssignments: "يرجى اختيار تكليف على الأقل لاعضاء اللجنة",
        EmployeeForgetPrintReport: "تقرير نسيان بصمة الحضور والانصراف",
        copy: "نسخ",
        exportExcel: "تصدير اكسيل",
        exportAllToExcel:"تصدير الكل الي اكسيل ",
        exportExcelToBank: "تصدير إكسيل إلى البنك",
        markCoulms: "تحديد الاعمدة",
        print: "طباعة",
        loginDataRep: " تقرير بيانات دخول الموقع للعام الدراسي",
        stuArchieve: " ارشيف الطلاب للعام الدراسي",
        parentArchieve: "ارشيف اولياء الامور للعام الدراسي",
        classReport: " تقرير قوائم الفصول للعام الدراسي",
        phaseReport: "تقرير المراحل للعام الدراسي",
        QualityReport: "تقرير الجودة للعام الدراسي",
        stu41Report: "تقرير استمارة 4 للمستجدين للعام الدراسي1",
        stuAgeOCtRep: "تقرير سن الطلاب حتى اكتوبر للعام الدراسي",
        stu12Rep: "تقرير كشف 12 للعام الدراسي",
        parentReport: "تقرير اولياء الامور للعام الدراسي",
        sameNameRep: "تقرير الاسماء المتشابهة للعام الدراسي",
        unCompleteData: "تقرير بيانات غير مكتملة للعام الدراسي",
        studentAffairs: "شؤون  الطلاب",
        schoolManger: "مدير المدرسة",
        pleaseEnterChipName: "يرجى ادخال اسم الشريحة",
        PleaseAddAttendenceSetting: "يرجى ضبط اعدادت الحضور و الانصراف",
        pleaseAddMainSetting: "يرجى ادخال البيانات الاساسية",
        pleaseAddVacationSetting: "يرجى ادخال بيانات الاجازات",
        pleaseAddDelaySetting: "يرجى ادخال اعدادت التاخير",
        pleaseAddLeaveSetting: "يرجى ادخال اعدادت الحضور المبكر ",
        pleaseAddBenfitSetting: "يرجى ادخال اعدادت البدلات",
        pleaseAddAbsencSetting: "يرجى ادخال اعدادت الغياب",
        saturday: "السبت",
        sunday: "الاحد",
        monday: "الاتنين",
        tuesDay: "الثلاثاء",
        wednesday: "الاربعاء",
        thursDay: "الخميس",
        friday: "الجمعة",
        noBusSubscription: "لا يوجد اشتراك حافلة للطالب قم باضافةاشتراك جديد",
        PleaseEnterReportTitle: "يرجى ادخال عنوان التقرير",
        changeDepartmentForYear: "تغيير القسم للعام الدراسي",
        theStage: "المرحلة",
        theGrade: "الصف",
        theClass: "الفصل",
        theDepartment: "القسم",
        totalFees: "اجمالى المصروفات",
        theDiscount: "الخصم",
        Pounds: "جنيها",
        paied: "المدفوع",
        remain: "المتبقى",
        chkStuName: "اسم الطالب",
        chkEngName: "الطالب بالانجليزية",
        chkStuStatus: "حالة الطالب",
        chkGender: "النوع",
        chkBirthDate: "تاريخ الميلاد",
        chkBirthPlace: "مكان الميلاد",
        chkBirthNum: "رقم شهادة الميلاد",
        chkReligion: "الديانة",
        chkNationality: "الجنسية",
        chkJoinDate: "تاريخ الالتحاق",
        chkStuAddress: "العنوان",
        chkStuPhone: "هاتف الطالب",
        chkHomePhone: "هاتف المنزل ",
        chkFatherName: "اسم الاب ",
        chkFathPhone: "هاتف الاب",
        chkFathJob: "وظيفة الاب ",
        chkMothName: "اسم الام ",
        chkMothJob: "وظيفة الام",
        chkDept: "القسم",
        chkAbBranch: "الفرع",
        chkAbPhase: "المرحلة التعليمية",
        chkParentKindship: "الولاية التعليمية",

        chkAbSubPh: "الصف",
        chkAbClass: "الفصل",
        chkAgeOct: "السن حتى اكتوبر",
        hasBrothers: "لديه اخوة",
        BusParticpition: "اشتراك الحافلة",
        chkFathStatus: "حالة الاب",
        chkMotherStatus: "حالة الام",
        chkMedical: "الحالة الصحية",
        chkUser: "اسم المستخدم للطالب",
        chkParentUser: "اسم المستخدم لولي الامر",
        chkKeedNum: "رقم القيد",
        chkPassport: "الباسبور",
        chkLastSch: "انتقل من مدرسة",
        chkNatioExpire: "تاريخ انتهاء رقم الهوية",
        chkNationalId: "الرقم القومي",
        chkJoinYear: "عام الالتحاق",
        chkFathNati: "الرقم القومي للاب",
        chkMothNatio: "الرقم القومي للام",
        pleaseSelectDataTable: "يرجى اختيارات بيانات للطلاب لعرض الجدول",
        noStudentForThisFilter: "لا يوجد طلاب لهذة البيانات اللتي قمت باختيارها",
        wrongDate: "صيغة عام الالتحاق خاطئة يجب ان تكون مثل 2002/2003",
        PleaseEnterRelation: "يرجى ادخال العلاقة",
        PleaseEnterReciverName: "يرجى ادخال اسم المستلم",
        theirIsRelatedGradeFees: "هناك رسوم صفوف على هذا البند",
        pleaseSetFisicalYearForThisBranch: "قم بضبط اعدادت السنة الافتراضية لهذه المدرسة",
        exportPdf: "تصدير ك PDF ",

        SetdefaultChip: "هل تريد تعيين الشريحة كافتراضية",
        defaultChipExistBefore: "يوجد شريحة افتراضية اخرى يجب اختيار واحدة فقط",
        removeDefaultChip: "هل تريد الغاء افتراضية الشريحة",
        pleaseSetTheAttachedAccount: "قم بضبط الحساب المرتبط فى شجرة الحسابات",
        dublicateOrder: "الترتيب مكرر",
        AtypeMustContainOneMandatoryItemAtleast: "يجب ان تحتوى الفئة على بند واحد اجبارى على الاقل",
        SetdefaultCurrency: "هل ترغب في التحديث كعملة رئيسية",
        noDataForBranch: "لا يوجد بيانات لهذا الفرع",
        June: "يونيو",
        July: "يوليو",
        August: "اغسطس",
        September: "سبتمبر",
        October: "اكتوبر",
        November: "نوفمبر",
        December: "ديسيمبر",
        January: "يناير",
        February: "فبراير",
        March: "مارس",
        April: "ابريل",

        noSubjPropForSubject: "لا يوجد عناصر لهذة المادة",
        noSubjForGrade: "لا يوجد مواد دراسية لهذا الصف",
        dublicatNational: "الرقم القومي لمستلم هذا الطالب مكرر",
        ParentExistBefore: "تم اضافة هذا المستلم للطالب من قبل",
        BusExistBefore: "تم اضافة هذه الحافلة للطالب من قبل",
        details: "تفاصيل",
        errorInRemove: "خطأ في الحذف",
        infrationExist: "لا يمكن الحذف يوجد مخالفات لهذا التصنيف",
        May: "مايو",
        choose: "اختر",
        NameAlreadyExisted: "هذا الاسم مسجل بالفعل",
        TotalStudentCount: "اجمالي عدد الطلاب",
        StopedStudentCount: "عدد الطلاب الموقوفين",
        StudentsWitoutClasses: "عدد الطلاب بدون فصول",
        ClassesCount: "عدد الفصول",
        MaleStudents: "الطلاب الذكور",
        FemalStudents: "الطلاب الإناث",
        LocationMustBeGoogle: "موقع المدرسه رابط خرائط جوجل",

        requiredData: "بيانات مطلوبة",
        comunicateData: "بيانات الاتصال",
        CantDeletedPleaseCheckOnlineConnection: "لا يمكن الحذف   يرجي مراجعة الإتصال بالنظام  online ",

        otherData: "معلومات اخرى",
        ItemNameCheck: "اسم البند مسجل في حساب القيمه المضافه",
        theValueIsMoreThan: "القيمه اقل من اعلي درجه او تسويها ",
        extractRequestConfirmation: "هل انت متاكد من الوافقه علي طلب المستخرج ؟",
        AddNewFeesSettings: "اضافه اعدادات رسوم دفع ",
        EditFeesSettingsTitle: "تعديل اعدادات رسوم ",
        All: "الكل",
        PleaseSearchBeforSave: "برجاء اتمام عمليه البحث قبل الحفظ",
        حفظ: "حفظ",
        NoFeesSeetingsForthisDepartmentAndSubphase: "لا توجد اعدادات رسوم لهذالصف والقسم",
        ValueMustBeMoreThanZero: "القيمه يجب ان تكون اكبر من 0",

        default: "افتراضي",
        Quality: "تقرير الجودة",
        Phase: "تقرير المراحل",
        Class: "تقرير قوائم الفصول",
        StudentAgeReport: "تقرير الطلاب بالسن حتى اول اكتوبر",
        StudentApp41: "تقرير استمارة 41 للطلاب",
        Student12Report: "كشف 12 للطلاب",
        StudentArchive: "تقرير ارشيف الطلاب",
        SameNameReport: "تقرير الاسماء المتشابهة للطلاب",
        LoginDataReport: "بيانات دخول الموقع",
        ParentReport: "تقرير اولياء الامور",
        ParentArchiveRep: "تقرير ارشيف اولياء الامور",
        UnCompleteRep: "تقرير بيانات لم تكتمل ",
        StudentInfractionReport: "تقرير مخالفات الطلاب",
        TotalPhaseStatisticReport: "تقريراحصاء المراحل (كل) ",
        PhaseStatisticReport: "تقرير احصاء المراحل",
        SubPhaseStatisticReport: "تقرير احصاء الصفوف",
        SubPhaseCountStatisticReport: "تقرير ميزانية المدرسة كصفوف",
        PhaseCountStatisticReport: "تقرير ميزانية المدرسة كمراحل",
        StudentCards: "البطاقة التعريفية للطلاب",
        StudentPermissionReport: "تقرير اذن خروج للطالب",
        StuStatementReport: "تقرير افادة",
        cannotChangeBecauseItIsPaied: "عدم امكانية التعديل لتدفيع القيم",
        cannotDeleteBecauseItIsPaied: "عدم امكانية الحذف لتدفيع القيم",
        Main: "اساسي",
        DailyStudentAbsenceReport:"تقرير غياب الطلاب اليومي",
        CannotdeleteDueToCost: "القيد المحدد موزع علي مركز تكلفة يرجي إلغاء التوزيع أولاً ",
        changeDefaultSuccessfully: "تم تغيير الافتراضية بنجاح",
        stuAccountAggregateReport: "تقرير مطالبة الطلاب بالرسوم",
        PleaseEnterArea: "يرجى ادخال Area",
        PleaseEnterController: "يرجى ادخال Controller",
        PleaseEnterAction: "يرجى ادخال Action",
        dublicateData: "بيانات الصفحة موجودة من قبل",
        PayerNameRequired: "اسم المسدد مطلوب",
        InValidValue: "القيمه غير صالحه",
        ConfirmChangeState: "هل انت متاكد من تغيير حالة المستخدم ؟؟",
        noDefaultChipAddFirst: "لا يوجد شريحة افتراضية قم بالاضافة اولا في صفحة شريحة الموظف",
        DataInTableWillBeDeleted: "سيتم حذف جميع البيانات من الجدول !!!!!",
        CriditMustEqualDibit: "يجب ان تتساوي قيمه الدائن والمدين ف الجدول ",
        EnterSomeDataInTable: "برجاء ادخال بيانات ف الجدول",
        endMustBeLastOrder: " اخر صف في المرحلة يجب ان يكون اكبر ترتيب",
        AccountIncomePermissionReport: "تقرير سند القبض",
        AccountExchangePermissionReport: "تقرير اذن الصرف",
        remove: "حذف",
        collection: "قبض",
        Disbursement: "صرف",
        PleaseEnterSubtopic: "يرجى اختيار الموضوع الفرعي",
        PleaseEnterThetargetgroup: "يرجى اختيار الفئة المستهدفة",
        PleaseChooseOneRequest: "يرجى اختيار طلب واحد على الاقل",
        mainAccountRequired: "يرجى اختيار الحساب الرئيسي",
        FeesPaymentRecipsReport: "ايصال دفع الرسوم",
        selectteacherorsubject: "يجب ادخال مدرس واحد او مادة واحدة لكل حصة",
        thisteacherdonotmatchsubject: "يجب اختيار المادة المناسبة للمدرس ",
        wrongplaceforteacherandsubject: "ضع اسم المدرس و المادة في المكان الصحيح",
        noAccountBranch: "لا يوجد حساب لهذة المدرسة قم بالاضافة في الاعدادت",
        selectOneDocument: "يرجى اختيار مستند واحد على الاقل",
        numberStudentOutOfClassRange: "عدد الطلاب تخطى السعة المحددة للفصل ",
        enterAllFields: "يجب ادخال جميع بيانات الطالب ",
        enterAllFieldsForForm: "يجب ادخال جميع بيانات ",
        session: "الحصة",
        day: "اليوم",
        from: "من",
        to: "الي",
        FinalAccountsReport:"تقرير الحاسبات النهائيه ",
        noScheduleAvalilable: "لا يوجد جدول ",
        subject: "المادة",
        deleteScheduleFirst: " !! المدرس لديه حصة لهذا الصف قم بحذفها اولا",
        teacher: "المدرس",
        ReasonfiledIsRequired: "برجاء تحديد سبب الحذف",
        Approved: "تم الموافقه",
        Refused: "تم الرفض",
        NotYetApproved: "لم يعتمد",
        pending: "قيد التنفيذ",
        DateCanNotLessthanToday: "اختر تاريخ حديث",
        StudentAlreadyInVactionInSomeOfThisDays: "الطالب بالفعل اجازة في تلك الفتره",
        OnlyUpdatePendingRequestes: "لا يمكن التعديل الا علي الطلبات قيد التنفيذ",
        Debit: "مدين",
        Credit: "دائن",

        Diffrence: "الفرق",
        DateToMustBeGreater: "تاريخ الي يجب ان يكون اكبر",
        ConstraintStatementReport: "بيان قيد",
        Statement: "البيان",
        CertificateOfGoodConductReport: "شهادة حسن سير وسلوك",
        ClearancesOfStudentExpenses: "مخالصه بمصروفات طالب",
        ApprovalOfLevelUpgradeReport: "اقرار رفع مستوي",
        APledgeToLeave: "تعهد انصراف",
        StudentVacationReport: "تقرير اجازة طالب",
        Reason: "السبب",
        EmailAddressNotValid: "عنوان البريد الاكتروني خطأ",
        selectBlock: "اختر قالب",
        selectAtLeastOneUser: "يجب اختيار مستخدم واحد على الاقل",
        sentSuccessfuly: "تم الارسال بنجاح",
        sendby: "ارسال خلال ",
        Nodataavailableintable: "لا توجد بيانات في الجدول",
        InvalidSalary: "مرتب غير صحيح",
        ReviewsFromLessThanTo: "درجه التقيم من يجب  اقل من الي",
        FromValueLessThanToValue: "قيمه من يجب ان تكون اقل من قيمه الي",
        Confirmation: "تأكيد",
        dayToDay: "يوم",
        dayToValue: "يوم",
        minuteToMinute: "دقيقة",
        minuteToValue: "دقيقة",
        StudentsInfrationReport: "مخالفه علي الطالب ",

        Filed: "الحقل",
        Record: "سجل",
        AreYouShureFromCancelDeportation: "هل انت متاكد من الغاء ترحيل المرحله ؟",
        AreYouShureToContinueWitoutSelectedStudent: "لم يتم اختيار اي طالب ..! لتاكيد الترحيل بدون اختيار طلاب  اضغط موافق ؟",
        yes: "نعم",
        state: "الحالة",
        Next: "التالي",
        Previous: "السابق",
        Finish: "انهاء",
        SkillAddedSuccfuly: "تم اضافه المهاره",
        CourseAddedSuccfuly: "تم اضافه الكورس",
        AllowanceAddedSuccfuly: "تم اضافه البدل",
        DeductAddedSuccfuly: "تم اضافه استقطاع",
        ModelNotValid: "يوجد بيانات غير صحيحه",
        StoreTransferReport: "تحويل من مخزن لأخر",
        enterDuration: "يرجى ادخال فترة واحدة على الاقل",
        enterAnnualHoliday: "يرجى ادخال بيانات الاجازة السنوية",
        enterAnnualAgeHoliday: "يرجى ادخال بيانات الاجازة السنوية بسن الموظف",
        errorHoliday: "عدد الاجازات في الجدول لا يساوي اجمالي الاجازات الشهرية",
        errorpermission: "عدد الاذونات في الجدول لا يساوي اجمالي الاذونات",
        errorHolidayAnnual: "عدد الاجازات في الجدول لا يساوي اجمالي الاجازات السنوية",
        errorHolidayAnnualAge: "عدد الاجازات في الجدول لا يساوي اجمالي الاجازات السنوية بسن الموظف",
        discountGreaterThanRemain: "قيمة الخصم اكبر من قيمة المتبقي",
        remainIsZero: "الطالب قام بالدفع لا يوجد متبقي",
        father: "اب",
        mother: "ام",
        other: "اخرى",
        studentsCertificateReport: "شهادة درجات الطلاب",
        studentsCertificateFirstReport: "شهادة درجات الفصل الاول ",
        newAdvance: "جديد",
        certified: "معتمد",
        rejected: "مرفوض",
        jobTitle: "المسمى الوظيفي",
        adminstration: "الادارة",
        employee: "الموظف",
        chkadminsName: "الادارة",
        chkBankAccountNumber: "رقم الحساب البنكي",
        chkEmployeeCode: "كود الموظف",
        chksalary: "الراتب",
        chkAssessment: "التقديرات",
        chkUniversities: "الجامعة",
        chkisInsurance: "مؤمن",
        chkgender: "النوع",
        chkqualifName: "المؤهل",
        chkAddress: "العنوان",
        chkcity: "المدينة",
        chkGovernorate: "المحافظة",
        chkCountry: "الدولة",
        chkNationalStayingId: "رقم القومي",
        chkMobile: "رقم الهاتف",
        chkbirthData: "تاريخ الميلاد",
        chksocialStatus: "الحالة الاجتماعية",
        chkDateOfHiring: "تاريخ التعيين",
        chkempName: "اسم الموظف",
        chkDepartmnetName: "اسم القسم",
        chkworkSystem: "نظام العمل",
        chkactive: "نشط",
        HalekStoreReport: "تقرير مخزن الهالك",
        chknameEN: "الاسم بالانجليزيه",
        chkRecievingJobDate: "تاريخ استلام العمل",
        chkEndingContractDate: "تاريخ انتهاء العقد",
        chkFingerprintNumber: "رقم البصمه",
        chkBasicSalary: "الراتب الاساسى",
        chkInsuranceSubscriptionFee: "اجر الاشتراك التامينى",
        chkInsuranceNumber: "الرقم التامينى",
        chkjobTitle: "المسمى الوظيفى",
        chkemail: "البريد الالكترونى",
        chkHighStudies: "الدراسات العليا",
        chkReligion: "الديانه",
        chkSkills: "المهارات",
        chkCourses: "الكورسات",
        TheStudent: "الطالب",
        SeatNumber: "رقم جلوس",
        Repeted: "مكرر",
        Empty: "فارغ",
        ClassesReport: "تقرير الفصول الدراسية",
        ClassesCapacityReport: "تقرير سعة الفصول الدراسية",
        CanNotSave: "لا يمكن الحفظ",
        SubjectNotHaveProperties: "الماده لا تحتوي علي عناصر",
        YouCanNotEditCertifiedReward: "لا يمكنك تعديل علي مكافاة موظف تم الموافقه عليها ",
        YouCanNotEditCertifiedDeduction: "لا يمكنك تعديل علي خصم موظف تم الموافقه عليه ",
        PaymentstatementReport: "تقرير مدفوعات الطلاب ",
        StudentRecoveryReport: "تقرير استرجاع المدفوعات ",
        AccountStatementReport: "تقرير كشف حساب",
        FeesNotPaiedForsonsReport: "تقرير المصروفات الغير مدفوعه لأولياء الامور",
        studentsItemsAndSavesReport: "تقرير البنود والخزن للطلاب",
        studentsAcountsAggregateReport: "تقرير حسابات الطلاب المجمع",
        addDepitFirst: "اضف مديونية اجبارية على الطالب لهذا العام اولا",
        StudentStatus_new: "مستجد",
        StudentStatus_failer: "باق",
        StudentStatus_successfulandtransferable: "ناجح ومنقول",
        StudentStatus_accepted: "مقبول",
        StudentStatus_check: "فحص",
        YouCanNotEditCertifiedPenalty: "لا يمكنك تعديل الجزاءات المعتمدة",
        AtLeastChoseOneEmployee: "يجب اختيار موظف واحد علي الاقل",
        HolidayBalanceAddSuccessfully: "تم حساب رصيد الاجازات بنجاح ",
        HolidayBalanceCertficationSuccesfully: "تم اعتماد رصيد الاجازات بنجاح ",
        TherIsNoStatusNewInSelectedEmployeeholidayBalance: "لا يوجد اي رصيد اجازات حالته جديده في المختار",
        pleaseSelectEmpDataTable: "يرجى اختيار بيانات للموظفين ",
        chkReturendFrom: "الدولة العائد \اللاجئ منها",
        NoDepitForStudent: "لم يتم اضافة مديونية على هذا الطالب",
        StudentIsDeported: "تم ترحيل الطالب خارج المدرسة",
        NoMemberAdded: "لم يتم اضافة اعضاء لهذا الكنترول",

        ForDateFromHolidayBalanceNotBeAvilable: "تاريخ بدأ الاجازة  اقل من تاريخ تفعيل رصيد الاجازات ",
        HolidayBalanceCanNotAllowThisDaysCount: "رصيد الموظف من  نوع الاجازة لا يسمح بعدد الايام",
        CanNotSkipAllowedDaysInMonth: "غير مسموح بتخطي عدد ايام الاجازة المحدده خلال شهر واحد يمكنك تخطي هذا القيد عن طريق تحديد تخطي حد الاجازات الشهريه",
        CanNotProvideHolidaytodynamicDurationEmployee: "لا يمكن اضافه اجازة من رصيد الاجازات للموظف الغير ملتزم بفترة",
        ChosenDurationAlreadyHolidayForEmployee: "الفترة المختارة للاجازة هي بالفعل اجازة عند الموظف",
        AskedHolidaydaysContainsSomeAlreadyHolidaysForEmployee: "فترة الاجازة المختاره تحتوي علي ايام اجازة مسجله ومعتمدة من قبل",
        AttrIsAlereadyExcisit: "هذا الحقل موجود مسبقا",
        printStudentData: "طباعه بيانات الطالب",
        studentOnlineregistrationformReport: "طباعه استمارة تسجيل",
        PrintRegistrationOrder: "طباعه  طلب التحاق",
        printStudentPledgeReport: "طباعه   تعهد",
        ConfirmCertificate: "هل انت متاكد من الاعتماد ؟ ",
        ConfirmReject: "هل انت متاكد من الرفض ؟",
        CanNotDeleteSubMainLink: "لا يمكن حذف ربط الحساب ",
        remainPermissionLessThanPermission: "اجمالي الاذونات المتبقية اقل من مدة الاذن للموظف",
        dublicateEmpPermission: "توقيت الاذن مكرر للموظف ",
        InEditYouCanNotEditAttendingLeavingType: "لا يمكن تعديل نوع الحضور او الانصراف في حاله التعديل",
        LeavingTimeMustBeGraterThanAttending: "وقت الانصراف يجب ان يكون اكبر من الحضور",
        AttendingTimeMustBeSmallerThanAttending: "وقت الحضور يجب ان يكون اقل من الانصراف",
        AttendingForEmployeeInThisDayAddedbefor: "تم اضافه حضور لهذا الموظف في اليوم المحدد من قبل يمكنك التعديل عليه",
        feesPaymentItemsReport: "تقرير دفع رسوم الطالب",
        thereIsEmployeesWithDelayTimeNotHandeledInDelayTimeRulesPleaseCheckDelayTimeRules: "هناك بعض الموظفين عدد دقائق التاخير لديهم غير مدرجه في قواعد التاخير الموجوده بالنظام برجاء التاكد من وجود قاعدة تاخير بعدد  دقائق مفتوح او كبير لكل انظمه العمل لعدم حدوث مثل هذه المشكله مره اخري .",
        thereIsEmployeesWithExtraTimeNotHandeledInExtraTimeRulesPleaseCheckExtraTimeRules: "هناك بعض الموظفين عدد دقائق الاضافي لديهم غير مدرجه في قواعد الوقت الاضافي الموجوده بالنظام برجاء التاكد من وجود قاعدة وقت اضافي بعدد  دقائق مفتوح او كبير لجميع انظمه العمل لعدم حدوث مثل هذه المشكله مره اخري .",
        NumberAlreadySaved: "الرقم مسجل من قبل",
        TaxesUniqNumbersDublecated: "غير مسموح بتكرار ارقام الشرائح",
        TaxesRangesOverlapping: "هناك تدخال في مدي الشرائح ",
        MoreThaneToValue: "ادخل قيمه اقل من قيمه الــي",
        MoreThaneMaxValue: "ادخل قيمه اقل من الحد الاقصي",
        StoreExchangePermissionReport: "تقرير إذن صرف أصناف",
        StudentsWithoutClassesReport: "تقرير الطلاب بدون فصول ",
        CanNotCalculateTaxesPleaseFillTaxesSettingsBefor: "برجاء ملء بيانات الضرائب من اعددات شؤون  الموظفين لاتمام عمليه حساب الضرائب للموظف",
        pleaseFillAllRequierdFailds: "برجاء ملء جميع البيانات المطلوبه",
        CanNotCalculateInsurancePleaseFillInshuranceSettingsBefor: "برجاء ملء بيانات التامينات من اعددات شؤون  الموظفين لاتمام عمليه حساب التامينات للموظف",
        StudentBusReport: "تقرير الحافلات",
        PleaseEnterRuleType: "يرجى اختيار نوع القاعدة",
        AllowanceDateLittleThanHiringDate: "تاريخ البدل اقدم من تاريخ تعيين الموظف",
        PasswordWeakPleaseEnterAtLeast8LettersWithNumbers: "كلمه مرور ضعيفة ,, يرجي ادخال علي الاقل حرف واحد و 7 ارقام",
        //NoActionHasBeenTaken:"لم يتم اتخاذ اجراء",
        ActionHasBeenTaken: "تم اتخاذ اجراء ",
        thereisPunshedEmployeeIgnored: "تم تجاهل الموظفين المعاقبين من قبل عند تحديد الكل",
        FigerPrintAddedToEmployeeAutomaticlyFromForgetingfingerprintpage: "تم اضافه البصمه تلقائي عن طريق صفحه نسيان الحضور والانصراف",
        ConfirmAddAttendingForEmployeesSelected: "تاكيد اضافه بصمه تلقائي للموظفين المختارين ؟ لن تتمكن من الغاء العملية ؟",
        PenalityAddedBecouseofForgetFingerPrint: "تم اضافه الجزاء علي الموظف بسبب نسيان بصمة",
        required: "الزامي",

        YouMustremoveAllEmployeeRelatedData: "يجب حذف جميع العلاقات المرتبطه بهذا الموظف ثم محاولة الحذفمرة اخري",
        CertificatedIgnpredFromSelecting: "تم تجاهل كل ما هو حالته معتمد لانه غير قابل للتعديل",
        requiredDataNotEntered: "توجد بيانات مطلوبه لم يتم ادخالها",

        NoDiscountSettings: "لا يوجد اعدادت للخصم",
        DiscountAddedBeforeThisYear: "تم اضافة هذا الخصم مسبقا هذا العام",
        attendanceRepeat: "بيانات الجهاز مكررة",
        cannotRemoveEmpDuration: "لا يمكن حذف الفترة لارتباط موظف بها",
        notDeleted: "لم يتم الحذف",
        deleteChildsFirst: "قم بحذف الفئات الداخلية لهذه الفئة اولا",
        cannotEditTypethisItemHasChilds: "لا يمكن تعديل نوع هذه الفئة لانها تحتوى على فئات داخليه",
        PleaseAddYourAccountNameInEnglish: "يرجي اضافه اسم الحساب بالانجليزيه",
        theirIsItemsUnderThisCategory: "هناك اصناف تحت هذه الفئة",
        invalidData: "توجد بيانات مطلوبه لم يتم ارسالها",
        errorInAddingOperations: "خطأ فى عمليات الاضافة",
        PleaseAddTransactions: "يجب اضافة حركة واحده على الاقل",
        Transactions: "الحركات",

        selectOneAtLeast: "يرجى اختيار واحد على الاقل",
        RangesOverlaps: "هناك تداخل في القيم من و الي ",
        NoFeesForThisItemForThisStudents: "لا يوجد رسوم على هذا البند للطالب",
        StudentNationalIdExpireDate: "تقرير انتهاء الرقم القومي",
        DisconnectedStudents: "تقرير طلاب منقطعين",
        RemainCannotBeLessThanZero: "المتبقى لا يمكن ان يكون اقل من الصفر",
        PleaseEnterattribute: "حقل الخاصية مطلوب",
        PleaseEnterValue: "حقل القيمه مطلوب",
        invoiceNumberRepeated: "رقم الفاتورة مكرر",
        cannotEditThisInvoiceBecauseItIsTouched: "لا يمكن تعديل الفاتورة لان القيم العمليات المدخله فيه تم استخدامها",
        DoesNotHavepermissionForThis: "ليس لديك صلاحيه للقيام بهذا ",
        ConfirmDeleteCertficated: "سيتم حذف البيانات التي تم اعتمادهاوبعض من البيانات الاخري وعند اتمام الحذف لا يمكنك الرجوع في عملية الحذف مره اخري ؟ للتاكيد اضغط موافق",
        DeleteCertificatedRows: "حذف البيانات المعتمدة",
        ReceivingTablet: "اقرار استلام التابلت",
        pleaseSetTheconnectionsForGoodsStock: "من فضلك قم بضبط اعدادت شجرة الحسابات الخاصه بمخزون البضاعه",
        pleaseSetTheAccountIdForTheSupplier: "لا يوجد حساب مربوط بهذا المورد",
        pleaseSetTheTransferFeesConnectionAccount: "من فضلك قم بضبط اعدادت شجرة الحسابات الخاصه مصاريف النقل",
        pleaseSetTheEarnedDiscountAccountConnection: "من فضلك قم بضبط اعدادت شجرة الحسابات الخاصه بالخصم المكتسب",
        pleaseSetTheAddtionalValueAccountConnection: "من فضلك قم بضبط اعدادت شجرة الحسابات الخاصه بالقيمة المضافة",
        pleaseSetThecomplexTaxAccountConnection: "من فضلك قم بضبط اعدادت شجرة الحسابات الخاصه بالضريبة المركبة",
        ageInOctober: "السن في أول أكتوبر للعام الدراسي",
        ageInFirstOctober: "السن في أول أكتوبر ",
        month: "شهر",
        year: "سنة",
        day2: "يوم",
        Pause: "ايقاف",
        choosestudent: "ااختر طالب",
        pleaseSetTheAccountIdForTheClient: "لا يوجد حساب مربوط بهذا العميل",
        pleaseSetTheItemsSoldCostConnectionAccount: "من فضلك قم بضبط اعدادت شجرة الحسابات الخاصه بتكلفة البضاعة المباعة",
        pleaseSetTheallowedDiscountAccountConnection: "من فضلك قم بضبط اعدادت شجرة الحسابات الخاصه بحساب الخصم المسموح به",
        QuantityGreaterThanAvalialbleCountInStore: "الكمية اكبر من العدد المتاح للصنف في المخزن",
        UsernameIsAlreadyExisting: "اسم المستخدم مكرر",
        checkFinancialYearWithBranch: "يرجى ضبط اعدادات السنة المالية مع الفرع",
        returnGreaterThanStart: "تاريخ الارجاع يجب ان يكون اكبر من تاريخ الاستعارة",
        PleaseEnterURL: "يرجى ادخال URL",
        StudentAbsences_Report: "تقرير غياب الطلاب",
        PhaseAbsenceStatisticsReport: "تقرير احصائيات غياب الطلاب",
        youExceededTheNumberOfBranchesTheCompanySetForYouPleaseContactUsToIncreeseTheIt: "لا يمكن اضافة فروع اخرى برجاء التواصل مع الدعم الفنى لزيادة عدد الفروع المسموح بها فى النظام",
        schoolDataNotSetCorrectlyPleaseCheckIt: "برجاء التواصل مع الدعم لظبط الجداول الخاصه باعدادت النظام",
        MedicalTicketReport: "تقرير الكشوفات الطبية",
        StudentAbsence_Report: "تقرير غياب طالب",
        EmailAddressIsAlreadyExisting: "البريد الالكتروني مستخدم من قبل ",
        delivered: "تم التسليم",
        notDelivered: "لم يتم التسليم",
        deliver: "تسليم",
        unDeliver: "الغاء التسليم",
        notSaved: "لم يتم الحفظ",
        notSavedBeczuseOfWrongInpute: "لم يتم الحقظ...راجع البيانات ثم حاول مجددا ",
        Cancelled: "تم الالغاء",
        Rejected: "مرفوض",
        Invalid: "غير صحيح",
        Valid: "صحيح",
        Submitted: "تم الارسال",


        studentHasDepits: "لا يمكن التعديل بسبب وجود مديونية على الطالب",
        theirIsNoJoinYearsAdded: "لا توجد اعوام التحاق من فضلك قم باضافة اعوام التحاق",
        theirisNoStudentReportStatusAdded: "لا توجد حالات للطلاب من فضلك قم باضافة حالات للطلاب",
        pleaseCheckNationalityOrReligionOrBranches: "توجد بيانات مطلوبه  اما الفروع او الديانات او الجنسيات",
        pleaseCheckStagesAndGradesAndDepartmentsTheyMustHaveData: "توجد بيانات مطلوبه لم يتم اضافتها مراحل او صفوف او اقسام لن يتم اضافة  طالب بدون هذه البيانات",
        pleaseSelectTheCurrency: "قم بتحديد عملة",
        pleaceCheckFeesSettingsOnThisStudent: "تاكد من اعدادات الرسوم لهذا الطالب اولا",
        pleaseSelectOneFileAtleast: "برجاء تحديد علي الاقل ملف واحد",
        pleaseSelectAFile: "من فضلك اختر ملف",
        extentionNotAllowed: "صيغ الملفات غير مدعومة",
        itemNotFound: "الصنف غير موجود",
        notfound: "غير موجود",
        AddAProcessToTheSupplyOrder: "اضف عمليه الي امر التوريد",

        PleaseEnterTheLibaray: "برجاء اختيار المكتبة",
        PleaseEnterAnyValue: "  برجاء اختيار قيمه ",
        dublicateNationalId: "الرقم القومي مكرر",
        studentsCountInEveryStage: "تعداد الطلاب في كل مرحلة",
        studentsAbsanceReportsInTheCurrentStudyYear: "تقارير الغياب خلال شهور العام الدراسي الحالي",
        student: "طالب",
        studentsTypesInEveryStage: "انواع الطلاب في كل مرحلة",
        suspendedStudents: "طلاب موقوفين",
        acceptedStudents: "طلاب مقبولين",
        newStudents: "طلاب مستجدين",
        underExamination: "طلاب قيد الفحص",
        studentsWithOutClasses: "طلاب بدون فصول",
        parent: "ولى الأمر",
        total: "الاجمالى",
        SalePrice: "سعر البيع",
        BuyPrice: "سعر الشراء",
        CodeCategory: "كود الصنف",
        CheckEmailSentData: "برجاء مراجعة اعدادات البريد الالكتروني من المنطقة الادارية",
        errorInSendSms: "خطأ في ارسال ال sms يرجى مراجعة رقم الهاتف",
        errorInSendwhatsAppMsg: "خطأ في ارسال ال WhatsApp Message يرجى مراجعة رقم الهاتف ل",
        errorInSendMsg: "خطأ في ارسال الرسالة",
        NoMovementsHaveBeenAddedToTheSupplyOrder: "لا يوجد اي حركات علي امر التوريد",
        CheckServiceProviderData: "يرجى ظبط اعدادات ال sms في المنطقة الادارية",
        CheckServiceProviderDataWhatsapp: "يرجى ظبط اعدادات ال Whatsapp في المنطقة الادارية",
        errorInMobileNumber: "خطأ في رقم هاتف المستخدم",
        errorInSender: "خطأ في اسم الراسل يرجى ضبط الاعدادت ",
        wrongEmail: "خطأ في صيغة البريد الالكتروني او فارغ",
        StudentCheckOutFileReport: "طلب سحب ملف للطالب",
        StudentNominationLetterReport: "خطاب ترشيح للطالب",
        StudentStatementArrivalReport: "افادة وافد للطالب ",
        BusPathPermissionReport: "اذن تيسير حافلة",
        DesignStudentsReport: "تقرير بيانات الطلاب",
        itemName: "اسم البند",
        recovered: "مسترجع",
        noDepitAddedOnThisStudent: "لم يتم اضافة مديونية على هذا الطالب",
        totalStudentCredit: "اجمالي حساب الطالب",
        totalCreditForCurrentYear: "اجمالي الرصيد العام الحالي",
        BriefRestrictions: "تقرير القيود المختصرة",
        DetailedRestrictions: "تقرير القيود المفصلة",
        editEmail: "تعديل البريد الالكتروني",
        emailsNotSent: "بعض الايميلات لم يتم ارسالها",
        addBranch: "اضافة فرع",
        addClass: "اضافة فصل",
        addStage: "اضافة مرحلة",
        addGrade: "اضافة صف",
        senderName: "اسم المرسل",
        editNumber: "تعديل الرقم",
        RecevingReport: "محضر استلام",
        requiredProofId: "مطلوب ارفاق اثبات الشخصية",
        txt_medDose: "برجاء ادخال جرعة الدواء",
        EnterMedDesc: "برجاء ادخال سبب تناول الدواء",
        EnterMedDate: "برجاء ادخال تاريخ تناول الدواء",
        StudentNotes: "ملاحظات ",
        NotValidLink: "رابط غير صحيح",
        AlreadyExist: " مسجل من قبل",
        ConfirmActive: " تاكيد عمليه التنشيط",
        hyperLinkTextIsRequired: "نص الرابط في الهيدر مطلوب",
        syncDone: "تمت المزامنه بنجاح",
        provisionDone: "تمت التهيئة بنجاح",
        attatchmentsSyncDone: "تم مزامنة المرفقات",
        quantityIsGreaterThanAvalibleCredit: "الكمية اكبر من الرصيد المتاح",
        transferToDifferentBranch: "برجاء النقل الى فرع مختلف",
        transferToDifferentStore: "برجاء النقل الى مخزن مختلف",
        first: "الاول",
        second: "الثاني",
        firstSecond: "الاول\الثاني",
        properityName: "اسم العنصر",
        maxdegree: "الدرجة العظمى",
        mindegree: "الدرجة الصغرى",
        deactivate: "ايقاف",
        activate: "تفعيل",
        addProperity: "اضافة عنصر",
        chosseStudyYearToAddNew: "يجب اختيار السنه الدراسية الحاليه لاضافه جديد",
        dublicatePublicPrivateNum: "الرقم العام والخاص مكرر",
        dublicateParcode: "الباركود مكرر",
        dublicateParcodeAndPublicPrivateNum: "الرقم العام و الخاص و الباركود مكرر ",
        percent: "نسبة",
        error: "خطأ",
        pleaseDeleteProparitsBeforeDeActivate: "من فضل قم بمسح توزيع العنصر ثم قم بالحذف",
        noStudentDataInClass: "لا يوجد طلاب في هذا الفصل",
        pleaseEnterDateTosearch: "برجاء تحديد تاريخ للبحث",
        showSons: "عرض الابناء",
        canNotChangeEndDay: "لا يمكن تعديل يوم نهاية الشهر لوجود اوقات اضافية او حسميات للموظف معتمدة ",
        studentsCertificateTermReport: "شهادة درجات الفصل الدراسي",
        dublicateTerm: "يوجد درجات لهذا الفصل الدراسي من فبل",
        AccountsQuedesDeletedReport: "تقرير القيود المحذوفة",
        StudentsAccountDiscountReport: "تقرير الخصم للطلاب",
        dublicateDate: "التاريخ مكرر",
        pleaseSelectStore: "يرجى اختيار مخزن",
        noQuedes: "لا يوجد قيود",
        item2: "صنف",
        return: "استرجاع",
        drugAddedBefore: "تم اضافة الدواء من قبل",
        NoSetttingsAddedInEmployeeAffaresSettings: "لم يتم اضافة اعدادات شؤون  الموظفيين ",
        studentsCertificateForthDurationTermReport: "شهادة درجات الفصل الدراسي النهائي",
        dublicateEducator: "تم اضافة المربي لفصل اخر من قبل",
        canNotChangeSystemState: "لا يمكن تغيير حالة نظام العمل",
        PleaseEnterParent: "يجب اختيار ولي امر",
        NumberIsOutOfRange: "الرقم المدخل خارج مدى الدفتر",
        writeStudentName: "اكتب اسم الطالب",
        writeParentName: "اكتب اسم ولي الامر",
        addAnotherFees: " اضافة رسوم اخرى ",
        deleteMandatoryFees: "حذف المديوينة الاجبارية",
        editStoreCenter: "تعديل مركز الحسابات ",
        StoreCenter: "مركز الحسابات للمخزن",
        StudentBusPaymentsReport: "تقرير مدفوعات الحافلات",
        StoreAccountsCenterPermisionsReport: "تقرير اذونات صرف صنف مع مراكز حسابات المخازن الاضافية",
        notFoundOrderedSupply: "لا يوجد امر توريد لهذا الرقم",
        Thereisanincompletetransferprocess: "هناك عملية ترحيل غير مكتملة وتم ايقاف بعض امكانيات النظام حتى اكتمالها",
        pleaseEnterThevalue: "يرجى اختيار قيمة واحدة للمطالبة على الاقل",
        selectOneTypeAtLeast: "يرجى اختيار اعداد واحد على الاقل",
        invalidDomain: "صيغة دومين خاطئة",
        noEmailSettings: "يرجى ادخال اعدادت البريد الالكتروني ",
        CanNotDeleteDivisionFromClass: "لا يمكن حذف الشعبة من الفصل لوجود طلبة في هذة الشعبة",
        PleaseEnterCurrency: "يرجى ادخال اسم العملة",
        PleaseEnterCurrencyEn: "يرجى ادخال اسم العملة بالانجليزية",
        BookImages: "صور الكتاب",
        BriefReciepts: " تقرير الايصالات مختصر",
        DetailsReciepts: "تقرير الايصالات مفصل",
        chkFathMobile: "رقم هاتف الاب",
        PleaseAddYourAccountName: "يرجى ادخال اسم الحساب",
        chkMotherMobile: "رقم الهاتف الام",
        chkEmergencyPhone: "تليفون الطوارئ",
        EmployeeSubjectsReport: "تقرير المدرسيين و الفصول",
        EmployeeDegreeDanger: "فشل في اضافة تقدير للموظف",
        EmployeeDegreeSuccess: "تم اضافة التقدير للموظف بنجاح",
        AddDegreeSuccess: "تمت اضافة التقدير بنجاح",
        DeleteDegree: "تم حذف التقدير بنجاح",
        DegreeFailed: "لم تتم الاضافة حاول تغيير القيمة وجرب مجددا",
        EmployeeDegreeDeleted: "تم الحذف بنجاح",
        EmployeeDegreeDeletedFailed: "لم يتم الحذف حاول مجددا",
        DeleteDegreeFailed: "يوجد هناك بعض الموظفين يملكون هذا التقييم يجب عليك حذف هذه التقييمات اولا وبعدها حذف التقييم",
        FailedDeleteDegree: "حدث خطأ حاول لاحقا",
        OprationFailed: "حدث خطء اثناء التسجيل",
        ResendSuccessfully: "تم اعاده الارسال بنجاح",
        FaildToResend: "لم يتم الارسال برجاء مراجعه الارقام و التأكد من الرصيد ",
        NoSmsWasFound: "لايوجد رسائل ل يتم اعاده ارسالها ",
        AllResendSuccessfully: "تم اعاده ارسال جميع الرسائل",
        NoSotredSmsWasFound: "لا يوجد رسائل معلقه",
        someThingWentWrongWithDataTble: "حدث خطأ و لم نستطتع تحميل جدول رسائل لم يتم ارسالها",

        RelationsInDelete: "لا يمكن الحذف لارتباطه بعناصر اخري",
        RelationsForDelete: "لايمكن الحذف لإرتباطه بسجلات اخري ",
        DublicatedBus: "هناك باص اخر بنفس الاسم غير اسم الباص و حاول مجددا",
        DuplicatedPath: "اسم المسار موجود بالفعل غير الاسم وحاول مجددا",
        DuplicatedStation: "اسم المحطة موجود بالفعل غير الاسم وحاول مجددا",
        FinancialYearsNeeded: "أضف سنة مالية اولا",
        DuplicatedStation: "اسم المحطة موجود بالفعل غير الاسم وحاول مجددا",
        chkStuEmail: "البريد الالكتروني للطالب",
        chkstuStudyLanguage: "اللغه الاولى",
        chkstuSecodndLanguage: "اللغه الثانيه",
        chkFatherEmail: "البريد الالكتروني للاب",
        add: "اضافة",
        chkNotes: "الملاحظات",
        chkMotherEmail: "البريد الالكتروني للام",
        StudentBrotherReport: "تقرير الاخوه",
        StudentEmpSonsReport: "تقرير ابناء العاملين",
        StudentEmpSonsReport: "تقرير ابناء العاملين",
        GraduateYearOrBirthDateNotValid: "سنة التخرج او تاريخ الميلاد اكبر من التاريخ الحالي رجاءا ادخال هذه البيانات بشكل صحيح",
        ManageDebartmentIsRequired: "الادارة مطلوبة",
        DepartmentIsRequired: "القسم مطلوب",
        JobTitleIsRequired: "الوظيفة مطلوبة",
        NameIsRequired: "الاسم مطلوب",
        NationalIDIsRequired: "الرقم القومي مطلوب",
        PhoneIsRequired: "رقم الهاتف مطلوب",
        EducationIsRequired: "الدرجة التعليمية مطلوبة",
        EmailIsRequired: "البريد الالكتروني مطلوب",
        BirthDateIsRequired: "تاريخ الميلاد مطلوب",
        GraduateYearIsRequired: "سنة التخرج مطلوبة",
        BranchIsRequired: "الفرع مطلوب",
        AddSubscription: "اضافة اشتراك",
        GraduateYearIsRequired: "سنة التخرج مطلوبة",
        Excellent: "امتياز",
        VeryGood: "جيد جدا",
        Good: "جيد",
        Acceptable: "مقبول",
        New: "جديد",
        Rejected: "مرفوض",
        Certficate: "معتمد",
        freeTemporarily: "معافى نهائيا",
        freePermanently: "معافى مؤقتا",
        MilitaryDone: "تمت",
        Free: "معافى",
        Doctor: "دكتور",
        Master: "ماجيستير",
        Bachelor: "بكالريوس",
        Islamic: "اسلامي",
        Humans: "بشر",
        Academy: "اكاديمي",
        Diploma: "دبلومة",
        Secondary: "ثانوية",
        preparatory: "اعداداية",
        Primary: "ابتدائية",
        AzharSecondarySchool: "الثانوية الازهرية",
        Technicalschool: "المدرسة الفنية",
        None: "لا يوجد",
        freeTemporarily: "معافي مؤقتا",
        FreePermanently: "معافي نهائيا",
        MilitaryDone: "تمت",
        Free: "متاح",
        StudentVacationReportDay: "تقرير اجازات الطلاب",
        StudentVacationReportDayAll: "تقرير اجازات الطلاب",
        PleaseEnterSubPhaseName: "رجاء ادخل صف",
        PleaseEnterClassName: "رجاء ادخل فصل",
        PleaseEnterDevisionName: "رجاء ادخل شعبة",
        PleaseEnterPhaseName: "رجاء ادخل مرحلة تعليمية",
        BrothersCount: "عدد الأخوة",
        StudentAccountStatement: "كشف حساب الطالب",
        AddDepit: "اضف مديونية",
        ValueAdded: "القيمة المضافة",
        totalAdditionalValue: "اجمالى القيمة المضافة",
        totalPayedTax: "اجمالى المدفوع لحساب الضريبة",
        Remainder: "باقي",
        addDiscount: "اضافة خصم",
        DeleteItem: "حذف البند",
        Addingoptionalfeestothecategory: "اضافة رسوم اختيارية على الفئة ",
        studentmovements: "حركات الطالب",
        TotalValue: "اجمالي القيمة",
        TotalPaid: "اجمالى المدفوع",
        Paid: "المدفوع",
        paid: "تم الدفع ",
        notPaid: "لم يتم الدفع",
        totalDiscount: "اجمالي الخصم",
        Totalrecovered: "اجمالى المسترجع",
        totalresidualaddedvalue: "اجمالى القيمة المضافة المتبقية",
        totalcurrentyearbalance: "اجمالي الرصيد العام الحالي",
        Studenttotalaccount: "اجمالي حساب الطالب",
        AvailableValue: "القيمة المتاحة",
        WithTheTax: "مع الضريبة",
        PayedWithoutTax: "المدفوع بدون الضريبة",
        TaxAmount: "قيمة الضريبة",
        TheAmountPaid: "المبلغ المدفوع",
        banksAndSaves: "البنوك والخزن",
        TypeTheAmount: "اكتب المبلغ",
        debtor: "المدين",
        creditor: "الدائن",
        Date: "التاريخ",
        QuedNumber: "رقم القيد",
        Description: "وصف",
        user: "المستخدم",
        printareceipt: "طباعة ايصال",
        cancelTransactionDiscountPriv: "الغاء الخصم",
        cancelTransactionPayPriv: "الغاء الدفع",
        cancelTransactionPriv: "الغاء الاسترجاع",
        recovery: "استرجاع",
        no: "لا",
        Areyousuretocancelthepayment: "هل انت متأكد من الغاء الدفع؟",
        Usethevoucheragain: "استخدام الايصال مرة اخرى",
        Areyousuretocanceltheprocess: "هل انت متأكد من الغاء العملية؟",
        AddCategories: "اضافة الاصناف",
        Store: "المخزن",
        ItemName1: "اسم الصنف",
        PleaseEnterTheCommitteeName: "برجاء ادخال اسم اللجنه",
        Unit: "الوحدة",
        price: "السعر",
        Quantity: "الكمية",
        Itemalreadyadded: "الصنف مضاف من قبل",
        Someitemshavenotbeenselected: "بعض البنود لم يتم اختيار اصناف لها",
        SubPhaseAbsenceStatisticsReport: "تقرير احصائيات غياب الطلاب فى الصف",
        ClassAbsenceStatisticsReport: "تقرير احصائيات غياب الطلاب فى الفصل",
        SrPhase: "المرحلة التعليمية",
        SrSubPhase: "الصف",
        SrClass: "الفصل",
        EmployeeMovementReport: "تقرير حركه موظف",
        StudentPermissionsReport: "تقرير أذونات الطالب",
        EmployeeAdvanceReport: "تقرير سلف الموظف",
        EmployeePermissionReport: "تقرير اذن الموظف",
        totalGeneralbalance: "إجمالي الرصيد العام",
        studentaccounttotal: "إجمالي حساب الطالب",
        EmployeeHolidayReport: " طلب أجازة موظف",
        pleaseEnterLanguage: "يرجى ادخال اللغه",

        itemsSettings: "ضبط الاصناف",

        itemsExpireDateWarning: "تحذير انتهاء صلاحية الاصناف",
        orderedSupplyReport: "تقرير امر التوريد",
        StoreInvoiceReport: "تقرير الفواتير",
        pleaseSetTheconnectionsForSale: "من فضلك قم بضبط اعدادت شجرة الحسابات الخاصه بفاتوره المبيعات",
        Nosearchtypewasselected: "لم يتم اختيار نوع للبحث",
        therewasnogradchoosen: "لم يتم اختيار صف",
        Noitemorsafehasbeenselected: "لم يتم اختيار بند او خزينة",
        phone: "التليفون",
        Brothers: "الاخوه",
        DiscountValue: "قيمة الخصم",
        Thisisthelastclassandwhenthetransferiscompletedwilltheschoolyearbechangedtothesystem: "هذا اخر صف وعند اتمام الترحيل سيتم تغير العام الدراسي للنظام ؟",
        Somestudentsareinclasseswheretherearenoemptyspaces: "بعض الطلاب فى فصول لا يوجد اماكن فارغة فيها",
        Backupsaved: "تم حفظ الباك اب",
        ScheduleSessionsReport: "جدول الحصص",
        ParentNotFound: "لا يوجد ولي أمر بهذا الاسم",
        WorkSystemID: "برجاء ادخال نظام عمل",
        SpecificDaysValueRequired: "برجاء ادخال قيمة",
        DayRequired: "برجاء ادخال يوم",
        ThisDayIsInWeeklyVacationOrTheWorkSystemAddedThisDayBefore: "هذا اليوم ضمن الاجازة الاجازات الرسمية للمدرسة او ان نظام العمل قام بأضافة هذا اليوم من قبل",
        AllEmployeesDegreeReport: "تقرير تقييمات الموظفين",
        EmployeeDegreesReport: "تقرير تقييمات الموظف",
        PleaseEnterStudentandType: "برجاء اختيار طالب ومستلم",
        PleaseChooseRecieverType: "برجاء اختيار المستلم",
        PleaseEnterStationName: "بجاء ادخال اسم محطة",
        StudentInNotSubscribeInBus: "الطالب غير مشترك في حافلة",
        selectAteacher: "يجب اختيار مدرس",
        TeacherSessionsTableReport: "جدول حصص المدرسين",
        ConfirmLogOut: "تم التحديث ب نجاح ولكن سوف يتم الخروج من السيستم و يجب اعادة الدخول مرة اخرى",
        SubjectNotHavePropertiesSettingsInTerm: "لا يوجد اعدادات لدرجات عناصر المادة في الفصل الدراسي",
        totalDegreeNotEqualPropertyDegree: "اجمالي الدرجات لا يساوي درجة العنصر ",
        totalPercentmustEqual: "النسبة الكلية يجب ان لا تتعدى ال 100 % ",
        SubjectNotHavePropertiesSettingsInTerm: "لا يوجد اعدادات لدرجات عناصر المادة في الفصل الدراسي",
        joined: "مشترك",
        UnJoined: "غير مشترك",
        ConfirmLogOut: "تم التحديث ب نجاح ولكن سوف يتم الخروج من السيستم و يجب اعادة الدخول مرة اخرى",
        ReferenceNumber: "الرقم المرجعى",
        PaymentMethod: "طريقة الدفع",
        PleaaseChoosePaymentMethod: "يرجى اختيار طريقة الدفع",
        pleaseSelectUnit: "من فضلك اختر وحدة",
        Form2Insurances: "استماره 2 تامينات",
        printElectronicInvoice: "الفاتورة الالكترونية",
        printSettingNumbersByImageReport: "طباعة ارقام الجلوس بالصورة",
        printSettingNumbersReport: "طباعة ارقام الجلوس",
        controlMembersReport: "تقرير اعضاء الكنترول",
        feesPaymentElectronicInvoice: "الفاتورة الالكترونية",
        settingsNumbersReport: "طباعة ارقام الجلوس",
        printSettingNumbersReport: "طباعة ارقام الجلوس",
        IncomeStatementReport: "قائمه الدخل",
        BalanceSheetReport: "الميزانية العمومية",
        StudentSecondLanguage: "اللغه الثانيه للطلاب",
        therewasnosecondlanguagechoosen: "لم يتم اختيار لغه",
        RetirementAge: "سن المعاش",
        RetirementAgeReport: "تقرير سن المعاش",
        pleaseEnterMonthCount: "ادخل عدد الشهور",
        OfTheTotal: "من اصل",
        ReceiptOfDocuments: "استلام المستندات",
        GateOutPuts: "مخرجات البوابه ",
        GateOutPuts: "مخرجات البوابه ",
        ErrorstopTheDataFromBack: "حدث خطأ ما...لا يمكن تحميل البيانات المطلوبه ",

        committeAbsenceReport: "تقرير غياب اللجان",
        CommitteeMembersReport: "تقرير اعضاء اللجان",
        StudentsReceiversReport: "تقرير استلام الطلاب",
        StudentsReceiversByBusReport: "طريق استلام الطلاب بالحافلات",
        TotalSecondLanguageStatisticReport: " تقرير احصاء اللغة الثانية (مراحل)",
        therewasnoFeesGroupschoosen: "لم يتم اختيار رسوم الصفوف",
        TotalSecondLanguageStatisticReport: " تقرير احصاء اللغة الثانية (مراحل)",
        AddExchangePermissionsReport: "تقرير اذن صرف و اضافة الاصناف",
        KeedNumberrepeated: "رقم قيد مكرر",
        IdentityNumberrepeated: "رقم تعريفى مكرر",
        SaleElectronicInvoiceReport: "الفاتورة الالكترونية للمبيعات",
        payregisterFees: "دفع رسوم التسجيل",
        StopedSuccessfully: "تم الايقاف بنجاح",
        cannotStopStudents: "لا يمكن ايقاف الطلبة",
        cannotActivateStudents: "لا يمكن الغاء حجب الطلبة",
        DoneButSomeStudents: "تمت ولكن بعض الطلبة",
        AlreadyActivated: "نشط بالفعل",
        AlreadyStopped: "موقوف بالفعل",
        StudentNotActive: "الطالب غير نشط",
        payregisterFees: "دفع رسوم التسجيل",
        StudentsFeesRegisterReport: "تقرير رسوم التسجيل",
        TotalPhaseAbsenceStatisticsReport: "تقرير احصائيات الغياب الاجمالى فى المرحله",
        StudentTransferReport: "تقرير نقل الطالب",
        PleaseEnterAValue: "يرجى ادخال القيمه",
        Valuemustnotbegreaterthantheremain: "لا يجب ان تزيد القيمه باقى المبلغ",
        TotalSubPhasesReport: "تقرير احصاء الصفوف",
        sonsfeesRequestsDetailsReport: "إذن دفع",
        AddInternalLevel: "اضافة مستوى داخلى",
        AddHorizontalLevel: "اضافة مستوى افقى",
        OutPutsReport: "تقرير مخرجات البوابه",
        OutPutsReport: "تقرير مخرجات البوابه",
        CheckThereAreSomeInputDontHaveValue: "عذرا و لكن هناك حقول مطلوبه يجب ملأها اولا ",
        SorryButTheFileYouLookingForIsNotFound: "عذرا و لكن  لم نجده ليتم مسحه برجاء محاول تحميل الصفحه مجددا ",



        TotalSubPhasesDeptReport: "تقرير احصاء الصفوف بالمرحله",
        StudentPermitReport: "تقرير اذونات الطلاب",
        sendedsuccessfully: "تم الارسال بنجاح",
        /* feesPaymentElectronicInvoiceReportOne:"تقرير الفاتوره الالكترونيه",*/
        TotalSubPhasesDeptReport: "تقرير احصاء الصفوف بالمرحله",

        AccountTreeReport: "تقرير شجرة الحسابات",
        Phoneisnotconnected: " الهاتف غير متصل",
        ErrorinSending: "حدث خطأ اثناء ارسال الملف ل ",
        Edit: "تعديل",
        thereisalreadystudentsforthisparent: "هناك طلاب مسجلين تحت ولي الامر",

        invalidMobile: "يرجى التحقق من رقم الهاتف ل",
        settingforwhatsappapinotfound: "لا يوجد إعدادات للواتس اب api",
        ParentwillbedeletedfromBlackListtoconfirmpressok: "سيتم حذف ولي الأمر من القائمة السوداء للتأكيد اضغط موافق ؟",

        ParentBlackListReport: "تقرير القائمه السوداء لاولياء الامور",
        NationalIdHasBeenSavedFor: "الرقم القومى مسجل من قبل ",
        DoYouWantToContinue: "هل تريد المتابعه",
        QuestionsPaperRecievedSheetReport: " محضر استلام اوراق الأسئلة",
        AnswerPaperRecievedSheetReport: " محضر استلام اوراق الاجابه",
        ChooseTerm: "اختر الترم",
        Attendace: "حضور",
        Leave: "انصراف",
        absent: "غائب",
        pleaseChooseSomeEmployeesToSave: "من فضلك اختر بعض الموظفين للحفظ",
        savedsuccessfully: "تم الحفظ بنجاح",
        DeletedSuccessfully: "تم الحذف بنجاح",
        pleaseChooseSubjectToSave: "من فضلك اختار ماده للحفظ",
        pleaseLinkBranchWithOutStokAccount: "من فضلك اربط الفرع مع حساب الهالك",
        pleaseChooseSubjectToSave: "من فضلك اختار ماده للحفظ",
        ControlOpenRecord: "محضر فتح الكنترول",
        ControlCloseRecord: "محضر غلق الكنترول",
        pleaseChooseRecordType: "من فضلك ادخل نوع المحضر",
        pleaseChooseDegreeType: "من فضلك ادخل نوع الدرجه ",
        pleaseChooseDegree: "من فضلك ادخل الدرجه ",
        pleaseChooseDegreeName: "من فضلك ادخل اسم الدرجه",

        YouCannotDeleteThisRecordThereAreDataforThisRecord: "لا يمكنك حذف اعمال الكنترول يوجد بيانات لهذا العمل",
        JoinDocuments: "اوراق التقديم",
        RecieveJoinDocumentsReport: "تقرير استلام اوراق التقديم",
        ReceievedAndRemainDocumentsReport: "تقرير الاوراق المستلمه و المتبقيه",
        YouCannotDeleteThisRecordThereAreDataforThisRecord: "لا يمكنك حذف اعمال الكنترول يوجد بيانات لهذا العمل",
        Monetary: "نقدى",
        CreditCard: "بطاقة ائتمانية",
        Deposit: "ايداع",
        Cheques: "شيكات",
        EmployeeJoinDocuments: "اوراق التقديم للموظفين",
        EmployeesJoinDocuments: "اوراق تقديم الموظفين ",
        YouCannotDeleteThisRecord: "لا يمكنك الحذف",
        EnrollmentPapers: "مصوغات التعيين",
        EmployeesDocumentsReceiver: " إستلام أوراق التقديم ",
        EmployeeReceivedPapersReport: "اقرار استلام اوراق ملف الموظف ",
        EmployeeReceivedDocumentsReport: "تقرير إستلام أوراق التقديم ",
        EmployeesAbsenceReport: "تقرير غيابات الموظفين",
        CostCenterReport: "تقرير مركز التكلفة",
        halekReport: "تقرير الهالك",
        halekAllReport: "تقرير مجمع الهالك",
        StoreAddPermissionReprot: "تقرير إذن إضافة صنف",
        Filesizeexceedsthelimitof1MB: "حجم الايقومه تجاوز 1 ميجا",
        pleaseChooseSubjectToSave: "من فضلك قم باختيار ماده للحفظ",
        pleaseChooseReportType: "من فضلك اختار نوع الاقرار",

        EmployeeExpeninceReport: "تقرير خبرة ",
        EmployeeBusPayment: "نقرير دفع رسوم الحافلات",
        StudentReciverReport: "تقرير محضر استلام طالب",

        EmployeeHolidayBalanceReport: "تقرير رصيد إجازات الموظف",
        EmployeeTerminationReport: "تقرير إنهاء الخدمة",
        EmployeeAcknowledgmentReport: "تقرير إقرار الموظف",
        EmployeeClarificationReport: "تقرير طلب إستقالة",
        EmployeeConductReport: "تقرير حسن سير وسلوك",

        ExamEndorsementReport: "تقرير سير الامتحانات",
        EndTermReport: "تقرير انتهاء المنهج",
        MaxValuehundred: "اكبر قيمة 100",
        PleaseEnterControl: "يرجى ادخال الكنترول",
        PleaseEnterCommitte: "يرجى ادخال اللجنه",
        PleaseEnterSubject: "يرجى ادخال الماده",
        ThereIsNoClassWIthThisName: "لا يوجد فصل بهذا الاسم ",

        AccountCashAndBankStatementReport: "كشف حساب الخزائن والبنوك",
        StudentAttendaceAndAbsenceStatisticsReport: "تقرير احصائيات الحضور و الغياب",
        AllStatisticsReport: "تقرير الاحصائيات",
        DepitAndCreditAreNotTheSameThisWillCauseUnaccuracyInAccountantReportUntilTheTwoSidesAreEqualInAllBranches: "الجانب الدائن و الجانب المدين غير  متساويين  وهذا سيؤدي إلي عدم  دقة التقارير المحاسبية لحين تسوية الجانبين  بكل الفروع ",
        Attending_LeavingDetailsReport: "تقرير تفاصيل حضور و انصراف الموظف",
        ParentSummoningManuallyReport: "استدعاء ولى الامر يدويا",
        nodevicedetected: "تاكد من وجود فلاشه التوقيع",
        Noslotsfound: "تاكد من وجود فلاشه التوقيع",
        Certificatenotfound: " لا يوجد شهادة للتوقيع",
        noVacationCredit: " لا يوجد رصيد للأجازات",
        YouDoNotHaveThePermissions: "ليس لديك الصلاحيات",
        DoYouWantToDeleteTheGroup: "هل ترغب فى حذف المجموعة؟",
        YouDoNotHaveTheNecessaryPpermissions: "ليس لديك الصلاحيات الازمة",
        TheChannelHasBeenAdded: "تم اضافة القناة",
        AddingInProgressPleaseWait: " ..... جار الاضافة انتظر ",
        ReceiptAcknowledgmentOfDocuments: "اقرار استلام الاوراق",
        Resend: "اعاده ارسال",


        Departments: "الاقسام",

        SendToGuardian: " إرسال رسالة لولي الأمر",
        SendWhatsAppAPI: " إرسال عبر الـ WhatsApp API",
        SendWhatsApp: " إرسال عبر الـ WhatsApp",
        SendSMS: " إرسال عبر الـ SMS",
        SendEmail: " ارسال عبر البريد الالكتروني ",
        ThereAreNoAccountsForThisBranchPleaseCheckTheAccountDirectoryForAdjustments: " لا يوجد حسابات لهذا الفرع قم بالضبط من دليل الحسابات",
        AddNewReport: "اضافة تقرير جديد",

        AddingAnotherSchool: "اضافة مدرسة أخري",
        DiscountNotAllowed: "غير متاح الخصم",
        DiscountAllowed: "متاح الخصم",
        AddUserToLibaray: "اضافه مستخدمين الي المكتبه"


    },
    en: {
        AddUserToLibaray: "Add Users To Libaray",
        enterAllFieldsForForm: "Enter All Fields  ",
        AddLibarayUser: "Add Libaray User  ",
        DoYouWantToChangeTheStatusOfIheQuede: "Do You Want To Change The Status Of Ihe Quede ?",
        OprationFailed: "OprationFailed",
        ResendSuccessfully: "  ResendSuccessfully",
        FaildToResend: "FaildToResend ",
        NoSmsWasFound: " NoSmsWasFound",
        AllResendSuccessfully: "AllResendSuccessfully",
        NoSotredSmsWasFound: "NoSotredSmsWasFound",
        someThingWentWrongWithDataTble: "someThingWentWrongWithDataTble",

        OutgoingPriceTotal: "Outgoing Price Total",
        OutgoingTotal: " Outgoing Total",
        IncomingPriceTotal: "Incoming Price Total",
        IncomingTotal: "Incoming Total",
        PreviousBalanceTotal: "Previous Balance Total ",

        ComprehensiveItemMovementReport: "Comprehensive Item Movement Report",
        EmployeeCodeIsDublicate: "Employee Code Is Dublicate",
        PleaseAddAClientFirst: "Please add a client first",
        PleaseEnterInvoiceNumber: "Please Enter Invoice Number",

        PleaseAdjustTheEmployeeAffairsSettingsAndTheInsuranceAndTaxSettingsFirst: "Please adjust the employee affairs settings and the insurance and tax settings first ",
        ActualSubscriptionValueTotal: "  Actual Subscription Value Total",
        SubscriptionValueTotal: " Subscription Value Total",
        DifferenceTotal: " Difference Total",
        BusSubscriptionModificationsReport: "Bus Subscription Modifications Report",
        BusSubscriptionModifications: "Bus Subscription Modifications",
        supplierName: "supplier Name",
        ThereAreNoAccountsForThisBranchPleaseCheckTheAccountDirectoryForAdjustments: " There are no accounts for this branch. Please check the account directory for adjustments",
        AssignAVirtualEmail: "Assign a virtual email",
        NewColorTermDegreeReport: "New Color Term Degree Report",
        RelationsForDelete: "RelationsForDelete",

        TypeOfEar: "Type of Ear",
        CheckThereAreSomeInputDontHaveValue: "CheckThereAreSomeInputDontHaveValue",
        AllCommitteMembersReport: "All Committe Members Report",
        AddHorizontalLevel: "Add Horizontal Level",
        StudentReciverReport: "Student Reciver Report",
        Concise: "Concise",
        Detailed: "Detailed",
        alertForDataFeesLastYears: "The student has an outstanding balance from the previous year; please settle it first",
        TheChannelHasBeenAdded: "The channel has been added",
        YouDoNotHaveTheNecessaryPpermissions: "You do not have the necessary permissions",
        ThereAreIncorrectNumbers: "There are incorrect numbers",
        TodayIsDateIsAHolidayForTheBranch: "Today's date is a holiday for the branch",
        NoSeasonsSelected: "No Seasons Selected",
        SaveACopyToYourDevice: "Save A Copy To Your Device",
        PleaseSelectAtLeastOneStation: "Please Select At Least One Station",
        PleaseSelectAtLeastOnePath: "Please Select At Least One Path",
        InvalidFormData: "Invalid Form Data",
        NoBusFound: "No Bus Found",
        NoBusFound: "No Bus Found",
        NoAreaFound: "No Area Found",
        NoStationFound: "No Station Found",
        StudentAbsenceRegistrationForBusesBackReport: "Student Attendance and Absence Report for Bus Return",
        StudentAbsenceRegistrationForBusesGoReport: "Student Attendance and Absence Report for Bus Departure",
        StudentAbsenceRegistrationForBuses: "Student Absence Registration For Buses",
        CompleteTheData: "Complete The Data",
        exportAllToExcel:"export All To Excel",
        CodeCategory: "Code Category",
        BuyPrice: "Buy Price",
        SalePrice: "Sale Price",
        theValueIsMoreThan: "the Value Is More Than",
        ErrorstopTheDataFromBack: "Error stop The Data From Back",
        ApprovalOfSupplyOrders: "Approval Of Supply Orders",
        SupplyRequestsMenue: " Supply Requests Menue",
        TaxPaymentReport: "Tax Payment Report",
        AStudentWhoIsInDebtCannotBeStopped: "A student who is in debt cannot be stopped",
        SoryThisGradeFeesHaveStudent: "The code has been modified, but the value cannot be modified because there are students to whom fees have been added",
        TotalDegreesOfEmployeesReport: "Total Degrees Of Employees Report",
        SonsFeesRequestsReport: "Sons Fees Requests Report",
        PleaceEnterProcedure: "Pleace Enter Procedure",
        TotalDegreesOfEmployees: "Total Degrees Of Employees",
        NotApproved: "NotApproved",
        noVacationCredit: "There is no vacation credit",
        Certificatenotfound: "Certificate not found",
        nodevicedetected: "no device detected",
        Noslotsfound: "No slots found",
        CertificateFinalForStudentsMasterCross: "CertificateFinalForStudentsMaster2",
        CantDeletedPleaseCheckOnlineConnection: "Can't Deleted Please Check Online Connection",
        ParentSummoningManuallyReport: "Parent Summoning Manually Report",
        Attending_LeavingDetailsReport: "Attending_Leaving Employee Details Report",
        DepitAndCreditAreNotTheSameThisWillCauseUnaccuracyInAccountantReportUntilTheTwoSidesAreEqualInAllBranches: "Depit And Credit Are Not The Same This Will Cause Unaccuracy In Accountant Report Until The Two Sides Are Equal In All Branches",
        SetsNumberHaveSerialNumbers: "Sets Number Have Serial Numbers Please Delete (***) Before Delete Sets Number",
        userIsSelected: "User Is Selected",
        noResultsFound: "no Results Found",
        AllStatisticsReport: "Statistics Report",
        ThePercentShouldBeLessThan100: "The Percent Should Be Less Than 100%",
        noResultsFound: "no Results Found",
        AllStatisticsReport: "Statistics Report",
        StudentAttendaceAndAbsenceStatisticsReport: "Attendace And Absence Statistics Report",
        EmployeeExpeninceReport: "Employee Expenince Report",
        EmployeeHolidayBalanceReport: "Employee Holiday Balance Report",
        EmployeeTerminationReport: "Employee Termination Report",
        EmployeeAcknowledgmentReport: "Employee Acknowledgment Report",
        EmployeeClarificationReport: "Employee Clarification Report",
        EmployeeConductReport: "Employee Conduct Report",
        EmployeeBusPayment: "Employee Bus Payment",
        youmustchooseaschoolstagegradedepartment: "you must choose / a school / stage / grade / department",
        NoBroadcastChannelsAreAvailablePleaseTryAgainLater: "No broadcast channels are available. Please try again later.",
        Pounds: "$",
        theQuedeHasBeenLocked: "The Quede has been locked",
        NoUsersHaveBeenSelected: "No users have been selected",
        DoYouWantToDeleteTheGroup: "Do you want to delete the group?",
        creditAmount: "credit Amount",
        quedeIsLocked: "quede Is Locked",
        openIsQuede: "Open Is Quede",
        NewColorTermDegreeReportForSeconadary: "Report 3 for Degrees",


        creditAmount: "credit Amount",
        EditGateOutPut: "Edit Gate OutPut",
        DeleteGateOutPut: "Delete Gate OutPut",
        PleaseEnterControl: "Please Enter Control",
        PleaseEnterCommitte: "Please Enter Committe",
        PleaseEnterSubject: "Please Enter Subject",
        MaxValuehundred: "Max Value 100",
        settingsNumbersReport: "print Setting Numbers Report",
        EndTermReport: "End Term Report",
        ExamEndorsementReport: "Exam Endorsement Report",
        pleaseChooseReportType: "please Choose Report Type",
        pleaseChooseSubjectToSave: "Please Choose Subject To Save",
        Filesizeexceedsthelimitof1MB: "File size exceeds the limit of 1 MB",
        EmployeesAbsenceReport: "Employees Absence Report",
        EmployeeReceivedDocumentsReport: "Employee Received Documents Report",
        EmployeeReceivedPapersReport: "Employee Received Papers Report",
        EmployeesDocumentsReceiver: "Employees Documents Receiver",
        EnrollmentPapers: "Enrollment Papers",
        YouCannotDeleteThisRecord: "You Cannot Delete This Record",
        EmployeesJoinDocuments: "Employees Join Documents",
        EmployeeJoinDocuments: "Employee Join Documents",
        ReceievedAndRemainDocumentsReport: "Receieved And Remain Documents Report",
        RecieveJoinDocumentsReport: "Recieve Join Documents Report",
        JoinDocuments: "Join Documents",
        pleaseLinkBranchWithOutStokAccount: "Please Link Branch With OutStok Account",
        YouCannotDeleteThisRecordThereAreDataforThisRecord: "You Cannot Delete This Record There Are Data for This Record",
        pleaseChooseRecordType: "please Choose Record Type",
        ControlCloseRecord: "Control Close Record",
        ControlOpenRecord: "Control Open Record",
        pleaseChooseSubjectToSave: "Please Choose Subject To Save",
        DeletedSuccessfully: "Deleted Successfully",
        savedsuccessfully: "Saved Successfully",
        QuedeIsLocked: "Quede Is Locked",
        GetAdvancedEmpSearchReport:"Get Advanced EmpSearch Report",
        pleaseChooseSomeEmployeesToSave: "please Choose Some Employees To Save",
        absent: "absent",
        Leave: "Leave",
        Start: "Start",
        YouDoNotHaveThePermissions: "You do not have the permissions",
        Attendace: "Attendace",
        ChooseTerm: "Choose Term",
        /* feesPaymentElectronicInvoiceReportOne:"fees Payment Electronic Invoice ReportOne",*/
        AnswerPaperRecievedSheetReport: "Answer Paper Recieved Sheet Report",
        QuestionsPaperRecievedSheetReport: "Questions Paper Recieved Sheet Report",
        DoYouWantToContinue: "Do You Want To Continue",
        NationalIdHasBeenSavedFor: "NationalId Has Been Saved For",
        ParentBlackListReport: "Parent Black List Report",
        ParentwillbedeletedfromBlackListtoconfirmpressok: "Parent Will Be Deleted From Black List To Confirm Press Ok?",
        settingforwhatsappapinotfound: "setting for whatsapp api not found",
        invalidMobile: "Mobile number is invalid, please check it with ",
        thereisalreadystudentsforthisparent: "there is already students for this parent",
        Edit: "Edit",
        ErrorinSending: "Error in Sending file",
        Phoneisnotconnected: "Phone is not connected",
        sendedsuccessfully: "sended successfully",
        StudentPermitReport: "Student Permit Report",
        TotalSubPhasesDeptReport: "Total SubPhases Dept Report",
        sonsfeesRequestsDetailsReport: "sons fees Requests Details Report",
        TotalSubPhasesReport: "Total SubPhases Report",
        Valuemustnotbegreaterthantheremain: "Value mustnot be greater than the remain",
        StudentTransferReport: "Student Transfer Report",
        TotalPhaseAbsenceStatisticsReport: "Total Phase Absence Statistics Report",
        KeedNumberrepeated: "Keed Number Repeated",
        IdentityNumberrepeated: "Identity Number Repeated",
        noFeesAddedOnThisGrade: "no Fees Added On This Grade",
        PleaseAddAnotherComplementaryAccount: "Please add another complementary account",
        supplementIsRequired: "Please choose the supplement account",
        PleaseAddTheQuedNumber: 'Please Add The Qued Number',
        AccountIsRequired: "Account Is Required",
        PaperReceiptNumber: "Receipt number outside the specified period",
        InvalidDateTimeFormatting: "Invalid Date Time Formatting",
        BankIsRequired: "Bank Is Required",
        feesPaymentElectronicInvoiceRecoveryReport: "FeesPayment Electronic Invoice Recovery Report",
        DailyStudentAbsenceReport: "Daily Student Absence Report",
        DueDateaIsRequired: "Due Date Is Required",
        CheckNumberIsRequired: "Check Number Is Required",
        PleaseFillInAllTheDataInTheTableFirst: "Please Fill In All The Data In The Table First",
        PleaseSelectAtLeastOneAccountToCompleteTheProcess: "Please select at least one account to complete the process",
        AccountNumberReceiptIsRequired: "Account Number Receipt Is Required",
        TypeOfCashReceiptsIsRequired: "Type Of Cash Receipts IsRequired",
        DateOfReceiptIsRequired: "Date Of Receipt Is Required",
        PaperReceiptNumberIsRequired: "Paper Receipt Number Is Required",
        PaperReceiptIsRequired: "Paper Receipt Is Required",
        ConfirmDelete: "Confirm Delete Operation",
        AreYouWantToDeleteMessages: "Are You Want To Delete sent Messages related with this sender name",
        ConfirmPrint: "Thers is Previous Depts for the student , Prinrt Report ?",
        PasswordWeakPleaseEnterAtLeast5LettersOrNumbers: "Password Weak << Please Enter At Least 5 Letters Or Numbers",
        PasswordsDoNotMatch: "Passwords Donot Match",
        PleaseEnterAnEmailAddress: "Please Enter An Email Address",
        PleaseEnterTheDepartment: "Please Enter The Department",
        PleaseEnterTheDivision: "Please Enter The Division",
        PleaseChooseARequest: "Please Choose A Request",
        PleaseEnterAValue: "Please Enter A Value",
        Valuemustnotbegreaterthantheremain: "Value mustnot be greater than the remain",
        PleaseEnterName: "The Name Is Required !!",
        MaxLength: "Max Length",
        pleaseFillAllRequierdFailds: "please Fill All Requierd Failds",
        PleaseEnterMobile: "The Mobile Is Required !!",
        PleaseEnterReason: "The Reason Is Required !!",
        PleaseEnterNationID: "The NationID Is Required !!",
        PleaseEnterDate: "The Date Is Required !!",
        PleaseEnterTime: "The Time Is Required !!",
        PleaseEnterCallerName: "The Caller Name Is Required !!",
        PleaseEnterCallerMobile: "The Caller Mobile Is Required !!",
        PleaseEnterNotes: "The Notes Is Required !!",
        PleaseEnterDateTo: "The Date To Is Required !!",
        PleaseEnterDateFrom: "The Date From Is Required !!",
        ConfirmVisitEnd: "Confirm End Visit?",
        EndVisitSuccess: "Success End Visit",
        PleaseEnterGateEntriesItems: "The Gate Entries Items Is Required !!",
        SetdefaultNation: "Set Default Nationality?",
        PleaseEnterNationality: "The Nationality Name Is Required",
        PleaseEnterNationalityEn: "The Nationality English Name Is Required",
        WriteHere: "Write Here ...",
        NumberOfTreeLevelsCalculationsIsRequired: "Number Of Tree Levels Calculations Is Required",
        StagesOfTreeAccountsIsRequired: "Number Of Tree Stages  Calculations Is Required",
        MaximumValuesForLevelsAreOnly5Levels: "Maximum Values For Levels Are Only 10 Levels",
        DigitSystemIsRequired: "Digit System Is Required",
        ThereIsNoClassWIthThisName: "There Is No Class WIth This Name",
        pleaseChooseDegree: "pleaseChooseDegree",

        PleaseEnterBookName: "The Name Is Required !!",
        PleaseEnterBookNumber: "The Number Is Required !!",
        PleaseEnterBookParcode: "The Parcode Is Required !!",
        PleaseEnterBookPlace: "The Place Is Required !!",
        PleaseEnterPublisher: "The Publisher Is Required !!",
        branchIsRequired: "The Branch Is Required !!",
        financialYearIsRequired: "The Financial Year Is Required !!",
        historyOfTheMovementIsRequired: "The History Of The Movement Is Required !!",
        currencyIsRequired: "The Currency Is Required !!",
        PleaseEnterAboutBook: "The About Book Is Required !!",
        FileUploadSuccessfully: "File Upload Successfully",
        PleaseEnterPath: "The File Is Required !!",
        someThingWentWrong: "Some Thing Went Wrong",
        committeeNameOrNumberIsRepeated: "Committee Name Or Number Is Repeated",
        saved: "saved",
        DateOfReceipt: "Date Of Receipt",

        deleted: "deleted",
        AddingAnotherSchool: "adding another school",
        PleaseAddTheBeginningOfTheYear: "Please Add The Beginning Of The Year",
        PleaseAddTheEndOfTheYear: "Please Add The End Of The Year",
        makeSureToChoseAcontrolAdepartmentAndAbranche: "make Sure To Chose A control A department And A branche",
        text: "text",
        number: "number",
        checkbox: "check",
        bankNameArabic_Required: "Please add the name of the bank in Arabic",
        bankNameEnglish_Required: "Please add the name of the bank in English",
        bankAccountNumber_Required: "Please add bank account number",
        accountNumber_Required: "Please add the account number for the accounts tree",
        accountOwner_Required: "Please add the account owner name",
        address_Required: "Please add the address",
        safeNameArabic_Required: "Please add the name of the safe in Arabic",
        safeNameEnglish_Required: "Please add the name of the safe in English",
        cashierIsSafe_Required: "Please add the  cashier's safe Name",
        branche_Required: "Please link the account to at least one branch",
        PleaseAddTheNameOfTheBook: "Please add the name of the book",
        PleaseAddAStartDate: "please add the beginning of the period",
        PleaseAddAEndtDate: "please add the end of the period",
        studentName: "student Name",
        branches: "branches",
        TheDivision: "The Division",
        Department: "Department",
        Departments: "Departments",
        PaymentPermissionsReport: "Payment Permissions Report",
        PaymentPermission: "Payment Permission",
        pleaseChooseDegreeName: "please Choose Degree Name",
        paid: " paid ",
        notPaid: "not Paid",
        AddStudentBus: "Add Studen tBus",
        PleaseEnterDestructionABooknumber: "Please Enter Destruction Book number",
        Thestudentborrowedthebookbefore: "The student borrowed the book before",
        Allcopiesofthebookareborrowed: "All copies of the book are borrowed",
        SuccessfullyDeleted: "Successfully Deleted",
        deleteSenderNameFirst: "delete Sender Name First",
        busNumberMustRelatedWithBusName: "busNumber Must Related With BusName",
        NotDone: "Not Done",
        UpdatedSuccessfully: "Updated Successfully",
        DoneButNotAll: "Done But Not All",
        dailyQuedes: "Daily Qued",
        DeleteGateEntry: "Delete Gate Entries",
        EditGateEntry: "Edit Gate Entry",
        CannotdeleteHaveStudentMonthResult: "Cannot Delete This Property Have Student Month Result",
        ColorTermDegreeReport: "Color Term Degree Report",
        dailyQuedesHistoryReport: "daily Quedes History Report",
        studentCallDone: "Students Apply is called with count ",
        thereisnoStudentApply: "There is No Students Apply",
        ClientName: "Client Name",
        AutoMassagesSender: "Auto Massages Sender",
        StudentsInfrationReport: "StudentsInfrationReport",
        ClientName: "Client Name",
        Resend:"Resend",

        GroupNameIsRequired: "The Group Name Is Required !!",
        PleaseSpecifyPermissions: "Please specify permissions!!",
        PleaseEnterAUsername: "Please Enter A Username",
        PleaseEnterTheFullName: "Please Enter The Full Name",
        PleaseEnterYourPassword: "Please Enter Your Password",
        ControlMembersRelatedWithCommittees: "Control Members Related With Committees , Remove Committee First",
        CanNotAddMoreThan4Levels: "Can Not Add More Than 4 Levels",
        dateFromMustbelessthanDateto: "Date From Must be less than Date to",
        addToFavoritePages: "add To Favorite Pages",
        removeFromFavoritePages: "remove From Favorite Pages",
        BookName: "Book Name",
        PleaseEnterBookdivisionName: "Please Enter Book division Name",
        deleteConfimation: "do you want to delete?",
        commonData: "common Data",
        birthData: "birth Data",
        monthlyProp: "monthly Property",
        addressData: "address Data",
        medicalStateData: "medical State Data",
        date: "date",
        sortingByDateOfReceipt: "Sorting By Date Of Receipt",

        check: "Select",
        EditData: " Edit Data",
        ReceiptOfDocuments: "Receipt Of Documents",
        ReceiptAcknowledgmentOfDocuments: "Receipt Acknowledgment Of Documents",
        TheApprovalOrRejectionOfTheSupplyOrderCannotBeModified: "The approval or rejection of the supply order cannot be modified.",
        AutomaticBackup: "Automatic Backup",
        show: "show",
        PaymentPermession: "Payment Permession",
        PaymentPermessions: "Payment Permessions",
        theNumberisfoundbefore: "theNumber of suggesion is found before",
        Cannotdelete: "Cannot delete",
        DailyTimeReport: "DailyTimeReport",
        InvaildClient: "Invaild Client",
        EgsCodeRequired: "Egs Code Required",
        StudentTransferReport: "Student Transfer Report",
        FinalCertificateDegreeMasterReport: "Final Certificate Degree Report",
        Loading: "Loading . . .",
        PleaseEnterbranchName: "Please Enter branch Name",
        PleaseEnterstudentName: "Please Enter student Name",
        PleaseEnterAnotes: "Please Enter Anotes",
        PleaseEnterinfiractionrate: "Please Enter infiraction rate",
        PleaseEnterinfiractionname: "Please Enter infiraction name",
        Addedinfrationratingbefor: " infration rating added befor",
        CommitteeAddedBefore: "Committee Added Before",
        CommitteesCallingDetectionReport: "Committees Calling Detection Report",
        Attending_LeavingReport: "Attending_Leaving Report",
        StockInventoryDataReport: "Stock Inventory Data Report",
        StudentsDelaysReport: "Students Delays Report",
        StudentDelaysReport: "Student Delays Report",
        BusTrafficReport: "Bus Traffic Report",
        StoreClint: "Store Clint",
        StudentNotFound: "Student Not Found",

        Duplicatename: "Duplicate name",
        nobook: "book number not enough",
        AuthorName: "AuthorName",
        PublicNum: " PublicNum",
        PrivateNum: "PrivateNum",
        Publisher: "Publisher",
        InPeriod: "InPeriod",
        PleaseEnternewcount: "Please Enter new count",
        namerepeated: "name repeated",
        coderepeated: "code repeated",
        nationalidRepeated: "nationalid Repeated",
        accountNameRepeated: "account name repeated",
        thisRecordIsAnItem: "this record is an item",
        doYouWantToDelete: "do you want to delete ?",
        AddingInProgressPleaseWait: "Adding in progress, please wait......",
        item: "item",
        TheAccountHasBeenTransferredSuccessfully: "The account has been transferred successfully",

        TheDebtorAccountValuesMustBeEqualToTheCreditorAccounts: "The Debtor Account Values Must Be Equal To The Creditor Accounts",
        TotalTardinessAndAbsencesReport: "Total Tardiness And Absences Report",
        SalaryComponentsForTheEmployee: "Salary Components For The Employee",

        value: "value",
        edit: "edit",
        delete: "delete",
        Clarification: "Resignation Requset",
        pleaseSetTheAccountForStudentsFees: "please Set The Account For StudentsFees",
        noStudentsChoosen: "no Students Choosen",
        pleaseChooseSomeItemsToPay: "please Choose Some Items To Pay",
        noStudentsAccountSet: "please set a studnet Main account",

        PleaseEnterSpecialtyName: "Please Enter Specialty Name",
        PleaseEnterClinicName: "Please Enter Clinic Name",
        PleaseEnterDoctorName: "Please Enter Doctor Name",
        PleaseEnterPhone: "Please Enter Phone",
        PleaseEnterAddress: "Please Enter Address",
        PleaseEnterDiagnosisName: "Please Enter Diagnosis",
        clinicIsRequired: "Clinic Is Required",
        doctorIsRequired: "Doctor Is Required",
        studentIsRequired: "Student Is Required",
        SuspensionAndBlockRequired: "Suspension And Block Required",
        EnterSurgName: "Enter Surgery Name ",
        EnterSurgDesc: "Enter Surgery Description ",
        EnterSurgDate: "Enter Surgery Date ",
        EnterMedName: "Enter Medicien Name ",
        EnterMedReason: "Enter Medicien Reason",
        EnterMedDose: "Enter Medicien Dose",
        EntervaccName: "Enter Vaccination Name",
        EntersensName: "Enter Sensitivity Name",
        EntersensDesc: "Enter Sensitivity Description",
        ChooseStudentFirst: "Choose Student Name First!",
        cancel: "Cancel",
        choosestudent: "choose student",
        Delete: "Delete",
        TheValueMustBeGreaterThanZero: "The Amount Must Be Greater Than Zero",
        monthlyProp: "monthly Prop",
        areyousurefordeleation: "Are You Sure For Delete This Data",
        thiskindoftypedosenotexist: "This Kind dosenot Exist Please Upload",
        dosenotExist: "Does Not Exist",
        AddNew: "Add New",
        without: "Without",
        therewasnoclasseschoosen: "No Classes Choosen",
        DisplayOfAcademicTermAndEndOfYearResults: "Display Of Academic Term And End Of Year Results",
        thereAreErrorsinSubmission: "There are some errors in your submission. Please correct them.",
        theAppSubmittedSuccessfully: "The application has been successfully submitted!",
        reasonIsRequired: "Suggestion Reason Is Required",
        PleaseEnterSuggestion: "Enter Suggestion ",
        infoisrequired: 'Please Select Information',
        address: " Address",
        medicalNotes: "Medical Notes",
        SuccessfullyActivated: "Successfully Activated",
        ConfirmArchive: "Are You Sure You Want Change State",
        payedValueisBiggerThanTheRemainingValue: "payed Value is Bigger Than The Remaining Value",
        someItemsCannotBeParted: "some Items Cannot Be Parted",
        valueCannotBeZero: "value Cannot Be Zero",
        recieptNumberRepeated: "reciept Number Repeated",
        errorInUpdate: "Error In Update",
        errorInSendMsg: "Error In Send Message",
        DuplicateDiagname: "Repeated Diagnosis",
        PleaseEnterDay: "Please Enter The Day",
        PleaseEnterPhase: "Please Select Stage",
        pleaseEnterSubPhase: " please Select Grade ",
        PleaseEnterSessionName: "Please Enter Session Name",
        PleaseEnterStartTime: "Please Enter Start Time",
        PleaseEnterEndTime: "Please Enter End Time",
        DublicatedDate: "Session Date Is Exsist Before",
        PleaseEnterClass: "Please Select Class",
        PleaseEnterSubject: "Please Enter Subject Name",
        PleaseEnterTerm: "Please Select Term",
        PleaseEnterSubProp: "Please Select Subject Property",
        PleaseEnterorder: "Please Select Order",
        EmployeesMissionsReport: "EmployeesMissionsReport ",
        MissionReport: "MissionReport ",
        JobApplicationSummary: "Job Application Summary",



        thisGroupIsUsedOnlyNameCanChange: "This Group Is Used Only Name Can Change",
        valueIsBiggerThanTheRemain: "value Is Bigger Than The Remain",
        valueIsbiggerThanThePayedValue: "value Is bigger Than The Payed Value",
        cannotCancelRecovery: "cannot Cancel Recovery",
        noItemsChoosed: "No Items Choosed",
        PleaseEnterFDate: "Please Enter From Date",
        PleaseEnterTDate: " Please Enter To Date",
        PleaseEnterStudyYear: "Please Select Study Year",
        pleaseEnterDevision: "Please Select Devision",
        NoReturnedData: "No Returned Data ",
        startGreatEnd: "Start Time Greater Than End Time",
        pleaseUploadPhoto: "Please Upload Photo",
        PleaseEnterBusName: "Please Enter Bus Path Name",
        PleaseEnterBusFrom: "Please Enter Start Of Bus Path",
        PleaseEnterBusTo: "Please Enter End Of Bus Path",
        PleaseEnterpathName: "Please Enter Path Name",
        PleaseEnterBusNumber: "Please Enter Bus Number",
        PleaseEnterDrivName: "Please Enter Driver Name",
        PleaseEnterSuperVisor: "Please Enter SuperVisor Name",
        PleaseEnterQuantity: "Please Enter Student Quantity",
        PleaseEnterbusState: "Please Enter Bus State",
        PleaseEnterKeedNumber: "Please Enter Student ID Number",
        NoStudentWithKeedNumb: "Please Enter Correct Student Id Number",
        pleaseEnterReligion: "Please Enter Religion",
        pleaseEnterKinShip: "Plases Enter KinShip Degree",
        pleaseEnterHouse: "Please Enter House",
        pleaseEnterSocial: "Please Enter Social Status",
        pleaseEnterStatusReport: "Please Enter Status Reports",
        pleaseEnterJoinYear: "Please Enter Join Year",
        PleaseEnterAdminstartionName: "Please Enter Adminstration Name",
        PleaseEnterAdminsDepartment: "Please Enter Adminstrative Department",
        PleaseEnterTheCommitteeName: " Please Enter The Committee Name",

        EmpDuplicate: "Employee Name And Number Exist Before ",
        confirmActive: "Are You Sure You Want To Change Employee Activation",
        wrongExtension: "Wrong Extension File ",
        download: "Download",
        active: "Active",
        disactive: "Disactive",
        PleaseEnterEmployee: "Please Select Employee",
        PleaseEnterAssignment: "Please Select Assignment",
        PleaseEnterEmployeeAssignments: "Please Choose At least One Assignment for Committee Members",
        DepetsAlreadyExist: "A debt has been added to the student for the selected stage, grade, or department. This data cannot be enhanced before the debt is deleted",
        PleasDeleteSubjectDegree: "Pleas Delete Subject Degree",
        copy: "Copy",
        exportExcel: "Export Excel ",
        exportExcelToBank: "export Excel To Bank",
        exportPdf: "export to Pdf",

        markCoulms: "Mark Coulms",
        print: "Print",
        theirIsMovmentsAddedOnThiesItems: "their Is Movments Added On These Items",
        loginDataRep: "Login Data Report For Study Year",
        stuArchieve: "Student Archieve Report For Study Year",
        parentArchieve: "Parent Archieve Report For Study Year",
        classReport: "Class Data Report For Study Year",
        phaseReport: "Phase Report For Study Year",
        QualityReport: "Quality Report For Study Year",
        stu41Report: "Student Form 41 Report For Study Year",
        stuAgeOCtRep: "Student Age To Next October Report For Study Year",
        stu12Rep: "Student Data 12 Report For Study Year",
        parentReport: "Parents Data Report For Study Year",
        sameNameRep: "Student Same Name Report For Study Year",
        unCompleteData: "UnCompleted Data Report For Study Year",
        studentAffairs: "Students Affairs",
        schoolManger: "School Manager",
        pleaseEnterChipName: "Please Enter Chip Name",
        PleaseAddAttendenceSetting: "Please Add Attendence Setting",
        pleaseAddMainSetting: "Please Add Main Setting",
        pleaseAddVacationSetting: "Please Add Vacation Setting",
        pleaseAddDelaySetting: "Please Add Delay Setting",
        pleaseAddLeaveSetting: "Please Add Leave Early Setting",
        pleaseAddBenfitSetting: "Please Add Benfits Setting",
        pleaseAddAbsencSetting: "Please Add Absence Setting",
        saturday: "Saturday",
        notSavedBeczuseOfWrongInpute: "Not Saved Beczuse Of Wrong Inpute",
        sunday: "Sunday",
        monday: "Monday",
        tuesDay: "Tuesday",
        wednesday: "Wednesday",
        thursDay: "Thursday",
        friday: "Friday",
        noBusSubscription: "There is No Bus Subscription For Student,,Add New One",
        PleaseEnterReportTitle: "Please Enter Report Title",
        chkStuName: "Student Name",
        chkEngName: "Student Name English",
        chkStuStatus: "Student State",


        EmployeePenaltyReport: "Employee Penalty Report",
        FinalAccountsReport:"FinalAccounts Report",
        chkGender: "Gender",
        chkBirthDate: "BirthDate",
        chkBirthPlace: "BirthPlace",
        chkBirthNum: "Birth Certification Number",
        chkReligion: "Religion",
        chkNationality: "Nationality",
        chkJoinDate: "Join Date",
        chkStuAddress: "Address",
        chkStuPhone: "Student Phone",
        chkHomePhone: "Home Phone",
        pleaseDeleteProparitsBeforeDeActivate: "please Delete Proparits Before De Activate",
        chkFatherName: "Father Name",
        chkFathPhone: "Father Phone",
        chkFathJob: "Father Job",
        SorryButTheFileYouLookingForIsNotFound: "Sorry But The File You Are Looking For Is NotFound",
        chkMothName: "Mother Name",
        chkMothJob: "Mother Job",
        chkDept: "Department",
        chkAbBranch: "Branch",
        chkAbPhase: "Stage",
        chkAbSubPh: "Grade",
        chkAbClass: "Class",
        chkAgeOct: "Age To Next October",
        hasBrothers: "hasBrothers",
        BusParticpition: "BusParticpition",
        chkFathStatus: "Father Status",
        chkMotherStatus: "Mother Status",
        chkMedical: "Health State",
        chkUser: "Student User Name",
        chkParentUser: "Parent User Name",
        chkKeedNum: "Keed Number",
        chkPassport: "Passport",
        chkLastSch: "Last School",
        chkNatioExpire: "National Id Expire",
        chkNationalId: "National Id",
        SendToGuardian: "Send a message to the guardian/parent",
        SendWhatsAppAPI: "Send via WhatsApp API",
        SendWhatsApp: "Send via WhatsApp",
        SendSMS: "Send via SMS",
        SendEmail: " Send via Email",

        NotYetApproved: "Not yet approved",



        chkJoinYear: "Join Year",
        chkFathNati: "Father National Id",
        chkMothNatio: "Mother National Id",
        pleaseSelectDataTable: "Please Check Student Data To Show Table",
        noStudentForThisFilter: "No Student Data For This Entered Information",
        wrongDate: "Join Year is Wrong Syntax Must Be Like 2002/2003",
        PleaseEnterRelation: "Please Enter Relation",
        PleaseEnterReciverName: "Please Enter Reciver Name",
        changeDepartmentForYear: "Change Department For Year",
        ErrorFromPayment: "Error From Payment",
        theStage: "stage",
        theGrade: "grade",
        theClass: "class",
        theDepartment: "department",
        totalFees: "total Fees",
        theDiscount: "the Discount",
        DiscountNotAllowed: "Discount not allowed",
        DiscountAllowed: "Discount allowed",
        paied: "paied",
        remain: "remain",
        theirIsRelatedGradeFees: "their Is a Related Grade Fees",
        pleaseSetFisicalYearForThisBranch: "please Set Fisical Year For This Branch",
        StoreSuppliersReport: "Store Suppliers Report",
        StoreTransferReport: "Store Transfer Report",
        StoreTransferReport: "Store Transfer Report",
        pleaseChooseDegreeType: "please Choose Degree Type",

        SetdefaultChip: "Are You Want To Set Chip Default",
        defaultChipExistBefore: "There Was Another Default Chip , Select One Only",
        removeDefaultChip: "Are You Want To Make Chip Not Default",
        pleaseSetTheAttachedAccount: "please Set The Attached Account",
        dublicateOrder: "Order Is Exist Before ",
        AtypeMustContainOneMandatoryItemAtleast: "A type Must Contain One Mandatory Item Atleast",
        SetdefaultCurrency: "Do You Want To Make This Master Currency ??",
        noDataForBranch: "No Data For This Branch",
        June: "June",
        July: "July",
        August: "August",
        September: "September",
        October: "October",
        November: "November",
        December: "December",
        January: "January",
        February: "February",
        March: "March",
        April: "April",
        May: "May",
        OutPutsReport: "OutPutsReport",
        choose: "choose",
        ConfirmActive: "Confirm Active",
        NameAlreadyExisted: "this Name Already Existed",
        noSubjPropForSubject: "There Is No Properties For This Subject !",
        noSubjForGrade: "There Is No Subject For This Grade",
        dublicatNational: "Student Reciver NationalId Is Exist Before !",
        details: "Details",
        TheBroadcastIsBeingStartedPleaseWait: "The broadcast is being started. Please wait",
        errorInRemove: "Error In Delete",
        infrationExist: "Canot Delete ,There Was Classification In This Infration",
        TotalStudentCount: "Total Student Count",
        StopedStudentCount: "The number of students arrested",
        StudentsWitoutClasses: "Students Without Classes",
        ClassesCount: "Classes Count",
        MaleStudents: "Male Students",
        FemalStudents: "Female Students",
        LocationMustBeGoogle: "School Location Must Be Google Map Url",
        requiredData: " Required Data",
        comunicateData: " Comunicate Data",
        otherData: "Other Data",
        default: "Default",

        ItemNameCheck: "Item Name Invalid",
        extractRequestConfirmation: "Are you sure you agree to the extract request ?",
        Quality: "Quality Report",
        Phase: "Phase Report",
        Class: "Class Report",
        StudentAgeReport: "Student Age To October Report",
        StudentApp41: "Student Application 41 Report",
        Student12Report: "Student 12 Report",
        StudentArchive: "Student Archive Report",
        SameNameReport: "Student Same Name Report",
        LoginDataReport: "Login Data Report",
        ParentReport: "Parent Report",
        ParentArchiveRep: "Parent Archive Report",
        UnCompleteRep: "Un Complete Data Report",
        CaseStudiesReport: "Case Studies Report",
        StudentInfractionReport: "Student Infraction Report",
        TotalPhaseStatisticReport: "Total Stage Statistic Report",
        PhaseStatisticReport: "Stage Statistic Report",
        SubPhaseStatisticReport: "Grade Statistic Report",
        SubPhaseCountStatisticReport: "Grade Count Statistic Report",
        PhaseCountStatisticReport: "Stage Count Statistic Report",
        StudentCards: "Student Cards",
        StudentPermissionReport: "Student Permission Report",
        StuStatementReport: "Statement Report",
        Statement: "Statement",
        SimplifiedAccountsReport:"SimplifiedAccountsReport",
        cannotChangeBecauseItIsPaied: "Cannot Change Because It Is Paied",
        cannotDeleteBecauseItIsPaied: "Cannot Delete Because It Is Paied",
        Main: "Main",
        changeDefaultSuccessfully: "Change Default Successfully",
        stuAccountAggregateReport: "Student Accounts Clamis Report",
        PleaseEnterArea: "Please Enter Area",
        PleaseEnterController: "Please Enter Controller",
        PleaseEnterAction: "Please Enter Action",
        dublicateData: "Information Of Page Exist Before",
        PayerNameRequired: "Payer Name Required",
        InValidValue: "Invalid Value",
        cannotDeleteDepit: "cannot Delete Depit",
        theirIsOtherFeesOnTheStudent: "their Is Other Fees On The Student",
        threirIsApayedFees: "threir Is A payed Fees",
        theirIsAdepitAddedOnThisGradeInThisYear: "their Is Adepit Added On This Grade In This Year",
        ConfirmChangeState: "Are You Want To Change The State Of User ??",
        noDefaultChipAddFirst: "No Default Chip Please Add First In Employee Chip Page!",
        DataInTableWillBeDeleted: "All Data In Table Will Be Removed !!!!!!",
        CriditMustEqualDibit: "Credite  Must Be Equal Debit",
        EnterSomeDataInTable: "Please Enter Some Data in Table",
        endMustBeLastOrder: "Last Grade In Stage Must Take Last Order ",
        AccountIncomePermissionReport: "Income Permission Report",
        AccountExchangePermissionReport: "Exchange Permission Report",
        remove: "Delete",
        AccountStatementWithTaxReport: "Account Statement With Tax Report",
        PleaseEnterSubtopic: "Please Enter Sub-Topic !!",
        PleaseEnterThetargetgroup: "Please Enter The Target Group",
        PleaseChooseOneRequest: "Please Select Request",
        mainAccountRequired: "Please Select Main Account !!",
        FeesPaymentRecipsReport: "Fees Payment Recips",
        selectteacherorsubject: "Select One Teacher And One Subject For One Session",
        thisteacherdonotmatchsubject: "The Teacher And Subject Didn't Match ",
        wrongplaceforteacherandsubject: "Put Subject And Teacher In Correct Place",
        selectOneDocument: "Please Select One Document At Least !!",

        numberStudentOutOfClassRange: "Number Of Students Is Out Of Class Quantity !",
        enterAllFields: "You Should enter All Students Fields ",
        session: "Session",
        day: "Day",
        from: "From",
        to: "To",
        noScheduleAvalilable: "No Schedule Avalilable",
        subject: "Subject",
        deleteScheduleFirst: "Delete Session For Teacher First!",

        ReasonfiledIsRequired: "Reason filed Is Required",
        Approved: "Approved",
        Refused: "Refused",
        recovered: "recovered",
        pending: "pending",
        DateCanNotLessthanToday: "Chose recent Date",
        StudentAlreadyInVactionInSomeOfThisDays: "Student Already In Vaction In Some Of This Days",
        OnlyUpdatePendingRequestes: "OnlyUpdatePendingRequestes",
        Debit: "Debit",

        Credit: "Credit",
        Diffrence: "Diffrence",

        DateToMustBeGreater: "DateToMustBeGreater",
        ConstraintStatementReport: "Constraint Statement Report",
        CertificateOfGoodConductReport: "Certificate Of Good Conduct Report",
        ClearancesOfStudentExpenses: "Clearances Of Student Expenses",
        ApprovalOfLevelUpgradeReport: "Approval Of Level Upgrade Report",
        APledgeToLeave: "APledge To Leave",
        StudentVacationReport: "StudentVacationReport",
        Reason: "Reason",
        selectBlock: "Select SMS Block",
        selectAtLeastOneUser: "Select One User At Least",
        sentSuccessfuly: "Sent Successfuly",
        sendby: "Send By",

        Confirmation: "Confirmation",
        CodeIdentifierIsRepeated: "Code Identifier Is Repeated",
        EmailAddressNotValid: "EmailAddressNotValid",
        Nodataavailableintable: "No data available in table",
        InvalidSalary: "InvalidSalary",
        ReviewsFromLessThanTo: "ReviewsFromLessThanTo",
        FromValueLessThanToValue: "FromValueLessThanToValue",
        dayToDay: "Day",
        dayToValue: "Day",
        minuteToMinute: "Minute",
        minuteToValue: "Minute",
        AddNewFeesSettings: "AddNewFeesSettings",
        EditFeesSettingsTitle: "EditFeesSettingsTitle",
        Filed: "Filed",
        Record: "Record",
        AreYouShureFromCancelDeportation: "AreYouShureFromCancelDeportation",
        AreYouShureToContinueWitoutSelectedStudent: "No Chosen Students ..! For Cointiue Witout Chose Any Student Click OK ",
        yes: "Yes",
        state: "State",
        Next: "Next",
        Previous: "Previous",
        Finish: "Finish",
        SkillAddedSuccfuly: "SkillAddedSuccfuly",
        CourseAddedSuccfuly: "CourseAddedSuccfuly",
        AllowanceAddedSuccfuly: "AllowanceAddedSuccfuly",
        DeductAddedSuccfuly: "DeductAddedSuccfuly",
        ModelNotValid: "Some |In Valid Data",
        SalaryVocabularyIsExist: "Salary Vocabulary Is Exist Please Delete",
        CantEditSalaryVocabularyIsExist: "Cant Edit Salary Vocabulary Is Exist Please Delete",

        enterDuration: "Please Enter One Duartion At Least",
        enterAnnualHoliday: "Please Enter Annual Holiday Details",
        enterAnnualAgeHoliday: "Please Enter Annual Holiday By Age Details",
        errorHoliday: "Total Count Of Holiday Doesnot Equal Total Number Of Holiday",
        errorpermission: "Total Count Of Permission Doesnot Equal Total Number Of Permission",
        errorHolidayAnnual: "Total Count Of Annual Holiday Doesnot Equal Total Number Of Annual Holiday",
        errorHolidayAnnualAge: "Total Count Of Annual Holiday By Age Doesnot Equal Total Number Of  Annual Holiday By Age",
        discountGreaterThanRemain: "Discount Value Is Greater Than Remain",
        remainIsZero: " No Remain Student Pay The Value",
        father: "Father",
        mother: "Mother",
        chkParentKindship: "Parent Kindship",
        other: "Other",
        studentsCertificateReport: "Students Degrees Certificate",
        studentsCertificateFirstReport: "Students First Term Degrees Certificate ",
        newAdvance: "New",
        certified: "Certified",
        CertificateFinalForStudentsMasterCrossColor: "Certificate Final For Students Master Cross Color",
        rejected: "Rejected",
        jobTitle: "Job Title",
        adminstration: "Adminstration",
        chkadminsName: "Adminstration",
        chksalary: "Salary",
        chkUniversities: "University",
        chkisInsurance: "IsInsurance",
        Transactions: "Transactions",
        chkgender: "Gender",
        chkqualifName: "Qualification Name",
        chkAddress: "Address",
        chkcity: "City",
        chkGovernorate: "Governaorate",
        chkCountry: "Country",
        chkNationalStayingId: "NationalID",
        chkMobile: "Mobile",
        chkbirthData: "BirthDate",
        chksocialStatus: "Social Status",
        chkDateOfHiring: "Date Of Hiring",
        chkempName: "Employee Name",
        chkDepartmnetName: "Departmnet Name",
        chkworkSystem: "Work System",
        chkactive: "IsActive",
        chknotactive: "Not Active",
        employee: "Employee",
        chknameEN: "Name In English",
        chkRecievingJobDate: "Recieving Job Date",
        chkEndingContractDate: "Ending Contract Date",
        chkFingerprintNumber: "Fingerprint Number",
        chkBasicSalary: "Basic Salary",
        chkInsuranceSubscriptionFee: "Insurance Subscription Fee",
        chkInsuranceNumber: "Insurance Number",
        chkjobTitle: "Job Title",
        chkemail: "Email",
        chkHighStudies: "High Studies",
        chkReligion: "Religion",
        chkSkills: "Skills",
        chkCourses: "Courses",
        TheStudent: "Student",
        SeatNumber: "SeatNumber",
        Repeted: "Repeted",
        Empty: "Empty",
        CanNotSave: "CanNotSave",
        SubjectNotHaveProperties: "SubjectNotHaveProperties",
        YouCanNotEditCertifiedReward: "YouCanNotEditCertifiedReward",
        YouCanNotEditCertifiedDeduction: "YouCanNotEditCertifiedDeduction",
        PaymentstatementReport: "Student Paymentstatement Report",
        StudentRecoveryReport: "Student Recovery Report",
        AccountStatementReport: "Student Account Statmentreport Report",
        FeesNotPaiedForsonsReport: "Fees Not Paied For sons Report",
        studentsItemsAndSavesReport: "studentsItemsAndSavesReport",
        studentsAcountsAggregateReport: "studentsAcountsAggregateReport",
        StudentStatus_new: "new",
        StudentStatus_failer: "failer",
        StudentStatus_successfulandtransferable: "successfulandtransferable",
        StudentStatus_accepted: "accepted",
        NoMovementsHaveBeenAddedToTheSupplyOrder: "No Movements Have Been Added To The Supply Order",
        StudentStatus_check: "check",
        YouCanNotEditCertifiedPenalty: "YouCanNotEditCertifiedPenalty",
        AtLeastChoseOneEmployee: "AtLeastChoseOneEmployee",
        HolidayBalanceAddSuccessfully: "HolidayBalanceAddSuccessfully",
        HolidayBalanceCertficationSuccesfully: "HolidayBalanceCertficationSuccesfully",
        TherIsNoStatusNewInSelectedEmployeeholidayBalance: "TherIsNoStatusNewInSelectedEmployeeholidayBalance",
        addDepitFirst: "add Depit First",
        pleaseSelectEmpDataTable: "Please Select Employee Data",
        chkReturendFrom: "Returned From Country",
        NoDepitForStudent: "No Depit For This Student",
        StudentIsDeported: "Student Is Deported out of School",
        NoMemberAdded: "No Members Added in this Control",
        ForDateFromHolidayBalanceNotBeAvilable: "ForDateFromHolidayBalanceNotBeAvilable",
        HolidayBalanceCanNotAllowThisDaysCount: "HolidayTypeBalanceCanNotAllowThisDaysCount",
        CanNotSkipAllowedDaysInMonth: "CanNotSkipAllowedDaysInMonth",
        CanNotProvideHolidaytodynamicDurationEmployee: "CanNotProvideHolidaytodynamicDurationEmployee",
        ChosenDurationAlreadyHolidayForEmployee: "ChosenDurationAlreadyHolidayForEmployee",
        AskedHolidaydaysContainsSomeAlreadyHolidaysForEmployee: "AskedHolidaydaysContainsSomeAlreadyHolidaysForEmployee",

        collection: "Receipt",
        Disbursement: "Disbursement",

        AttrIsAlereadyExcisit: "this properity is already exists",
        printStudentData: "printStudentData",
        studentOnlineregistrationformReport: "studentOnlineregistrationformReport",
        PrintRegistrationOrder: "PrintRegistrationOrder",
        printStudentPledgeReport: "printStudentPledgeReport",
        ConfirmCertificate: "Do You Want To Certificate ??",
        ConfirmReject: "Do You Want To Reject ??",
        CanNotDeleteSubMainLink: "Cannot Remove Account Linking",
        feesPaymentItemsReport: "Student Fess Payment Report",
        remainPermissionLessThanPermission: "Remain Permission Duration Is Less Than Permission",
        dublicateEmpPermission: "Repeated Permisssion Date For Employee!",
        InEditYouCanNotEditAttendingLeavingType: "InEditYouCanNotEditAttendingLeavingType",
        LeavingTimeMustBeGraterThanAttending: "LeavingTimeMustBeGraterThanAttending",
        AttendingTimeMustBeSmallerThanAttending: "AttendingTimeMustBeSmallerThanAttending",
        AttendingForEmployeeInThisDayAddedbefor: "AttendingForEmployeeInThisDayAddedbefor",
        StudentBusReport: "Bus Report",
        ErrorSendToEta: "Error Send To Eta",
        SendToEta: "successfuly Send To Eta",
        thereIsEmployeesWithDelayTimeNotHandeledInDelayTimeRulesPleaseCheckDelayTimeRules: "thereIsEmployeesWithDelayTimeNotHandeledInDelayTimeRulesPleaseCheckDelayTimeRules",
        thereIsEmployeesWithExtraTimeNotHandeledInExtraTimeRulesPleaseCheckExtraTimeRules: "thereIsEmployeesWithExtraTimeNotHandeledInExtraTimeRulesPleaseCheckExtraTimeRules",
        NumberAlreadySaved: "NumberAlreadySaved",
        TaxesUniqNumbersDublecated: "TaxesUniqNumbersDublecated",
        TaxesRangesOverlapping: "TaxesRangesOverlapping",
        MoreThaneToValue: "MoreThaneToValue",
        chkAssessment: "Assessment",
        MoreThaneMaxValue: "MoreThaneMaxValue",
        CanNotCalculateInsurancePleaseFillInshuranceSettingsBefor: "CanNotCalculateInsurancePleaseFillInshuranceSettingsBefor",
        ActionHasBeenTaken: "ActionHasBeenTaken",
        thereisPunshedEmployeeIgnored: "thereisPunshedEmployeeIgnored",
        FigerPrintAddedToEmployeeAutomaticlyFromForgetingfingerprintpage: "FigerPrintAddedToEmployeeAutomaticlyFromForgetingfingerprintpage",
        CanNotCalculateTaxesPleaseFillTaxesSettingsBefor: "CanNotCalculateTaxesPleaseFillTaxesSettingsBefor",
        ConfirmAddAttendingForEmployeesSelected: "ConfirmAddAttendingForEmployeesSelected",
        PenalityAddedBecouseofForgetFingerPrint: "PenalityAddedBecouseofForgetFingerPrint",
        PleaseEnterRuleType: "Please Enter Rule Type",
        AllowanceDateLittleThanHiringDate: "Allowance Date Is Younger Than Employee Hiring Date",
        PasswordWeakPleaseEnterAtLeast8LettersWithNumbers: "Password Weak Please Enter At Least 8 Letters With Numbers",
        CertificatedIgnpredFromSelecting: "CertificatedIgnpredFromSelecting",
        required: "Required",
        YouMustremoveAllEmployeeRelatedData: "You Must remove All Employee Related Data And Try Again",
        requiredDataNotEntered: "required Data Not Entered",
        NoDiscountSettings: "Add Discount Settings First !!",
        DiscountAddedBeforeThisYear: "Discount Added Before For This Year",
        attendanceRepeat: "Data Of Device Is Repeated",
        cannotRemoveEmpDuration: "Can Not Delete Duration Associated With Employee",
        notDeleted: "not Deleted",
        IsEvaluation: "(Evaluation)",

        deleteChildsFirst: "please delete dependent categories",
        cannotEditTypethisItemHasChilds: "cannnot Edit this category type because it has childs",
        PleaseAddYourAccountNameInEnglish: "Please Add Your AccountName In English",
        theirIsItemsUnderThisCategory: "their Is Items Under This Category",
        invalidData: "invalid Data",
        errorInAddingOperations: "error In Adding Operations",
        PleaseAddTransactions: "Please Add Transactions",
        selectOneAtLeast: "Select One At Least",
        RangesOverlaps: "RangesOverlaps",
        NoFeesForThisItemForThisStudents: "No Fees For This Item For This Student",
        StudentNationalIdExpireDate: "NationalId ExpireDate Report",
        DisconnectedStudents: "Disconnected Students Report",
        ReceivingTablet: "Receiving Tablet Report",
        RemainCannotBeLessThanZero: "Remain Cannot Be Less Than Zero",
        PleaseEnterValue: "PleaseEnterValue",
        PleaseEnterattribute: "PleaseEnterattribute",

        invoiceNumberRepeated: "invoice Number Repeated",
        cannotEditThisInvoiceBecauseItIsTouched: "cannot Edit This Invoice Because It Is Touched",
        DoesNotHavepermissionForThis: "DoesNotHavepermissionForThis",
        ConfirmDeleteCertficated: "You Will Delete Acertificated Data And Some Other Data ? You Can Not Rollback ? For Confirm Press Okay",
        DeleteCertificatedRows: "DeleteCertificatedRows",
        errorSaving: "error saving",
        pleaseSetTheconnectionsForGoodsStock: "please Set The connection For GoodsStock",
        pleaseSetTheAccountIdForTheSupplier: "please Set The AccountId For The Supplier",
        pleaseSetTheTransferFeesConnectionAccount: "please Set The Transfer Fees Connection Account",
        pleaseSetTheEarnedDiscountAccountConnection: "please Set The Earned Discount Account Connection",
        pleaseSetTheAddtionalValueAccountConnection: "please Set The Addtional Value Account Connection",
        pleaseSetThecomplexTaxAccountConnection: "please Set The complexTax Account Connection",
        ageInOctober: "Age In October",
        ageInFirstOctober: "Age In October",
        month: "Month",
        year: "Year",
        day2: "Day",
        pleaseSetTheAccountIdForTheClient: "please Set TheAccountId For The Client",
        pleaseSetTheItemsSoldCostConnectionAccount: "please Set The Items Sold Cost Connection Account",
        pleaseSetTheallowedDiscountAccountConnection: "please Set The allowed Discount Account Connection",
        QuantityGreaterThanAvalialbleCountInStore: "Quantity Is Greater Than Avalible Count Of Item In Store !!",
        UsernameIsAlreadyExisting: "UserName Is Already Existing",
        checkFinancialYearWithBranch: "Make Settings Of Financial Year With Branch !!",
        returnGreaterThanStart: "Returned Date Must Be Greater Than Borrow Date",
        PleaseEnterURL: "Please Enter URL",
        StudentAbsences_Report: "Students Absences Report",
        PhaseAbsenceStatisticsReport: "Students Stages Absence Statistics Report",
        youExceededTheNumberOfBranchesTheCompanySetForYouPleaseContactUsToIncreeseTheIt: "please call the Technical support to increese the number of branches you can add",
        schoolDataNotSetCorrectlyPleaseCheckIt: "please call the Technical support to set the system settings tables",
        MedicalTicketReport: "Medical Ticket Report",
        StudentAbsence_Report: "Student Absence Report",
        EmailAddressIsAlreadyExisting: "Email Address Is Already Existing ",
        AllStudentFee: "All Student Fee Report",

        delivered: "delivered",
        notDelivered: "not delivered",
        deliver: "deliver",
        unDeliver: "un deliver",
        notSaved: "notSaved",
        itemNotFound: "Item not found",
        PleaseEnterTheLibaray: "PleaseEnterTheLibaray",
        dublicateNationalId: "Dublicated NationalId",
        AddAProcessToTheSupplyOrder: "Add a Process To The Supply Order",

        studentHasDepits: "student Has Depits",
        theirIsNoJoinYearsAdded: "their is no join years added please add join years",
        theirisNoStudentReportStatusAdded: "their is no student status added please add a student status",
        pleaseCheckNationalityOrReligionOrBranches: "please Check Nationality Or Religion Or Branches",
        pleaseCheckStagesAndGradesAndDepartmentsTheyMustHaveData: "please Check Stages And Grades And Departments They Must Have Data",
        pleaseSelectTheCurrency: "please Select The Currency",
        pleaceCheckFeesSettingsOnThisStudent: "please Check Fees Settings On This Student",
        pleaseSelectOneFileAtleast: "please Select One File At least",
        pleaseSelectAFile: "please Select A File",
        extentionNotAllowed: "extention Not Allowed",
        studentsCountInEveryStage: "students Count In Every Stage",
        studentsAbsanceReportsInTheCurrentStudyYear: "students Absance Reports In The Current Study Year",
        student: "student",
        PleaseEnterEmployeeName: "Please Enter Employee Name",
        studentsTypesInEveryStage: "students Types In Every Stage",
        suspendedStudents: "suspended Students",
        acceptedStudents: "accepted Students",
        newStudents: "new Students",
        underExamination: "under Examination",
        studentsWithOutClasses: "students With Out Classes",
        parent: "parent",
        total: "total",
        CheckEmailSentData: "Check Email Settings !!",
        errorInSendSms: "Error In Send Sms",
        CheckServiceProviderData: "Check SMS Settings ",
        CheckServiceProviderDataWhatsapp: "Check Service Provider Data Whatsapp",
        errorInMobileNumber: "Wrong Mobile Number !!",
        errorInSender: "Wrong Sender Name ",
        wrongEmail: "Wrong Email Or Empty !!",
        StudentCheckOutFileReport: "Student CheckOut File",
        StudentNominationLetterReport: "Student Nomination Letter",
        StudentStatementArrivalReport: "Student Statement Arrival ",
        BusPathPermissionReport: "Bus Path Permission Report",
        DesignStudentsReport: "Students Data Report",
        itemName: "item name",
        recovered: "recovered",
        noDepitAddedOnThisStudent: "no Depit Added On This Student",
        totalCreditForCurrentYear: "total Credit For the Current Year",
        totalStudentCredit: "total Student Credit",
        BriefRestrictions: "Brief Restrictions Report",
        DetailedRestrictions: "Detailed Restrictions Report",
        editEmail: "Edit Email",
        emailsNotSent: "Some Emails Not Sent",
        addBranch: "Add Branch",
        addClass: "Add Class",
        addStage: "Add Stage",
        addGrade: "Add Grade",
        senderName: "Sender Name",
        editNumber: "Edit Number",
        RecevingReport: "Receving Report",
        requiredProofId: "Proof Id Is Required",
        txt_medDose: "Please Enter Medcine Dose",
        EnterMedDesc: "Please Enter Medcine Reason",
        EnterMedDate: "Please Enter Medcine Date",
        StudentNotes: "Notes ",
        StudentsDailyPaymentsReport: "Students Daily Payments Report",
        EnterprodeuctionrUsername: "Enter prodeuctionr Username",
        EnterPreprodeuctionrUsername: " Enter Preprodeuctionr Username",
        EnterPreprodeuctionrPassword: "Enter Preprodeuctionr Password",
        EnterprodeuctionrPassword: "Enter prodeuctionr Password",
        AddingSuggetionReport: "Adding Suggetion Report",
        PendingSuggestionReport: "Pending Suggestion Report",
        CannotdeleteDueToCost: "The specified constraint is allocated to a cost center.Please cancel the allocation first.",




        DetailedRestrictions: "Detailed Restrictions Report",
        NotValidLink: "Not Valid Link",
        AlreadyExist: "Already Exist",
        hyperLinkTextIsRequired: "hyper Link Text Is Required",
        syncDone: "sync Done",
        provisionDone: "provision Done",
        attatchmentsSyncDone: "attatchments Sync Done",
        quantityIsGreaterThanAvalibleCredit: "Quantity Is Greater Than Avalible Credit",
        transferToDifferentBranch: "Transfer To Different Branch",
        transferToDifferentStore: "Transfer To Different Store",
        first: "first",
        second: "second",
        firstSecond: "first/Second",
        properityName: "properity Name",
        maxdegree: "max Degree",
        mindegree: "min Degree",
        deactivate: "Deactivate",
        activate: "Activate",
        Conduct: "Certificate of Good Conduct",
        addProperity: "Add Properity",
        Termination: "Termination",
        PleaseEnterAnyValue: "Please Enter Any Value",

        chosseStudyYearToAddNew: "chosse Current StudyYear To Add New",
        dublicatePublicPrivateNum: "Dublicate Public And Private Number",
        dublicateParcode: "Dublicate Parcode",
        dublicateParcodeAndPublicPrivateNum: "Dublicate Public And Private Number And Parcode  ",
        percent: "Percent",
        chooseEmployee: "choose Employee",
        error: "error",
        noStudentDataInClass: "No Student Data In Class",
        pleaseEnterDateTosearch: "Please Enter Date To search",
        showSons: "Show Sons",
        canNotChangeEndDay: "Cannot Change Enf Of Month There Was Certified ExtraTime Or Deduction To Employess",
        studentsCertificateTermReport: "Students Certificate Terms Report",
        dublicateTerm: "There Was Degrees For This Term ",
        AccountsQuedesDeletedReport: "Deleted Quedes Report",
        StudentsAccountDiscountReport: "Students Accounts Discount Report",
        dublicateDate: "Dublicate Date",
        pleaseSelectStore: "Please Select Store",
        noQuedes: "No Quedes Exist",
        item2: "item",
        Acknowledgment: "Acknowledgment",
        StudentsWithoutClassesReport: "Students Without Classes Report",
        drugAddedBefore: "Drug Added Before !!",
        NoSetttingsAddedInEmployeeAffaresSettings: "Employees Affairs Data Not Added !!",
        studentsCertificateForthDurationTermReport: "Students Certificate Final Term Report",
        dublicateEducator: "This Educator Added To Another Class ",
        canNotChangeSystemState: "Can Not Change Work-System Status",
        PleaseEnterParent: "Please Enter Parent",
        NumberIsOutOfRange: "Number Is Out Of Note Range ",
        writeStudentName: "Write Student Name",
        writeParentName: "Write Parent Name",
        addAnotherFees: " Add Another Fees",
        deleteMandatoryFees: "Delete Mandatory Fees",
        editStoreCenter: "Edit Store Center",
        StoreCenter: "Store Accounts Center",
        teacher: "Teacher",
        StudentBusPaymentsReport: "Student Bus Payments Report",
        StoreAccountsCenterPermisionsReport: "Store Accounts Center With Items Permisions Report",
        notFoundOrderedSupply: "This Ordered Supply Number Not Found !!!",
        Thereisanincompletetransferprocess: "There is an incompleted transfer process",
        pleaseEnterThevalue: "Please Select One Value For Request At Least !",
        selectOneTypeAtLeast: "Please Select One Type At Least",
        invalidDomain: "InValid Domain Name !!",
        noEmailSettings: "Please Set Email Format Settings",
        CanNotDeleteDivisionFromClass: "There Was Students In Division Can Not Delete Division From Class",
        PleaseEnterCurrency: "Please Enter Currency",
        PleaseEnterCurrencyEn: "Please Enter Currency English",
        BookImages: "Book Images",
        ClassesCapacityReport: "Classes Capacity Report",
        BriefReciepts: " Brief Reciepts Report",
        DetailsReciepts: "Details Reciepts Report",
        chkFathMobile: "Father Mobile ",
        PleaseAddYourAccountName: "Please Add Account Name",
        chkMotherMobile: "Mother Mobile",
        chkEmergencyPhone: "Emergency Phone",
        EmployeeSubjectsReport: "Teachers And Classes Report",
        EmployeeDegreeSuccess: "Add degree to employee done successfully",
        EmployeeDegreeDanger: "Add degree to employee not completed",
        EmployeeDegreeDeleted: "Delete Successfully",
        EmployeeDegreeDeletedFailed: "Delete not complete please try again",
        AddDegreeSuccess: "Degree Added Successfully",
        DegreeFailed: "Failed to add please try again and change value",
        DeleteDegree: "Degree Deleted Succcessfully",
        FailedDeleteDegree: "Failed to delete degree try again later",
        DeleteDegreeFailed: "There are some Employees have the same degree remove them from employees degree first",
        RelationsInDelete: "Cannot be deleted because it will be related with other elements",
        DublicatedBus: "There is another bus have the same bus name please change the bus name and try again",
        DuplicatedPath: "There is another Path have the same Path name please change the Path name and try again",
        DuplicatedStation: "There is another Station have the same Station name please change the Station name and try again",
        FinancialYearsNeeded: "Add Financial Years , First",
        chkStuEmail: "Student Email",
        chkFatherEmail: "Father Email",
        chkNotes: "Notes",
        chkMotherEmail: "Mother Email",
        StudentBrotherReport: "StudentBrotherReport",
        StudentEmpSonsReport: "StudentEmpSonsReport",
        StudentEmpSonsReport: "StudentEmpSonsReport",
        GraduateYearOrBirthDateNotValid: "Graduate Year Or BirthDate Not Valid",
        ManageDebartmentIsRequired: "Management is required",
        DepartmentIsRequired: "Department is required",
        JobTitleIsRequired: "Job title is required",
        NameIsRequired: "Name is required",
        NationalIDIsRequired: "National ID is required",
        PhoneIsRequired: "Phone is required",
        EducationIsRequired: "Education degree is required",
        EmailIsRequired: "Email is required",
        BirthDateIsRequired: "Birthdate is required",
        GraduateYearIsRequired: "Graduate year is required",
        BranchIsRequired: "Branch is required",
        AddSubscription: "Add Subscription",
        add: "Add",
        Excellent: "Excellent",
        VeryGood: "Very Good",
        Good: "Good",
        Acceptable: "Acceptable",
        New: "New",
        Rejected: "Rejected",
        Certficate: "Certficate",
        Doctor: "Doctor",
        Master: "Master",
        Bachelor: "Bachelor",
        Islamic: "Islamic",
        Humans: "Humans",
        Academy: "Academy",
        Diploma: "Diploma",
        Secondary: "Secondary",
        preparatory: "Perparatory",
        Primary: "Primary",
        AzharSecondarySchool: "Azhar Secondary School",
        Technicalschool: "Technical School",
        None: "None",
        freeTemporarily: "Free Temporarily",
        FreePermanently: "Free Permanently",
        MilitaryDone: "Military Done",
        Free: "Free",
        StudentVacationReportDay: "Student Vacation Report",
        StudentVacationReportDayAll: "Student Vacation Report",
        PleaseEnterSubPhaseName: "Please Enter SubPhase Name",
        PleaseEnterClassName: "Please Enter Class Name",
        PleaseEnterDevisionName: "Please Enter Division Name",
        PleaseEnterPhaseName: "Please Enter Phase Name",
        BrothersCount: "Brothers Count",
        StudentAccountStatement: "Student Account Statement",
        AddDepit: "Add Indebtedness",
        ValueAdded: "Value Added",
        TeachersandSubjectsManagement: "Teachers and Subject sManagement",
        totalAdditionalValue: "total Additional Value",
        totalPayedTax: "total Tax Payed",
        Remainder: "Remainder",
        addDiscount: "Add Discount",
        DeleteItem: "Delete item",
        Addingoptionalfeestothecategory: "Adding optional fees to the Category",
        studentmovements: "Student Movements",
        TotalValue: "Total Value",
        TotalPaid: "Total Paid",
        Paid: "Paid",
        OfTheTotal: "of the total",
        totalDiscount: "Total Discount",
        Totalrecovered: "Total Recovered",
        totalresidualaddedvalue: "Total Residual added value",
        totalcurrentyearbalance: "Total current year Balance",
        Studenttotalaccount: "Student Total Account",
        AvailableValue: "Available value",
        WithTheTax: "With the tax",
        TaxAmount: "Tax Amount",
        PayedWithoutTax: "Payed Without Tax",
        TheAmountPaid: "The amount paid",
        banksAndSaves: "Banks and Saves",
        TypeTheAmount: "Type the amount",
        debtor: "debtor",
        creditor: "creditor",
        Date: "Date",
        QuedNumber: "Qued Number",
        Description: "Description",
        user: "User",
        printareceipt: "print areceipt",
        cancelTransactionDiscountPriv: "Cancel Transaction Discount",
        cancelTransactionPayPriv: "Cancel Transaction Pay",
        cancelTransactionPriv: "Cancel Transaction",
        recovery: "Recovery",
        no: "no",
        AddInternalLevel: "Add Internal Level",
        Areyousuretocancelthepayment: "Are you sure to cancel the payment?",
        Usethevoucheragain: "Use the voucher again",
        Areyousuretocanceltheprocess: "Are you sure to cancel the process?",
        AddCategories: "Add Categories",
        Store: "Store",
        ItemName1: "Item Name",
        Unit: "Unit",
        price: "price",
        Quantity: "Quantity",
        Itemalreadyadded: "Item already added",
        Someitemshavenotbeenselected: "Some items have not been selected",
        SubPhaseAbsenceStatisticsReport: "SubPhase Absence Statistics Report",
        ClassAbsenceStatisticsReport: "Class Absence Statistics Report",
        SrPhase: "Phase",
        SrSubPhase: "SubPhase",
        SrClass: "Class",
        Experincecetrificate: "Experince cetrificate",
        EmployeeMovementReport: "Employee Movement Report",
        Someitemshavenotbeenselected: "Some items have not been selected",
        StudentPermissionsReport: "Student Permissions Report",
        EmployeeAdvanceReport: "Employee advances Report",
        EmployeePermissionReport: "Employee Permission Report",
        totalGeneralbalance: "Total general balance",
        studentaccounttotal: "Student account total",
        EmployeeHolidayReport: "Employee Holiday Report",
        pleaseEnterLanguage: "Please Enter Language",
        ClassesReport: "Classes Report",
        HalekStoreReport: "Halek Store Report",
        itemsSettings: "Items Settings",
        pleaseSelectUnit: "please Select Unit",
        AddNewReport: "Add New Report",

        REPORTED: "REPORTED",
        NOT_REPORTED: "NOT_REPORTED",
        itemsExpireDateWarning: "items Expire Date Warning",
        orderedSupplyReport: "ordered Supply Report",
        StoreInvoiceReport: "Store Invoice Report",
        pleaseSetTheconnectionsForSale: "please Set Theconnections For Sale",
        Nosearchtypewasselected: "No search type was selected",
        All: "All",
        therewasnogradchoosen: "There Was No Grad Choosen",
        Noitemorsafehasbeenselected: "No item or safe has been selected",
        phone: "Phone",
        Brothers: "Brothers",
        DiscountValue: "Discount Value",
        Thisisthelastclassandwhenthetransferiscompletedwilltheschoolyearbechangedtothesystem: "This is the last class, and when the transfer is completed, will the school year be changed to the system?",
        Somestudentsareinclasseswheretherearenoemptyspaces: "Some students are in classes where there are no empty spaces",
        Backupsaved: "Backup saved",
        ScheduleSessionsReport: "Schedule Sessions",
        ParentNotFound: "Parent not found",
        WorkSystemID: "Please enter work system",
        SpecificDaysValueRequired: "Please enter value",
        DayRequired: "Please enter day",
        ThisDayIsInWeeklyVacationOrTheWorkSystemAddedThisDayBefore: "This day is part of the official school holidays or the work system added this day before",
        AllEmployeesDegreeReport: "All Employees Degrees Report",
        EmployeeDegreesReport: "Employee Degrees Report",
        PleaseEnterStudentandType: "Please choose Student and reciever",
        PleaseChooseRecieverType: "Please choose reciever type",
        PleaseEnterStationName: "Please enter station name",
        StudentInNotSubscribeInBus: "The student is not subscribed to a bus",
        selectAteacher: "Please Select A Teacher !!",
        TeacherSessionsTableReport: "Teacher Sessions Table",
        ConfirmLogOut: "Changes Done Successfully System will Logout And You Must Login Again ",
        SubjectNotHavePropertiesSettingsInTerm: "No Subject Propirties Settings Degrees In This Term !!",
        totalDegreeNotEqualPropertyDegree: "total Degree Not Equal Property Degree ",
        totalPercentmustEqual: "total Percent must Equal 100%",
        totalPercentmustEqual: "total Percent must Equal 100%",
        notfound: "not found",
        SubjectNotHavePropertiesSettingsInTerm: "No Subject Propirties Settings Degrees In This Term !!",
        joined: "joined",
        UnJoined: "UnJoined",
        ConfirmLogOut: "Changes Done Successfully System will Logout And You Must Login Again ",
        ReferenceNumber: "Reference Number",
        PaymentMethod: "Payment Method",
        Form2Insurances: "Form 2 Insurances",
        PleaaseChoosePaymentMethod: "Pleaase Choose Payment Method",
        PaymentMethod: "Payment Method",
        printElectronicInvoice: "Electronic Invoice",
        printSettingNumbersByImageReport: "print Setting Numbers ByImage Report",
        printSettingNumbersReport: "print Setting Numbers Report",
        controlMembersReport: "Control Members Report",
        feesPaymentElectronicInvoice: "fees Payment Electronic Invoice",
        printSettingNumbersReport: "print Setting Numbers Report",
        IncomeStatementReport: "Income Statement Report",
        BalanceSheetReport: "Balance Sheet Report",
        StudentSecondLanguage: "Student Second Language",
        therewasnosecondlanguagechoosen: "there was no second language choosen",
        RetirementAge: "Retirement Age",
        RetirementAgeReport: "Retirement Age Report",
        pleaseEnterMonthCount: "please Enter Month Count",
        committeAbsenceReport: "Committe Absence Report",
        CommitteeMembersReport: "Committee Members Report",
        StudentsReceiversReport: "Students Receivers Report",
        StudentsReceiversByBusReport: "Students Receivers By Bus Report",
        TotalSecondLanguageStatisticReport: " Second Language Statistic Report",
        therewasnoFeesGroupschoosen: "there was no Fees Groups choosen",
        TotalSecondLanguageStatisticReport: " Second Language Statistic Report",
        AddExchangePermissionsReport: "Add And Exchange Items Report",
        SaleElectronicInvoiceReport: "Sale Electronic Invoice Report",
        payregisterFees: "Pay Register Fees",
        GateOutPuts: "Gate Out Puts",

        sendmessage: "send message",
        Monetary: "Monetary",
        CreditCard: "CreditCard",
        Deposit: "Deposit",
        Cheques: "Cheques",
        Pause: "Pause",
        purchaseInvoiceReport: "purchaseInvoiceReport",
        DoneButSomeStudents: "Done But Some Students",

        AlreadyActivated: "Already Activated",
        AlreadyStopped: "AlreadyStopped",
        StudentNotActive: "Student Not Active",
        AccountTreeReport: " Account Tree Report ",
        CostCenterReport: "Cost Center Report ",
        halekReport: "halek Report",
        halekAllReport: "All halek Report",
        AccountCashAndBankStatementReport: "Account Statement For Report Cash And Bank",
        EmployeeForgetPrintReport: "Employee Forget Print Report",
        StoreAddPermissionReprot: "Store Add Permission Reprot",
        StoreExchangePermissionReport: "Store Exchang ePermission Report",
        EmployeebusPayment: 'Employee bus Payment',
        sendTosignature: 'send To signature',
        return: 'return',
    }
};
function getCookieValue(a) {
    var b = document.cookie.match('(^|[^;]+)\\s*' + a + '\\s*=\\s*([^;]+)');
    return b ? b.pop() : '';
}

function getToken(key) {
    let lang = getCookieValue('Usre_Culture') === 'ar-EG' ? 'ar' : 'en';
    let Result = cluturedata[lang][key];
    return Result == undefined ? key : Result;
}


angular.module("SchoolApp_Services").factory("CulutrServices", () => {

    let getToken = (key) => {
        let lang = getCookieValue('Usre_Culture') === 'ar-EG' ? 'ar' : 'en';

        return cluturedata[lang][key];
    };
    return {
        GetToken: getToken
    };
});