# Generated by Django 5.2.4 on 2025-07-30 17:48

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0006_teacheravailability_timetabletemplate_and_more"),
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("students", "0008_grade_max_age_grade_max_capacity_grade_min_age"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="GradeReport",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("progress", "Progress Report"),
                            ("midterm", "Midterm Report"),
                            ("final", "Final Report"),
                            ("semester", "Semester Report"),
                            ("annual", "Annual Report"),
                        ],
                        max_length=15,
                        verbose_name="Report Type",
                    ),
                ),
                (
                    "report_period_start",
                    models.DateField(verbose_name="Report Period Start"),
                ),
                (
                    "report_period_end",
                    models.DateField(verbose_name="Report Period End"),
                ),
                (
                    "generated_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Generated At"
                    ),
                ),
                (
                    "report_data",
                    models.JSONField(
                        help_text="Complete report data in JSON format",
                        verbose_name="Report Data",
                    ),
                ),
                (
                    "overall_gpa",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=4,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(4.0),
                        ],
                        verbose_name="Overall GPA",
                    ),
                ),
                (
                    "total_subjects",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Total Subjects"
                    ),
                ),
                (
                    "subjects_passed",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Subjects Passed"
                    ),
                ),
                (
                    "attendance_percentage",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Attendance Percentage",
                    ),
                ),
                (
                    "teacher_comments",
                    models.TextField(
                        blank=True, null=True, verbose_name="Teacher Comments"
                    ),
                ),
                (
                    "parent_viewed",
                    models.BooleanField(default=False, verbose_name="Viewed by Parent"),
                ),
                (
                    "parent_viewed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Parent Viewed At"
                    ),
                ),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="grade_reports",
                        to="core.academicyear",
                        verbose_name="Academic Year",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="generated_reports",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Generated By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "semester",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="grade_reports",
                        to="core.semester",
                        verbose_name="Semester",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="grade_reports",
                        to="students.student",
                        verbose_name="Student",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Grade Report",
                "verbose_name_plural": "Grade Reports",
                "ordering": ["-generated_at"],
                "indexes": [
                    models.Index(
                        fields=["school", "academic_year", "report_type"],
                        name="academics_g_school__e2de5d_idx",
                    ),
                    models.Index(
                        fields=["student", "report_type"],
                        name="academics_g_student_627c3f_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="GradingScale",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Scale Name")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "scale_definition",
                    models.JSONField(
                        help_text="JSON definition of grading scale with grade letters, ranges, and points",
                        verbose_name="Scale Definition",
                    ),
                ),
                (
                    "is_default",
                    models.BooleanField(default=False, verbose_name="Is Default Scale"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Grading Scale",
                "verbose_name_plural": "Grading Scales",
                "ordering": ["name"],
                "indexes": [
                    models.Index(
                        fields=["school", "is_default"],
                        name="academics_g_school__2c5208_idx",
                    ),
                    models.Index(
                        fields=["school", "is_active"],
                        name="academics_g_school__11927b_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="StudentGPA",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "gpa_type",
                    models.CharField(
                        choices=[
                            ("semester", "Semester GPA"),
                            ("cumulative", "Cumulative GPA"),
                            ("yearly", "Yearly GPA"),
                        ],
                        max_length=15,
                        verbose_name="GPA Type",
                    ),
                ),
                (
                    "gpa_value",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=4,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(4.0),
                        ],
                        verbose_name="GPA Value",
                    ),
                ),
                (
                    "total_credit_hours",
                    models.PositiveIntegerField(verbose_name="Total Credit Hours"),
                ),
                (
                    "earned_credit_hours",
                    models.PositiveIntegerField(verbose_name="Earned Credit Hours"),
                ),
                (
                    "quality_points",
                    models.DecimalField(
                        decimal_places=2, max_digits=8, verbose_name="Quality Points"
                    ),
                ),
                (
                    "class_rank",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Class Rank"
                    ),
                ),
                (
                    "total_students",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Total Students in Class"
                    ),
                ),
                (
                    "calculation_date",
                    models.DateTimeField(
                        auto_now=True, verbose_name="Calculation Date"
                    ),
                ),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="student_gpas",
                        to="core.academicyear",
                        verbose_name="Academic Year",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "semester",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="student_gpas",
                        to="core.semester",
                        verbose_name="Semester",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="gpa_records",
                        to="students.student",
                        verbose_name="Student",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Student GPA",
                "verbose_name_plural": "Student GPAs",
                "ordering": ["-calculation_date"],
                "indexes": [
                    models.Index(
                        fields=["school", "academic_year", "gpa_type"],
                        name="academics_s_school__43838c_idx",
                    ),
                    models.Index(
                        fields=["student", "gpa_type"],
                        name="academics_s_student_c812a4_idx",
                    ),
                ],
                "unique_together": {
                    ("student", "academic_year", "semester", "gpa_type")
                },
            },
        ),
        migrations.CreateModel(
            name="Transcript",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "transcript_type",
                    models.CharField(
                        choices=[
                            ("official", "Official Transcript"),
                            ("unofficial", "Unofficial Transcript"),
                            ("partial", "Partial Transcript"),
                            ("transfer", "Transfer Transcript"),
                        ],
                        default="unofficial",
                        max_length=15,
                        verbose_name="Transcript Type",
                    ),
                ),
                (
                    "generated_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Generated At"
                    ),
                ),
                (
                    "is_sealed",
                    models.BooleanField(
                        default=False,
                        help_text="Sealed transcripts cannot be modified",
                        verbose_name="Is Sealed",
                    ),
                ),
                (
                    "seal_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Seal Date"
                    ),
                ),
                (
                    "transcript_data",
                    models.JSONField(
                        help_text="Complete transcript data in JSON format",
                        verbose_name="Transcript Data",
                    ),
                ),
                (
                    "cumulative_gpa",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=4,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(4.0),
                        ],
                        verbose_name="Cumulative GPA",
                    ),
                ),
                (
                    "total_credit_hours",
                    models.PositiveIntegerField(verbose_name="Total Credit Hours"),
                ),
                (
                    "class_rank",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Class Rank"
                    ),
                ),
                (
                    "graduation_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Graduation Date"
                    ),
                ),
                (
                    "academic_year_end",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transcript_ends",
                        to="core.academicyear",
                        verbose_name="End Academic Year",
                    ),
                ),
                (
                    "academic_year_start",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transcript_starts",
                        to="core.academicyear",
                        verbose_name="Start Academic Year",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="generated_transcripts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Generated By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transcripts",
                        to="students.student",
                        verbose_name="Student",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transcript",
                "verbose_name_plural": "Transcripts",
                "ordering": ["-generated_at"],
                "indexes": [
                    models.Index(
                        fields=["school", "student"],
                        name="academics_t_school__768a83_idx",
                    ),
                    models.Index(
                        fields=["transcript_type", "is_sealed"],
                        name="academics_t_transcr_5d438c_idx",
                    ),
                ],
            },
        ),
    ]
