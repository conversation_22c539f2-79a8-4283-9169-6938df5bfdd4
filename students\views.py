from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.db.models import Q, Count
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import timedelta, date, datetime
from .models import (
    Student, Parent, Class, Grade, StudentDocument, VacationRequest,
    StudentInfraction, StudentTransfer, StudentAttachment, ElectronicRegistration,
    AcademicHistory, TransferDocument, VacationCalendar, VacationReport,
    StudentSuspension, DisciplinaryAction, DisciplineReport
)
from .forms import (
    StudentForm, ParentForm, ClassForm, GradeForm, VacationRequestForm,
    StudentInfractionForm, StudentTransferForm, StudentDocumentForm,
    StudentAttachmentForm, ElectronicRegistrationForm, AdmissionDocumentForm,
    AdmissionReviewForm, StudentIDGenerationForm, DocumentVerificationForm,
    StudentSuspensionForm, DisciplinaryActionForm, DisciplineReportForm
)

# Student Management Views
class StudentDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'students/student_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current date for filtering
        today = timezone.now().date()
        thirty_days_ago = today - timedelta(days=30)
        current_year = today.year

        # Student statistics
        context['total_students'] = Student.objects.filter(is_active=True).count()
        context['new_admissions'] = Student.objects.filter(
            admission_date__gte=thirty_days_ago,
            is_active=True
        ).count()
        context['active_students'] = Student.objects.filter(
            is_active=True,
            is_graduated=False
        ).count()
        context['graduating_students'] = Student.objects.filter(
            is_active=True,
            current_class__grade__level__gte=12  # Assuming grade 12 is graduating
        ).count()

        # Recent students (last 10)
        context['recent_students'] = Student.objects.filter(
            is_active=True
        ).select_related('current_class', 'current_class__grade').order_by('-admission_date')[:10]

        # Pending tasks counts
        context['pending_admissions'] = ElectronicRegistration.objects.filter(
            status='submitted'
        ).count()
        context['pending_documents'] = StudentDocument.objects.filter(
            is_verified=False
        ).count()
        context['pending_fees'] = Student.objects.filter(
            is_active=True,
            # Add fee-related filtering when finance models are available
        ).count() if hasattr(Student, 'fees') else 0
        context['transfer_requests'] = StudentTransfer.objects.filter(
            status='pending'
        ).count()
        context['id_card_requests'] = Student.objects.filter(
            is_active=True,
            id_card_issued=False
        ).count() if hasattr(Student, 'id_card_issued') else 0

        return context

class StudentListView(LoginRequiredMixin, ListView):
    model = Student
    template_name = 'students/student_list.html'
    context_object_name = 'students'
    paginate_by = 20

    def get_queryset(self):
        queryset = Student.objects.select_related('user', 'parent', 'current_class').all()
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(student_id__icontains=search) |
                Q(admission_number__icontains=search)
            )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Breadcrumb navigation
        context['breadcrumb_items'] = [
            {'title': _('Home'), 'url': reverse('accounts:dashboard')},
            {'title': _('Students'), 'url': None},
        ]

        # Additional statistics
        context['active_students'] = Student.objects.filter(is_active=True).count()
        context['total_classes'] = Class.objects.filter(is_active=True).count()
        context['new_admissions'] = Student.objects.filter(
            admission_date__gte=timezone.now().date() - timedelta(days=30)
        ).count()

        return context

class StudentDetailView(LoginRequiredMixin, DetailView):
    model = Student
    template_name = 'students/student_detail.html'
    context_object_name = 'student'

class StudentCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    permission_required = 'students.add_student'
    success_url = reverse_lazy('students:list')

    def form_valid(self, form):
        messages.success(self.request, _('Student added successfully!'))
        return super().form_valid(form)

class StudentUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    permission_required = 'students.change_student'

    def get_success_url(self):
        return reverse_lazy('students:detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(self.request, _('Student updated successfully!'))
        return super().form_valid(form)

class StudentDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView):
    model = Student
    template_name = 'students/student_confirm_delete.html'
    permission_required = 'students.delete_student'
    success_url = reverse_lazy('students:list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Student deleted successfully!'))
        return super().delete(request, *args, **kwargs)

class StudentAdvancedSearchView(LoginRequiredMixin, TemplateView):
    template_name = 'students/advanced_search.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['grades'] = Grade.objects.all()
        context['classes'] = Class.objects.all()
        return context

class DistributeStudentsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """View for distributing students across classes."""
    template_name = 'students/distribute_students.html'
    permission_required = 'students.change_student'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['grades'] = Grade.objects.filter(is_active=True).order_by('level')
        context['classes'] = Class.objects.filter(is_active=True).select_related('grade').order_by('grade__level', 'name')
        context['unassigned_students'] = Student.objects.filter(
            is_active=True, 
            current_class__isnull=True
        ).order_by('first_name', 'last_name')
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle student distribution requests."""
        student_ids = request.POST.getlist('student_ids')
        target_class_id = request.POST.get('target_class')
        
        if not student_ids or not target_class_id:
            messages.error(request, _('Please select students and a target class.'))
            return redirect('students:distribute')
        
        try:
            target_class = Class.objects.get(id=target_class_id)
            students = Student.objects.filter(id__in=student_ids)
            
            for student in students:
                student.current_class = target_class
                student.save()
            
            messages.success(
                request, 
                _('Successfully distributed {count} students to {class_name}.').format(
                    count=students.count(),
                    class_name=target_class.name
                )
            )
        except Class.DoesNotExist:
            messages.error(request, _('Target class not found.'))
        
        return redirect('students:distribute')

class StudentTransferView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/student_transfer.html'
    permission_required = 'students.change_student'

class StudentCardsView(LoginRequiredMixin, TemplateView):
    template_name = 'students/student_cards.html'

# Parent Management Views
class ParentListView(LoginRequiredMixin, ListView):
    """View for listing parents with pagination and statistics."""
    model = Parent
    template_name = 'students/parent_list.html'
    context_object_name = 'parents'
    paginate_by = 20

    def get_queryset(self):
        """Return annotated queryset of parents with children count."""
        queryset = Parent.objects.annotate(
            children_count=Count('children', filter=Q(children__is_active=True))
        )
        
        # Apply search filter if provided
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(father_name__icontains=search) |
                Q(mother_name__icontains=search) |
                Q(father_phone__icontains=search) |
                Q(mother_phone__icontains=search)
            )
            
        # Apply children count filter if provided
        children_count = self.request.GET.get('children_count')
        if children_count:
            if children_count == '1':
                queryset = queryset.filter(children_count=1)
            elif children_count == '2':
                queryset = queryset.filter(children_count=2)
            elif children_count == '3+':
                queryset = queryset.filter(children_count__gte=3)
        
        return queryset.order_by('father_name')

    def get_context_data(self, **kwargs):
        """Add additional context data for the template."""
        context = super().get_context_data(**kwargs)

        # Statistics
        context['total_parents'] = Parent.objects.count()
        context['fathers_count'] = Parent.objects.filter(father_name__isnull=False).count()
        context['mothers_count'] = Parent.objects.filter(mother_name__isnull=False).count()
        context['guardians_count'] = 0  # No guardian field, so default to 0

        return context

class ParentDetailView(LoginRequiredMixin, DetailView):
    model = Parent
    template_name = 'students/parent_detail.html'
    context_object_name = 'parent'

class ParentCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Parent
    form_class = ParentForm
    template_name = 'students/parent_form.html'
    permission_required = 'students.add_parent'
    success_url = reverse_lazy('students:parent_list')

class ParentUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Parent
    form_class = ParentForm
    template_name = 'students/parent_form.html'
    permission_required = 'students.change_parent'

    def get_success_url(self):
        return reverse_lazy('students:parent_detail', kwargs={'pk': self.object.pk})

class AddParentManualView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/add_parent_manual.html'
    permission_required = 'students.add_parent'

class BulkImportView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/bulk_import.html'
    permission_required = 'students.add_student'

class StudentReportsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/reports.html'
    permission_required = 'students.view_student'

class StudentIDCardsView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = Student
    template_name = 'students/id_cards.html'
    permission_required = 'students.view_student'
    context_object_name = 'students'
    paginate_by = 12

    def get_queryset(self):
        queryset = Student.objects.select_related(
            'user', 'current_class', 'current_class__grade'
        ).filter(is_active=True)

        # Filter by search query
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(student_id__icontains=search)
            )

        # Filter by class
        class_id = self.request.GET.get('class')
        if class_id:
            queryset = queryset.filter(current_class_id=class_id)

        # Filter by grade
        grade_id = self.request.GET.get('grade')
        if grade_id:
            queryset = queryset.filter(current_class__grade_id=grade_id)

        return queryset.order_by('current_class__grade__level', 'current_class__name', 'first_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['grades'] = Grade.objects.filter(is_active=True).order_by('level')
        context['classes'] = Class.objects.filter(is_active=True).select_related('grade').order_by('grade__level', 'name')
        context['search_query'] = self.request.GET.get('search', '')
        context['selected_class'] = self.request.GET.get('class', '')
        context['selected_grade'] = self.request.GET.get('grade', '')
        return context

class StudentCertificatesView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """
    View for managing student certificates.
    Handles both GET requests for displaying certificates and POST requests for generating new certificates.
    """
    template_name = 'students/certificates.html'
    permission_required = 'students.view_student'
    
    def get_context_data(self, **kwargs):
        """Add context data for the template."""
        context = super().get_context_data(**kwargs)
        context['students'] = Student.objects.filter(is_active=True).order_by('first_name', 'last_name')
        # In a real application, you would fetch actual certificates here
        context['certificates'] = []
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle POST requests for generating certificates."""
        certificate_type = request.POST.get('certificate_type')
        student_id = request.POST.get('student_id')
        issue_date = request.POST.get('issue_date')
        language = request.POST.get('language', 'en')
        additional_notes = request.POST.get('additional_notes', '')
        
        if not certificate_type or not student_id:
            messages.error(request, _('Certificate type and student are required.'))
            return redirect('students:certificates')
        
        try:
            student = Student.objects.get(id=student_id)
            
            # In a real application, you would generate the actual certificate here
            # For now, we'll just show a success message
            messages.success(
                request, 
                _('Certificate generated successfully for {student_name}!').format(
                    student_name=student.full_name
                )
            )
            
        except Student.DoesNotExist:
            messages.error(request, _('Student not found.'))
        
        return redirect('students:certificates')


class CertificatePreviewView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """View for previewing certificates before generation."""
    template_name = 'students/certificate_preview.html'
    permission_required = 'students.view_student'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        certificate_type = self.request.GET.get('type')
        student_id = self.request.GET.get('student')
        
        if student_id:
            try:
                context['student'] = Student.objects.get(id=student_id)
            except Student.DoesNotExist:
                context['student'] = None
        
        context['certificate_type'] = certificate_type
        return context


class CertificateViewView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """View for displaying generated certificates."""
    template_name = 'students/certificate_view.html'
    permission_required = 'students.view_student'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        certificate_id = kwargs.get('certificate_id')
        # In a real application, you would fetch the certificate from database
        context['certificate_id'] = certificate_id
        return context


class CertificateDownloadView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """View for downloading certificates as PDF."""
    permission_required = 'students.view_student'
    
    def get(self, request, *args, **kwargs):
        certificate_id = kwargs.get('certificate_id')
        # In a real application, you would generate and return the PDF
        messages.info(request, _('Certificate download feature will be implemented soon.'))
        return redirect('students:certificates')


class CertificatePrintView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """View for print-friendly certificate display."""
    template_name = 'students/certificate_print.html'
    permission_required = 'students.view_student'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        certificate_id = kwargs.get('certificate_id')
        # In a real application, you would fetch the certificate from database
        context['certificate_id'] = certificate_id
        return context

# Class Management Views
class ClassListView(LoginRequiredMixin, ListView):
    model = Class
    template_name = 'students/class_list.html'
    context_object_name = 'classes'

    def get_queryset(self):
        return Class.objects.select_related('grade').annotate(
            student_count=Count('students', filter=Q(students__is_active=True))
        ).order_by('grade__level', 'name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Statistics
        context['total_classes'] = Class.objects.count()
        context['active_classes'] = Class.objects.filter(is_active=True).count()
        context['total_students'] = Student.objects.filter(is_active=True).count()
        context['total_grades'] = Grade.objects.filter(is_active=True).count()

        # For filters
        context['grades'] = Grade.objects.filter(is_active=True).order_by('level')

        return context

class ClassDetailView(LoginRequiredMixin, DetailView):
    model = Class
    template_name = 'students/class_detail.html'
    context_object_name = 'class_obj'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        class_obj = self.get_object()
        
        # Get students in this class
        context['students'] = Student.objects.filter(
            current_class=class_obj,
            is_active=True
        ).select_related('parent').order_by('first_name', 'last_name')
        
        # Get gender statistics
        context['male_students_count'] = context['students'].filter(gender='M').count()
        context['female_students_count'] = context['students'].filter(gender='F').count()
        
        # Get subjects taught in this class
        from academics.models import ClassSubject
        class_subjects = ClassSubject.objects.filter(
            class_obj=class_obj,
            is_active=True
        ).select_related('subject', 'teacher').prefetch_related('schedules')
        
        context['class_subjects'] = class_subjects
        context['subjects_count'] = class_subjects.count()
        
        # Calculate total weekly hours
        weekly_hours = 0
        for cs in class_subjects:
            weekly_hours += cs.weekly_hours
        
        context['weekly_hours'] = weekly_hours
        
        return context

class ClassCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Class
    form_class = ClassForm
    template_name = 'students/class_form.html'
    permission_required = 'students.add_class'
    success_url = reverse_lazy('students:class_list')

class ClassUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Class
    form_class = ClassForm
    template_name = 'students/class_form.html'
    permission_required = 'students.change_class'

    def get_success_url(self):
        return reverse_lazy('students:class_detail', kwargs={'pk': self.object.pk})

class ClassDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView):
    model = Class
    template_name = 'students/class_confirm_delete.html'
    permission_required = 'students.delete_class'
    success_url = reverse_lazy('students:class_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Class deleted successfully!'))
        return super().delete(request, *args, **kwargs)

# Grade Management Views
class GradeListView(LoginRequiredMixin, ListView):
    model = Grade
    template_name = 'students/grade_list.html'
    context_object_name = 'grades'

    def get_queryset(self):
        return Grade.objects.annotate(
            class_count=Count('classes', filter=Q(classes__is_active=True)),
            student_count=Count('classes__students', filter=Q(classes__students__is_active=True))
        ).order_by('level')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Statistics
        context['total_grades'] = Grade.objects.count()
        context['active_grades'] = Grade.objects.filter(is_active=True).count()
        context['total_classes'] = Class.objects.filter(is_active=True).count()
        context['total_students'] = Student.objects.filter(is_active=True).count()

        return context

class GradeCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Grade
    form_class = GradeForm
    template_name = 'students/grade_form.html'
    permission_required = 'students.add_grade'
    success_url = reverse_lazy('students:grade_list')

class GradeUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Grade
    form_class = GradeForm
    template_name = 'students/grade_form.html'
    permission_required = 'students.change_grade'
    success_url = reverse_lazy('students:grade_list')

# Student Affairs Views
class StudentInfractionsView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = StudentInfraction
    template_name = 'students/infractions.html'
    context_object_name = 'infractions'
    permission_required = 'students.view_studentinfraction'
    paginate_by = 20

    def get_queryset(self):
        queryset = StudentInfraction.objects.select_related('student', 'reported_by', 'handled_by').all()
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(student__first_name__icontains=search) |
                Q(student__last_name__icontains=search) |
                Q(description__icontains=search)
            )
        return queryset.order_by('-incident_date')


class StudentInfractionCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = StudentInfraction
    form_class = StudentInfractionForm
    template_name = 'students/infraction_form.html'
    permission_required = 'students.add_studentinfraction'
    success_url = reverse_lazy('students:infractions')

    def form_valid(self, form):
        form.instance.reported_by = self.request.user
        messages.success(self.request, _('Infraction recorded successfully!'))
        return super().form_valid(form)


class StudentInfractionUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = StudentInfraction
    form_class = StudentInfractionForm
    template_name = 'students/infraction_form.html'
    permission_required = 'students.change_studentinfraction'
    success_url = reverse_lazy('students:infractions')

    def form_valid(self, form):
        messages.success(self.request, _('Infraction updated successfully!'))
        return super().form_valid(form)


class SuspensionAndBlockView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/suspension_block.html'
    permission_required = 'students.change_student'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get current suspensions
        context['active_suspensions'] = StudentSuspension.objects.filter(
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None,
            status='active'
        ).select_related('student', 'issued_by').order_by('-start_date')
        
        # Get suspension statistics
        context['suspension_stats'] = {
            'total_active': StudentSuspension.objects.filter(status='active').count(),
            'in_school': StudentSuspension.objects.filter(
                status='active', 
                suspension_type='in_school'
            ).count(),
            'out_of_school': StudentSuspension.objects.filter(
                status='active', 
                suspension_type='out_of_school'
            ).count(),
        }
        
        # Get students for the form
        context['students'] = Student.objects.filter(
            is_active=True
        ).select_related('current_class').order_by('first_name', 'last_name')
        
        return context

    def post(self, request, *args, **kwargs):
        """Handle suspension creation"""
        form = StudentSuspensionForm(request.POST)
        
        if form.is_valid():
            suspension = form.save(commit=False)
            suspension.school = request.user.employee.school if hasattr(request.user, 'employee') else None
            suspension.issued_by = request.user
            suspension.save()
            
            messages.success(
                request,
                _('Suspension created successfully for {student}.').format(
                    student=suspension.student.full_name
                )
            )
            
            return redirect('students:suspension')
        
        # If form is invalid, redisplay with errors
        context = self.get_context_data()
        context['form_errors'] = form.errors
        return render(request, self.template_name, context)


class StudentSuspensionListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    """
    View for listing and managing student suspensions
    """
    model = StudentSuspension
    template_name = 'students/suspension_list.html'
    context_object_name = 'suspensions'
    permission_required = 'students.view_studentsuspension'
    paginate_by = 20

    def get_queryset(self):
        queryset = StudentSuspension.objects.select_related(
            'student', 'issued_by'
        ).order_by('-start_date')
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # Filter by suspension type
        suspension_type = self.request.GET.get('type')
        if suspension_type:
            queryset = queryset.filter(suspension_type=suspension_type)
        
        # Search by student name
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(student__first_name__icontains=search) |
                Q(student__last_name__icontains=search)
            )
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['suspension_types'] = StudentSuspension.SUSPENSION_TYPES
        context['status_choices'] = StudentSuspension.STATUS_CHOICES
        return context


class DisciplinaryActionListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    """
    View for listing disciplinary actions
    """
    model = DisciplinaryAction
    template_name = 'students/disciplinary_actions.html'
    context_object_name = 'actions'
    permission_required = 'students.view_disciplinaryaction'
    paginate_by = 20

    def get_queryset(self):
        queryset = DisciplinaryAction.objects.select_related(
            'infraction', 'infraction__student', 'assigned_by', 'supervised_by'
        ).order_by('-assigned_date')
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # Filter by action type
        action_type = self.request.GET.get('action_type')
        if action_type:
            queryset = queryset.filter(action_type=action_type)
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action_types'] = DisciplinaryAction.ACTION_TYPES
        context['status_choices'] = DisciplinaryAction.STATUS_CHOICES
        
        # Statistics
        context['stats'] = {
            'total_actions': DisciplinaryAction.objects.count(),
            'pending_actions': DisciplinaryAction.objects.filter(status='pending').count(),
            'overdue_actions': DisciplinaryAction.objects.filter(
                due_date__lt=date.today(),
                status__in=['pending', 'in_progress']
            ).count(),
        }
        
        return context


class DisciplinaryActionCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """
    View for creating disciplinary actions
    """
    model = DisciplinaryAction
    form_class = DisciplinaryActionForm
    template_name = 'students/disciplinary_action_form.html'
    permission_required = 'students.add_disciplinaryaction'
    success_url = reverse_lazy('students:disciplinary_actions')

    def form_valid(self, form):
        form.instance.school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        form.instance.assigned_by = self.request.user
        messages.success(self.request, _('Disciplinary action created successfully!'))
        return super().form_valid(form)


class DisciplineReportView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """
    View for generating and viewing discipline reports
    """
    template_name = 'students/discipline_reports.html'
    permission_required = 'students.view_disciplinereport'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get recent reports
        context['recent_reports'] = DisciplineReport.objects.filter(
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).order_by('-created_at')[:10]
        
        # Report form
        context['form'] = DisciplineReportForm(
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        )
        
        return context

    def post(self, request, *args, **kwargs):
        """Handle report generation"""
        form = DisciplineReportForm(
            request.POST,
            school=request.user.employee.school if hasattr(request.user, 'employee') else None
        )
        
        if form.is_valid():
            report = form.save(commit=False)
            report.school = request.user.employee.school if hasattr(request.user, 'employee') else None
            report.generated_by = request.user
            
            # Set filters from form
            filters = {}
            if form.cleaned_data.get('student'):
                filters['student_id'] = form.cleaned_data['student'].id
            if form.cleaned_data.get('class_filter'):
                filters['class_id'] = form.cleaned_data['class_filter'].id
            if form.cleaned_data.get('severity_filter'):
                filters['severity'] = form.cleaned_data['severity_filter']
            
            report.filters = filters
            report.save()
            
            # Generate the report data
            report.generate_report_data()
            
            messages.success(request, _('Report generated successfully!'))
            return redirect('students:discipline_report_detail', pk=report.pk)
        
        context = self.get_context_data()
        context['form'] = form
        return render(request, self.template_name, context)


class DisciplineReportDetailView(LoginRequiredMixin, PermissionRequiredMixin, DetailView):
    """
    View for displaying discipline report details
    """
    model = DisciplineReport
    template_name = 'students/discipline_report_detail.html'
    context_object_name = 'report'
    permission_required = 'students.view_disciplinereport'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report = self.get_object()
        
        # If report data is empty, generate it
        if not report.report_data:
            report.generate_report_data()
        
        context['report_data'] = report.report_data
        return context


class VacationRequestsView(LoginRequiredMixin, ListView):
    model = VacationRequest
    template_name = 'students/vacation_requests.html'
    context_object_name = 'vacation_requests'
    paginate_by = 20

    def get_queryset(self):
        queryset = VacationRequest.objects.select_related('student', 'requested_by', 'approved_by').all()
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        return queryset.order_by('-created_at')


class VacationRequestCreateView(LoginRequiredMixin, CreateView):
    model = VacationRequest
    form_class = VacationRequestForm
    template_name = 'students/vacation_request_form.html'
    success_url = reverse_lazy('students:vacation_requests')

    def form_valid(self, form):
        form.instance.requested_by = self.request.user
        messages.success(self.request, _('Vacation request submitted successfully!'))
        return super().form_valid(form)


class ApproveVacationRequestsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/approve_vacation.html'
    permission_required = 'students.change_vacationrequest'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['pending_requests'] = VacationRequest.objects.filter(
            status='pending'
        ).select_related('student', 'requested_by')
        return context

    def post(self, request, *args, **kwargs):
        request_id = request.POST.get('request_id')
        action = request.POST.get('action')
        rejection_reason = request.POST.get('rejection_reason', '')

        try:
            vacation_request = VacationRequest.objects.get(id=request_id)
            if action == 'approve':
                vacation_request.status = 'approved'
                vacation_request.approved_by = request.user
                vacation_request.approved_at = timezone.now()
                messages.success(request, _('Vacation request approved successfully!'))
            elif action == 'reject':
                vacation_request.status = 'rejected'
                vacation_request.rejection_reason = rejection_reason
                messages.success(request, _('Vacation request rejected.'))

            vacation_request.save()
        except VacationRequest.DoesNotExist:
            messages.error(request, _('Vacation request not found.'))

        return redirect('students:approve_vacation')


class StudentSecondLanguageView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/second_language.html'
    permission_required = 'students.change_student'

# Documents and Attachments Views
class StudentDocumentsView(LoginRequiredMixin, ListView):
    model = StudentDocument
    template_name = 'students/documents.html'
    context_object_name = 'documents'
    paginate_by = 20

    def get_queryset(self):
        queryset = StudentDocument.objects.select_related('student', 'uploaded_by', 'verified_by').all()
        student_id = self.request.GET.get('student')
        document_type = self.request.GET.get('type')

        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if document_type:
            queryset = queryset.filter(document_type=document_type)

        return queryset.order_by('-created_at')


class StudentDocumentCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = StudentDocument
    form_class = StudentDocumentForm
    template_name = 'students/document_form.html'
    permission_required = 'students.add_studentdocument'
    success_url = reverse_lazy('students:documents')

    def form_valid(self, form):
        form.instance.uploaded_by = self.request.user
        messages.success(self.request, _('Document uploaded successfully!'))
        return super().form_valid(form)


class StudentDocumentReceiverView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """View for receiving and processing student documents."""
    template_name = 'students/document_receiver.html'
    permission_required = 'students.change_student'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['pending_documents'] = StudentDocument.objects.filter(
            is_verified=False
        ).select_related('student', 'uploaded_by').order_by('-created_at')
        context['document_types'] = StudentDocument.DOCUMENT_TYPES
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle document verification."""
        document_id = request.POST.get('document_id')
        action = request.POST.get('action')
        
        if not document_id or not action:
            messages.error(request, _('Invalid request.'))
            return redirect('students:document_receiver')
        
        try:
            document = StudentDocument.objects.get(id=document_id)
            
            if action == 'verify':
                document.is_verified = True
                document.verified_by = request.user
                document.verified_at = timezone.now()
                document.save()
                messages.success(request, _('Document verified successfully.'))
            elif action == 'reject':
                document.delete()
                messages.success(request, _('Document rejected and removed.'))
            
        except StudentDocument.DoesNotExist:
            messages.error(request, _('Document not found.'))
        
        return redirect('students:document_receiver')


class StudentAttachmentsView(LoginRequiredMixin, ListView):
    model = StudentAttachment
    template_name = 'students/attachments.html'
    context_object_name = 'attachments'
    paginate_by = 20

    def get_queryset(self):
        queryset = StudentAttachment.objects.select_related('student', 'uploaded_by').all()
        student_id = self.request.GET.get('student')
        attachment_type = self.request.GET.get('type')

        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if attachment_type:
            queryset = queryset.filter(attachment_type=attachment_type)

        return queryset.order_by('-created_at')


class StudentAttachmentCreateView(LoginRequiredMixin, CreateView):
    model = StudentAttachment
    form_class = StudentAttachmentForm
    template_name = 'students/attachment_form.html'
    success_url = reverse_lazy('students:attachments')

    def form_valid(self, form):
        form.instance.uploaded_by = self.request.user
        messages.success(self.request, _('Attachment uploaded successfully!'))
        return super().form_valid(form)

# Electronic Registration
class ElectronicRegistrationView(LoginRequiredMixin, ListView):
    model = ElectronicRegistration
    template_name = 'students/electronic_registration.html'
    context_object_name = 'registrations'
    paginate_by = 20

    def get_queryset(self):
        queryset = ElectronicRegistration.objects.select_related('desired_grade', 'reviewed_by', 'student').all()
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Statistics
        context['total_registrations'] = ElectronicRegistration.objects.count()
        context['pending_registrations'] = ElectronicRegistration.objects.filter(
            status__in=['submitted', 'under_review']
        ).count()
        context['approved_registrations'] = ElectronicRegistration.objects.filter(
            status='approved'
        ).count()
        context['completed_registrations'] = ElectronicRegistration.objects.filter(
            status='completed'
        ).count()

        return context


class ElectronicRegistrationCreateView(CreateView):
    model = ElectronicRegistration
    form_class = ElectronicRegistrationForm
    template_name = 'students/electronic_registration_form.html'
    success_url = reverse_lazy('students:electronic_registration')

    def form_valid(self, form):
        form.instance.submitted_at = timezone.now()
        form.instance.status = 'submitted'
        messages.success(self.request, _('Registration submitted successfully! We will review your application.'))
        return super().form_valid(form)


# Student Transfer Views
class StudentTransferListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    """
    View for listing and managing student transfers.
    Handles both GET requests for listing transfers and POST requests for approving/rejecting transfers.
    """
    model = StudentTransfer
    template_name = 'students/transfers.html'
    context_object_name = 'transfers'
    permission_required = 'students.view_studenttransfer'
    paginate_by = 20

    def get_queryset(self):
        """Return filtered queryset of transfers based on request parameters."""
        queryset = StudentTransfer.objects.select_related(
            'student', 'from_class', 'to_class', 'requested_by', 'approved_by'
        )
        
        # Apply filters if provided
        transfer_type = self.request.GET.get('type')
        status = self.request.GET.get('status')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')

        if transfer_type:
            queryset = queryset.filter(transfer_type=transfer_type)
        if status:
            queryset = queryset.filter(status=status)
        if date_from:
            queryset = queryset.filter(transfer_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(transfer_date__lte=date_to)

        return queryset.order_by('-transfer_date')
    
    def get_context_data(self, **kwargs):
        """Add additional context data for the template."""
        context = super().get_context_data(**kwargs)
        
        # Add statistics
        context['pending_count'] = StudentTransfer.objects.filter(status='pending').count()
        context['approved_count'] = StudentTransfer.objects.filter(status='approved').count()
        context['completed_count'] = StudentTransfer.objects.filter(status='completed').count()
        
        return context
        
    def post(self, request, *args, **kwargs):
        """Handle POST requests for approving or rejecting transfers."""
        action = request.GET.get('action')
        transfer_id = request.GET.get('id')
        
        if not transfer_id:
            messages.error(request, _('Transfer ID not provided.'))
            return redirect('students:transfers')
            
        try:
            transfer = StudentTransfer.objects.get(id=transfer_id)
            
            if action == 'approve':
                self._approve_transfer(request, transfer)
            elif action == 'reject':
                self._reject_transfer(request, transfer)
            else:
                messages.error(request, _('Invalid action.'))
                
        except StudentTransfer.DoesNotExist:
            messages.error(request, _('Transfer request not found.'))
            
        return redirect('students:transfers')
    
    def _approve_transfer(self, request, transfer):
        """Helper method to approve a transfer."""
        if not self.has_permission():
            messages.error(request, _('You do not have permission to approve transfers.'))
            return
            
        transfer.status = 'approved'
        transfer.approved_by = request.user
        transfer.approved_at = timezone.now()
        transfer.notes = request.POST.get('notes', '')
        transfer.save()
        messages.success(request, _('Transfer request approved successfully!'))
    
    def _reject_transfer(self, request, transfer):
        """Helper method to reject a transfer."""
        if not self.has_permission():
            messages.error(request, _('You do not have permission to reject transfers.'))
            return
            
        transfer.status = 'rejected'
        transfer.notes = request.POST.get('rejection_reason', '')
        transfer.save()
        messages.success(request, _('Transfer request rejected successfully!'))


class StudentTransferCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = StudentTransfer
    form_class = StudentTransferForm
    template_name = 'students/transfer_form.html'
    permission_required = 'students.add_studenttransfer'
    success_url = reverse_lazy('students:transfers')

    def form_valid(self, form):
        form.instance.requested_by = self.request.user
        messages.success(self.request, _('Transfer request created successfully!'))
        return super().form_valid(form)

# Settings Views
class StudentSettingsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """Main settings view for student module."""
    template_name = 'students/settings.html'
    permission_required = 'students.change_student'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['settings_sections'] = [
            {
                'title': _('Field Settings'),
                'description': _('Configure visible fields and their properties'),
                'url': 'students:fields_settings',
                'icon': 'fas fa-cogs'
            },
            {
                'title': _('Tab Settings'),
                'description': _('Manage tabs and their visibility'),
                'url': 'students:tabs_settings',
                'icon': 'fas fa-tabs'
            },
            {
                'title': _('General Settings'),
                'description': _('General student module configuration'),
                'url': '#',
                'icon': 'fas fa-sliders-h'
            }
        ]
        return context


class StudentFieldsSettingsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """Settings view for configuring student fields."""
    template_name = 'students/fields_settings.html'
    permission_required = 'students.change_student'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['field_categories'] = [
            {
                'name': _('Basic Information'),
                'fields': ['first_name', 'last_name', 'student_id', 'admission_number']
            },
            {
                'name': _('Personal Details'),
                'fields': ['date_of_birth', 'gender', 'nationality', 'blood_type']
            },
            {
                'name': _('Contact Information'),
                'fields': ['parent', 'home_address', 'emergency_contact']
            },
            {
                'name': _('Academic Information'),
                'fields': ['current_class', 'admission_date', 'previous_school']
            }
        ]
        return context


class StudentTabsSettingsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """Settings view for configuring student tabs."""
    template_name = 'students/tabs_settings.html'
    permission_required = 'students.change_student'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['available_tabs'] = [
            {'name': 'basic_info', 'title': _('Basic Information'), 'enabled': True},
            {'name': 'academic', 'title': _('Academic Information'), 'enabled': True},
            {'name': 'documents', 'title': _('Documents'), 'enabled': True},
            {'name': 'attendance', 'title': _('Attendance'), 'enabled': True},
            {'name': 'grades', 'title': _('Grades'), 'enabled': True},
            {'name': 'infractions', 'title': _('Infractions'), 'enabled': True},
            {'name': 'transfers', 'title': _('Transfers'), 'enabled': False},
        ]
        return context

# Admission and Registration Views
class OnlineAdmissionView(CreateView):
    """
    Public view for online student admission
    """
    model = ElectronicRegistration
    form_class = ElectronicRegistrationForm
    template_name = 'students/online_admission.html'
    success_url = reverse_lazy('students:admission_success')

    def form_valid(self, form):
        form.instance.submitted_at = timezone.now()
        form.instance.status = 'submitted'
        
        # Send confirmation email (would integrate with email service)
        messages.success(
            self.request, 
            _('Your admission application has been submitted successfully! '
              'You will receive a confirmation email shortly.')
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['available_grades'] = Grade.objects.filter(is_active=True).order_by('level')
        return context


class AdmissionSuccessView(TemplateView):
    """
    Success page after admission submission
    """
    template_name = 'students/admission_success.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['next_steps'] = [
            _('Check your email for confirmation'),
            _('Upload required documents'),
            _('Wait for application review'),
            _('Receive admission decision'),
        ]
        return context


class AdmissionDocumentUploadView(CreateView):
    """
    View for uploading admission documents
    """
    template_name = 'students/admission_document_upload.html'
    form_class = AdmissionDocumentForm
    success_url = reverse_lazy('students:admission_documents_success')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['initial'] = {'registration_id': self.request.GET.get('registration')}
        return kwargs

    def form_valid(self, form):
        registration_id = form.cleaned_data['registration_id']
        
        try:
            registration = ElectronicRegistration.objects.get(id=registration_id)
            
            # Save each uploaded document
            for field_name, file_field in form.cleaned_data.items():
                if field_name != 'registration_id' and file_field:
                    StudentDocument.objects.create(
                        student=None,  # Will be linked when student is created
                        document_type=field_name,
                        title=f"{registration.full_name} - {field_name.replace('_', ' ').title()}",
                        file=file_field,
                        uploaded_by=None,  # Public upload
                        description=f"Admission document for {registration.full_name}"
                    )
            
            # Update registration status
            registration.status = 'under_review'
            registration.save()
            
            messages.success(
                self.request,
                _('Documents uploaded successfully! Your application is now under review.')
            )
            
        except ElectronicRegistration.DoesNotExist:
            messages.error(self.request, _('Registration not found.'))
            return redirect('students:online_admission')
        
        return super().form_valid(form)


class AdmissionDocumentsSuccessView(TemplateView):
    """
    Success page after document upload
    """
    template_name = 'students/admission_documents_success.html'


class AdmissionReviewListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    """
    View for reviewing admission applications
    """
    model = ElectronicRegistration
    template_name = 'students/admission_review_list.html'
    context_object_name = 'applications'
    permission_required = 'students.change_electronicregistration'
    paginate_by = 20

    def get_queryset(self):
        queryset = ElectronicRegistration.objects.select_related('desired_grade', 'reviewed_by')
        
        # Filter by status
        status = self.request.GET.get('status', 'submitted')
        if status:
            queryset = queryset.filter(status=status)
        
        # Filter by grade
        grade_id = self.request.GET.get('grade')
        if grade_id:
            queryset = queryset.filter(desired_grade_id=grade_id)
        
        return queryset.order_by('-submitted_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['grades'] = Grade.objects.filter(is_active=True).order_by('level')
        context['status_counts'] = {
            'submitted': ElectronicRegistration.objects.filter(status='submitted').count(),
            'under_review': ElectronicRegistration.objects.filter(status='under_review').count(),
            'approved': ElectronicRegistration.objects.filter(status='approved').count(),
            'rejected': ElectronicRegistration.objects.filter(status='rejected').count(),
        }
        return context


class AdmissionReviewDetailView(LoginRequiredMixin, PermissionRequiredMixin, DetailView):
    """
    Detailed view for reviewing a single admission application
    """
    model = ElectronicRegistration
    template_name = 'students/admission_review_detail.html'
    context_object_name = 'application'
    permission_required = 'students.change_electronicregistration'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        application = self.get_object()
        
        # Get related documents
        context['documents'] = StudentDocument.objects.filter(
            title__icontains=application.full_name
        )
        
        # Get available classes for the desired grade
        context['available_classes'] = Class.objects.filter(
            grade=application.desired_grade,
            academic_year__is_current=True
        )
        
        # Review form
        context['review_form'] = AdmissionReviewForm(
            initial={'registration_id': application.id},
            school=application.school if hasattr(application, 'school') else None
        )
        
        return context

    def post(self, request, *args, **kwargs):
        application = self.get_object()
        form = AdmissionReviewForm(
            request.POST,
            school=application.school if hasattr(application, 'school') else None
        )
        
        if form.is_valid():
            status = form.cleaned_data['status']
            assigned_class = form.cleaned_data.get('assigned_class')
            notes = form.cleaned_data.get('notes')
            rejection_reason = form.cleaned_data.get('rejection_reason')
            
            # Update application
            application.status = status
            application.reviewed_by = request.user
            application.reviewed_at = timezone.now()
            application.notes = notes
            
            if status == 'approved':
                # Create student record
                student = self._create_student_from_application(application, assigned_class)
                application.student = student
                application.status = 'completed'
                
                messages.success(
                    request,
                    _('Application approved and student record created successfully!')
                )
            else:
                application.notes = rejection_reason
                messages.success(request, _('Application rejected.'))
            
            application.save()
            return redirect('students:admission_review_list')
        
        # If form is invalid, redisplay with errors
        context = self.get_context_data()
        context['review_form'] = form
        return render(request, self.template_name, context)

    def _create_student_from_application(self, application, assigned_class):
        """
        Create a student record from approved application
        """
        from accounts.models import User
        
        # Create user account for parent
        parent_user = User.objects.create_user(
            username=application.father_email,
            email=application.father_email,
            first_name=application.father_name.split()[0],
            last_name=' '.join(application.father_name.split()[1:]) if len(application.father_name.split()) > 1 else '',
            user_type='parent'
        )
        
        # Create parent record
        parent = Parent.objects.create(
            school=application.school if hasattr(application, 'school') else None,
            user=parent_user,
            father_name=application.father_name,
            mother_name=application.mother_name,
            father_phone=application.father_phone,
            mother_phone=application.mother_phone,
            home_address=application.home_address
        )
        
        # Create user account for student
        student_username = f"{application.first_name.lower()}.{application.last_name.lower()}"
        counter = 1
        original_username = student_username
        while User.objects.filter(username=student_username).exists():
            student_username = f"{original_username}{counter}"
            counter += 1
        
        student_user = User.objects.create_user(
            username=student_username,
            email=f"{student_username}@school.edu",
            first_name=application.first_name,
            last_name=application.last_name,
            user_type='student'
        )
        
        # Create student record
        student = Student.objects.create(
            school=application.school if hasattr(application, 'school') else None,
            user=student_user,
            first_name=application.first_name,
            last_name=application.last_name,
            first_name_ar=application.first_name_ar,
            last_name_ar=application.last_name_ar,
            date_of_birth=application.date_of_birth,
            gender=application.gender,
            nationality=application.nationality,
            parent=parent,
            current_class=assigned_class,
            admission_date=timezone.now().date(),
            previous_school=application.previous_school
        )
        
        return student


class StudentIDGenerationView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """
    View for generating student IDs
    """
    template_name = 'students/student_id_generation.html'
    permission_required = 'students.change_student'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get students without IDs
        context['students_without_ids'] = Student.objects.filter(
            student_id__isnull=True
        ).select_related('current_class', 'current_class__grade')
        
        context['form'] = StudentIDGenerationForm()
        return context

    def post(self, request, *args, **kwargs):
        form = StudentIDGenerationForm(request.POST)
        
        if form.is_valid():
            student_ids = request.POST.getlist('student_ids')
            id_format = form.cleaned_data['id_format']
            custom_prefix = form.cleaned_data.get('custom_prefix')
            starting_number = form.cleaned_data.get('starting_number', 1)
            
            students = Student.objects.filter(id__in=student_ids)
            generated_count = 0
            
            for i, student in enumerate(students):
                if id_format == 'auto':
                    # Use the existing auto-generation method
                    if not student.student_id:
                        student.save()  # This will trigger auto-generation
                        generated_count += 1
                else:
                    # Use custom format
                    student_id = f"{custom_prefix}{starting_number + i:04d}"
                    
                    # Ensure uniqueness
                    while Student.objects.filter(student_id=student_id).exists():
                        starting_number += 1
                        student_id = f"{custom_prefix}{starting_number + i:04d}"
                    
                    student.student_id = student_id
                    student.save()
                    generated_count += 1
            
            messages.success(
                request,
                _('Successfully generated IDs for {count} students.').format(count=generated_count)
            )
            
            return redirect('students:student_id_generation')
        
        context = self.get_context_data()
        context['form'] = form
        return render(request, self.template_name, context)


class DocumentVerificationView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    """
    View for verifying uploaded documents
    """
    model = StudentDocument
    template_name = 'students/document_verification.html'
    context_object_name = 'documents'
    permission_required = 'students.change_studentdocument'
    paginate_by = 20

    def get_queryset(self):
        queryset = StudentDocument.objects.select_related('student', 'uploaded_by')
        
        # Filter unverified documents by default
        verification_status = self.request.GET.get('status', 'unverified')
        if verification_status == 'unverified':
            queryset = queryset.filter(is_verified=False)
        elif verification_status == 'verified':
            queryset = queryset.filter(is_verified=True)
        
        # Filter by document type
        document_type = self.request.GET.get('type')
        if document_type:
            queryset = queryset.filter(document_type=document_type)
        
        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['document_types'] = StudentDocument.DOCUMENT_TYPES
        context['verification_counts'] = {
            'unverified': StudentDocument.objects.filter(is_verified=False).count(),
            'verified': StudentDocument.objects.filter(is_verified=True).count(),
        }
        return context

    def post(self, request, *args, **kwargs):
        document_id = request.POST.get('document_id')
        verification_status = request.POST.get('verification_status')
        verification_notes = request.POST.get('verification_notes', '')
        
        try:
            document = StudentDocument.objects.get(id=document_id)
            
            if verification_status == 'verified':
                document.verify_document(request.user)
                messages.success(request, _('Document verified successfully.'))
            elif verification_status == 'rejected':
                document.delete()
                messages.success(request, _('Document rejected and removed.'))
            elif verification_status == 'needs_resubmission':
                # In a real implementation, you would notify the user to resubmit
                messages.info(request, _('Document marked for resubmission.'))
            
        except StudentDocument.DoesNotExist:
            messages.error(request, _('Document not found.'))
        
        return redirect('students:document_verification')


class AdmissionWorkflowView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """
    View showing the admission workflow status
    """
    template_name = 'students/admission_workflow.html'
    permission_required = 'students.view_electronicregistration'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Workflow statistics
        context['workflow_stats'] = {
            'applications_submitted': ElectronicRegistration.objects.filter(status='submitted').count(),
            'documents_uploaded': StudentDocument.objects.filter(is_verified=False).count(),
            'under_review': ElectronicRegistration.objects.filter(status='under_review').count(),
            'approved': ElectronicRegistration.objects.filter(status='approved').count(),
            'students_created': ElectronicRegistration.objects.filter(status='completed').count(),
        }
        
        # Recent activities
        context['recent_applications'] = ElectronicRegistration.objects.order_by('-created_at')[:10]
        context['recent_documents'] = StudentDocument.objects.order_by('-created_at')[:10]
        
        return context


class TransferWorkflowView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """
    View showing the transfer workflow status and management
    """
    template_name = 'students/transfer_workflow.html'
    permission_required = 'students.view_studenttransfer'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Transfer statistics
        context['transfer_stats'] = {
            'pending_transfers': StudentTransfer.objects.filter(status='pending').count(),
            'approved_transfers': StudentTransfer.objects.filter(status='approved').count(),
            'completed_transfers': StudentTransfer.objects.filter(status='completed').count(),
            'rejected_transfers': StudentTransfer.objects.filter(status='rejected').count(),
            'internal_transfers': StudentTransfer.objects.filter(transfer_type='internal').count(),
            'outgoing_transfers': StudentTransfer.objects.filter(transfer_type='outgoing').count(),
            'incoming_transfers': StudentTransfer.objects.filter(transfer_type='incoming').count(),
        }
        
        # Recent transfers
        context['recent_transfers'] = StudentTransfer.objects.select_related(
            'student', 'requested_by', 'approved_by'
        ).order_by('-created_at')[:10]
        
        # Transfers requiring attention
        context['urgent_transfers'] = StudentTransfer.objects.filter(
            status='pending',
            transfer_date__lte=date.today() + timedelta(days=7)
        ).select_related('student', 'requested_by')
        
        # Document generation statistics
        context['document_stats'] = {
            'certificates_generated': TransferDocument.objects.filter(
                document_type='transfer_certificate'
            ).count(),
            'transcripts_generated': TransferDocument.objects.filter(
                document_type='academic_transcript'
            ).count(),
            'pending_documents': StudentTransfer.objects.filter(
                status='approved',
                documents__isnull=True
            ).count(),
        }
        
        return context


class TransferDetailView(LoginRequiredMixin, PermissionRequiredMixin, DetailView):
    """
    Detailed view for a single transfer request
    """
    model = StudentTransfer
    template_name = 'students/transfer_detail.html'
    context_object_name = 'transfer'
    permission_required = 'students.view_studenttransfer'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        transfer = self.get_object()
        
        # Get transfer timeline
        context['timeline'] = transfer.get_transfer_timeline()
        
        # Get required approvals
        context['required_approvals'] = transfer.get_required_approvals()
        
        # Get checklist status
        context['checklist'] = transfer.get_checklist_status()
        
        # Get related documents
        context['documents'] = transfer.documents.all()
        
        # Get academic history if preserved
        context['academic_history'] = transfer.academic_records.all()
        
        # Get student's current academic status
        context['current_enrollment'] = transfer.student.get_current_enrollment()
        
        return context

    def post(self, request, *args, **kwargs):
        """Handle transfer actions"""
        transfer = self.get_object()
        action = request.POST.get('action')
        
        if action == 'approve':
            transfer.approve(request.user)
            messages.success(request, _('Transfer approved successfully.'))
        
        elif action == 'complete':
            if transfer.can_be_completed():
                # Preserve academic history
                transfer.preserve_academic_history()
                
                # Generate transfer documents
                document_types = request.POST.getlist('document_types')
                if document_types:
                    transfer.generate_transfer_documents(document_types)
                
                # Complete the transfer
                transfer.complete_transfer()
                
                messages.success(request, _('Transfer completed successfully.'))
            else:
                messages.error(request, _('Transfer cannot be completed. Please check all requirements.'))
        
        elif action == 'generate_documents':
            if transfer.status == 'approved':
                document_types = request.POST.getlist('document_types')
                if document_types:
                    documents = transfer.generate_transfer_documents(document_types)
                    messages.success(
                        request,
                        _('Generated {} documents successfully.').format(len(documents))
                    )
                else:
                    messages.error(request, _('Please select at least one document type.'))
            else:
                messages.error(request, _('Transfer must be approved before generating documents.'))
        
        return redirect('students:transfer_detail', pk=transfer.pk)


class TransferDocumentView(LoginRequiredMixin, PermissionRequiredMixin, DetailView):
    """
    View for displaying transfer documents
    """
    model = TransferDocument
    template_name = 'students/transfer_document.html'
    context_object_name = 'document'
    permission_required = 'students.view_studenttransfer'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        document = self.get_object()
        
        # Format content for display
        context['formatted_content'] = self._format_document_content(document)
        
        return context

    def _format_document_content(self, document):
        """Format document content for display"""
        content = document.content
        
        if document.document_type == 'transfer_certificate':
            return {
                'title': _('Transfer Certificate'),
                'sections': [
                    {
                        'title': _('Student Information'),
                        'items': [
                            (_('Name'), content.get('student_name')),
                            (_('Student ID'), content.get('student_id')),
                            (_('Admission Number'), content.get('admission_number')),
                            (_('Date of Birth'), content.get('date_of_birth')),
                            (_('Father\'s Name'), content.get('father_name')),
                            (_('Mother\'s Name'), content.get('mother_name')),
                        ]
                    },
                    {
                        'title': _('Transfer Information'),
                        'items': [
                            (_('Last Class Attended'), content.get('last_class_attended')),
                            (_('Reason for Leaving'), content.get('reason_for_leaving')),
                            (_('Transfer Date'), content.get('transfer_date')),
                            (_('Conduct'), content.get('conduct')),
                            (_('Dues Cleared'), content.get('dues_cleared')),
                        ]
                    }
                ]
            }
        
        elif document.document_type == 'academic_transcript':
            return {
                'title': _('Academic Transcript'),
                'sections': [
                    {
                        'title': _('Student Information'),
                        'items': [
                            (_('Name'), content.get('student_name')),
                            (_('Student ID'), content.get('student_id')),
                        ]
                    },
                    {
                        'title': _('Academic Records'),
                        'table': content.get('academic_records', [])
                    }
                ]
            }
        
        return {'title': document.get_document_type_display(), 'content': content}


class AcademicHistoryView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    """
    View for displaying student academic history
    """
    model = AcademicHistory
    template_name = 'students/academic_history.html'
    context_object_name = 'academic_records'
    permission_required = 'students.view_student'
    paginate_by = 20

    def get_queryset(self):
        queryset = AcademicHistory.objects.select_related(
            'student', 'academic_year', 'class_obj', 'transfer_related'
        )
        
        # Filter by student if provided
        student_id = self.request.GET.get('student')
        if student_id:
            queryset = queryset.filter(student_id=student_id)
        
        # Filter by academic year
        academic_year_id = self.request.GET.get('academic_year')
        if academic_year_id:
            queryset = queryset.filter(academic_year_id=academic_year_id)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        return queryset.order_by('-academic_year__start_date', '-enrollment_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add filter options
        context['students'] = Student.objects.filter(is_active=True).order_by('first_name', 'last_name')
        context['academic_years'] = AcademicYear.objects.all().order_by('-start_date')
        
        # Statistics
        context['stats'] = {
            'total_records': AcademicHistory.objects.count(),
            'transferred_students': AcademicHistory.objects.filter(status='transferred').count(),
            'completed_records': AcademicHistory.objects.filter(status='completed').count(),
        }
        
        return context


class VacationCalendarView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """
    View for displaying vacation calendar with school holidays and student vacations
    """
    template_name = 'students/vacation_calendar.html'
    permission_required = 'students.view_vacationrequest'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get current academic year
        current_academic_year = AcademicYear.objects.filter(is_current=True).first()
        
        # Get vacation calendar entries
        vacation_calendar = VacationCalendar.objects.filter(
            academic_year=current_academic_year
        ).order_by('start_date') if current_academic_year else []
        
        # Get approved vacation requests for the current academic year
        approved_vacations = VacationRequest.objects.filter(
            status='approved',
            start_date__gte=current_academic_year.start_date if current_academic_year else date.today(),
            end_date__lte=current_academic_year.end_date if current_academic_year else date.today()
        ).select_related('student', 'student__current_class')
        
        # Prepare calendar events
        calendar_events = []
        
        # Add school holidays
        for holiday in vacation_calendar:
            calendar_events.append({
                'title': holiday.name,
                'start': holiday.start_date.isoformat(),
                'end': (holiday.end_date + timedelta(days=1)).isoformat(),  # FullCalendar end is exclusive
                'color': self._get_holiday_color(holiday.vacation_type),
                'type': 'holiday',
                'description': holiday.description or '',
            })
        
        # Add student vacations
        for vacation in approved_vacations:
            calendar_events.append({
                'title': f"{vacation.student.full_name} - Vacation",
                'start': vacation.start_date.isoformat(),
                'end': (vacation.end_date + timedelta(days=1)).isoformat(),
                'color': '#17a2b8',  # Info color
                'type': 'student_vacation',
                'description': vacation.reason,
                'student': vacation.student.full_name,
                'class': str(vacation.student.current_class) if vacation.student.current_class else 'N/A',
            })
        
        context['calendar_events'] = calendar_events
        context['vacation_calendar'] = vacation_calendar
        context['current_academic_year'] = current_academic_year
        
        return context

    def _get_holiday_color(self, vacation_type):
        """Get color for different types of holidays"""
        colors = {
            'school_holiday': '#dc3545',  # Red
            'public_holiday': '#28a745',  # Green
            'exam_period': '#ffc107',     # Yellow
            'maintenance': '#6c757d',     # Gray
            'special_event': '#6f42c1',   # Purple
        }
        return colors.get(vacation_type, '#007bff')  # Default blue


class VacationReportsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """
    View for generating and displaying vacation reports
    """
    template_name = 'students/vacation_reports.html'
    permission_required = 'students.view_vacationrequest'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get recent reports
        context['recent_reports'] = VacationReport.objects.order_by('-created_at')[:10]
        
        # Get current academic year for default date range
        current_academic_year = AcademicYear.objects.filter(is_current=True).first()
        
        if current_academic_year:
            context['default_start_date'] = current_academic_year.start_date
            context['default_end_date'] = current_academic_year.end_date
        else:
            context['default_start_date'] = date.today().replace(month=1, day=1)
            context['default_end_date'] = date.today().replace(month=12, day=31)
        
        # Quick statistics
        context['quick_stats'] = self._get_quick_statistics()
        
        return context

    def post(self, request, *args, **kwargs):
        """Handle report generation requests"""
        report_type = request.POST.get('report_type')
        start_date = request.POST.get('start_date')
        end_date = request.POST.get('end_date')
        
        if not all([report_type, start_date, end_date]):
            messages.error(request, _('Please provide all required fields.'))
            return redirect('students:vacation_reports')
        
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            # Create and generate report
            report = VacationReport.objects.create(
                school=request.user.employee.school if hasattr(request.user, 'employee') else None,
                report_type=report_type,
                start_date=start_date,
                end_date=end_date,
                generated_by=request.user
            )
            
            report.generate_report_data()
            
            messages.success(request, _('Report generated successfully.'))
            return redirect('students:vacation_report_detail', pk=report.pk)
            
        except ValueError:
            messages.error(request, _('Invalid date format.'))
            return redirect('students:vacation_reports')

    def _get_quick_statistics(self):
        """Get quick vacation statistics"""
        today = date.today()
        current_month_start = today.replace(day=1)
        
        return {
            'pending_requests': VacationRequest.objects.filter(status='pending').count(),
            'this_month_requests': VacationRequest.objects.filter(
                created_at__gte=current_month_start
            ).count(),
            'approved_this_month': VacationRequest.objects.filter(
                status='approved',
                approved_at__gte=current_month_start
            ).count(),
            'active_vacations': VacationRequest.objects.filter(
                status='approved',
                start_date__lte=today,
                end_date__gte=today
            ).count(),
        }


class VacationReportDetailView(LoginRequiredMixin, PermissionRequiredMixin, DetailView):
    """
    View for displaying detailed vacation report
    """
    model = VacationReport
    template_name = 'students/vacation_report_detail.html'
    context_object_name = 'report'
    permission_required = 'students.view_vacationrequest'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report = self.get_object()
        
        # Format report data for display
        context['formatted_data'] = self._format_report_data(report.report_data)
        
        return context

    def _format_report_data(self, data):
        """Format report data for better display"""
        if not data:
            return {}
        
        formatted = {
            'summary': data.get('summary', {}),
            'class_breakdown': [],
            'monthly_breakdown': [],
            'top_reasons': data.get('top_reasons', []),
        }
        
        # Format class breakdown
        for class_name, stats in data.get('class_breakdown', {}).items():
            formatted['class_breakdown'].append({
                'class_name': class_name,
                'stats': stats
            })
        
        # Format monthly breakdown
        for month, stats in data.get('monthly_breakdown', {}).items():
            try:
                month_date = datetime.strptime(month, '%Y-%m')
                formatted['monthly_breakdown'].append({
                    'month': month_date.strftime('%B %Y'),
                    'stats': stats
                })
            except ValueError:
                continue
        
        return formatted


class VacationNotificationView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """
    View for managing vacation notifications
    """
    template_name = 'students/vacation_notifications.html'
    permission_required = 'students.change_vacationrequest'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get upcoming vacations that need reminders
        upcoming_vacations = VacationRequest.objects.filter(
            status='approved',
            start_date__gte=date.today(),
            start_date__lte=date.today() + timedelta(days=7)
        ).select_related('student', 'student__parent')
        
        context['upcoming_vacations'] = upcoming_vacations
        
        # Get pending requests that need attention
        context['pending_requests'] = VacationRequest.objects.filter(
            status='pending',
            created_at__lte=timezone.now() - timedelta(days=2)
        ).select_related('student', 'requested_by')
        
        return context

    def post(self, request, *args, **kwargs):
        """Handle notification sending"""
        action = request.POST.get('action')
        vacation_ids = request.POST.getlist('vacation_ids')
        
        if action == 'send_reminders' and vacation_ids:
            sent_count = 0
            for vacation_id in vacation_ids:
                try:
                    vacation = VacationRequest.objects.get(id=vacation_id)
                    if vacation.send_notification('reminder'):
                        sent_count += 1
                except VacationRequest.DoesNotExist:
                    continue
            
            messages.success(
                request,
                _('Sent {} reminder notifications.').format(sent_count)
            )
        
        return redirect('students:vacation_notifications')