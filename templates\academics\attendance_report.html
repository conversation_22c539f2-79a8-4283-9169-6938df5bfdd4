{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Attendance Report" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .attendance-report-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .attendance-report-card:hover {
        transform: translateY(-2px);
    }
    .report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .attendance-summary {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        margin: 20px 0;
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .attendance-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85em;
        font-weight: bold;
    }
    .status-present { background-color: #d4edda; color: #155724; }
    .status-absent { background-color: #f8d7da; color: #721c24; }
    .status-late { background-color: #fff3cd; color: #856404; }
    .status-excused { background-color: #d1ecf1; color: #0c5460; }
    .chart-container {
        position: relative;
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Attendance Report" %}
                    </h1>
                    <p class="text-muted">{% trans "Comprehensive attendance analysis and statistics" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>{% trans "Export Report" %}
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printReport()">
                        <i class="fas fa-print me-2"></i>{% trans "Print" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filter-section">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="class_filter" class="form-label">{% trans "Class" %}</label>
                        <select name="class" id="class_filter" class="form-select">
                            <option value="">{% trans "All Classes" %}</option>
                            {% for class in classes %}
                                <option value="{{ class.id }}" {% if selected_class and class.id == selected_class.id %}selected{% endif %}>
                                    {{ class.grade.name }} - {{ class.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" value="{{ date_from|default:'' }}">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" value="{{ date_to|default:'' }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>{% trans "Generate Report" %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card attendance-report-card">
                <div class="card-body text-center">
                    <i class="fas fa-users text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">{{ total_students|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Students" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-report-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">{{ average_attendance|floatformat:1|default:0 }}%</h4>
                    <p class="mb-0">{% trans "Average Attendance" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-report-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-check text-info mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-info">{{ present_today|default:0 }}</h4>
                    <p class="mb-0">{% trans "Present Today" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-report-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-times text-danger mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-danger">{{ absent_today|default:0 }}</h4>
                    <p class="mb-0">{% trans "Absent Today" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card attendance-report-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Attendance Trends" %}</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="attendanceTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card attendance-report-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Attendance Distribution" %}</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="attendanceDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Attendance Table -->
    <div class="row">
        <div class="col-12">
            <div class="card attendance-report-card">
                <div class="card-header report-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>{% trans "Detailed Attendance Records" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Class" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Check In" %}</th>
                                    <th>{% trans "Check Out" %}</th>
                                    <th>{% trans "Remarks" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if attendance_records %}
                                    {% for record in attendance_records %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {% if record.student.user.profile_picture %}
                                                        <img src="{{ record.student.user.profile_picture.url }}" alt="{{ record.student.user.get_full_name }}" class="rounded-circle me-2" style="width: 30px; height: 30px;">
                                                    {% else %}
                                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <h6 class="mb-0">{{ record.student.user.first_name }} {{ record.student.user.last_name }}</h6>
                                                        <small class="text-muted">{{ record.student.student_id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ record.student.current_class.grade.name }} - {{ record.student.current_class.name }}</td>
                                            <td>{{ record.date|date:"M d, Y" }}</td>
                                            <td>
                                                <span class="attendance-status 
                                                    {% if record.status == 'present' %}status-present
                                                    {% elif record.status == 'absent' %}status-absent
                                                    {% elif record.status == 'late' %}status-late
                                                    {% elif record.status == 'excused' %}status-excused
                                                    {% endif %}">
                                                    {{ record.get_status_display }}
                                                </span>
                                            </td>
                                            <td>{{ record.check_in_time|time:"H:i"|default:"-" }}</td>
                                            <td>{{ record.check_out_time|time:"H:i"|default:"-" }}</td>
                                            <td>{{ record.remarks|default:"-" }}</td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No attendance records found matching your criteria." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Attendance Trends Chart
    const trendsCtx = document.getElementById('attendanceTrendsChart').getContext('2d');
    const trendsChart = new Chart(trendsCtx, {
        type: 'line',
        data: {
            labels: {{ trend_labels|safe|default:"[]" }},
            datasets: [{
                label: '{% trans "Attendance Rate %" %}',
                data: {{ trend_data|safe|default:"[]" }},
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Attendance Distribution Chart
    const distributionCtx = document.getElementById('attendanceDistributionChart').getContext('2d');
    const distributionChart = new Chart(distributionCtx, {
        type: 'doughnut',
        data: {
            labels: ['{% trans "Present" %}', '{% trans "Absent" %}', '{% trans "Late" %}', '{% trans "Excused" %}'],
            datasets: [{
                data: [
                    {{ present_count|default:0 }},
                    {{ absent_count|default:0 }},
                    {{ late_count|default:0 }},
                    {{ excused_count|default:0 }}
                ],
                backgroundColor: [
                    '#28a745',
                    '#dc3545',
                    '#ffc107',
                    '#17a2b8'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    function exportReport() {
        const params = new URLSearchParams(window.location.search);
        window.location.href = '{% url "academics:attendance_export" %}?' + params.toString();
    }

    function printReport() {
        window.print();
    }

    // Auto-submit form on filter change
    document.getElementById('class_filter').addEventListener('change', function() {
        this.form.submit();
    });

    // Set default dates
    document.addEventListener('DOMContentLoaded', function() {
        const dateFrom = document.getElementById('date_from');
        const dateTo = document.getElementById('date_to');
        
        if (!dateFrom.value) {
            const firstDayOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
            dateFrom.value = firstDayOfMonth.toISOString().split('T')[0];
        }
        
        if (!dateTo.value) {
            const today = new Date();
            dateTo.value = today.toISOString().split('T')[0];
        }
    });
</script>
{% endblock %}
