{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Leave Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .leave-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .leave-card:hover {
        transform: translateY(-2px);
    }
    .leave-header {
        background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .leave-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .status-approved {
        background-color: #d4edda;
        color: #155724;
    }
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }
    .status-rejected {
        background-color: #f8d7da;
        color: #721c24;
    }
    .status-cancelled {
        background-color: #e2e3e5;
        color: #383d41;
    }
    .leave-type {
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    .type-annual {
        background-color: #cce5ff;
        color: #004085;
    }
    .type-sick {
        background-color: #ffe6e6;
        color: #721c24;
    }
    .type-emergency {
        background-color: #fff0e6;
        color: #856404;
    }
    .type-maternity {
        background-color: #e6f3ff;
        color: #0c5460;
    }
    .employee-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
    }
    .leave-balance {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
    }
    .balance-item {
        margin-bottom: 1rem;
    }
    .balance-number {
        font-size: 2rem;
        font-weight: bold;
        display: block;
    }
    .balance-label {
        font-size: 0.875rem;
        opacity: 0.9;
    }
    .calendar-widget {
        background: white;
        border-radius: 15px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .calendar-day {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 1px;
        font-size: 0.875rem;
        cursor: pointer;
    }
    .calendar-day.leave-day {
        background-color: #fc466b;
        color: white;
    }
    .calendar-day.today {
        border: 2px solid #007bff;
        font-weight: bold;
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-calendar-times text-primary me-2"></i>{% trans "Leave Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage employee leave requests and vacation balances" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#newLeaveModal">
                        <i class="fas fa-plus me-2"></i>{% trans "New Leave Request" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export Report" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card leave-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h3 class="mb-1">{{ pending_requests|default:0 }}</h3>
                    <p class="mb-0">{% trans "Pending Requests" %}</p>
                    <small class="text-muted">{% trans "Awaiting approval" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card leave-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h3 class="mb-1">{{ approved_requests|default:0 }}</h3>
                    <p class="mb-0">{% trans "Approved This Month" %}</p>
                    <small class="text-muted">{% trans "Total approved" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card leave-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-times fa-2x text-info mb-2"></i>
                    <h3 class="mb-1">{{ employees_on_leave|default:0 }}</h3>
                    <p class="mb-0">{% trans "Currently on Leave" %}</p>
                    <small class="text-muted">{% trans "Active leaves" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card leave-card">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                    <h3 class="mb-1">{{ rejected_requests|default:0 }}</h3>
                    <p class="mb-0">{% trans "Rejected This Month" %}</p>
                    <small class="text-muted">{% trans "Total rejected" %}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Leave Balance and Calendar -->
        <div class="col-lg-4 mb-4">
            <!-- Leave Balance -->
            <div class="leave-balance mb-4">
                <h5 class="mb-4">
                    <i class="fas fa-chart-pie me-2"></i>{% trans "Leave Balance Overview" %}
                </h5>
                <div class="row">
                    <div class="col-6">
                        <div class="balance-item">
                            <span class="balance-number">{{ annual_leave_balance|default:0 }}</span>
                            <span class="balance-label">{% trans "Annual Leave" %}</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="balance-item">
                            <span class="balance-number">{{ sick_leave_balance|default:0 }}</span>
                            <span class="balance-label">{% trans "Sick Leave" %}</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="balance-item">
                            <span class="balance-number">{{ emergency_leave_balance|default:0 }}</span>
                            <span class="balance-label">{% trans "Emergency Leave" %}</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="balance-item">
                            <span class="balance-number">{{ total_used|default:28 }}</span>
                            <span class="balance-label">{% trans "Total Used" %}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card leave-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#bulkApprovalModal">
                            <i class="fas fa-check-double me-2"></i>{% trans "Bulk Approval" %}
                        </button>
                        <button class="btn btn-outline-success">
                            <i class="fas fa-chart-bar me-2"></i>{% trans "Leave Report" %}
                        </button>
                        <button class="btn btn-outline-info">
                            <i class="fas fa-calendar-alt me-2"></i>{% trans "Leave Calendar" %}
                        </button>
                        <button class="btn btn-outline-warning">
                            <i class="fas fa-bell me-2"></i>{% trans "Send Reminders" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Leave Requests List -->
        <div class="col-lg-8">
            <!-- Filters -->
            <div class="filter-section">
                <h5 class="mb-3">{% trans "Filter Leave Requests" %}</h5>
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <select class="form-select" name="status">
                            <option value="">{% trans "All Status" %}</option>
                            <option value="pending">{% trans "Pending" %}</option>
                            <option value="approved">{% trans "Approved" %}</option>
                            <option value="rejected">{% trans "Rejected" %}</option>
                            <option value="cancelled">{% trans "Cancelled" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="leave_type">
                            <option value="">{% trans "All Types" %}</option>
                            <option value="annual">{% trans "Annual Leave" %}</option>
                            <option value="sick">{% trans "Sick Leave" %}</option>
                            <option value="emergency">{% trans "Emergency Leave" %}</option>
                            <option value="maternity">{% trans "Maternity Leave" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="department">
                            <option value="">{% trans "All Departments" %}</option>
                            <option value="teaching">{% trans "Teaching" %}</option>
                            <option value="administration">{% trans "Administration" %}</option>
                            <option value="support">{% trans "Support Staff" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                        </button>
                    </div>
                </form>
            </div>

            <!-- Leave Requests Table -->
            <div class="card leave-card">
                <div class="leave-header card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "Leave Requests" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Employee" %}</th>
                                    <th>{% trans "Leave Type" %}</th>
                                    <th>{% trans "Start Date" %}</th>
                                    <th>{% trans "End Date" %}</th>
                                    <th>{% trans "Days" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if leave_requests %}
                                    {% for request in leave_requests %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {% if request.employee.user.profile_picture %}
                                                        <img src="{{ request.employee.user.profile_picture.url }}" alt="{{ request.employee.user.get_full_name }}" class="employee-avatar me-3">
                                                    {% else %}
                                                        <div class="employee-avatar bg-secondary d-flex align-items-center justify-content-center me-3">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <h6 class="mb-1">{{ request.employee.user.first_name }} {{ request.employee.user.last_name }}</h6>
                                                        <small class="text-muted">{{ request.employee.employee_id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="leave-type
                                                    {% if request.leave_type.name|lower == 'annual' or 'annual' in request.leave_type.name|lower %}type-annual
                                                    {% elif request.leave_type.name|lower == 'sick' or 'sick' in request.leave_type.name|lower %}type-sick
                                                    {% elif request.leave_type.name|lower == 'emergency' or 'emergency' in request.leave_type.name|lower %}type-emergency
                                                    {% elif request.leave_type.name|lower == 'maternity' or 'maternity' in request.leave_type.name|lower %}type-maternity
                                                    {% else %}type-annual{% endif %}">
                                                    {{ request.leave_type.name }}
                                                </span>
                                            </td>
                                            <td>{{ request.start_date|date:"M d, Y" }}</td>
                                            <td>{{ request.end_date|date:"M d, Y" }}</td>
                                            <td>{{ request.duration_days|default:0 }}</td>
                                            <td>
                                                <span class="leave-status
                                                    {% if request.status == 'pending' %}status-pending
                                                    {% elif request.status == 'approved' %}status-approved
                                                    {% elif request.status == 'rejected' %}status-rejected
                                                    {% elif request.status == 'cancelled' %}status-cancelled
                                                    {% else %}status-pending{% endif %}">
                                                    {% if request.status == 'pending' %}{% trans "Pending" %}
                                                    {% elif request.status == 'approved' %}{% trans "Approved" %}
                                                    {% elif request.status == 'rejected' %}{% trans "Rejected" %}
                                                    {% elif request.status == 'cancelled' %}{% trans "Cancelled" %}
                                                    {% else %}{{ request.status }}{% endif %}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'hr:leave_detail' request.id %}" class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    {% if request.status == 'pending' %}
                                                        <a href="{% url 'hr:leave_approve' request.id %}" class="btn btn-outline-success" title="{% trans 'Approve' %}">
                                                            <i class="fas fa-check"></i>
                                                        </a>
                                                        <a href="{% url 'hr:leave_reject' request.id %}" class="btn btn-outline-danger" title="{% trans 'Reject' %}">
                                                            <i class="fas fa-times"></i>
                                                        </a>
                                                    {% else %}
                                                        <a href="{% url 'hr:leave_update' request.id %}" class="btn btn-outline-info" title="{% trans 'Edit' %}">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No leave requests found. Create your first leave request to get started." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Leave requests pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <span class="page-link">{% trans "Previous" %}</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">{% trans "Next" %}</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Leave Request Modal -->
<div class="modal fade" id="newLeaveModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "New Leave Request" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="leaveEmployee" class="form-label">{% trans "Employee" %}</label>
                            <select class="form-select" id="leaveEmployee" required>
                                <option value="">{% trans "Select Employee" %}</option>
                                {% for employee in employees %}
                                    <option value="{{ employee.id }}">{{ employee.user.first_name }} {{ employee.user.last_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="leaveType" class="form-label">{% trans "Leave Type" %}</label>
                            <select class="form-select" id="leaveType" required>
                                <option value="">{% trans "Select Type" %}</option>
                                <option value="annual">{% trans "Annual Leave" %}</option>
                                <option value="sick">{% trans "Sick Leave" %}</option>
                                <option value="emergency">{% trans "Emergency Leave" %}</option>
                                <option value="maternity">{% trans "Maternity Leave" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="startDate" class="form-label">{% trans "Start Date" %}</label>
                            <input type="date" class="form-control" id="startDate" required>
                        </div>
                        <div class="col-md-6">
                            <label for="endDate" class="form-label">{% trans "End Date" %}</label>
                            <input type="date" class="form-control" id="endDate" required>
                        </div>
                        <div class="col-12">
                            <label for="leaveReason" class="form-label">{% trans "Reason" %}</label>
                            <textarea class="form-control" id="leaveReason" rows="3" required placeholder="{% trans 'Please provide reason for leave...' %}"></textarea>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="halfDay">
                                <label class="form-check-label" for="halfDay">
                                    {% trans "Half day leave" %}
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Submit Request" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Leave Details Modal -->
<div class="modal fade" id="leaveDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Leave Request Details" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <strong>{% trans "Employee:" %}</strong><br>
                        أحمد محمد علي (EMP001)
                    </div>
                    <div class="col-md-6">
                        <strong>{% trans "Leave Type:" %}</strong><br>
                        {% trans "Annual Leave" %}
                    </div>
                    <div class="col-md-6">
                        <strong>{% trans "Start Date:" %}</strong><br>
                        July 15, 2024
                    </div>
                    <div class="col-md-6">
                        <strong>{% trans "End Date:" %}</strong><br>
                        July 19, 2024
                    </div>
                    <div class="col-md-6">
                        <strong>{% trans "Total Days:" %}</strong><br>
                        5 days
                    </div>
                    <div class="col-md-6">
                        <strong>{% trans "Status:" %}</strong><br>
                        <span class="leave-status status-pending">{% trans "Pending" %}</span>
                    </div>
                    <div class="col-12">
                        <strong>{% trans "Reason:" %}</strong><br>
                        {% trans "Family vacation and personal matters that require my attention." %}
                    </div>
                    <div class="col-12">
                        <strong>{% trans "Applied On:" %}</strong><br>
                        July 10, 2024 at 2:30 PM
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <button type="button" class="btn btn-success">{% trans "Approve" %}</button>
                <button type="button" class="btn btn-danger">{% trans "Reject" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Approve leave function
function approveLeave(leaveId) {
    if (confirm('{% trans "Are you sure you want to approve this leave request?" %}')) {
        // Here you would make an AJAX call to approve the leave
        alert('{% trans "Leave request approved successfully!" %}');
        location.reload();
    }
}

// Reject leave function
function rejectLeave(leaveId) {
    const reason = prompt('{% trans "Please provide reason for rejection:" %}');
    if (reason) {
        // Here you would make an AJAX call to reject the leave
        alert('{% trans "Leave request rejected successfully!" %}');
        location.reload();
    }
}

// Auto-submit filters
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('select[name="status"], select[name="leave_type"], select[name="department"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Calculate days when dates change
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    
    function calculateDays() {
        if (startDate.value && endDate.value) {
            const start = new Date(startDate.value);
            const end = new Date(endDate.value);
            const diffTime = Math.abs(end - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
            
            // You could display the calculated days somewhere
            console.log('Total days:', diffDays);
        }
    }
    
    if (startDate && endDate) {
        startDate.addEventListener('change', calculateDays);
        endDate.addEventListener('change', calculateDays);
    }
});
</script>
{% endblock %}
