{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Student Documents" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-file-alt text-primary me-2"></i>{% trans "Student Documents" %}
                            </h2>
                            <p class="text-muted">{% trans "Manage student documents and files" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:add_document' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Upload Document" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter options -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <label for="student" class="form-label">{% trans "Student" %}</label>
                                    <select class="form-select" id="student" name="student">
                                        <option value="">{% trans "All Students" %}</option>
                                        <!-- Add student options here -->
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="type" class="form-label">{% trans "Document Type" %}</label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="">{% trans "All Types" %}</option>
                                        <option value="birth_certificate">{% trans "Birth Certificate" %}</option>
                                        <option value="passport">{% trans "Passport" %}</option>
                                        <option value="medical_record">{% trans "Medical Record" %}</option>
                                        <option value="previous_school_record">{% trans "Previous School Record" %}</option>
                                        <option value="photo">{% trans "Photo" %}</option>
                                        <option value="vaccination_record">{% trans "Vaccination Record" %}</option>
                                        <option value="other">{% trans "Other" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter me-2"></i>{% trans "Apply Filters" %}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Documents" %}</h5>
                        </div>
                        <div class="card-body">
                            {% if documents %}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Student" %}</th>
                                                <th>{% trans "Document Type" %}</th>
                                                <th>{% trans "Title" %}</th>
                                                <th>{% trans "Uploaded By" %}</th>
                                                <th>{% trans "Upload Date" %}</th>
                                                <th>{% trans "Status" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for document in documents %}
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                                <span class="text-white fw-bold">{{ document.student.first_name|slice:":1" }}{{ document.student.last_name|slice:":1" }}</span>
                                                            </div>
                                                            <div>
                                                                <h6 class="mb-0">{{ document.student.full_name }}</h6>
                                                                <small class="text-muted">{{ document.student.student_id }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">{{ document.get_document_type_display }}</span>
                                                    </td>
                                                    <td>{{ document.title }}</td>
                                                    <td>{{ document.uploaded_by.get_full_name }}</td>
                                                    <td>{{ document.created_at|date:"d/m/Y H:i" }}</td>
                                                    <td>
                                                        {% if document.is_verified %}
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-check me-1"></i>{% trans "Verified" %}
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-warning">
                                                                <i class="fas fa-clock me-1"></i>{% trans "Pending" %}
                                                            </span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="{{ document.file.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="{{ document.file.url }}" download class="btn btn-sm btn-outline-success">
                                                                <i class="fas fa-download"></i>
                                                            </a>
                                                            {% if not document.is_verified %}
                                                                <button type="button" class="btn btn-sm btn-outline-info">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            {% endif %}
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>{% trans "No documents found." %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}