/* School ERP Custom Styles */

:root {
    --primary-color: #4c63d2;
    --secondary-color: #6c5ce7;
    --success-color: #00b894;
    --danger-color: #e17055;
    --warning-color: #fdcb6e;
    --info-color: #0984e3;
    --light-color: #f8f9fa;
    --dark-color: #2d3436;
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --text-color: #2d3436;
    --text-muted: #636e72;
    --border-color: #ddd;
    --focus-color: #4c63d2;
    --hover-bg: #f1f2f6;
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .navbar-brand {
    margin-right: 0;
    margin-left: 1rem;
}

[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f6fa;
    line-height: 1.6;
    color: var(--text-color);
    font-size: 16px;
}

/* Arabic Font Support */
[lang="ar"], [dir="rtl"] {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

/* Navigation */
.navbar {
    box-shadow: var(--box-shadow);
    border-bottom: 3px solid var(--secondary-color);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: 8px;
    margin: 0 4px;
    padding: 0.5rem 1rem !important;
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 14px;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    color: white !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-nav .nav-link:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: var(--border-radius);
}

.dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 14px;
    color: var(--text-color);
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--hover-bg);
    color: var(--primary-color);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-bottom: none;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    border: 2px solid transparent;
    padding: 0.75rem 1.5rem;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: 3px solid rgba(76, 99, 210, 0.5);
    outline-offset: 2px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(76, 99, 210, 0.4);
    background: linear-gradient(135deg, #3d52c7 0%, #5a4bdb 100%);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 184, 148, 0.4);
    background-color: #00a085;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(225, 112, 85, 0.4);
    background-color: #d63031;
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--text-color);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(253, 203, 110, 0.4);
    background-color: #f39c12;
    color: white;
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: white;
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(9, 132, 227, 0.4);
    background-color: #0770c4;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(76, 99, 210, 0.3);
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(108, 117, 125, 0.3);
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
    transition: var(--transition);
    padding: 0.75rem 1rem;
    font-size: 16px;
    color: var(--text-color);
    background-color: #ffffff;
}

.form-control:focus {
    border-color: var(--focus-color);
    box-shadow: 0 0 0 0.25rem rgba(76, 99, 210, 0.25);
    outline: none;
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.8;
}

.form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 14px;
}

.form-text {
    color: var(--text-muted);
    font-size: 13px;
}

/* Enhanced form validation styles */
.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.25rem rgba(225, 112, 85, 0.25);
}

.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.25rem rgba(0, 184, 148, 0.25);
}

.invalid-feedback {
    color: var(--danger-color);
    font-weight: 500;
    font-size: 13px;
}

.valid-feedback {
    color: var(--success-color);
    font-weight: 500;
    font-size: 13px;
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    background-color: #ffffff;
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1rem;
    font-size: 14px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: var(--hover-bg);
    transform: translateY(-1px);
}

.table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.table tbody tr:nth-child(even):hover {
    background-color: var(--hover-bg);
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
    color: var(--text-color);
    font-size: 14px;
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 500;
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Badges */
.badge {
    border-radius: 20px;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

/* Loading Spinner */
.spinner-border {
    color: var(--primary-color);
}

/* Custom Utilities */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.shadow-custom {
    box-shadow: var(--box-shadow);
}

.border-radius-custom {
    border-radius: var(--border-radius);
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .alert {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #2c3e50;
        --dark-color: #ecf0f1;
    }
    
    body {
        background-color: #2c3e50;
        color: #ecf0f1;
    }
    
    .card {
        background-color: #34495e;
        color: #ecf0f1;
    }
    
    .form-control {
        background-color: #34495e;
        border-color: #4a5f7a;
        color: #ecf0f1;
    }
    
    .table {
        background-color: #34495e;
        color: #ecf0f1;
    }
}

/* Modern Navigation System */
.section-nav-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 0;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-nav {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.section-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border-radius: 12px;
    text-decoration: none;
    color: var(--text-color);
    transition: all 0.3s ease;
    background: white;
    border: 2px solid transparent;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 100px;
    position: relative;
    overflow: hidden;
}

.section-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.section-nav-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: var(--primary-color);
}

.section-nav-item:hover::before {
    transform: scaleX(1);
}

.section-nav-item.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 99, 210, 0.3);
}

.section-nav-item.active::before {
    transform: scaleX(1);
    background: white;
}

.section-nav-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    transition: transform 0.3s ease;
}

.section-nav-item:hover .section-nav-icon {
    transform: scale(1.1);
}

.section-nav-label {
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    line-height: 1.2;
}

/* Modern Breadcrumb */
.modern-breadcrumb {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 10px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.modern-breadcrumb .breadcrumb-item {
    font-size: 14px;
    font-weight: 500;
}

.modern-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--text-muted);
    font-weight: bold;
    font-size: 1.2rem;
}

.breadcrumb-link {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.breadcrumb-link:hover {
    background-color: var(--hover-bg);
    color: var(--secondary-color);
    text-decoration: none;
}

.modern-breadcrumb .breadcrumb-item.active {
    color: var(--text-color);
    font-weight: 600;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.page-header h1 {
    margin: 0;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.page-header p {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Responsive Navigation */
@media (max-width: 768px) {
    .section-nav {
        gap: 0.5rem;
    }

    .section-nav-item {
        min-width: 80px;
        padding: 0.75rem 0.5rem;
    }

    .section-nav-icon {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }

    .section-nav-label {
        font-size: 0.75rem;
    }

    .modern-breadcrumb {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }

    .page-header {
        padding: 1.5rem 0;
        text-align: center;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Enhanced Accessibility Styles */
.btn:focus,
.form-control:focus,
.nav-link:focus,
.dropdown-item:focus {
    outline: 3px solid var(--focus-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 0.25rem rgba(76, 99, 210, 0.25);
}

/* Skip to main content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    font-weight: 600;
}

.skip-link:focus {
    top: 6px;
    outline: 3px solid white;
}

/* Enhanced text contrast */
.text-muted {
    color: var(--text-muted) !important;
    font-weight: 500;
}

.small, small {
    color: var(--text-muted);
    font-weight: 500;
}

/* Status badges with better contrast */
.badge {
    font-weight: 600;
    font-size: 12px;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
}

.badge.bg-success {
    background-color: var(--success-color) !important;
    color: white;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
    color: white;
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
    color: var(--text-color);
}

.badge.bg-info {
    background-color: var(--info-color) !important;
    color: white;
}

.badge.bg-primary {
    background-color: var(--primary-color) !important;
    color: white;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
    color: white;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --secondary-color: #800080;
        --success-color: #008000;
        --danger-color: #ff0000;
        --warning-color: #ff8c00;
        --info-color: #0000ff;
        --text-color: #000000;
        --border-color: #000000;
    }

    .card {
        border: 2px solid #000;
    }

    .btn {
        border: 2px solid #000;
    }

    .form-control {
        border: 2px solid #000;
    }

    .table {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .btn:hover,
    .card:hover,
    .table tbody tr:hover {
        transform: none;
    }
}
