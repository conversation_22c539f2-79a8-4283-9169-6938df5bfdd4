"""
Complete integration tests for core module multi-tenancy and authentication
"""
from datetime import date, timedelta
from django.test import TestCase, TransactionTestCase, Client
from django.contrib.auth import get_user_model, authenticate
from django.urls import reverse
from django.core.management import call_command
from django.test.utils import override_settings
from unittest.mock import patch
from .models import School, AcademicYear, Semester, AuditLog
from .backends import SchoolAuthBackend
from .permissions import has_school_permission
from .utils import IDGenerator, SecurityUtils, DateUtils

User = get_user_model()


class CompleteMultiTenancyTest(TransactionTestCase):
    """Complete integration test for multi-tenancy functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.school1 = School.objects.create(
            name='Test School 1',
            code='TEST001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Principal 1',
            established_date=date(2020, 1, 1)
        )
        
        self.school2 = School.objects.create(
            name='Test School 2',
            code='TEST002',
            address='456 Test Avenue',
            phone='+1234567891',
            email='<EMAIL>',
            principal_name='Principal 2',
            established_date=date(2020, 1, 1)
        )
    
    def test_multi_tenancy_isolation(self):
        """Test that schools are properly isolated"""
        # Create academic years for both schools
        ay1 = AcademicYear.objects.create(
            school=self.school1,
            name='2023-2024',
            start_date=date(2023, 9, 1),
            end_date=date(2024, 6, 30),
            is_current=True
        )
        
        ay2 = AcademicYear.objects.create(
            school=self.school2,
            name='2023-2024',
            start_date=date(2023, 9, 1),
            end_date=date(2024, 6, 30),
            is_current=True
        )
        
        # Verify isolation
        school1_years = AcademicYear.objects.filter(school=self.school1)
        school2_years = AcademicYear.objects.filter(school=self.school2)
        
        self.assertEqual(school1_years.count(), 1)
        self.assertEqual(school2_years.count(), 1)
        self.assertEqual(school1_years.first(), ay1)
        self.assertEqual(school2_years.first(), ay2)