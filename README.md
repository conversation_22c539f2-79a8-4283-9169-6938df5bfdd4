# 🏫 School ERP System

A comprehensive Django-based School Enterprise Resource Planning (ERP) system with Arabic translation support, offline capabilities, and multi-device synchronization.

## ✨ Features

### 🎯 Core Modules
- **Admin Area**: System settings, school information, backup & sync
- **Student Affairs**: Registration, data management, documents, transfers
- **Academic Management**: Classes, subjects, schedules, grades, exams
- **Human Resources**: Employee management, attendance, payroll
- **Financial Management**: Accounting, fees, payments, reports
- **Reports & Analytics**: Comprehensive reporting with charts and statistics

### 🌐 Internationalization
- **Arabic Translation**: Complete RTL (Right-to-Left) support
- **Bilingual Interface**: English and Arabic languages
- **Localized Content**: Date formats, number formats, and cultural adaptations

### 📱 Offline Capabilities
- **Service Workers**: Offline functionality for critical operations
- **Data Synchronization**: Multi-device sync when online
- **Local Storage**: Important data cached locally
- **Conflict Resolution**: Smart merge strategies for data conflicts

### 🔧 Technical Features
- **Django Framework**: Following Django style guide and best practices
- **REST API**: Complete API for mobile and external integrations
- **Docker Support**: Easy deployment and scaling
- **Redis Caching**: Performance optimization and session management
- **Celery Tasks**: Background processing for heavy operations

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package manager)
- Git
- Docker (optional, for containerized deployment)

### Installation

#### Option 1: Automated Installation (Recommended)

**Linux/macOS:**
```bash
chmod +x install.sh
./install.sh
```

**Windows:**
```cmd
install.bat
```

#### Option 2: Manual Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd school-onlinepro
```

2. **Install dependencies:**
```bash
pip install pipenv
pipenv install
```

3. **Set up environment:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Run migrations:**
```bash
pipenv run python manage.py migrate
```

5. **Create superuser:**
```bash
pipenv run python manage.py createsuperuser
```

6. **Start development server:**
```bash
pipenv run python manage.py runserver
```

### Docker Deployment

1. **Build and run with Docker Compose:**
```bash
docker-compose up --build
```

2. **Access the application:**
- Web Interface: http://localhost:8000
- Admin Panel: http://localhost:8000/admin

## 📁 Project Structure

```
school-onlinepro/
├── accounts/           # User management and authentication
├── students/           # Student affairs and management
├── academics/          # Academic modules (classes, subjects, etc.)
├── hr/                # Human resources management
├── finance/           # Financial and accounting modules
├── reports/           # Reporting and analytics
├── core/              # Core utilities and base models
├── templates/         # HTML templates
├── static/            # Static files (CSS, JS, images)
├── media/             # User uploaded files
├── locale/            # Translation files
├── school_erp/        # Main project settings
├── docker-compose.yml # Docker configuration
├── Dockerfile         # Docker image definition
├── Pipfile           # Python dependencies
├── install.sh        # Linux/macOS installation script
├── install.bat       # Windows installation script
└── README.md         # This file
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the project root:

```env
DEBUG=True
SECRET_KEY=your-secret-key-here
DATABASE_URL=postgresql://user:password@localhost:5432/school_erp
REDIS_URL=redis://localhost:6379/0
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
```

### Database Configuration

**SQLite (Development):**
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}
```

**PostgreSQL (Production):**
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'school_erp',
        'USER': 'school_user',
        'PASSWORD': 'school_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

## 🌐 Internationalization

### Adding New Languages

1. **Add language to settings:**
```python
LANGUAGES = [
    ('en', 'English'),
    ('ar', 'العربية'),
    ('fr', 'Français'),  # New language
]
```

2. **Generate translation files:**
```bash
pipenv run python manage.py makemessages -l fr
```

3. **Translate strings in locale/fr/LC_MESSAGES/django.po**

4. **Compile translations:**
```bash
pipenv run python manage.py compilemessages
```

## 📱 Offline Mode

The system includes service workers for offline functionality:

- **Cached Resources**: Critical CSS, JS, and HTML files
- **Offline Forms**: Important forms work offline and sync when online
- **Data Storage**: IndexedDB for offline data storage
- **Sync Indicators**: Visual indicators for sync status

## 🔄 Multi-Device Sync

### Sync Features
- **Real-time Updates**: WebSocket connections for live updates
- **Conflict Resolution**: Automatic and manual conflict resolution
- **Sync Queue**: Queued operations for offline devices
- **Data Integrity**: Checksums and validation for data consistency

## 🧪 Testing

Run the test suite:
```bash
pipenv run python manage.py test
```

Run with coverage:
```bash
pipenv run coverage run --source='.' manage.py test
pipenv run coverage report
```

## 📊 Monitoring and Logging

### Log Files
- **Application Logs**: `logs/django.log`
- **Error Logs**: `logs/error.log`
- **Access Logs**: `logs/access.log`

### Monitoring
- **Health Checks**: `/health/` endpoint
- **Metrics**: Prometheus metrics available
- **Performance**: Django Debug Toolbar in development

## 🚀 Deployment

### Production Checklist
- [ ] Set `DEBUG=False`
- [ ] Configure proper `SECRET_KEY`
- [ ] Set up PostgreSQL database
- [ ] Configure Redis for caching
- [ ] Set up SSL/HTTPS
- [ ] Configure email settings
- [ ] Set up backup strategy
- [ ] Configure monitoring

### Docker Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Contact the development team

## 🔄 Updates and Migration

The system includes automatic update mechanisms:
- **Database Migrations**: Automatic schema updates
- **Data Migration**: Safe data transformation scripts
- **Backup Before Update**: Automatic backups before major updates
- **Rollback Support**: Easy rollback to previous versions
