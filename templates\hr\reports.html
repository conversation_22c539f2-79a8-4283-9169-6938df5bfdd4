{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "HR Reports" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .report-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s, box-shadow 0.2s;
        cursor: pointer;
    }
    .report-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }
    .report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .report-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.8;
    }
    .report-category {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
    }
    .quick-stats {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        margin-bottom: 30px;
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .report-preview {
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "HR Reports" %}
                    </h1>
                    <p class="text-muted">{% trans "Comprehensive human resources reporting and analytics" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#customReportModal">
                        <i class="fas fa-plus me-2"></i>{% trans "Custom Report" %}
                    </button>
                    <button class="btn btn-outline-secondary" onclick="exportAllReports()">
                        <i class="fas fa-download me-2"></i>{% trans "Export All" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="quick-stats">
                <div class="row">
                    <div class="col-md-3">
                        <h3>{{ total_employees|default:0 }}</h3>
                        <p class="mb-0">{% trans "Total Employees" %}</p>
                    </div>
                    <div class="col-md-3">
                        <h3>{{ active_employees|default:0 }}</h3>
                        <p class="mb-0">{% trans "Active Employees" %}</p>
                    </div>
                    <div class="col-md-3">
                        <h3>{{ departments_count|default:0 }}</h3>
                        <p class="mb-0">{% trans "Departments" %}</p>
                    </div>
                    <div class="col-md-3">
                        <h3>{{ pending_leaves|default:0 }}</h3>
                        <p class="mb-0">{% trans "Pending Leaves" %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Reports -->
    <div class="report-category">
        <h4 class="mb-4">
            <i class="fas fa-users me-2"></i>{% trans "Employee Reports" %}
        </h4>
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('employee_list')">
                    <div class="card-body text-center">
                        <i class="fas fa-list-ul report-icon text-primary"></i>
                        <h5>{% trans "Employee List" %}</h5>
                        <p class="text-muted">{% trans "Complete employee directory with details" %}</p>
                        <span class="badge bg-primary">{{ total_employees|default:0 }} {% trans "employees" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('employee_profile')">
                    <div class="card-body text-center">
                        <i class="fas fa-id-card report-icon text-success"></i>
                        <h5>{% trans "Employee Profiles" %}</h5>
                        <p class="text-muted">{% trans "Detailed employee information and history" %}</p>
                        <span class="badge bg-success">{% trans "Detailed" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('new_hires')">
                    <div class="card-body text-center">
                        <i class="fas fa-user-plus report-icon text-info"></i>
                        <h5>{% trans "New Hires" %}</h5>
                        <p class="text-muted">{% trans "Recently hired employees report" %}</p>
                        <span class="badge bg-info">{{ new_hires_count|default:0 }} {% trans "this month" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('terminations')">
                    <div class="card-body text-center">
                        <i class="fas fa-user-minus report-icon text-warning"></i>
                        <h5>{% trans "Terminations" %}</h5>
                        <p class="text-muted">{% trans "Employee termination records" %}</p>
                        <span class="badge bg-warning">{{ terminations_count|default:0 }} {% trans "this year" %}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Reports -->
    <div class="report-category">
        <h4 class="mb-4">
            <i class="fas fa-calendar-check me-2"></i>{% trans "Attendance Reports" %}
        </h4>
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('daily_attendance')">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-day report-icon text-primary"></i>
                        <h5>{% trans "Daily Attendance" %}</h5>
                        <p class="text-muted">{% trans "Daily attendance summary" %}</p>
                        <span class="badge bg-primary">{% trans "Today" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('monthly_attendance')">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-alt report-icon text-success"></i>
                        <h5>{% trans "Monthly Attendance" %}</h5>
                        <p class="text-muted">{% trans "Monthly attendance analysis" %}</p>
                        <span class="badge bg-success">{% trans "This Month" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('attendance_summary')">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-pie report-icon text-info"></i>
                        <h5>{% trans "Attendance Summary" %}</h5>
                        <p class="text-muted">{% trans "Overall attendance statistics" %}</p>
                        <span class="badge bg-info">{{ attendance_rate|default:0 }}% {% trans "rate" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('late_arrivals')">
                    <div class="card-body text-center">
                        <i class="fas fa-clock report-icon text-warning"></i>
                        <h5>{% trans "Late Arrivals" %}</h5>
                        <p class="text-muted">{% trans "Late arrival tracking report" %}</p>
                        <span class="badge bg-warning">{{ late_arrivals|default:0 }} {% trans "today" %}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Reports -->
    <div class="report-category">
        <h4 class="mb-4">
            <i class="fas fa-calendar-times me-2"></i>{% trans "Leave Reports" %}
        </h4>
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('leave_requests')">
                    <div class="card-body text-center">
                        <i class="fas fa-file-alt report-icon text-primary"></i>
                        <h5>{% trans "Leave Requests" %}</h5>
                        <p class="text-muted">{% trans "All leave requests and status" %}</p>
                        <span class="badge bg-primary">{{ total_leave_requests|default:0 }} {% trans "requests" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('leave_balance')">
                    <div class="card-body text-center">
                        <i class="fas fa-balance-scale report-icon text-success"></i>
                        <h5>{% trans "Leave Balance" %}</h5>
                        <p class="text-muted">{% trans "Employee leave balance report" %}</p>
                        <span class="badge bg-success">{% trans "Current" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('leave_calendar')">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar report-icon text-info"></i>
                        <h5>{% trans "Leave Calendar" %}</h5>
                        <p class="text-muted">{% trans "Monthly leave calendar view" %}</p>
                        <span class="badge bg-info">{% trans "Calendar" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('leave_trends')">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line report-icon text-warning"></i>
                        <h5>{% trans "Leave Trends" %}</h5>
                        <p class="text-muted">{% trans "Leave usage trends and patterns" %}</p>
                        <span class="badge bg-warning">{% trans "Analytics" %}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payroll Reports -->
    <div class="report-category">
        <h4 class="mb-4">
            <i class="fas fa-money-bill-wave me-2"></i>{% trans "Payroll Reports" %}
        </h4>
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('payroll_summary')">
                    <div class="card-body text-center">
                        <i class="fas fa-calculator report-icon text-primary"></i>
                        <h5>{% trans "Payroll Summary" %}</h5>
                        <p class="text-muted">{% trans "Monthly payroll summary report" %}</p>
                        <span class="badge bg-primary">${{ total_payroll|default:0 }}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('salary_slips')">
                    <div class="card-body text-center">
                        <i class="fas fa-receipt report-icon text-success"></i>
                        <h5>{% trans "Salary Slips" %}</h5>
                        <p class="text-muted">{% trans "Individual salary slip generation" %}</p>
                        <span class="badge bg-success">{% trans "Generate" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('tax_reports')">
                    <div class="card-body text-center">
                        <i class="fas fa-file-invoice-dollar report-icon text-info"></i>
                        <h5>{% trans "Tax Reports" %}</h5>
                        <p class="text-muted">{% trans "Tax deduction and compliance reports" %}</p>
                        <span class="badge bg-info">{% trans "Tax" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('benefits_report')">
                    <div class="card-body text-center">
                        <i class="fas fa-gift report-icon text-warning"></i>
                        <h5>{% trans "Benefits Report" %}</h5>
                        <p class="text-muted">{% trans "Employee benefits and allowances" %}</p>
                        <span class="badge bg-warning">{% trans "Benefits" %}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Reports -->
    <div class="report-category">
        <h4 class="mb-4">
            <i class="fas fa-chart-bar me-2"></i>{% trans "Performance Reports" %}
        </h4>
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('performance_reviews')">
                    <div class="card-body text-center">
                        <i class="fas fa-star report-icon text-primary"></i>
                        <h5>{% trans "Performance Reviews" %}</h5>
                        <p class="text-muted">{% trans "Employee performance evaluation reports" %}</p>
                        <span class="badge bg-primary">{{ performance_reviews|default:0 }} {% trans "reviews" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('training_reports')">
                    <div class="card-body text-center">
                        <i class="fas fa-graduation-cap report-icon text-success"></i>
                        <h5>{% trans "Training Reports" %}</h5>
                        <p class="text-muted">{% trans "Employee training and development" %}</p>
                        <span class="badge bg-success">{{ training_sessions|default:0 }} {% trans "sessions" %}</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card report-card" onclick="generateReport('goals_tracking')">
                    <div class="card-body text-center">
                        <i class="fas fa-bullseye report-icon text-info"></i>
                        <h5>{% trans "Goals Tracking" %}</h5>
                        <p class="text-muted">{% trans "Employee goals and achievements" %}</p>
                        <span class="badge bg-info">{{ goals_completed|default:0 }}% {% trans "completed" %}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Preview Area -->
    <div class="row">
        <div class="col-12">
            <div class="card report-card">
                <div class="card-header report-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>{% trans "Report Preview" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="report-preview" id="reportPreview">
                        <i class="fas fa-chart-bar fa-4x mb-3"></i>
                        <h4>{% trans "Select a Report" %}</h4>
                        <p class="mb-0">{% trans "Click on any report card above to generate and preview the report" %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Report Modal -->
<div class="modal fade" id="customReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Create Custom Report" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customReportForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="reportName" class="form-label">{% trans "Report Name" %}</label>
                            <input type="text" class="form-control" id="reportName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="reportType" class="form-label">{% trans "Report Type" %}</label>
                            <select class="form-select" id="reportType" required>
                                <option value="">{% trans "Select type" %}</option>
                                <option value="employee">{% trans "Employee" %}</option>
                                <option value="attendance">{% trans "Attendance" %}</option>
                                <option value="leave">{% trans "Leave" %}</option>
                                <option value="payroll">{% trans "Payroll" %}</option>
                                <option value="performance">{% trans "Performance" %}</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="dateFrom" class="form-label">{% trans "From Date" %}</label>
                            <input type="date" class="form-control" id="dateFrom">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="dateTo" class="form-label">{% trans "To Date" %}</label>
                            <input type="date" class="form-control" id="dateTo">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="reportFields" class="form-label">{% trans "Fields to Include" %}</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_name" value="name">
                                    <label class="form-check-label" for="field_name">{% trans "Name" %}</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_department" value="department">
                                    <label class="form-check-label" for="field_department">{% trans "Department" %}</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_position" value="position">
                                    <label class="form-check-label" for="field_position">{% trans "Position" %}</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_salary" value="salary">
                                    <label class="form-check-label" for="field_salary">{% trans "Salary" %}</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_hire_date" value="hire_date">
                                    <label class="form-check-label" for="field_hire_date">{% trans "Hire Date" %}</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_status" value="status">
                                    <label class="form-check-label" for="field_status">{% trans "Status" %}</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" onclick="generateCustomReport()">{% trans "Generate Report" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function generateReport(reportType) {
    const previewArea = document.getElementById('reportPreview');
    
    // Show loading state
    previewArea.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">{% trans "Generating report..." %}</p>
        </div>
    `;
    
    // Simulate report generation (replace with actual AJAX call)
    setTimeout(() => {
        const reportContent = getReportContent(reportType);
        previewArea.innerHTML = reportContent;
    }, 1500);
}

function getReportContent(reportType) {
    const reportTemplates = {
        'employee_list': `
            <h5>{% trans "Employee List Report" %}</h5>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{% trans "Name" %}</th>
                            <th>{% trans "Department" %}</th>
                            <th>{% trans "Position" %}</th>
                            <th>{% trans "Status" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td colspan="4" class="text-center">{% trans "Loading employee data..." %}</td></tr>
                    </tbody>
                </table>
            </div>
            <div class="mt-3">
                <button class="btn btn-primary me-2" onclick="exportReport('employee_list')">
                    <i class="fas fa-download me-2"></i>{% trans "Export PDF" %}
                </button>
                <button class="btn btn-success" onclick="exportReport('employee_list', 'excel')">
                    <i class="fas fa-file-excel me-2"></i>{% trans "Export Excel" %}
                </button>
            </div>
        `,
        'daily_attendance': `
            <h5>{% trans "Daily Attendance Report" %}</h5>
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h4 class="text-success">{{ present_today|default:0 }}</h4>
                            <p class="mb-0">{% trans "Present" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h4 class="text-danger">{{ absent_today|default:0 }}</h4>
                            <p class="mb-0">{% trans "Absent" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h4 class="text-warning">{{ late_today|default:0 }}</h4>
                            <p class="mb-0">{% trans "Late" %}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <button class="btn btn-primary me-2" onclick="exportReport('daily_attendance')">
                    <i class="fas fa-download me-2"></i>{% trans "Export PDF" %}
                </button>
                <button class="btn btn-success" onclick="exportReport('daily_attendance', 'excel')">
                    <i class="fas fa-file-excel me-2"></i>{% trans "Export Excel" %}
                </button>
            </div>
        `
    };
    
    return reportTemplates[reportType] || `
        <h5>${reportType.replace('_', ' ').toUpperCase()} {% trans "Report" %}</h5>
        <p>{% trans "Report content will be displayed here." %}</p>
        <div class="mt-3">
            <button class="btn btn-primary me-2" onclick="exportReport('${reportType}')">
                <i class="fas fa-download me-2"></i>{% trans "Export PDF" %}
            </button>
            <button class="btn btn-success" onclick="exportReport('${reportType}', 'excel')">
                <i class="fas fa-file-excel me-2"></i>{% trans "Export Excel" %}
            </button>
        </div>
    `;
}

function exportReport(reportType, format = 'pdf') {
    const url = `/hr/reports/${reportType}/export/?format=${format}`;
    window.open(url, '_blank');
}

function exportAllReports() {
    window.location.href = '/hr/reports/export-all/';
}

function generateCustomReport() {
    const form = document.getElementById('customReportForm');
    if (form.checkValidity()) {
        const formData = new FormData(form);
        
        // Get selected fields
        const selectedFields = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
            .map(cb => cb.value);
        
        // Here you would typically send the data to the server
        alert('{% trans "Custom report generated successfully!" %}');
        bootstrap.Modal.getInstance(document.getElementById('customReportModal')).hide();
    } else {
        form.reportValidity();
    }
}

// Set default dates
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    document.getElementById('dateFrom').value = firstDayOfMonth.toISOString().split('T')[0];
    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
});
</script>
{% endblock %}
