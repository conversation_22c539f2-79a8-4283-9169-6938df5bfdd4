{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Delete Class" %} - {{ object.name }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .delete-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .delete-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        border-radius: 10px;
    }
    .btn-secondary {
        border-radius: 10px;
    }
    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        opacity: 0.8;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">{% trans "Students" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'students:class_list' %}">{% trans "Classes" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'students:class_detail' object.pk %}">{{ object.name }}</a></li>
            <li class="breadcrumb-item active">{% trans "Delete" %}</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card delete-card">
                <div class="delete-header">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h4 class="mb-0">{% trans "Delete Class" %}</h4>
                        <p class="mb-0 opacity-75">{% trans "This action cannot be undone" %}</p>
                    </div>
                </div>
                
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="fas fa-users warning-icon mb-3"></i>
                        <h5 class="text-danger">{% trans "Are you sure you want to delete this class?" %}</h5>
                    </div>

                    <!-- Class Information -->
                    <div class="card bg-light mb-4">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-info-circle text-primary me-2"></i>{% trans "Class Details" %}
                            </h6>
                            <div class="row">
                                <div class="col-sm-4"><strong>{% trans "Name" %}:</strong></div>
                                <div class="col-sm-8">{{ object.name }}</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>{% trans "Grade" %}:</strong></div>
                                <div class="col-sm-8">{{ object.grade.name }}</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>{% trans "Students" %}:</strong></div>
                                <div class="col-sm-8">{{ object.student_count }} {% trans "students" %}</div>
                            </div>
                            {% if object.room_number %}
                            <div class="row">
                                <div class="col-sm-4"><strong>{% trans "Room" %}:</strong></div>
                                <div class="col-sm-8">{{ object.room_number }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Warning Messages -->
                    <div class="alert alert-danger" role="alert">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Warning!" %}
                        </h6>
                        <p class="mb-2">{% trans "Deleting this class will:" %}</p>
                        <ul class="mb-0">
                            <li>{% trans "Remove the class permanently from the system" %}</li>
                            {% if object.student_count > 0 %}
                            <li class="text-danger"><strong>{% trans "Affect" %} {{ object.student_count }} {% trans "students currently enrolled in this class" %}</strong></li>
                            {% endif %}
                            <li>{% trans "Remove all associated attendance records" %}</li>
                            <li>{% trans "Remove all associated schedule entries" %}</li>
                            <li>{% trans "This action cannot be undone" %}</li>
                        </ul>
                    </div>

                    {% if object.student_count > 0 %}
                    <div class="alert alert-warning" role="alert">
                        <h6 class="alert-heading">
                            <i class="fas fa-users me-2"></i>{% trans "Students Affected" %}
                        </h6>
                        <p class="mb-2">{% trans "The following students are currently enrolled in this class:" %}</p>
                        <div class="row">
                            {% for student in object.students.all|slice:":10" %}
                            <div class="col-md-6">
                                <small class="text-muted">• {{ student.get_full_name }}</small>
                            </div>
                            {% endfor %}
                            {% if object.student_count > 10 %}
                            <div class="col-12">
                                <small class="text-muted">{% trans "... and" %} {{ object.student_count|add:"-10" }} {% trans "more students" %}</small>
                            </div>
                            {% endif %}
                        </div>
                        <hr>
                        <p class="mb-0">
                            <strong>{% trans "Recommendation:" %}</strong> 
                            {% trans "Consider transferring students to other classes before deleting this class." %}
                        </p>
                    </div>
                    {% endif %}

                    <!-- Confirmation Form -->
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'students:class_detail' object.pk %}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-danger btn-lg" onclick="return confirm('{% trans "Are you absolutely sure? This action cannot be undone!" %}')">
                                <i class="fas fa-trash me-2"></i>{% trans "Yes, Delete Class" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add extra confirmation for delete button
    const deleteButton = document.querySelector('button[type="submit"]');
    if (deleteButton) {
        deleteButton.addEventListener('click', function(e) {
            const className = '{{ object.name|escapejs }}';
            const studentCount = {{ object.student_count }};
            
            let confirmMessage = `{% trans "Are you absolutely sure you want to delete the class" %} "${className}"?`;
            
            if (studentCount > 0) {
                confirmMessage += `\n\n{% trans "This will affect" %} ${studentCount} {% trans "students currently enrolled in this class." %}`;
            }
            
            confirmMessage += '\n\n{% trans "This action cannot be undone!" %}';
            
            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }
        });
    }
});
</script>
{% endblock %}