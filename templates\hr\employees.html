{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Employee Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .employee-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .employee-card:hover {
        transform: translateY(-2px);
    }
    .employee-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .employee-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
    }
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
    }
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    .filter-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
    }
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1.5rem;
    }
    .search-box:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-users text-primary me-2"></i>{% trans "Employee Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage employee information, positions, and records" %}</p>
                </div>
                <div>
                    <a href="{% url 'hr:employee_add' %}" class="btn btn-primary me-2">
                        <i class="fas fa-user-plus me-2"></i>{% trans "Add Employee" %}
                    </a>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export List" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card employee-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                    <h3 class="mb-1">{{ total_employees|default:0 }}</h3>
                    <p class="mb-0">{% trans "Total Employees" %}</p>
                    <small class="text-muted">{% trans "All staff members" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card employee-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                    <h3 class="mb-1">{{ active_employees|default:0 }}</h3>
                    <p class="mb-0">{% trans "Active Employees" %}</p>
                    <small class="text-muted">{% trans "Currently working" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card employee-card">
                <div class="card-body text-center">
                    <i class="fas fa-building fa-2x text-info mb-2"></i>
                    <h3 class="mb-1">{{ departments_count|default:8 }}</h3>
                    <p class="mb-0">{% trans "Departments" %}</p>
                    <small class="text-muted">{% trans "Active departments" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card employee-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-plus fa-2x text-warning mb-2"></i>
                    <h3 class="mb-1">{{ new_hires|default:5 }}</h3>
                    <p class="mb-0">{% trans "New Hires" %}</p>
                    <small class="text-muted">{% trans "This month" %}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card filter-card">
                <div class="card-body">
                    <h5 class="card-title mb-3">{% trans "Search and Filter Employees" %}</h5>
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control search-box" name="search" 
                                   placeholder="{% trans 'Search by name, ID, or email...' %}" 
                                   value="{{ request.GET.search }}">
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" name="department">
                                <option value="">{% trans "All Departments" %}</option>
                                {% for dept in departments %}
                                <option value="{{ dept.id }}" {% if request.GET.department == dept.id|stringformat:"s" %}selected{% endif %}>
                                    {{ dept.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" name="status">
                                <option value="">{% trans "All Status" %}</option>
                                <option value="active" {% if request.GET.status == "active" %}selected{% endif %}>{% trans "Active" %}</option>
                                <option value="inactive" {% if request.GET.status == "inactive" %}selected{% endif %}>{% trans "Inactive" %}</option>
                                <option value="terminated" {% if request.GET.status == "terminated" %}selected{% endif %}>{% trans "Terminated" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" name="position">
                                <option value="">{% trans "All Positions" %}</option>
                                {% for pos in positions %}
                                <option value="{{ pos.id }}" {% if request.GET.position == pos.id|stringformat:"s" %}selected{% endif %}>
                                    {{ pos.title }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-light w-100">
                                <i class="fas fa-search me-2"></i>{% trans "Search" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee List -->
    <div class="row">
        <div class="col-12">
            <div class="card employee-card">
                <div class="employee-header card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "Employee Directory" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Employee" %}</th>
                                    <th>{% trans "ID" %}</th>
                                    <th>{% trans "Position" %}</th>
                                    <th>{% trans "Department" %}</th>
                                    <th>{% trans "Hire Date" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if employee.user.profile_picture %}
                                            <img src="{{ employee.user.profile_picture.url }}" alt="{{ employee.user.get_full_name }}" class="employee-avatar me-3">
                                            {% else %}
                                            <img src="{% static 'images/default-avatar.png' %}" alt="{{ employee.user.get_full_name }}" class="employee-avatar me-3">
                                            {% endif %}
                                            <div>
                                                <h6 class="mb-1">{{ employee.user.get_full_name }}</h6>
                                                <small class="text-muted">{{ employee.user.email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ employee.employee_id }}</span>
                                    </td>
                                    <td>{{ employee.position.title }}</td>
                                    <td>{{ employee.position.department.name }}</td>
                                    <td>{{ employee.hire_date|date:"M d, Y" }}</td>
                                    <td>
                                        {% if employee.employment_status == 'active' %}
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle me-1"></i>{% trans "Active" %}
                                        </span>
                                        {% else %}
                                        <span class="status-badge status-inactive">
                                            <i class="fas fa-times-circle me-1"></i>{{ employee.get_employment_status_display }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'hr:employee_detail' employee.pk %}" class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'hr:employee_edit' employee.pk %}" class="btn btn-outline-success" title="{% trans 'Edit' %}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-danger" title="{% trans 'Delete' %}" onclick="confirmDelete({{ employee.pk }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">{% trans "No employees found matching your criteria." %}</p>
                                        <a href="{% url 'hr:employee_add' %}" class="btn btn-primary">
                                            <i class="fas fa-user-plus me-2"></i>{% trans "Add First Employee" %}
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Employee pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.department %}&department={{ request.GET.department }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{% trans "First" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.department %}&department={{ request.GET.department }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{% trans "Previous" %}</a>
                            </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.department %}&department={{ request.GET.department }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{% trans "Next" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.department %}&department={{ request.GET.department }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{% trans "Last" %}</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Confirm Delete" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                {% trans "Are you sure you want to delete this employee? This action cannot be undone." %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">{% trans "Delete" %}</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(employeeId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/hr/employees/${employeeId}/delete/`;
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Auto-submit search form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('select[name="department"], select[name="status"], select[name="position"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});
</script>
{% endblock %}
