{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Grade" %} - {{ block.super }}
    {% else %}
        {% trans "Add Grade" %} - {{ block.super }}
    {% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .grade-form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .grade-form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .form-floating > label {
        color: #6c757d;
    }
    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label {
        color: #0d6efd;
    }
    .grade-preview {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
    }
    .grade-scale {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
    }
    .grade-scale-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #dee2e6;
    }
    .grade-scale-item:last-child {
        border-bottom: none;
    }
    .grade-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85em;
        font-weight: bold;
    }
    .grade-a { background-color: #d4edda; color: #155724; }
    .grade-b { background-color: #d1ecf1; color: #0c5460; }
    .grade-c { background-color: #fff3cd; color: #856404; }
    .grade-d { background-color: #f8d7da; color: #721c24; }
    .grade-f { background-color: #f5c6cb; color: #721c24; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-star me-2"></i>
                        {% if object %}
                            {% trans "Edit Grade" %}
                        {% else %}
                            {% trans "Add Grade" %}
                        {% endif %}
                    </h1>
                    <p class="text-muted">{% trans "Manage student grade records" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:grades' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Grades" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Grade Form -->
        <div class="col-lg-8">
            <div class="card grade-form-card">
                <div class="card-header grade-form-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>{% trans "Grade Information" %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" id="gradeForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    {{ form.student }}
                                    <label for="{{ form.student.id_for_label }}">{% trans "Student" %} *</label>
                                </div>
                                {% if form.student.errors %}
                                    <div class="text-danger small">{{ form.student.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    {{ form.exam }}
                                    <label for="{{ form.exam.id_for_label }}">{% trans "Exam" %} *</label>
                                </div>
                                {% if form.exam.errors %}
                                    <div class="text-danger small">{{ form.exam.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    {{ form.marks_obtained }}
                                    <label for="{{ form.marks_obtained.id_for_label }}">{% trans "Marks Obtained" %} *</label>
                                </div>
                                {% if form.marks_obtained.errors %}
                                    <div class="text-danger small">{{ form.marks_obtained.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    {{ form.total_marks }}
                                    <label for="{{ form.total_marks.id_for_label }}">{% trans "Total Marks" %} *</label>
                                </div>
                                {% if form.total_marks.errors %}
                                    <div class="text-danger small">{{ form.total_marks.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="percentageDisplay" readonly>
                                    <label for="percentageDisplay">{% trans "Percentage" %}</label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    {{ form.grade }}
                                    <label for="{{ form.grade.id_for_label }}">{% trans "Grade" %}</label>
                                </div>
                                {% if form.grade.errors %}
                                    <div class="text-danger small">{{ form.grade.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    {{ form.graded_at }}
                                    <label for="{{ form.graded_at.id_for_label }}">{% trans "Graded Date" %}</label>
                                </div>
                                {% if form.graded_at.errors %}
                                    <div class="text-danger small">{{ form.graded_at.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-floating">
                                {{ form.remarks }}
                                <label for="{{ form.remarks.id_for_label }}">{% trans "Remarks" %}</label>
                            </div>
                            {% if form.remarks.errors %}
                                <div class="text-danger small">{{ form.remarks.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {% trans "Active" %}
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'academics:grades' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}
                                    {% trans "Update Grade" %}
                                {% else %}
                                    {% trans "Save Grade" %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Grade Preview & Scale -->
        <div class="col-lg-4">
            <!-- Grade Preview -->
            <div class="card grade-form-card mb-4">
                <div class="card-body">
                    <div class="grade-preview">
                        <h4 class="mb-1" id="gradeDisplay">--</h4>
                        <p class="mb-0">{% trans "Current Grade" %}</p>
                    </div>
                </div>
            </div>

            <!-- Grading Scale -->
            <div class="card grade-form-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Grading Scale" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="grade-scale">
                        <div class="grade-scale-item">
                            <span>90% - 100%</span>
                            <span class="grade-badge grade-a">A</span>
                        </div>
                        <div class="grade-scale-item">
                            <span>80% - 89%</span>
                            <span class="grade-badge grade-b">B</span>
                        </div>
                        <div class="grade-scale-item">
                            <span>70% - 79%</span>
                            <span class="grade-badge grade-c">C</span>
                        </div>
                        <div class="grade-scale-item">
                            <span>60% - 69%</span>
                            <span class="grade-badge grade-d">D</span>
                        </div>
                        <div class="grade-scale-item">
                            <span>Below 60%</span>
                            <span class="grade-badge grade-f">F</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const marksObtainedField = document.getElementById('{{ form.marks_obtained.id_for_label }}');
    const totalMarksField = document.getElementById('{{ form.total_marks.id_for_label }}');
    const percentageDisplay = document.getElementById('percentageDisplay');
    const gradeDisplay = document.getElementById('gradeDisplay');
    const gradeField = document.getElementById('{{ form.grade.id_for_label }}');

    function calculateGrade() {
        const marksObtained = parseFloat(marksObtainedField.value) || 0;
        const totalMarks = parseFloat(totalMarksField.value) || 0;
        
        if (totalMarks > 0) {
            const percentage = (marksObtained / totalMarks) * 100;
            percentageDisplay.value = percentage.toFixed(2) + '%';
            
            let grade = 'F';
            if (percentage >= 90) grade = 'A';
            else if (percentage >= 80) grade = 'B';
            else if (percentage >= 70) grade = 'C';
            else if (percentage >= 60) grade = 'D';
            
            gradeDisplay.textContent = grade;
            gradeField.value = grade;
        } else {
            percentageDisplay.value = '';
            gradeDisplay.textContent = '--';
            gradeField.value = '';
        }
    }

    marksObtainedField.addEventListener('input', calculateGrade);
    totalMarksField.addEventListener('input', calculateGrade);
    
    // Calculate on page load if values exist
    calculateGrade();

    // Form validation
    document.getElementById('gradeForm').addEventListener('submit', function(e) {
        const marksObtained = parseFloat(marksObtainedField.value) || 0;
        const totalMarks = parseFloat(totalMarksField.value) || 0;
        
        if (marksObtained > totalMarks) {
            e.preventDefault();
            alert('{% trans "Marks obtained cannot be greater than total marks" %}');
            marksObtainedField.focus();
        }
    });
});
</script>
{% endblock %}
