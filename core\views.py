from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, CreateView
from django.http import HttpResponse
from django.urls import reverse_lazy
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm

def placeholder_view(request):
    return HttpResponse("Core settings - Coming soon!")

# Admin Area Views
class AdminDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'core/admin_dashboard.html'

class UserCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create new user view"""
    model = User
    form_class = UserCreationForm
    template_name = 'core/user_create.html'
    permission_required = 'auth.add_user'
    success_url = reverse_lazy('core:dashboard')

    def form_valid(self, form):
        messages.success(self.request, _('User created successfully!'))
        return super().form_valid(form)

class SyncView(LoginRequiredMixin, TemplateView):
    template_name = 'core/sync.html'

class BackupView(LoginRequiredMixin, TemplateView):
    template_name = 'core/backup.html'

class HistoryReportView(LoginRequiredMixin, TemplateView):
    template_name = 'core/history_report.html'

class ReportDesignerView(LoginRequiredMixin, TemplateView):
    template_name = 'core/report_designer.html'

class StructureAdministrativeView(LoginRequiredMixin, TemplateView):
    template_name = 'core/structure_admin.html'

class QuickSupportView(LoginRequiredMixin, TemplateView):
    template_name = 'core/quick_support.html'

# General Site Settings Views
class SettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/settings.html'

class LoginPageSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/login_page_settings.html'

# School Information Views
class SchoolInfoView(LoginRequiredMixin, TemplateView):
    template_name = 'core/school_info.html'

class SchoolDataView(LoginRequiredMixin, TemplateView):
    template_name = 'core/school_data.html'

class SchoolDataSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/school_data_settings.html'

class NationalityView(LoginRequiredMixin, TemplateView):
    template_name = 'core/nationality.html'

class CurrencyView(LoginRequiredMixin, TemplateView):
    template_name = 'core/currency.html'

# Taxation System Views
class EgyptianTaxSystemSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/tax_settings.html'

class ZakatAndIncomeView(LoginRequiredMixin, TemplateView):
    template_name = 'core/zakat_income.html'

# Email Settings Views
class EmailSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/email_settings.html'

class SentEmailView(LoginRequiredMixin, TemplateView):
    template_name = 'core/sent_emails.html'

class EmailFormatSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/email_format_settings.html'

# Students Receiver Views
class StudentsReceiverView(LoginRequiredMixin, TemplateView):
    template_name = 'core/students_receiver.html'

class StudentReceiverReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/receiver_reports.html'

class ShowReceiverReportView(LoginRequiredMixin, TemplateView):
    template_name = 'core/show_receiver_report.html'

# Service Providers - SMS Views
class SMSSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/sms_settings.html'

class MessageTemplatesView(LoginRequiredMixin, TemplateView):
    template_name = 'core/message_templates.html'

class SentMessagesView(LoginRequiredMixin, TemplateView):
    template_name = 'core/sent_messages.html'

class AutoMessagesView(LoginRequiredMixin, TemplateView):
    template_name = 'core/auto_messages.html'

class SMSReportView(LoginRequiredMixin, TemplateView):
    template_name = 'core/sms_report.html'

# Service Providers - WhatsApp Views
class WhatsAppSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/whatsapp_settings.html'

# Service Providers - Payment Views
class PaymentProviderView(LoginRequiredMixin, TemplateView):
    template_name = 'core/payment_provider.html'
