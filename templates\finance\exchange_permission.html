{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Exchange Permission" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .exchange-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .exchange-header {
        background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .exchange-form {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
    }
    .exchange-rate-display {
        background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        margin: 20px 0;
    }
    .currency-selector {
        background-color: #fff;
        border: 2px solid #dee2e6;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        cursor: pointer;
        transition: all 0.3s;
    }
    .currency-selector:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    .currency-selector.selected {
        border-color: #007bff;
        background-color: #e3f2fd;
    }
    .exchange-preview {
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 20px;
        background-color: #fff;
    }
    .approval-section {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
    }
    .signature-box {
        border: 1px solid #dee2e6;
        border-radius: 5px;
        height: 80px;
        background-color: #f8f9fa;
        display: flex;
        align-items: end;
        justify-content: center;
        padding: 10px;
        font-size: 0.9em;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>{% trans "Exchange Permission" %}
                    </h1>
                    <p class="text-muted">{% trans "Currency exchange authorization and management" %}</p>
                </div>
                <div>
                    <button class="btn btn-warning me-2" onclick="submitForApproval()">
                        <i class="fas fa-check me-2"></i>{% trans "Submit for Approval" %}
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printPermission()">
                        <i class="fas fa-print me-2"></i>{% trans "Print" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Exchange Form -->
        <div class="col-lg-6">
            <div class="card exchange-card">
                <div class="card-header exchange-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>{% trans "Exchange Details" %}
                    </h5>
                </div>
                <div class="card-body">
                    <form id="exchangeForm">
                        {% csrf_token %}
                        
                        <div class="exchange-form">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="requestNumber" class="form-label">{% trans "Request Number" %}</label>
                                    <input type="text" class="form-control" id="requestNumber" value="EX-{{ request_number|default:'001' }}" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="requestDate" class="form-label">{% trans "Request Date" %}</label>
                                    <input type="date" class="form-control" id="requestDate" value="{% now 'Y-m-d' %}">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="requestedBy" class="form-label">{% trans "Requested By" %}</label>
                                <input type="text" class="form-control" id="requestedBy" value="{{ user.get_full_name }}" readonly>
                            </div>

                            <div class="mb-3">
                                <label for="department" class="form-label">{% trans "Department" %}</label>
                                <select class="form-select" id="department">
                                    <option value="">{% trans "Select department" %}</option>
                                    {% for dept in departments %}
                                        <option value="{{ dept.id }}">{{ dept.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="purpose" class="form-label">{% trans "Purpose of Exchange" %}</label>
                                <textarea class="form-control" id="purpose" rows="3" placeholder="{% trans 'Enter exchange purpose' %}"></textarea>
                            </div>
                        </div>

                        <!-- Currency Selection -->
                        <div class="mb-4">
                            <h6 class="mb-3">{% trans "Currency Exchange" %}</h6>
                            
                            <!-- From Currency -->
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">{% trans "From Currency" %}</label>
                                    <div class="currency-selector" data-currency="USD">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">USD - US Dollar</h6>
                                                <small class="text-muted">United States Dollar</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-primary">$</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="currency-selector" data-currency="EUR">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">EUR - Euro</h6>
                                                <small class="text-muted">European Union Euro</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-info">€</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- To Currency -->
                                <div class="col-md-6">
                                    <label class="form-label">{% trans "To Currency" %}</label>
                                    <div class="currency-selector" data-currency="SAR">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">SAR - Saudi Riyal</h6>
                                                <small class="text-muted">Saudi Arabian Riyal</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-success">ر.س</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="currency-selector" data-currency="AED">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">AED - UAE Dirham</h6>
                                                <small class="text-muted">United Arab Emirates Dirham</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-warning">د.إ</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Amount and Rate -->
                        <div class="exchange-form">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="fromAmount" class="form-label">{% trans "Amount to Exchange" %}</label>
                                    <div class="input-group">
                                        <span class="input-group-text" id="fromCurrencySymbol">$</span>
                                        <input type="number" class="form-control" id="fromAmount" step="0.01" placeholder="0.00">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="exchangeRate" class="form-label">{% trans "Exchange Rate" %}</label>
                                    <input type="number" class="form-control" id="exchangeRate" step="0.0001" placeholder="0.0000">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="toAmount" class="form-label">{% trans "Amount to Receive" %}</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="toCurrencySymbol">ر.س</span>
                                    <input type="number" class="form-control" id="toAmount" step="0.01" placeholder="0.00" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="exchange-rate-display">
                            <h4 class="mb-1" id="rateDisplay">1 USD = 3.75 SAR</h4>
                            <p class="mb-0">{% trans "Current Exchange Rate" %}</p>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Exchange Preview -->
        <div class="col-lg-6">
            <div class="card exchange-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>{% trans "Exchange Permission Preview" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="exchange-preview" id="exchangePreview">
                        <!-- School Header -->
                        <div class="text-center mb-4">
                            <h4>{{ school_name|default:"School Name" }}</h4>
                            <p class="mb-1">{{ school_address|default:"School Address" }}</p>
                            <p class="mb-0">{{ school_phone|default:"Phone" }} | {{ school_email|default:"Email" }}</p>
                        </div>

                        <hr>

                        <!-- Permission Header -->
                        <div class="text-center mb-4">
                            <h3 class="text-warning">{% trans "EXCHANGE PERMISSION" %}</h3>
                            <p class="mb-0">No: <span id="previewRequestNumber">EX-001</span></p>
                        </div>

                        <!-- Permission Details -->
                        <div class="row mb-3">
                            <div class="col-6">
                                <strong>{% trans "Date:" %}</strong> <span id="previewDate">{% now "M d, Y" %}</span>
                            </div>
                            <div class="col-6 text-end">
                                <strong>{% trans "Department:" %}</strong> <span id="previewDepartment">-</span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <strong>{% trans "Requested By:" %}</strong><br>
                            <span id="previewRequestedBy">{{ user.get_full_name }}</span>
                        </div>

                        <div class="mb-3">
                            <strong>{% trans "Purpose:" %}</strong><br>
                            <span id="previewPurpose" class="text-muted">{% trans "Enter exchange purpose" %}</span>
                        </div>

                        <div class="mb-3">
                            <strong>{% trans "Exchange Details:" %}</strong><br>
                            <span id="previewExchangeDetails">{% trans "Select currencies and amount" %}</span>
                        </div>

                        <div class="row mb-4">
                            <div class="col-6">
                                <strong>{% trans "Exchange Rate:" %}</strong><br>
                                <span id="previewRate">-</span>
                            </div>
                            <div class="col-6">
                                <strong>{% trans "Total Amount:" %}</strong><br>
                                <span id="previewTotal">-</span>
                            </div>
                        </div>

                        <hr>

                        <!-- Approval Section -->
                        <div class="approval-section">
                            <h6 class="mb-3">{% trans "Approval Section" %}</h6>
                            
                            <div class="row">
                                <div class="col-6">
                                    <div class="signature-box">
                                        {% trans "Department Head" %}
                                    </div>
                                    <div class="text-center mt-2">
                                        <small>{% trans "Signature & Date" %}</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="signature-box">
                                        {% trans "Finance Manager" %}
                                    </div>
                                    <div class="text-center mt-2">
                                        <small>{% trans "Signature & Date" %}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Exchange Requests -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card exchange-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>{% trans "Recent Exchange Requests" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Request No." %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Requested By" %}</th>
                                    <th>{% trans "From" %}</th>
                                    <th>{% trans "To" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if recent_requests %}
                                    {% for request in recent_requests %}
                                        <tr>
                                            <td>{{ request.request_number }}</td>
                                            <td>{{ request.date|date:"M d, Y" }}</td>
                                            <td>{{ request.requested_by }}</td>
                                            <td>{{ request.from_currency }}</td>
                                            <td>{{ request.to_currency }}</td>
                                            <td>{{ request.from_amount|floatformat:2 }}</td>
                                            <td>
                                                <span class="badge 
                                                    {% if request.status == 'approved' %}bg-success
                                                    {% elif request.status == 'pending' %}bg-warning
                                                    {% elif request.status == 'rejected' %}bg-danger
                                                    {% else %}bg-secondary{% endif %}">
                                                    {{ request.get_status_display }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="viewRequest({{ request.id }})">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="printRequest({{ request.id }})">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No exchange requests found." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let fromCurrency = 'USD';
    let toCurrency = 'SAR';
    
    const exchangeRates = {
        'USD_SAR': 3.75,
        'USD_AED': 3.67,
        'EUR_SAR': 4.08,
        'EUR_AED': 3.99
    };

    // Currency selection
    document.querySelectorAll('.currency-selector').forEach(selector => {
        selector.addEventListener('click', function() {
            const currency = this.dataset.currency;
            const isFromSection = this.closest('.col-md-6').querySelector('.form-label').textContent.includes('From');
            
            // Remove selected class from siblings
            this.parentNode.querySelectorAll('.currency-selector').forEach(s => s.classList.remove('selected'));
            this.classList.add('selected');
            
            if (isFromSection) {
                fromCurrency = currency;
                updateCurrencySymbol('fromCurrencySymbol', currency);
            } else {
                toCurrency = currency;
                updateCurrencySymbol('toCurrencySymbol', currency);
            }
            
            updateExchangeRate();
            updatePreview();
        });
    });

    function updateCurrencySymbol(elementId, currency) {
        const symbols = {
            'USD': '$',
            'EUR': '€',
            'SAR': 'ر.س',
            'AED': 'د.إ'
        };
        document.getElementById(elementId).textContent = symbols[currency] || currency;
    }

    function updateExchangeRate() {
        const rateKey = `${fromCurrency}_${toCurrency}`;
        const rate = exchangeRates[rateKey] || 1;
        
        document.getElementById('exchangeRate').value = rate;
        document.getElementById('rateDisplay').textContent = `1 ${fromCurrency} = ${rate} ${toCurrency}`;
        
        calculateAmount();
    }

    function calculateAmount() {
        const fromAmount = parseFloat(document.getElementById('fromAmount').value) || 0;
        const rate = parseFloat(document.getElementById('exchangeRate').value) || 0;
        const toAmount = fromAmount * rate;
        
        document.getElementById('toAmount').value = toAmount.toFixed(2);
    }

    function updatePreview() {
        // Update request number
        document.getElementById('previewRequestNumber').textContent = document.getElementById('requestNumber').value;
        
        // Update date
        const date = new Date(document.getElementById('requestDate').value);
        document.getElementById('previewDate').textContent = date.toLocaleDateString();
        
        // Update department
        const department = document.getElementById('department');
        const departmentText = department.options[department.selectedIndex].text;
        document.getElementById('previewDepartment').textContent = departmentText;
        
        // Update purpose
        const purpose = document.getElementById('purpose').value;
        document.getElementById('previewPurpose').textContent = purpose || '{% trans "Enter exchange purpose" %}';
        
        // Update exchange details
        const fromAmount = document.getElementById('fromAmount').value || '0';
        const toAmount = document.getElementById('toAmount').value || '0';
        const exchangeDetails = `${fromAmount} ${fromCurrency} → ${toAmount} ${toCurrency}`;
        document.getElementById('previewExchangeDetails').textContent = exchangeDetails;
        
        // Update rate
        const rate = document.getElementById('exchangeRate').value;
        document.getElementById('previewRate').textContent = rate ? `1 ${fromCurrency} = ${rate} ${toCurrency}` : '-';
        
        // Update total
        document.getElementById('previewTotal').textContent = toAmount ? `${toAmount} ${toCurrency}` : '-';
    }

    // Event listeners
    document.getElementById('fromAmount').addEventListener('input', function() {
        calculateAmount();
        updatePreview();
    });

    document.getElementById('exchangeRate').addEventListener('input', function() {
        calculateAmount();
        updatePreview();
    });

    document.getElementById('exchangeForm').addEventListener('input', updatePreview);
    document.getElementById('exchangeForm').addEventListener('change', updatePreview);

    // Initialize
    document.querySelector('.currency-selector[data-currency="USD"]').classList.add('selected');
    document.querySelector('.currency-selector[data-currency="SAR"]').classList.add('selected');
    updateExchangeRate();
    updatePreview();
});

function submitForApproval() {
    const form = document.getElementById('exchangeForm');
    const formData = new FormData(form);
    
    // Validate form
    const fromAmount = document.getElementById('fromAmount').value;
    const purpose = document.getElementById('purpose').value;
    
    if (!fromAmount || !purpose) {
        alert('{% trans "Please fill in all required fields" %}');
        return;
    }
    
    // Here you would typically send the data to the server
    alert('{% trans "Exchange request submitted for approval!" %}');
}

function printPermission() {
    window.print();
}

function viewRequest(id) {
    window.location.href = `/finance/exchange-permission/${id}/`;
}

function printRequest(id) {
    window.open(`/finance/exchange-permission/${id}/print/`, '_blank');
}
</script>
{% endblock %}
