{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Field Settings" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-cogs text-primary me-2"></i>{% trans "Field Settings" %}
                            </h2>
                            <p class="text-muted">{% trans "Configure visible fields and their properties" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:settings' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Settings" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Field Categories -->
            {% for category in field_categories %}
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">{{ category.name }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for field in category.fields %}
                                        <div class="col-md-6 mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="field_{{ field }}" checked>
                                                <label class="form-check-label" for="field_{{ field }}">
                                                    {{ field|title|replace:"_":" " }}
                                                </label>
                                            </div>
                                            <div class="ms-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="required_{{ field }}">
                                                    <label class="form-check-label small text-muted" for="required_{{ field }}">
                                                        {% trans "Required" %}
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="readonly_{{ field }}">
                                                    <label class="form-check-label small text-muted" for="readonly_{{ field }}">
                                                        {% trans "Read Only" %}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}

            <!-- Save Button -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <button type="button" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% trans "Save Field Settings" %}
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-undo me-2"></i>{% trans "Reset to Defaults" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}