{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Grade" %} - {{ object.name }}
    {% else %}
        {% trans "Add New Grade" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-layer-group text-primary me-2"></i>
                        {% if object %}
                            {% trans "Edit Grade" %} - {{ object.name }}
                        {% else %}
                            {% trans "Add New Grade" %}
                        {% endif %}
                    </h2>
                    <p class="text-muted">
                        {% if object %}
                            {% trans "Update grade information" %}
                        {% else %}
                            {% trans "Create a new grade level in the system" %}
                        {% endif %}
                    </p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        {{ form.name|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        {{ form.name_ar|as_crispy_field }}
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        {{ form.level|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-3 mt-4">
                                            <input class="form-check-input" type="checkbox" id="id_is_active" name="is_active" {% if not object or object.is_active %}checked{% endif %}>
                                            <label class="form-check-label" for="id_is_active">
                                                {% trans "Active" %}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.description|as_crispy_field }}
                                </div>
                                
                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>{% trans "Save Grade" %}
                                    </button>
                                    <a href="{% url 'students:grade_list' %}" class="btn btn-secondary ms-2">
                                        <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Information" %}</h5>
                        </div>
                        <div class="card-body">
                            <p>{% trans "Grades represent the different academic levels in the school." %}</p>
                            <p>{% trans "Each grade should have:" %}</p>
                            <ul>
                                <li>{% trans "A unique name" %}</li>
                                <li>{% trans "A level number (used for sorting)" %}</li>
                                <li>{% trans "Optional Arabic name for bilingual schools" %}</li>
                            </ul>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                {% trans "After creating a grade, you can add classes to it." %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}