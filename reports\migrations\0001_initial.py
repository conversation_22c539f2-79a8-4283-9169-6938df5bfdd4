# Generated by Django 5.2.4 on 2025-07-13 11:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Analytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('metric_type', models.CharField(choices=[('enrollment', 'Enrollment'), ('attendance', 'Attendance'), ('financial', 'Financial'), ('academic', 'Academic Performance'), ('hr', 'Human Resources')], max_length=20, verbose_name='Metric Type')),
                ('metric_name', models.CharField(max_length=100, verbose_name='Metric Name')),
                ('value', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='Value')),
                ('date', models.DateField(verbose_name='Date')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='Metadata')),
            ],
            options={
                'verbose_name': 'Analytics',
                'verbose_name_plural': 'Analytics',
                'ordering': ['-date', 'metric_type'],
                'unique_together': {('metric_type', 'metric_name', 'date')},
            },
        ),
        migrations.CreateModel(
            name='Dashboard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=200, verbose_name='Dashboard Name')),
                ('name_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Dashboard Name (Arabic)')),
                ('layout', models.JSONField(default=dict, verbose_name='Dashboard Layout')),
                ('widgets', models.JSONField(default=list, verbose_name='Dashboard Widgets')),
                ('is_default', models.BooleanField(default=False, verbose_name='Is Default Dashboard')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dashboards', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Dashboard',
                'verbose_name_plural': 'Dashboards',
                'ordering': ['user', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=200, verbose_name='Report Name')),
                ('name_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Report Name (Arabic)')),
                ('report_type', models.CharField(choices=[('student', 'Student Report'), ('financial', 'Financial Report'), ('academic', 'Academic Report'), ('attendance', 'Attendance Report'), ('hr', 'HR Report'), ('custom', 'Custom Report')], max_length=20, verbose_name='Report Type')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('query', models.TextField(verbose_name='SQL Query')),
                ('parameters', models.JSONField(blank=True, default=dict, verbose_name='Parameters')),
                ('is_public', models.BooleanField(default=False, verbose_name='Is Public')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_reports', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'Report Template',
                'verbose_name_plural': 'Report Templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ReportExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('parameters_used', models.JSONField(blank=True, default=dict, verbose_name='Parameters Used')),
                ('execution_time', models.DurationField(verbose_name='Execution Time')),
                ('row_count', models.PositiveIntegerField(verbose_name='Row Count')),
                ('file_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='Generated File Path')),
                ('executed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executed_reports', to=settings.AUTH_USER_MODEL, verbose_name='Executed By')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executions', to='reports.reporttemplate', verbose_name='Report Template')),
            ],
            options={
                'verbose_name': 'Report Execution',
                'verbose_name_plural': 'Report Executions',
                'ordering': ['-created_at'],
            },
        ),
    ]
