# Generated by Django 5.2.4 on 2025-07-13 11:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=100, verbose_name='Department Name')),
                ('name_ar', models.Char<PERSON>ield(blank=True, max_length=100, null=True, verbose_name='Department Name (Arabic)')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Department Code')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('head', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='headed_departments', to=settings.AUTH_USER_MODEL, verbose_name='Department Head')),
            ],
            options={
                'verbose_name': 'Department',
                'verbose_name_plural': 'Departments',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('title', models.CharField(max_length=100, verbose_name='Position Title')),
                ('title_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Position Title (Arabic)')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Job Description')),
                ('requirements', models.TextField(blank=True, null=True, verbose_name='Requirements')),
                ('min_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Minimum Salary')),
                ('max_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Maximum Salary')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='positions', to='hr.department', verbose_name='Department')),
            ],
            options={
                'verbose_name': 'Position',
                'verbose_name_plural': 'Positions',
                'ordering': ['department', 'title'],
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='Employee ID')),
                ('hire_date', models.DateField(verbose_name='Hire Date')),
                ('termination_date', models.DateField(blank=True, null=True, verbose_name='Termination Date')),
                ('employment_status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('terminated', 'Terminated'), ('resigned', 'Resigned'), ('retired', 'Retired')], default='active', max_length=20, verbose_name='Employment Status')),
                ('salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Salary')),
                ('emergency_contact_name', models.CharField(max_length=100, verbose_name='Emergency Contact Name')),
                ('emergency_contact_phone', models.CharField(max_length=20, verbose_name='Emergency Contact Phone')),
                ('emergency_contact_relationship', models.CharField(max_length=50, verbose_name='Emergency Contact Relationship')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='employee_profile', to=settings.AUTH_USER_MODEL, verbose_name='User Account')),
                ('position', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='hr.position', verbose_name='Position')),
            ],
            options={
                'verbose_name': 'Employee',
                'verbose_name_plural': 'Employees',
                'ordering': ['user__first_name', 'user__last_name'],
            },
        ),
    ]
