{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ employee.user.get_full_name }} - {% trans "Employee Details" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .employee-profile-card {
        border: none;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
    }
    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 5px solid white;
        object-fit: cover;
        margin: 0 auto;
    }
    .info-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .info-card:hover {
        transform: translateY(-2px);
    }
    .info-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    .info-value {
        font-size: 1.1rem;
        color: #495057;
        margin-top: 0.25rem;
    }
    .status-badge {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
    }
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    .tab-content {
        padding: 1.5rem;
    }
    .nav-pills .nav-link {
        border-radius: 25px;
        margin-right: 0.5rem;
        font-weight: 500;
    }
    .nav-pills .nav-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .timeline {
        position: relative;
        padding-left: 2rem;
    }
    .timeline::before {
        content: '';
        position: absolute;
        left: 0.75rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    .timeline-item {
        position: relative;
        margin-bottom: 1.5rem;
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -1.75rem;
        top: 0.5rem;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #007bff;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #007bff;
    }
    .performance-meter {
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }
    .performance-bar {
        height: 100%;
        border-radius: 4px;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">{% trans "HR Dashboard" %}</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'hr:employees' %}">{% trans "Employees" %}</a></li>
                            <li class="breadcrumb-item active">{{ employee.user.get_full_name }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'hr:employee_edit' employee.pk %}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-2"></i>{% trans "Edit Employee" %}
                    </a>
                    <button class="btn btn-success">
                        <i class="fas fa-print me-2"></i>{% trans "Print Profile" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Employee Profile Card -->
        <div class="col-lg-4 mb-4">
            <div class="card employee-profile-card">
                <div class="profile-header">
                    {% if employee.user.profile_picture %}
                    <img src="{{ employee.user.profile_picture.url }}" alt="{{ employee.user.get_full_name }}" class="profile-avatar">
                    {% else %}
                    <img src="{% static 'images/default-avatar.png' %}" alt="{{ employee.user.get_full_name }}" class="profile-avatar">
                    {% endif %}
                    <h3 class="mt-3 mb-1">{{ employee.user.get_full_name }}</h3>
                    <p class="mb-2">{{ employee.position.title }}</p>
                    {% if employee.employment_status == 'active' %}
                    <span class="status-badge status-active">
                        <i class="fas fa-check-circle me-1"></i>{% trans "Active Employee" %}
                    </span>
                    {% else %}
                    <span class="status-badge status-inactive">
                        <i class="fas fa-times-circle me-1"></i>{{ employee.get_employment_status_display }}
                    </span>
                    {% endif %}
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="info-label">{% trans "Employee ID" %}</div>
                            <div class="info-value">{{ employee.employee_id }}</div>
                        </div>
                        <div class="col-12">
                            <div class="info-label">{% trans "Department" %}</div>
                            <div class="info-value">{{ employee.position.department.name }}</div>
                        </div>
                        <div class="col-12">
                            <div class="info-label">{% trans "Hire Date" %}</div>
                            <div class="info-value">{{ employee.hire_date|date:"F d, Y" }}</div>
                        </div>
                        <div class="col-12">
                            <div class="info-label">{% trans "Email" %}</div>
                            <div class="info-value">{{ employee.user.email }}</div>
                        </div>
                        <div class="col-12">
                            <div class="info-label">{% trans "Phone" %}</div>
                            <div class="info-value">{{ employee.user.phone|default:"Not provided" }}</div>
                        </div>
                        {% if employee.salary %}
                        <div class="col-12">
                            <div class="info-label">{% trans "Salary" %}</div>
                            <div class="info-value">${{ employee.salary|floatformat:2 }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card info-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary">
                            <i class="fas fa-calendar-check me-2"></i>{% trans "Mark Attendance" %}
                        </button>
                        <button class="btn btn-outline-success">
                            <i class="fas fa-money-bill me-2"></i>{% trans "Generate Payslip" %}
                        </button>
                        <button class="btn btn-outline-info">
                            <i class="fas fa-chart-bar me-2"></i>{% trans "Performance Review" %}
                        </button>
                        <button class="btn btn-outline-warning">
                            <i class="fas fa-envelope me-2"></i>{% trans "Send Message" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee Details Tabs -->
        <div class="col-lg-8">
            <div class="card info-card">
                <div class="card-header">
                    <ul class="nav nav-pills" id="employeeTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="pill" data-bs-target="#overview" type="button" role="tab">
                                <i class="fas fa-user me-2"></i>{% trans "Overview" %}
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="attendance-tab" data-bs-toggle="pill" data-bs-target="#attendance" type="button" role="tab">
                                <i class="fas fa-calendar-check me-2"></i>{% trans "Attendance" %}
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="performance-tab" data-bs-toggle="pill" data-bs-target="#performance" type="button" role="tab">
                                <i class="fas fa-chart-line me-2"></i>{% trans "Performance" %}
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="documents-tab" data-bs-toggle="pill" data-bs-target="#documents" type="button" role="tab">
                                <i class="fas fa-file-alt me-2"></i>{% trans "Documents" %}
                            </button>
                        </li>
                    </ul>
                </div>

                <div class="tab-content" id="employeeTabsContent">
                    <!-- Overview Tab -->
                    <div class="tab-pane fade show active" id="overview" role="tabpanel">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">{% trans "Personal Information" %}</h6>
                                <div class="row g-3">
                                    <div class="col-12">
                                        <div class="info-label">{% trans "Full Name" %}</div>
                                        <div class="info-value">{{ employee.user.get_full_name }}</div>
                                    </div>
                                    <div class="col-12">
                                        <div class="info-label">{% trans "Date of Birth" %}</div>
                                        <div class="info-value">{{ employee.user.date_of_birth|date:"F d, Y"|default:"Not provided" }}</div>
                                    </div>
                                    <div class="col-12">
                                        <div class="info-label">{% trans "Address" %}</div>
                                        <div class="info-value">{{ employee.user.address|default:"Not provided" }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">{% trans "Employment Details" %}</h6>
                                <div class="row g-3">
                                    <div class="col-12">
                                        <div class="info-label">{% trans "Position" %}</div>
                                        <div class="info-value">{{ employee.position.title }}</div>
                                    </div>
                                    <div class="col-12">
                                        <div class="info-label">{% trans "Employment Status" %}</div>
                                        <div class="info-value">{{ employee.get_employment_status_display }}</div>
                                    </div>
                                    {% if employee.termination_date %}
                                    <div class="col-12">
                                        <div class="info-label">{% trans "Termination Date" %}</div>
                                        <div class="info-value">{{ employee.termination_date|date:"F d, Y" }}</div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-12">
                                <h6 class="text-primary mb-3">{% trans "Emergency Contact" %}</h6>
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="info-label">{% trans "Contact Name" %}</div>
                                        <div class="info-value">{{ employee.emergency_contact_name|default:"Not provided" }}</div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="info-label">{% trans "Contact Phone" %}</div>
                                        <div class="info-value">{{ employee.emergency_contact_phone|default:"Not provided" }}</div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="info-label">{% trans "Relationship" %}</div>
                                        <div class="info-value">{{ employee.emergency_contact_relationship|default:"Not provided" }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance Tab -->
                    <div class="tab-pane fade" id="attendance" role="tabpanel">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="text-success">92%</h3>
                                    <p class="text-muted">{% trans "Attendance Rate" %}</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="text-primary">23</h3>
                                    <p class="text-muted">{% trans "Days Present" %}</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="text-warning">2</h3>
                                    <p class="text-muted">{% trans "Days Absent" %}</p>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <h6 class="text-primary mb-3">{% trans "Recent Attendance" %}</h6>
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="mb-1">{% trans "Present" %}</h6>
                                        <small class="text-muted">{% trans "Today" %} - 08:30 AM</small>
                                    </div>
                                    <span class="badge bg-success">{% trans "On Time" %}</span>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="mb-1">{% trans "Present" %}</h6>
                                        <small class="text-muted">{% trans "Yesterday" %} - 08:45 AM</small>
                                    </div>
                                    <span class="badge bg-warning">{% trans "Late" %}</span>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="mb-1">{% trans "Present" %}</h6>
                                        <small class="text-muted">{% trans "2 days ago" %} - 08:25 AM</small>
                                    </div>
                                    <span class="badge bg-success">{% trans "On Time" %}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Tab -->
                    <div class="tab-pane fade" id="performance" role="tabpanel">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">{% trans "Performance Metrics" %}</h6>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>{% trans "Quality of Work" %}</span>
                                        <span>4.5/5</span>
                                    </div>
                                    <div class="performance-meter">
                                        <div class="performance-bar bg-success" style="width: 90%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>{% trans "Productivity" %}</span>
                                        <span>4.2/5</span>
                                    </div>
                                    <div class="performance-meter">
                                        <div class="performance-bar bg-info" style="width: 84%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>{% trans "Communication" %}</span>
                                        <span>4.8/5</span>
                                    </div>
                                    <div class="performance-meter">
                                        <div class="performance-bar bg-success" style="width: 96%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">{% trans "Recent Evaluations" %}</h6>
                                <div class="timeline">
                                    <div class="timeline-item">
                                        <div>
                                            <h6 class="mb-1">{% trans "Quarterly Review" %}</h6>
                                            <p class="mb-1">{% trans "Overall Rating: 4.5/5" %}</p>
                                            <small class="text-muted">{% trans "March 2024" %}</small>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div>
                                            <h6 class="mb-1">{% trans "Mid-Year Review" %}</h6>
                                            <p class="mb-1">{% trans "Overall Rating: 4.3/5" %}</p>
                                            <small class="text-muted">{% trans "June 2023" %}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Tab -->
                    <div class="tab-pane fade" id="documents" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-primary mb-0">{% trans "Employee Documents" %}</h6>
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-upload me-2"></i>{% trans "Upload Document" %}
                            </button>
                        </div>
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-file-pdf fa-2x text-danger me-3"></i>
                                            <div>
                                                <h6 class="mb-1">{% trans "Employment Contract" %}</h6>
                                                <small class="text-muted">{% trans "Uploaded on" %} Jan 15, 2024</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-file-image fa-2x text-primary me-3"></i>
                                            <div>
                                                <h6 class="mb-1">{% trans "ID Copy" %}</h6>
                                                <small class="text-muted">{% trans "Uploaded on" %} Jan 10, 2024</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
