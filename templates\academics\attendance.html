{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Student Attendance" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>
                        <i class="fas fa-clipboard-check text-primary me-2"></i>{% trans "Student Attendance" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage and track student attendance records" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:take_attendance' %}" class="btn btn-primary me-2">
                        <i class="fas fa-users me-2"></i>{% trans "Take Attendance" %}
                    </a>
                    <a href="{% url 'academics:attendance_create' %}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Record" %}
                    </a>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-file-export me-2"></i>{% trans "Export" %}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{% url 'academics:attendance_export' %}?format=pdf">{% trans "Export as PDF" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'academics:attendance_export' %}?format=excel">{% trans "Export as Excel" %}</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter options -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="class" class="form-label">{% trans "Class" %}</label>
                            <select class="form-select" id="class" name="class">
                                <option value="">{% trans "All Classes" %}</option>
                                {% for class_obj in classes %}
                                    <option value="{{ class_obj.id }}" {% if request.GET.class == class_obj.id|stringformat:"i" %}selected{% endif %}>
                                        {{ class_obj.grade.name }} - {{ class_obj.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="subject" class="form-label">{% trans "Subject" %}</label>
                            <select class="form-select" id="subject" name="subject">
                                <option value="">{% trans "All Subjects" %}</option>
                                {% for subject in subjects %}
                                    <option value="{{ subject.id }}" {% if request.GET.subject == subject.id|stringformat:"i" %}selected{% endif %}>
                                        {{ subject.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request.GET.date_from|default:'' }}">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request.GET.date_to|default:'' }}">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">{% trans "Status" %}</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">{% trans "All" %}</option>
                                <option value="present" {% if request.GET.status == 'present' %}selected{% endif %}>{% trans "Present" %}</option>
                                <option value="absent" {% if request.GET.status == 'absent' %}selected{% endif %}>{% trans "Absent" %}</option>
                                <option value="late" {% if request.GET.status == 'late' %}selected{% endif %}>{% trans "Late" %}</option>
                                <option value="excused" {% if request.GET.status == 'excused' %}selected{% endif %}>{% trans "Excused" %}</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>{% trans "Apply Filters" %}
                            </button>
                            <a href="{% url 'academics:attendance' %}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-redo me-2"></i>{% trans "Reset" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Total Records" %}</h5>
                    <h2 class="display-4">{{ total_records|default:"0" }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Present" %}</h5>
                    <h2 class="display-4">{{ present_count|default:"0" }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Late" %}</h5>
                    <h2 class="display-4">{{ late_count|default:"0" }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Absent" %}</h5>
                    <h2 class="display-4">{{ absent_count|default:"0" }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance records -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{% trans "Attendance Records" %}</h5>
                </div>
                <div class="card-body">
                    {% if attendance_records %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Student" %}</th>
                                        <th>{% trans "Class" %}</th>
                                        <th>{% trans "Subject" %}</th>
                                        <th>{% trans "Date" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Marked By" %}</th>
                                        <th>{% trans "Notes" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in attendance_records %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'students:detail' record.student.id %}">
                                                    {{ record.student.full_name }}
                                                </a>
                                            </td>
                                            <td>{{ record.class_subject.class_obj }}</td>
                                            <td>{{ record.class_subject.subject.name }}</td>
                                            <td>{{ record.date }}</td>
                                            <td>
                                                {% if record.status == 'present' %}
                                                    <span class="badge bg-success">{% trans "Present" %}</span>
                                                {% elif record.status == 'absent' %}
                                                    <span class="badge bg-danger">{% trans "Absent" %}</span>
                                                {% elif record.status == 'late' %}
                                                    <span class="badge bg-warning text-dark">{% trans "Late" %}</span>
                                                {% elif record.status == 'excused' %}
                                                    <span class="badge bg-info">{% trans "Excused" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ record.marked_by.get_full_name }}</td>
                                            <td>
                                                {% if record.notes %}
                                                    <span data-bs-toggle="tooltip" title="{{ record.notes }}">
                                                        <i class="fas fa-comment-dots"></i>
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{% url 'academics:attendance_detail' record.id %}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'academics:attendance_update' record.id %}" class="btn btn-sm btn-outline-secondary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        {% if attendance_records.has_other_pages %}
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if attendance_records.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ attendance_records.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for i in attendance_records.paginator.page_range %}
                                        {% if attendance_records.number == i %}
                                            <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                                        {% elif i > attendance_records.number|add:'-3' and i < attendance_records.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if attendance_records.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ attendance_records.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ attendance_records.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>{% trans "No attendance records found matching your criteria." %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
    
    // Class selector change - update subjects dropdown
    const classSelect = document.getElementById('class');
    const subjectSelect = document.getElementById('subject');
    
    if (classSelect && subjectSelect) {
        classSelect.addEventListener('change', function() {
            // Here you would load subjects for the selected class
            // This would typically be an AJAX call to get subjects for the selected class
            console.log('Class changed:', this.value);
        });
    }
});
</script>
{% endblock %}