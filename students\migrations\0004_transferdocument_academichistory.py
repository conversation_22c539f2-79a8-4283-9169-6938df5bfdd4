# Generated by Django 5.2.4 on 2025-07-30 08:44

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("students", "0003_class_created_by_class_school_class_updated_by_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="TransferDocument",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "document_type",
                    models.CharField(
                        choices=[
                            ("transfer_certificate", "Transfer Certificate"),
                            ("academic_transcript", "Academic Transcript"),
                            ("conduct_certificate", "Conduct Certificate"),
                            ("attendance_record", "Attendance Record"),
                            ("medical_record", "Medical Record"),
                            ("disciplinary_record", "Disciplinary Record"),
                        ],
                        max_length=30,
                        verbose_name="Document Type",
                    ),
                ),
                (
                    "document_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Document Number"
                    ),
                ),
                (
                    "issue_date",
                    models.DateField(auto_now_add=True, verbose_name="Issue Date"),
                ),
                (
                    "content",
                    models.JSONField(
                        default=dict,
                        help_text="JSON field containing document data",
                        verbose_name="Document Content",
                    ),
                ),
                (
                    "file_path",
                    models.CharField(
                        blank=True,
                        max_length=500,
                        null=True,
                        verbose_name="Generated File Path",
                    ),
                ),
                (
                    "is_official",
                    models.BooleanField(
                        default=True, verbose_name="Is Official Document"
                    ),
                ),
                (
                    "verification_code",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="Verification Code"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "issued_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="issued_transfer_documents",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Issued By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "transfer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="students.studenttransfer",
                        verbose_name="Transfer",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transfer Document",
                "verbose_name_plural": "Transfer Documents",
                "ordering": ["-issue_date"],
            },
        ),
        migrations.CreateModel(
            name="AcademicHistory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("enrollment_date", models.DateField(verbose_name="Enrollment Date")),
                (
                    "completion_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Completion Date"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("transferred", "Transferred"),
                            ("withdrawn", "Withdrawn"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "final_grade",
                    models.CharField(
                        blank=True, max_length=5, null=True, verbose_name="Final Grade"
                    ),
                ),
                (
                    "attendance_percentage",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name="Attendance Percentage",
                    ),
                ),
                (
                    "conduct_grade",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("excellent", "Excellent"),
                            ("very_good", "Very Good"),
                            ("good", "Good"),
                            ("satisfactory", "Satisfactory"),
                            ("needs_improvement", "Needs Improvement"),
                        ],
                        max_length=20,
                        null=True,
                        verbose_name="Conduct Grade",
                    ),
                ),
                (
                    "achievements",
                    models.TextField(
                        blank=True, null=True, verbose_name="Achievements and Awards"
                    ),
                ),
                (
                    "disciplinary_actions",
                    models.TextField(
                        blank=True, null=True, verbose_name="Disciplinary Actions"
                    ),
                ),
                (
                    "teacher_comments",
                    models.TextField(
                        blank=True, null=True, verbose_name="Teacher Comments"
                    ),
                ),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.academicyear",
                        verbose_name="Academic Year",
                    ),
                ),
                (
                    "class_obj",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.class",
                        verbose_name="Class",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="academic_history",
                        to="students.student",
                        verbose_name="Student",
                    ),
                ),
                (
                    "transfer_related",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="academic_records",
                        to="students.studenttransfer",
                        verbose_name="Related Transfer",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Academic History",
                "verbose_name_plural": "Academic Histories",
                "ordering": ["-academic_year__start_date", "-enrollment_date"],
                "unique_together": {("student", "academic_year", "class_obj")},
            },
        ),
    ]
