{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Record Attendance" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2>
                <i class="fas fa-clipboard-check text-primary me-2"></i>{% trans "Record Attendance" %}
            </h2>
            <p class="text-muted">{% trans "Record student attendance for a class subject" %}</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {{ form|crispy }}
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% trans "Save Attendance" %}
                            </button>
                            <a href="{% url 'academics:attendance' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Attendance Information" %}</h5>
                </div>
                <div class="card-body">
                    <p>{% trans "Please select the student, class subject, date, and attendance status." %}</p>
                    <p>{% trans "For bulk attendance recording, use the Take Attendance feature instead." %}</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "Attendance records are used for generating reports and calculating student attendance percentages." %}
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'academics:take_attendance' %}" class="btn btn-outline-primary">
                            <i class="fas fa-users me-2"></i>{% trans "Take Bulk Attendance" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}