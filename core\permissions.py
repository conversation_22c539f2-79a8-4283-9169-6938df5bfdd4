"""
Permission classes for School ERP system
"""
from rest_framework.permissions import BasePermission, IsAuthenticated
from django.core.exceptions import PermissionDenied
from .models import School


class SchoolPermission(BasePermission):
    """
    Permission class to ensure users can only access their school's data
    """
    
    def has_permission(self, request, view):
        """
        Check if user has permission to access the view
        """
        if not request.user.is_authenticated:
            return False
        
        # Superusers can access everything
        if request.user.is_superuser:
            return True
        
        # Check if user belongs to a school
        school = self._get_user_school(request.user)
        if school:
            request.school = school
            return True
        
        return False
    
    def has_object_permission(self, request, view, obj):
        """
        Check if user has permission to access specific object
        """
        if request.user.is_superuser:
            return True
        
        # Check if object belongs to user's school
        user_school = self._get_user_school(request.user)
        if not user_school:
            return False
        
        # Check if object has school attribute
        if hasattr(obj, 'school'):
            return obj.school == user_school
        
        # For objects without school attribute, allow access
        return True
    
    def _get_user_school(self, user):
        """
        Get school associated with user
        """
        # Check employee relationship
        if hasattr(user, 'employee') and user.employee:
            return user.employee.school
        
        # Check student relationship
        if hasattr(user, 'student') and user.student:
            return user.student.school
        
        # Check parent relationship
        if hasattr(user, 'parent') and user.parent:
            return user.parent.school
        
        return None


class IsSchoolAdmin(BasePermission):
    """
    Permission for school administrators
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user is school admin
        if hasattr(request.user, 'employee') and request.user.employee:
            return request.user.employee.is_admin
        
        return False


class IsTeacher(BasePermission):
    """
    Permission for teachers
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user is a teacher
        if hasattr(request.user, 'employee') and request.user.employee:
            return request.user.employee.role in ['TEACHER', 'HEAD_TEACHER', 'ADMIN']
        
        return False


class IsStudent(BasePermission):
    """
    Permission for students
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user is a student
        return hasattr(request.user, 'student') and request.user.student


class IsParent(BasePermission):
    """
    Permission for parents
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user is a parent
        return hasattr(request.user, 'parent') and request.user.parent


class IsOwnerOrReadOnly(BasePermission):
    """
    Permission to only allow owners of an object to edit it
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for any request
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return True
        
        # Write permissions only to the owner
        if hasattr(obj, 'created_by'):
            return obj.created_by == request.user
        
        if hasattr(obj, 'user'):
            return obj.user == request.user
        
        return False


class CanManageStudents(BasePermission):
    """
    Permission for managing students
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user can manage students
        if hasattr(request.user, 'employee') and request.user.employee:
            employee = request.user.employee
            return employee.role in ['ADMIN', 'REGISTRAR', 'TEACHER', 'HEAD_TEACHER']
        
        return False


class CanManageFinance(BasePermission):
    """
    Permission for managing finances
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user can manage finances
        if hasattr(request.user, 'employee') and request.user.employee:
            employee = request.user.employee
            return employee.role in ['ADMIN', 'ACCOUNTANT', 'FINANCE_MANAGER']
        
        return False


class CanManageHR(BasePermission):
    """
    Permission for managing HR
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user can manage HR
        if hasattr(request.user, 'employee') and request.user.employee:
            employee = request.user.employee
            return employee.role in ['ADMIN', 'HR_MANAGER']
        
        return False


class CanViewReports(BasePermission):
    """
    Permission for viewing reports
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Most authenticated users can view basic reports
        if hasattr(request.user, 'employee') and request.user.employee:
            return True
        
        # Students can view their own reports
        if hasattr(request.user, 'student') and request.user.student:
            return True
        
        # Parents can view their children's reports
        if hasattr(request.user, 'parent') and request.user.parent:
            return True
        
        return False


class CanManageLibrary(BasePermission):
    """
    Permission for managing library
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user can manage library
        if hasattr(request.user, 'employee') and request.user.employee:
            employee = request.user.employee
            return employee.role in ['ADMIN', 'LIBRARIAN', 'TEACHER']
        
        return False


class CanManageTransportation(BasePermission):
    """
    Permission for managing transportation
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user can manage transportation
        if hasattr(request.user, 'employee') and request.user.employee:
            employee = request.user.employee
            return employee.role in ['ADMIN', 'TRANSPORT_MANAGER', 'DRIVER']
        
        return False


class CanManageInventory(BasePermission):
    """
    Permission for managing inventory
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user can manage inventory
        if hasattr(request.user, 'employee') and request.user.employee:
            employee = request.user.employee
            return employee.role in ['ADMIN', 'INVENTORY_MANAGER', 'MAINTENANCE']
        
        return False


class CanManageHealth(BasePermission):
    """
    Permission for managing health records
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user can manage health records
        if hasattr(request.user, 'employee') and request.user.employee:
            employee = request.user.employee
            return employee.role in ['ADMIN', 'NURSE', 'DOCTOR']
        
        return False


# Utility function to check permissions programmatically
def has_school_permission(user, school):
    """
    Check if user has permission to access school data
    """
    if user.is_superuser:
        return True
    
    user_school = None
    
    if hasattr(user, 'employee') and user.employee:
        user_school = user.employee.school
    elif hasattr(user, 'student') and user.student:
        user_school = user.student.school
    elif hasattr(user, 'parent') and user.parent:
        user_school = user.parent.school
    
    return user_school == school


def require_school_permission(user, school):
    """
    Raise PermissionDenied if user doesn't have school permission
    """
    if not has_school_permission(user, school):
        raise PermissionDenied("You don't have permission to access this school's data")