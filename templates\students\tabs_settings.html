{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Tab Settings" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-tabs text-primary me-2"></i>{% trans "Tab Settings" %}
                            </h2>
                            <p class="text-muted">{% trans "Manage tabs and their visibility" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:settings' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Settings" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Configuration -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Available Tabs" %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Tab Name" %}</th>
                                            <th>{% trans "Enabled" %}</th>
                                            <th>{% trans "Order" %}</th>
                                            <th>{% trans "Actions" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sortable-tabs">
                                        {% for tab in available_tabs %}
                                            <tr data-tab="{{ tab.name }}">
                                                <td>
                                                    <i class="fas fa-grip-vertical text-muted me-2"></i>
                                                    {{ tab.title }}
                                                </td>
                                                <td>
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="tab_{{ tab.name }}" {% if tab.enabled %}checked{% endif %}>
                                                        <label class="form-check-label" for="tab_{{ tab.name }}"></label>
                                                    </div>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" style="width: 80px;" value="{{ forloop.counter }}" min="1">
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="moveUp(this)">
                                                            <i class="fas fa-arrow-up"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="moveDown(this)">
                                                            <i class="fas fa-arrow-down"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <button type="button" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% trans "Save Tab Settings" %}
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-undo me-2"></i>{% trans "Reset to Defaults" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function moveUp(button) {
    const row = button.closest('tr');
    const prevRow = row.previousElementSibling;
    if (prevRow) {
        row.parentNode.insertBefore(row, prevRow);
        updateOrderNumbers();
    }
}

function moveDown(button) {
    const row = button.closest('tr');
    const nextRow = row.nextElementSibling;
    if (nextRow) {
        row.parentNode.insertBefore(nextRow, row);
        updateOrderNumbers();
    }
}

function updateOrderNumbers() {
    const rows = document.querySelectorAll('#sortable-tabs tr');
    rows.forEach((row, index) => {
        const orderInput = row.querySelector('input[type="number"]');
        if (orderInput) {
            orderInput.value = index + 1;
        }
    });
}

// Make table sortable with drag and drop
document.addEventListener('DOMContentLoaded', function() {
    // Add drag and drop functionality here if needed
});
</script>
{% endblock %}