"""
WebSocket consumers for real-time functionality
"""
import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from .models import School

logger = logging.getLogger(__name__)
User = get_user_model()


class NotificationConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time notifications
    """
    
    async def connect(self):
        """
        Handle WebSocket connection
        """
        self.school_code = self.scope['url_route']['kwargs']['school_code']
        self.school_group_name = f'notifications_{self.school_code}'
        
        # Check if user is authenticated and belongs to the school
        user = self.scope['user']
        if user.is_anonymous:
            await self.close()
            return
        
        # Verify user belongs to the school
        school = await self.get_school(self.school_code)
        if not school or not await self.user_belongs_to_school(user, school):
            await self.close()
            return
        
        # Join school notification group
        await self.channel_layer.group_add(
            self.school_group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"User {user.username} connected to notifications for school {self.school_code}")
    
    async def disconnect(self, close_code):
        """
        Handle WebSocket disconnection
        """
        # Leave school notification group
        await self.channel_layer.group_discard(
            self.school_group_name,
            self.channel_name
        )
        
        logger.info(f"User disconnected from notifications for school {self.school_code}")
    
    async def receive(self, text_data):
        """
        Handle messages from WebSocket
        """
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': text_data_json.get('timestamp')
                }))
        except json.JSONDecodeError:
            logger.error("Invalid JSON received in notification consumer")
    
    async def notification_message(self, event):
        """
        Handle notification messages from group
        """
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'message': event['message'],
            'title': event.get('title', ''),
            'level': event.get('level', 'info'),
            'timestamp': event.get('timestamp')
        }))
    
    @database_sync_to_async
    def get_school(self, school_code):
        """
        Get school by code
        """
        try:
            return School.objects.get(code=school_code, is_active=True)
        except School.DoesNotExist:
            return None
    
    @database_sync_to_async
    def user_belongs_to_school(self, user, school):
        """
        Check if user belongs to the school
        """
        if user.is_superuser:
            return True
        
        # Check employee relationship
        if hasattr(user, 'employee') and user.employee:
            return user.employee.school == school
        
        # Check student relationship
        if hasattr(user, 'student') and user.student:
            return user.student.school == school
        
        # Check parent relationship
        if hasattr(user, 'parent') and user.parent:
            return user.parent.school == school
        
        return False


class ChatConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time chat
    """
    
    async def connect(self):
        """
        Handle WebSocket connection
        """
        self.room_name = self.scope['url_route']['kwargs']['room_name']
        self.room_group_name = f'chat_{self.room_name}'
        
        # Check if user is authenticated
        user = self.scope['user']
        if user.is_anonymous:
            await self.close()
            return
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"User {user.username} joined chat room {self.room_name}")
    
    async def disconnect(self, close_code):
        """
        Handle WebSocket disconnection
        """
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        
        logger.info(f"User left chat room {self.room_name}")
    
    async def receive(self, text_data):
        """
        Handle messages from WebSocket
        """
        try:
            text_data_json = json.loads(text_data)
            message = text_data_json['message']
            user = self.scope['user']
            
            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': message,
                    'username': user.username,
                    'timestamp': text_data_json.get('timestamp')
                }
            )
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Invalid message format in chat consumer: {e}")
    
    async def chat_message(self, event):
        """
        Handle chat messages from group
        """
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message'],
            'username': event['username'],
            'timestamp': event.get('timestamp')
        }))


class AttendanceConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time attendance updates
    """
    
    async def connect(self):
        """
        Handle WebSocket connection
        """
        self.school_code = self.scope['url_route']['kwargs']['school_code']
        self.attendance_group_name = f'attendance_{self.school_code}'
        
        # Check if user is authenticated and authorized
        user = self.scope['user']
        if user.is_anonymous:
            await self.close()
            return
        
        # Verify user belongs to the school and can view attendance
        school = await self.get_school(self.school_code)
        if not school or not await self.user_can_view_attendance(user, school):
            await self.close()
            return
        
        # Join attendance group
        await self.channel_layer.group_add(
            self.attendance_group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"User {user.username} connected to attendance updates for school {self.school_code}")
    
    async def disconnect(self, close_code):
        """
        Handle WebSocket disconnection
        """
        # Leave attendance group
        await self.channel_layer.group_discard(
            self.attendance_group_name,
            self.channel_name
        )
        
        logger.info(f"User disconnected from attendance updates for school {self.school_code}")
    
    async def receive(self, text_data):
        """
        Handle messages from WebSocket
        """
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': text_data_json.get('timestamp')
                }))
        except json.JSONDecodeError:
            logger.error("Invalid JSON received in attendance consumer")
    
    async def attendance_update(self, event):
        """
        Handle attendance update messages from group
        """
        await self.send(text_data=json.dumps({
            'type': 'attendance_update',
            'student_id': event.get('student_id'),
            'status': event.get('status'),
            'timestamp': event.get('timestamp'),
            'class_id': event.get('class_id')
        }))
    
    @database_sync_to_async
    def get_school(self, school_code):
        """
        Get school by code
        """
        try:
            return School.objects.get(code=school_code, is_active=True)
        except School.DoesNotExist:
            return None
    
    @database_sync_to_async
    def user_can_view_attendance(self, user, school):
        """
        Check if user can view attendance for the school
        """
        if user.is_superuser:
            return True
        
        # Check if user is employee with appropriate role
        if hasattr(user, 'employee') and user.employee:
            employee = user.employee
            return (employee.school == school and 
                   employee.role in ['ADMIN', 'TEACHER', 'HEAD_TEACHER'])
        
        return False