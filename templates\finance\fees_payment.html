{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}{% trans "Student Fees Payment" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .payment-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .payment-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .student-info-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    .fee-item {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s;
    }
    .fee-item:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    .fee-item.selected {
        border-color: #007bff;
        background-color: #e3f2fd;
    }
    .payment-summary {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        border-left: 4px solid #007bff;
    }
    .btn-pay {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: bold;
    }
    .btn-pay:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(17, 153, 142, 0.4);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'finance:dashboard' %}">{% trans "Finance" %}</a></li>
                    <li class="breadcrumb-item active">{% trans "Fees Payment" %}</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-money-bill text-success me-2"></i>{% trans "Student Fees Payment" %}
                    </h2>
                    <p class="text-muted">{% trans "Process student fee payments and generate receipts" %}</p>
                </div>
                <div>
                    <a href="{% url 'finance:payment_history' %}" class="btn btn-outline-primary">
                        <i class="fas fa-history me-2"></i>{% trans "Payment History" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form method="post" id="paymentForm">
        {% csrf_token %}
        <div class="row">
            <!-- Student Selection -->
            <div class="col-lg-4 mb-4">
                <div class="card payment-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-graduate me-2"></i>{% trans "Select Student" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="student_search" class="form-label">{% trans "Search Student" %}</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="student_search" placeholder="{% trans 'Enter student name or ID' %}">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="student_select" class="form-label">{% trans "Or Select from List" %}</label>
                            <select class="form-select" id="student_select" name="student">
                                <option value="">{% trans "Choose a student..." %}</option>
                                {% for student in students %}
                                    <option value="{{ student.id }}" data-class="{{ student.current_class }}" data-fees="{{ student.pending_fees }}">
                                        {{ student.first_name }} {{ student.last_name }} - {{ student.admission_number }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Student Info Display -->
                        <div id="student_info" class="student-info-card p-3 d-none">
                            <div class="text-center">
                                <i class="fas fa-user-circle fa-3x mb-2"></i>
                                <h5 id="student_name" class="mb-1"></h5>
                                <p id="student_class" class="mb-2"></p>
                                <p id="student_id" class="mb-0 opacity-75"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fee Selection -->
            <div class="col-lg-5 mb-4">
                <div class="card payment-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list-check me-2"></i>{% trans "Select Fees to Pay" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="fee_items">
                            <div class="text-center py-4 text-muted">
                                <i class="fas fa-info-circle fa-2x mb-2"></i>
                                <p>{% trans "Please select a student to view pending fees" %}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Summary -->
            <div class="col-lg-3 mb-4">
                <div class="card payment-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>{% trans "Payment Summary" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="payment-summary">
                            <div class="d-flex justify-content-between mb-2">
                                <span>{% trans "Subtotal:" %}</span>
                                <span id="subtotal">$0.00</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>{% trans "Discount:" %}</span>
                                <span id="discount">$0.00</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>{% trans "Late Fee:" %}</span>
                                <span id="late_fee">$0.00</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>{% trans "Total Amount:" %}</strong>
                                <strong id="total_amount">$0.00</strong>
                            </div>
                            
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">{% trans "Payment Method" %}</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="cash">{% trans "Cash" %}</option>
                                    <option value="bank_transfer">{% trans "Bank Transfer" %}</option>
                                    <option value="credit_card">{% trans "Credit Card" %}</option>
                                    <option value="check">{% trans "Check" %}</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="amount_paid" class="form-label">{% trans "Amount Paid" %}</label>
                                <input type="number" class="form-control" id="amount_paid" name="amount_paid" step="0.01" min="0">
                            </div>
                            
                            <div class="mb-3">
                                <label for="payment_notes" class="form-label">{% trans "Notes" %}</label>
                                <textarea class="form-control" id="payment_notes" name="notes" rows="2" placeholder="{% trans 'Optional payment notes' %}"></textarea>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-pay" id="process_payment" disabled>
                                    <i class="fas fa-credit-card me-2"></i>{% trans "Process Payment" %}
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="clear_form">
                                    <i class="fas fa-times me-2"></i>{% trans "Clear" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!-- Recent Payments -->
    <div class="row">
        <div class="col-12">
            <div class="card payment-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>{% trans "Recent Payments" %}
                        </h5>
                        <a href="{% url 'finance:payment_history' %}" class="btn btn-sm btn-outline-primary">
                            {% trans "View All" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Fee Type" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Method" %}</th>
                                    <th>{% trans "Receipt" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2025-01-15</td>
                                    <td>Ahmed Mohamed Ali</td>
                                    <td>{% trans "Tuition Fee" %}</td>
                                    <td class="text-success">$500.00</td>
                                    <td>{% trans "Cash" %}</td>
                                    <td><span class="badge bg-success">{% trans "Generated" %}</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2025-01-14</td>
                                    <td>Fatima Hassan Ahmed</td>
                                    <td>{% trans "Transport Fee" %}</td>
                                    <td class="text-success">$50.00</td>
                                    <td>{% trans "Bank Transfer" %}</td>
                                    <td><span class="badge bg-success">{% trans "Generated" %}</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2025-01-13</td>
                                    <td>Omar Khalid Salem</td>
                                    <td>{% trans "Activity Fee" %}</td>
                                    <td class="text-success">$25.00</td>
                                    <td>{% trans "Credit Card" %}</td>
                                    <td><span class="badge bg-success">{% trans "Generated" %}</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const studentSelect = document.getElementById('student_select');
    const studentInfo = document.getElementById('student_info');
    const feeItems = document.getElementById('fee_items');
    const processPaymentBtn = document.getElementById('process_payment');
    const amountPaidInput = document.getElementById('amount_paid');
    
    // Fee data from backend
    const availableFees = {{ available_fees|safe|default:"{}" }};

    // Fallback sample data if no backend data
    const sampleFees = Object.keys(availableFees).length > 0 ? availableFees : {
        'tuition': { name: '{% trans "Tuition Fee" %}', amount: 0.00, due_date: '{% now "Y-m-d" %}' },
        'transport': { name: '{% trans "Transport Fee" %}', amount: 0.00, due_date: '{% now "Y-m-d" %}' },
        'activity': { name: '{% trans "Activity Fee" %}', amount: 0.00, due_date: '{% now "Y-m-d" %}' },
        'library': { name: '{% trans "Library Fee" %}', amount: 0.00, due_date: '{% now "Y-m-d" %}' }
    };
    
    let selectedFees = [];
    let totalAmount = 0;
    
    // Student selection handler
    studentSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        
        if (this.value) {
            // Show student info
            document.getElementById('student_name').textContent = selectedOption.text.split(' - ')[0];
            document.getElementById('student_class').textContent = selectedOption.dataset.class || 'N/A';
            document.getElementById('student_id').textContent = 'ID: ' + this.value;
            studentInfo.classList.remove('d-none');
            
            // Load fee items
            loadFeeItems();
        } else {
            studentInfo.classList.add('d-none');
            feeItems.innerHTML = '<div class="text-center py-4 text-muted"><i class="fas fa-info-circle fa-2x mb-2"></i><p>{% trans "Please select a student to view pending fees" %}</p></div>';
        }
    });
    
    function loadFeeItems() {
        let html = '';
        Object.keys(sampleFees).forEach(feeKey => {
            const fee = sampleFees[feeKey];
            html += `
                <div class="fee-item" data-fee="${feeKey}" data-amount="${fee.amount}">
                    <div class="form-check">
                        <input class="form-check-input fee-checkbox" type="checkbox" value="${feeKey}" id="fee_${feeKey}">
                        <label class="form-check-label" for="fee_${feeKey}">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>${fee.name}</strong>
                                    <br><small class="text-muted">Due: ${fee.due_date}</small>
                                </div>
                                <div class="text-end">
                                    <strong>$${fee.amount.toFixed(2)}</strong>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            `;
        });
        feeItems.innerHTML = html;
        
        // Add event listeners to checkboxes
        document.querySelectorAll('.fee-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updatePaymentSummary);
        });
    }
    
    function updatePaymentSummary() {
        selectedFees = [];
        totalAmount = 0;
        
        document.querySelectorAll('.fee-checkbox:checked').forEach(checkbox => {
            const feeKey = checkbox.value;
            const amount = parseFloat(checkbox.closest('.fee-item').dataset.amount);
            selectedFees.push(feeKey);
            totalAmount += amount;
            
            // Highlight selected items
            checkbox.closest('.fee-item').classList.add('selected');
        });
        
        // Remove highlight from unselected items
        document.querySelectorAll('.fee-checkbox:not(:checked)').forEach(checkbox => {
            checkbox.closest('.fee-item').classList.remove('selected');
        });
        
        // Update summary
        document.getElementById('subtotal').textContent = '$' + totalAmount.toFixed(2);
        document.getElementById('total_amount').textContent = '$' + totalAmount.toFixed(2);
        amountPaidInput.value = totalAmount.toFixed(2);
        
        // Enable/disable payment button
        processPaymentBtn.disabled = selectedFees.length === 0;
    }
    
    // Clear form
    document.getElementById('clear_form').addEventListener('click', function() {
        studentSelect.value = '';
        studentInfo.classList.add('d-none');
        feeItems.innerHTML = '<div class="text-center py-4 text-muted"><i class="fas fa-info-circle fa-2x mb-2"></i><p>{% trans "Please select a student to view pending fees" %}</p></div>';
        selectedFees = [];
        totalAmount = 0;
        updatePaymentSummary();
    });
    
    // Form submission
    document.getElementById('paymentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (selectedFees.length === 0) {
            alert('{% trans "Please select at least one fee to pay" %}');
            return;
        }
        
        if (!amountPaidInput.value || parseFloat(amountPaidInput.value) <= 0) {
            alert('{% trans "Please enter a valid payment amount" %}');
            return;
        }
        
        // Show confirmation
        if (confirm('{% trans "Are you sure you want to process this payment?" %}')) {
            // Here you would submit the form to the backend
            alert('{% trans "Payment processed successfully! Receipt will be generated." %}');
            // Reset form
            document.getElementById('clear_form').click();
        }
    });
});
</script>
{% endblock %}
