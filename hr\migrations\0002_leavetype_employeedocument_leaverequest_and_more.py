# Generated by Django 5.2.4 on 2025-07-13 17:08

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=100, verbose_name='Leave Type Name')),
                ('name_ar', models.Char<PERSON>ield(blank=True, max_length=100, null=True, verbose_name='Leave Type Name (Arabic)')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Leave Type Code')),
                ('max_days_per_year', models.PositiveIntegerField(default=30, verbose_name='Max Days Per Year')),
                ('is_paid', models.BooleanField(default=True, verbose_name='Is Paid Leave')),
                ('requires_approval', models.BooleanField(default=True, verbose_name='Requires Approval')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Leave Type',
                'verbose_name_plural': 'Leave Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('document_type', models.CharField(choices=[('contract', 'Employment Contract'), ('id_copy', 'ID Copy'), ('resume', 'Resume/CV'), ('certificate', 'Certificate'), ('medical', 'Medical Certificate'), ('reference', 'Reference Letter'), ('other', 'Other')], max_length=20, verbose_name='Document Type')),
                ('title', models.CharField(max_length=200, verbose_name='Document Title')),
                ('file', models.FileField(upload_to='hr/employee_documents/', verbose_name='Document File')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_confidential', models.BooleanField(default=False, verbose_name='Is Confidential')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='Expiry Date')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='hr.employee', verbose_name='Employee')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_employee_documents', to=settings.AUTH_USER_MODEL, verbose_name='Uploaded By')),
            ],
            options={
                'verbose_name': 'Employee Document',
                'verbose_name_plural': 'Employee Documents',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LeaveRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('reason', models.TextField(verbose_name='Reason')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')], default='pending', max_length=20, verbose_name='Status')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='Approved At')),
                ('rejection_reason', models.TextField(blank=True, null=True, verbose_name='Rejection Reason')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='hr/leave_attachments/', verbose_name='Attachment')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leave_requests', to=settings.AUTH_USER_MODEL, verbose_name='Approved By')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_requests', to='hr.employee', verbose_name='Employee')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_requests', to='hr.leavetype', verbose_name='Leave Type')),
            ],
            options={
                'verbose_name': 'Leave Request',
                'verbose_name_plural': 'Leave Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PayrollPeriod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=100, verbose_name='Period Name')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('is_closed', models.BooleanField(default=False, verbose_name='Is Closed')),
                ('closed_at', models.DateTimeField(blank=True, null=True, verbose_name='Closed At')),
                ('closed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='closed_payroll_periods', to=settings.AUTH_USER_MODEL, verbose_name='Closed By')),
            ],
            options={
                'verbose_name': 'Payroll Period',
                'verbose_name_plural': 'Payroll Periods',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='PerformanceEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('evaluation_period_start', models.DateField(verbose_name='Evaluation Period Start')),
                ('evaluation_period_end', models.DateField(verbose_name='Evaluation Period End')),
                ('quality_of_work', models.PositiveIntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')], validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Quality of Work')),
                ('productivity', models.PositiveIntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')], validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Productivity')),
                ('communication', models.PositiveIntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')], validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Communication')),
                ('teamwork', models.PositiveIntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')], validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Teamwork')),
                ('punctuality', models.PositiveIntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')], validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Punctuality')),
                ('initiative', models.PositiveIntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')], validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Initiative')),
                ('overall_rating', models.PositiveIntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')], validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Overall Rating')),
                ('strengths', models.TextField(verbose_name='Strengths')),
                ('areas_for_improvement', models.TextField(verbose_name='Areas for Improvement')),
                ('goals_for_next_period', models.TextField(verbose_name='Goals for Next Period')),
                ('employee_comments', models.TextField(blank=True, null=True, verbose_name='Employee Comments')),
                ('is_finalized', models.BooleanField(default=False, verbose_name='Is Finalized')),
                ('finalized_at', models.DateTimeField(blank=True, null=True, verbose_name='Finalized At')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_evaluations', to='hr.employee', verbose_name='Employee')),
                ('evaluator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conducted_evaluations', to=settings.AUTH_USER_MODEL, verbose_name='Evaluator')),
            ],
            options={
                'verbose_name': 'Performance Evaluation',
                'verbose_name_plural': 'Performance Evaluations',
                'ordering': ['-evaluation_period_end', 'employee'],
            },
        ),
        migrations.CreateModel(
            name='AttendanceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('date', models.DateField(verbose_name='Date')),
                ('check_in_time', models.TimeField(blank=True, null=True, verbose_name='Check In Time')),
                ('check_out_time', models.TimeField(blank=True, null=True, verbose_name='Check Out Time')),
                ('break_start_time', models.TimeField(blank=True, null=True, verbose_name='Break Start Time')),
                ('break_end_time', models.TimeField(blank=True, null=True, verbose_name='Break End Time')),
                ('status', models.CharField(choices=[('present', 'Present'), ('absent', 'Absent'), ('late', 'Late'), ('half_day', 'Half Day'), ('sick_leave', 'Sick Leave'), ('vacation', 'Vacation'), ('permission', 'Permission')], default='present', max_length=20, verbose_name='Status')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('is_manual', models.BooleanField(default=False, verbose_name='Is Manual Entry')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_attendance_records', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='hr.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Attendance Record',
                'verbose_name_plural': 'Attendance Records',
                'ordering': ['-date', 'employee'],
                'unique_together': {('employee', 'date')},
            },
        ),
        migrations.CreateModel(
            name='Payroll',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Basic Salary')),
                ('allowances', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Allowances')),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Overtime Hours')),
                ('overtime_rate', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Overtime Rate')),
                ('deductions', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Deductions')),
                ('tax_deduction', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Tax Deduction')),
                ('insurance_deduction', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Insurance Deduction')),
                ('net_salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Net Salary')),
                ('is_paid', models.BooleanField(default=False, verbose_name='Is Paid')),
                ('paid_date', models.DateField(blank=True, null=True, verbose_name='Paid Date')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payrolls', to='hr.employee', verbose_name='Employee')),
                ('period', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payrolls', to='hr.payrollperiod', verbose_name='Payroll Period')),
            ],
            options={
                'verbose_name': 'Payroll',
                'verbose_name_plural': 'Payrolls',
                'ordering': ['-period__start_date', 'employee'],
                'unique_together': {('employee', 'period')},
            },
        ),
    ]
