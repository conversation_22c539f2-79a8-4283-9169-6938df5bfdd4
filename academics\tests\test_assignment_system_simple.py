"""
Simplified test suite for Assignment Management System (Task 4.6)

This module tests the core assignment management functionality with minimal setup.
"""

import pytest
from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal

from core.models import School, AcademicYear, Semester
from students.models import Grade, Class
from academics.models import Subject, Teacher, ClassSubject, Assignment
from academics.assignment_system import (
    AssignmentManager, SubmissionManager, GroupManager,
    PlagiarismDetector, AnalyticsManager
)
from accounts.models import User


class SimpleAssignmentSystemTestCase(TestCase):
    """Simplified test case with minimal setup"""
    
    def setUp(self):
        """Set up minimal test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="555-0123",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date="2020-01-01"
        )
        
        # Create academic year
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date="2024-09-01",
            end_date="2025-06-30",
            is_current=True
        )
        
        # Create semester
        self.semester = Semester.objects.create(
            school=self.school,
            academic_year=self.academic_year,
            name="First Semester",
            start_date="2024-09-01",
            end_date="2025-01-31",
            is_current=True
        )
        
        # Create grade and class
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 10",
            level=10,
            max_capacity=30
        )
        
        self.class_obj = Class.objects.create(
            school=self.school,
            name="A",
            grade=self.grade,
            academic_year=self.academic_year,
            max_students=25
        )
        
        # Create subject
        self.subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH101",
            credit_hours=3,
            weekly_hours=5
        )
        self.subject.grades.add(self.grade)
        
        # Create teacher user and teacher
        self.teacher_user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        self.teacher = Teacher.objects.create(
            school=self.school,
            user=self.teacher_user,
            employee_id="T001",
            hire_date="2024-01-01",
            qualification="Master's in Mathematics"
        )
        self.teacher.subjects.add(self.subject)
        
        # Create class subject
        self.class_subject = ClassSubject.objects.create(
            school=self.school,
            class_obj=self.class_obj,
            subject=self.subject,
            teacher=self.teacher,
            academic_year=self.academic_year,
            semester=self.semester
        )
        
        # Initialize managers
        self.assignment_manager = AssignmentManager(self.school)


class AssignmentCreationSimpleTests(SimpleAssignmentSystemTestCase):
    """Test assignment creation and basic management"""
    
    def test_create_basic_assignment(self):
        """Test creating a basic assignment"""
        due_date = timezone.now() + timedelta(days=7)
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Algebra Homework",
            description="Complete exercises 1-20",
            due_date=due_date,
            assignment_type="homework",
            max_score=100,
            weight_percentage=10
        )
        
        self.assertIsNotNone(assignment)
        self.assertEqual(assignment.title, "Algebra Homework")
        self.assertEqual(assignment.class_subject, self.class_subject)
        self.assertEqual(assignment.status, "draft")
        self.assertEqual(assignment.max_score, 100)
        self.assertEqual(assignment.weight_percentage, 10)
        self.assertEqual(assignment.assignment_type, "homework")
    
    def test_assignment_properties(self):
        """Test assignment properties and methods"""
        due_date = timezone.now() + timedelta(days=7)
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Test Assignment",
            description="Test description",
            due_date=due_date,
            max_score=100
        )
        
        # Test properties
        self.assertFalse(assignment.is_overdue)
        self.assertEqual(assignment.days_until_due, 7)
        self.assertEqual(assignment.submission_count, 0)
        self.assertEqual(assignment.graded_submission_count, 0)
        self.assertIsNone(assignment.average_score)
    
    def test_publish_assignment(self):
        """Test publishing an assignment"""
        due_date = timezone.now() + timedelta(days=7)
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Test Assignment",
            description="Test description",
            due_date=due_date
        )
        
        success = self.assignment_manager.publish_assignment(assignment.id)
        
        self.assertTrue(success)
        assignment.refresh_from_db()
        self.assertEqual(assignment.status, "published")
    
    def test_assignment_statistics(self):
        """Test getting assignment statistics"""
        due_date = timezone.now() + timedelta(days=7)
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Test Assignment",
            description="Test description",
            due_date=due_date
        )
        
        stats = self.assignment_manager.get_assignment_statistics(assignment.id)
        
        self.assertIn('total_students', stats)
        self.assertIn('submitted', stats)
        self.assertIn('not_submitted', stats)
        self.assertEqual(stats['submitted'], 0)  # No submissions yet
    
    def test_overdue_assignments(self):
        """Test getting overdue assignments"""
        # Create overdue assignment
        overdue_date = timezone.now() - timedelta(days=1)
        overdue_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Overdue Assignment",
            description="This is overdue",
            due_date=overdue_date,
            status="published"
        )
        
        # Create future assignment
        future_date = timezone.now() + timedelta(days=7)
        future_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Future Assignment",
            description="This is in the future",
            due_date=future_date,
            status="published"
        )
        
        overdue_assignments = self.assignment_manager.get_overdue_assignments()
        
        self.assertEqual(overdue_assignments.count(), 1)
        self.assertEqual(overdue_assignments.first().title, "Overdue Assignment")
    
    def test_upcoming_assignments(self):
        """Test getting upcoming assignments"""
        # Create assignment due in 3 days
        upcoming_date = timezone.now() + timedelta(days=3)
        upcoming_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Upcoming Assignment",
            description="Due soon",
            due_date=upcoming_date,
            status="published"
        )
        
        # Create assignment due in 10 days
        far_future_date = timezone.now() + timedelta(days=10)
        far_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Far Future Assignment",
            description="Due later",
            due_date=far_future_date,
            status="published"
        )
        
        upcoming_assignments = self.assignment_manager.get_upcoming_assignments(days_ahead=7)
        
        self.assertEqual(upcoming_assignments.count(), 1)
        self.assertEqual(upcoming_assignments.first().title, "Upcoming Assignment")
    
    def test_assignment_workload_analysis(self):
        """Test assignment workload analysis"""
        # Create multiple assignments with different estimated durations
        for i in range(3):
            due_date = timezone.now() + timedelta(days=i*7 + 3)
            self.assignment_manager.create_assignment(
                class_subject=self.class_subject,
                title=f"Assignment {i+1}",
                description=f"Assignment {i+1} description",
                due_date=due_date,
                estimated_duration_hours=2.0 + i,
                status="published"
            )
        
        analysis = self.assignment_manager.get_assignment_workload_analysis(
            self.class_obj, 
            date_range_days=30
        )
        
        self.assertIn('total_assignments', analysis)
        self.assertIn('total_estimated_hours', analysis)
        self.assertIn('weekly_breakdown', analysis)
        self.assertEqual(analysis['total_assignments'], 3)
        self.assertEqual(analysis['total_estimated_hours'], 9.0)  # 2+3+4
    
    def test_group_assignment_creation(self):
        """Test creating a group assignment"""
        due_date = timezone.now() + timedelta(days=14)
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Math Project",
            description="Group project on statistics",
            due_date=due_date,
            assignment_type="project",
            group_assignment=True,
            max_group_size=4,
            max_score=200
        )
        
        self.assertTrue(assignment.group_assignment)
        self.assertEqual(assignment.max_group_size, 4)
        self.assertEqual(assignment.assignment_type, "project")
        self.assertEqual(assignment.max_score, 200)
    
    def test_assignment_with_rubric(self):
        """Test creating assignment with grading rubric"""
        due_date = timezone.now() + timedelta(days=10)
        rubric = {
            "criteria": [
                {"name": "Content", "points": 40, "description": "Quality of content"},
                {"name": "Organization", "points": 30, "description": "Structure and flow"},
                {"name": "Grammar", "points": 30, "description": "Language mechanics"}
            ]
        }
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Essay Assignment",
            description="Write a 500-word essay",
            due_date=due_date,
            assignment_type="essay",
            rubric=rubric,
            max_score=100
        )
        
        self.assertEqual(assignment.rubric, rubric)
        self.assertEqual(len(assignment.rubric["criteria"]), 3)
        self.assertEqual(assignment.assignment_type, "essay")
    
    def test_assignment_validation(self):
        """Test assignment validation"""
        # Test due date validation
        past_date = timezone.now() - timedelta(days=1)
        
        with self.assertRaises(ValidationError):
            assignment = Assignment(
                school=self.school,
                class_subject=self.class_subject,
                title="Invalid Assignment",
                description="Invalid due date",
                assigned_date=timezone.now(),
                due_date=past_date
            )
            assignment.clean()
    
    def test_late_penalty_calculation(self):
        """Test late penalty calculation"""
        due_date = timezone.now() - timedelta(days=2)  # 2 days overdue
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Late Penalty Test",
            description="Test late penalty calculation",
            due_date=due_date,
            late_submission_allowed=True,
            late_penalty_per_day=10,  # 10% per day
            max_late_days=5
        )
        
        # Test penalty calculation
        submission_date = timezone.now()
        penalty = assignment.calculate_late_penalty(submission_date)
        
        self.assertEqual(penalty, 20)  # 2 days * 10% = 20%
    
    def test_assignment_managers_initialization(self):
        """Test that all manager classes can be initialized"""
        assignment_manager = AssignmentManager(self.school)
        submission_manager = SubmissionManager(self.school)
        group_manager = GroupManager(self.school)
        plagiarism_detector = PlagiarismDetector(self.school)
        analytics_manager = AnalyticsManager(self.school)
        
        self.assertIsNotNone(assignment_manager)
        self.assertIsNotNone(submission_manager)
        self.assertIsNotNone(group_manager)
        self.assertIsNotNone(plagiarism_detector)
        self.assertIsNotNone(analytics_manager)
        
        # Test that they have the correct school
        self.assertEqual(assignment_manager.school, self.school)
        self.assertEqual(submission_manager.school, self.school)
        self.assertEqual(group_manager.school, self.school)
        self.assertEqual(plagiarism_detector.school, self.school)
        self.assertEqual(analytics_manager.school, self.school)


if __name__ == '__main__':
    pytest.main([__file__])