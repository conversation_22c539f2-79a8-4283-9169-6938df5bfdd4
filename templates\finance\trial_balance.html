{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Trial Balance" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .trial-balance-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .trial-balance-header {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .balance-summary {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        margin: 20px 0;
    }
    .account-group {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
        border-left: 4px solid #007bff;
    }
    .account-group.assets {
        border-left-color: #28a745;
    }
    .account-group.liabilities {
        border-left-color: #dc3545;
    }
    .account-group.equity {
        border-left-color: #6f42c1;
    }
    .account-group.revenue {
        border-left-color: #17a2b8;
    }
    .account-group.expenses {
        border-left-color: #fd7e14;
    }
    .balance-row {
        border-bottom: 1px solid #dee2e6;
        padding: 8px 0;
    }
    .balance-row:last-child {
        border-bottom: none;
        font-weight: bold;
        background-color: #e9ecef;
        margin-top: 10px;
        padding: 12px;
        border-radius: 5px;
    }
    .debit-amount {
        color: #28a745;
        font-weight: bold;
    }
    .credit-amount {
        color: #dc3545;
        font-weight: bold;
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .total-section {
        background-color: #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
        border: 2px solid #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-balance-scale me-2"></i>{% trans "Trial Balance" %}
                    </h1>
                    <p class="text-muted">{% trans "Financial statement showing debit and credit balances" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="exportTrialBalance()">
                        <i class="fas fa-download me-2"></i>{% trans "Export" %}
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printTrialBalance()">
                        <i class="fas fa-print me-2"></i>{% trans "Print" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filter-section">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" value="{{ date_from|default:'' }}">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" value="{{ date_to|default:'' }}">
                    </div>
                    <div class="col-md-3">
                        <label for="account_type" class="form-label">{% trans "Account Type" %}</label>
                        <select name="account_type" id="account_type" class="form-select">
                            <option value="">{% trans "All Types" %}</option>
                            <option value="assets" {% if account_type == 'assets' %}selected{% endif %}>{% trans "Assets" %}</option>
                            <option value="liabilities" {% if account_type == 'liabilities' %}selected{% endif %}>{% trans "Liabilities" %}</option>
                            <option value="equity" {% if account_type == 'equity' %}selected{% endif %}>{% trans "Equity" %}</option>
                            <option value="revenue" {% if account_type == 'revenue' %}selected{% endif %}>{% trans "Revenue" %}</option>
                            <option value="expenses" {% if account_type == 'expenses' %}selected{% endif %}>{% trans "Expenses" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>{% trans "Generate" %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Balance Summary -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card trial-balance-card">
                <div class="card-body text-center">
                    <i class="fas fa-plus-circle text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">${{ total_debits|floatformat:2|default:"0.00" }}</h4>
                    <p class="mb-0">{% trans "Total Debits" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card trial-balance-card">
                <div class="card-body text-center">
                    <i class="fas fa-minus-circle text-danger mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-danger">${{ total_credits|floatformat:2|default:"0.00" }}</h4>
                    <p class="mb-0">{% trans "Total Credits" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card trial-balance-card">
                <div class="card-body text-center">
                    <i class="fas fa-equals text-info mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-info">${{ balance_difference|floatformat:2|default:"0.00" }}</h4>
                    <p class="mb-0">{% trans "Difference" %}</p>
                    {% if balance_difference == 0 %}
                        <small class="text-success">{% trans "Balanced" %}</small>
                    {% else %}
                        <small class="text-warning">{% trans "Out of Balance" %}</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Trial Balance Report -->
    <div class="row">
        <div class="col-12">
            <div class="card trial-balance-card">
                <div class="card-header trial-balance-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>{% trans "Trial Balance Report" %}
                        </h5>
                        <div>
                            <span class="badge bg-light text-dark">
                                {% if date_from and date_to %}
                                    {{ date_from|date:"M d, Y" }} - {{ date_to|date:"M d, Y" }}
                                {% else %}
                                    {% trans "All Periods" %}
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Assets -->
                    <div class="account-group assets">
                        <h6 class="mb-3">
                            <i class="fas fa-building me-2"></i>{% trans "ASSETS" %}
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "Account Name" %}</th>
                                        <th class="text-end">{% trans "Debit" %}</th>
                                        <th class="text-end">{% trans "Credit" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if asset_accounts %}
                                        {% for account in asset_accounts %}
                                            <tr class="balance-row">
                                                <td>{{ account.name }}</td>
                                                <td class="text-end debit-amount">
                                                    {% if account.debit_balance %}${{ account.debit_balance|floatformat:2 }}{% else %}-{% endif %}
                                                </td>
                                                <td class="text-end credit-amount">
                                                    {% if account.credit_balance %}${{ account.credit_balance|floatformat:2 }}{% else %}-{% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        <tr class="balance-row">
                                            <td><strong>{% trans "Total Assets" %}</strong></td>
                                            <td class="text-end debit-amount"><strong>${{ total_asset_debits|floatformat:2|default:"0.00" }}</strong></td>
                                            <td class="text-end credit-amount"><strong>${{ total_asset_credits|floatformat:2|default:"0.00" }}</strong></td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">{% trans "No asset accounts found" %}</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Liabilities -->
                    <div class="account-group liabilities">
                        <h6 class="mb-3">
                            <i class="fas fa-credit-card me-2"></i>{% trans "LIABILITIES" %}
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "Account Name" %}</th>
                                        <th class="text-end">{% trans "Debit" %}</th>
                                        <th class="text-end">{% trans "Credit" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if liability_accounts %}
                                        {% for account in liability_accounts %}
                                            <tr class="balance-row">
                                                <td>{{ account.name }}</td>
                                                <td class="text-end debit-amount">
                                                    {% if account.debit_balance %}${{ account.debit_balance|floatformat:2 }}{% else %}-{% endif %}
                                                </td>
                                                <td class="text-end credit-amount">
                                                    {% if account.credit_balance %}${{ account.credit_balance|floatformat:2 }}{% else %}-{% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        <tr class="balance-row">
                                            <td><strong>{% trans "Total Liabilities" %}</strong></td>
                                            <td class="text-end debit-amount"><strong>${{ total_liability_debits|floatformat:2|default:"0.00" }}</strong></td>
                                            <td class="text-end credit-amount"><strong>${{ total_liability_credits|floatformat:2|default:"0.00" }}</strong></td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">{% trans "No liability accounts found" %}</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Equity -->
                    <div class="account-group equity">
                        <h6 class="mb-3">
                            <i class="fas fa-chart-pie me-2"></i>{% trans "EQUITY" %}
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "Account Name" %}</th>
                                        <th class="text-end">{% trans "Debit" %}</th>
                                        <th class="text-end">{% trans "Credit" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if equity_accounts %}
                                        {% for account in equity_accounts %}
                                            <tr class="balance-row">
                                                <td>{{ account.name }}</td>
                                                <td class="text-end debit-amount">
                                                    {% if account.debit_balance %}${{ account.debit_balance|floatformat:2 }}{% else %}-{% endif %}
                                                </td>
                                                <td class="text-end credit-amount">
                                                    {% if account.credit_balance %}${{ account.credit_balance|floatformat:2 }}{% else %}-{% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        <tr class="balance-row">
                                            <td><strong>{% trans "Total Equity" %}</strong></td>
                                            <td class="text-end debit-amount"><strong>${{ total_equity_debits|floatformat:2|default:"0.00" }}</strong></td>
                                            <td class="text-end credit-amount"><strong>${{ total_equity_credits|floatformat:2|default:"0.00" }}</strong></td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">{% trans "No equity accounts found" %}</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Revenue -->
                    <div class="account-group revenue">
                        <h6 class="mb-3">
                            <i class="fas fa-arrow-up me-2"></i>{% trans "REVENUE" %}
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "Account Name" %}</th>
                                        <th class="text-end">{% trans "Debit" %}</th>
                                        <th class="text-end">{% trans "Credit" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if revenue_accounts %}
                                        {% for account in revenue_accounts %}
                                            <tr class="balance-row">
                                                <td>{{ account.name }}</td>
                                                <td class="text-end debit-amount">
                                                    {% if account.debit_balance %}${{ account.debit_balance|floatformat:2 }}{% else %}-{% endif %}
                                                </td>
                                                <td class="text-end credit-amount">
                                                    {% if account.credit_balance %}${{ account.credit_balance|floatformat:2 }}{% else %}-{% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        <tr class="balance-row">
                                            <td><strong>{% trans "Total Revenue" %}</strong></td>
                                            <td class="text-end debit-amount"><strong>${{ total_revenue_debits|floatformat:2|default:"0.00" }}</strong></td>
                                            <td class="text-end credit-amount"><strong>${{ total_revenue_credits|floatformat:2|default:"0.00" }}</strong></td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">{% trans "No revenue accounts found" %}</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Expenses -->
                    <div class="account-group expenses">
                        <h6 class="mb-3">
                            <i class="fas fa-arrow-down me-2"></i>{% trans "EXPENSES" %}
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "Account Name" %}</th>
                                        <th class="text-end">{% trans "Debit" %}</th>
                                        <th class="text-end">{% trans "Credit" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if expense_accounts %}
                                        {% for account in expense_accounts %}
                                            <tr class="balance-row">
                                                <td>{{ account.name }}</td>
                                                <td class="text-end debit-amount">
                                                    {% if account.debit_balance %}${{ account.debit_balance|floatformat:2 }}{% else %}-{% endif %}
                                                </td>
                                                <td class="text-end credit-amount">
                                                    {% if account.credit_balance %}${{ account.credit_balance|floatformat:2 }}{% else %}-{% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        <tr class="balance-row">
                                            <td><strong>{% trans "Total Expenses" %}</strong></td>
                                            <td class="text-end debit-amount"><strong>${{ total_expense_debits|floatformat:2|default:"0.00" }}</strong></td>
                                            <td class="text-end credit-amount"><strong>${{ total_expense_credits|floatformat:2|default:"0.00" }}</strong></td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">{% trans "No expense accounts found" %}</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Grand Total -->
                    <div class="total-section">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="mb-0">{% trans "GRAND TOTAL" %}</h5>
                            </div>
                            <div class="col-md-2 text-end">
                                <h5 class="mb-0 debit-amount">${{ total_debits|floatformat:2|default:"0.00" }}</h5>
                            </div>
                            <div class="col-md-2 text-end">
                                <h5 class="mb-0 credit-amount">${{ total_credits|floatformat:2|default:"0.00" }}</h5>
                            </div>
                        </div>
                        {% if balance_difference != 0 %}
                            <div class="row mt-2">
                                <div class="col-12">
                                    <div class="alert alert-warning mb-0">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        {% trans "Trial Balance is out of balance by" %} ${{ balance_difference|floatformat:2 }}
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <div class="row mt-2">
                                <div class="col-12">
                                    <div class="alert alert-success mb-0">
                                        <i class="fas fa-check-circle me-2"></i>
                                        {% trans "Trial Balance is in balance" %}
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportTrialBalance() {
    const params = new URLSearchParams(window.location.search);
    window.location.href = '{% url "finance:trial_balance_export" %}?' + params.toString();
}

function printTrialBalance() {
    window.print();
}

// Auto-submit form on date change
document.getElementById('date_from').addEventListener('change', function() {
    if (document.getElementById('date_to').value) {
        this.form.submit();
    }
});

document.getElementById('date_to').addEventListener('change', function() {
    if (document.getElementById('date_from').value) {
        this.form.submit();
    }
});

// Set default dates if not provided
document.addEventListener('DOMContentLoaded', function() {
    const dateFrom = document.getElementById('date_from');
    const dateTo = document.getElementById('date_to');
    
    if (!dateFrom.value) {
        const firstDayOfYear = new Date(new Date().getFullYear(), 0, 1);
        dateFrom.value = firstDayOfYear.toISOString().split('T')[0];
    }
    
    if (!dateTo.value) {
        const today = new Date();
        dateTo.value = today.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
