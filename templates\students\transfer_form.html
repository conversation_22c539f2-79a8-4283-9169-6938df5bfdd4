{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Transfer" %}
    {% else %}
        {% trans "New Transfer Request" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-exchange-alt text-primary me-2"></i>
                        {% if object %}
                            {% trans "Edit Transfer Request" %}
                        {% else %}
                            {% trans "New Transfer Request" %}
                        {% endif %}
                    </h2>
                    <p class="text-muted">
                        {% if object %}
                            {% trans "Update transfer request information" %}
                        {% else %}
                            {% trans "Create a new student transfer request" %}
                        {% endif %}
                    </p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        {{ form.student|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        {{ form.transfer_type|as_crispy_field }}
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        {{ form.transfer_date|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6" id="fromSchoolField">
                                        {{ form.from_school|as_crispy_field }}
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6" id="fromClassField">
                                        {{ form.from_class|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6" id="toSchoolField">
                                        {{ form.to_school|as_crispy_field }}
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6" id="toClassField">
                                        {{ form.to_class|as_crispy_field }}
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.reason|as_crispy_field }}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.notes|as_crispy_field }}
                                </div>
                                
                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>{% trans "Save Transfer Request" %}
                                    </button>
                                    <a href="{% url 'students:transfers' %}" class="btn btn-secondary ms-2">
                                        <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Information" %}</h5>
                        </div>
                        <div class="card-body">
                            <p>{% trans "Student transfers can be of three types:" %}</p>
                            <ul>
                                <li><strong>{% trans "Incoming" %}</strong>: {% trans "Student coming from another school" %}</li>
                                <li><strong>{% trans "Outgoing" %}</strong>: {% trans "Student leaving to another school" %}</li>
                                <li><strong>{% trans "Internal" %}</strong>: {% trans "Student moving between classes within the school" %}</li>
                            </ul>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                {% trans "Transfer requests need to be approved before they take effect." %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide fields based on transfer type
    const transferType = document.getElementById('id_transfer_type');
    const fromSchoolField = document.getElementById('fromSchoolField');
    const fromClassField = document.getElementById('fromClassField');
    const toSchoolField = document.getElementById('toSchoolField');
    const toClassField = document.getElementById('toClassField');
    
    function updateFields() {
        if (transferType.value === 'incoming') {
            fromSchoolField.style.display = 'block';
            fromClassField.style.display = 'none';
            toSchoolField.style.display = 'none';
            toClassField.style.display = 'block';
        } else if (transferType.value === 'outgoing') {
            fromSchoolField.style.display = 'none';
            fromClassField.style.display = 'block';
            toSchoolField.style.display = 'block';
            toClassField.style.display = 'none';
        } else if (transferType.value === 'internal') {
            fromSchoolField.style.display = 'none';
            fromClassField.style.display = 'block';
            toSchoolField.style.display = 'none';
            toClassField.style.display = 'block';
        } else {
            // Default - show all fields
            fromSchoolField.style.display = 'block';
            fromClassField.style.display = 'block';
            toSchoolField.style.display = 'block';
            toClassField.style.display = 'block';
        }
    }
    
    // Initial state
    updateFields();
    
    // Add event listener
    transferType.addEventListener('change', updateFields);
});
</script>
{% endblock %}