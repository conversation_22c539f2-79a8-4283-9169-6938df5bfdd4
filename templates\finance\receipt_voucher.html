{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Receipt Voucher" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .voucher-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .voucher-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .voucher-form {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
    }
    .amount-display {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        margin: 20px 0;
    }
    .voucher-preview {
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 20px;
        background-color: #fff;
    }
    .voucher-number {
        font-family: 'Courier New', monospace;
        font-weight: bold;
        color: #007bff;
    }
    .signature-box {
        border: 1px solid #dee2e6;
        border-radius: 5px;
        height: 80px;
        background-color: #f8f9fa;
        display: flex;
        align-items: end;
        justify-content: center;
        padding: 10px;
        font-size: 0.9em;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-receipt me-2"></i>{% trans "Receipt Voucher" %}
                    </h1>
                    <p class="text-muted">{% trans "Generate and manage receipt vouchers" %}</p>
                </div>
                <div>
                    <button class="btn btn-success me-2" onclick="printVoucher()">
                        <i class="fas fa-print me-2"></i>{% trans "Print" %}
                    </button>
                    <button class="btn btn-outline-primary" onclick="saveVoucher()">
                        <i class="fas fa-save me-2"></i>{% trans "Save" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Voucher Form -->
        <div class="col-lg-6">
            <div class="card voucher-card">
                <div class="card-header voucher-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>{% trans "Voucher Details" %}
                    </h5>
                </div>
                <div class="card-body">
                    <form id="voucherForm">
                        {% csrf_token %}
                        
                        <div class="voucher-form">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="voucherNumber" class="form-label">{% trans "Voucher Number" %}</label>
                                    <input type="text" class="form-control" id="voucherNumber" value="RV-{{ voucher_number|default:'001' }}" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="voucherDate" class="form-label">{% trans "Date" %}</label>
                                    <input type="date" class="form-control" id="voucherDate" value="{% now 'Y-m-d' %}">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="receivedFrom" class="form-label">{% trans "Received From" %}</label>
                                <input type="text" class="form-control" id="receivedFrom" placeholder="{% trans 'Enter payer name' %}">
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">{% trans "Amount" %}</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="amount" step="0.01" placeholder="0.00">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="paymentMethod" class="form-label">{% trans "Payment Method" %}</label>
                                    <select class="form-select" id="paymentMethod">
                                        <option value="">{% trans "Select method" %}</option>
                                        <option value="cash">{% trans "Cash" %}</option>
                                        <option value="check">{% trans "Check" %}</option>
                                        <option value="bank_transfer">{% trans "Bank Transfer" %}</option>
                                        <option value="credit_card">{% trans "Credit Card" %}</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="purpose" class="form-label">{% trans "Purpose/Description" %}</label>
                                <textarea class="form-control" id="purpose" rows="3" placeholder="{% trans 'Enter payment purpose' %}"></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="account" class="form-label">{% trans "Account" %}</label>
                                    <select class="form-select" id="account">
                                        <option value="">{% trans "Select account" %}</option>
                                        {% for account in accounts %}
                                            <option value="{{ account.id }}">{{ account.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="reference" class="form-label">{% trans "Reference Number" %}</label>
                                    <input type="text" class="form-control" id="reference" placeholder="{% trans 'Optional reference' %}">
                                </div>
                            </div>
                        </div>

                        <div class="amount-display">
                            <h4 class="mb-1" id="amountDisplay">$0.00</h4>
                            <p class="mb-0" id="amountWords">{% trans "Zero Dollars" %}</p>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Voucher Preview -->
        <div class="col-lg-6">
            <div class="card voucher-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>{% trans "Voucher Preview" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="voucher-preview" id="voucherPreview">
                        <!-- School Header -->
                        <div class="text-center mb-4">
                            <h4>{{ school_name|default:"School Name" }}</h4>
                            <p class="mb-1">{{ school_address|default:"School Address" }}</p>
                            <p class="mb-0">{{ school_phone|default:"Phone" }} | {{ school_email|default:"Email" }}</p>
                        </div>

                        <hr>

                        <!-- Voucher Header -->
                        <div class="text-center mb-4">
                            <h3 class="text-success">{% trans "RECEIPT VOUCHER" %}</h3>
                            <p class="voucher-number mb-0">No: <span id="previewVoucherNumber">RV-001</span></p>
                        </div>

                        <!-- Voucher Details -->
                        <div class="row mb-3">
                            <div class="col-6">
                                <strong>{% trans "Date:" %}</strong> <span id="previewDate">{% now "M d, Y" %}</span>
                            </div>
                            <div class="col-6 text-end">
                                <strong>{% trans "Reference:" %}</strong> <span id="previewReference">-</span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <strong>{% trans "Received From:" %}</strong><br>
                            <span id="previewReceivedFrom" class="text-muted">{% trans "Enter payer name" %}</span>
                        </div>

                        <div class="mb-3">
                            <strong>{% trans "Amount:" %}</strong> $<span id="previewAmount">0.00</span><br>
                            <strong>{% trans "In Words:" %}</strong> <span id="previewAmountWords">{% trans "Zero Dollars" %}</span>
                        </div>

                        <div class="mb-3">
                            <strong>{% trans "Purpose:" %}</strong><br>
                            <span id="previewPurpose" class="text-muted">{% trans "Enter payment purpose" %}</span>
                        </div>

                        <div class="row mb-4">
                            <div class="col-6">
                                <strong>{% trans "Payment Method:" %}</strong><br>
                                <span id="previewPaymentMethod" class="text-muted">{% trans "Select method" %}</span>
                            </div>
                            <div class="col-6">
                                <strong>{% trans "Account:" %}</strong><br>
                                <span id="previewAccount" class="text-muted">{% trans "Select account" %}</span>
                            </div>
                        </div>

                        <hr>

                        <!-- Signatures -->
                        <div class="row mt-4">
                            <div class="col-6">
                                <div class="signature-box">
                                    {% trans "Received By" %}
                                </div>
                                <div class="text-center mt-2">
                                    <small>{% trans "Signature & Date" %}</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="signature-box">
                                    {% trans "Authorized By" %}
                                </div>
                                <div class="text-center mt-2">
                                    <small>{% trans "Signature & Date" %}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Vouchers -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card voucher-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>{% trans "Recent Receipt Vouchers" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Voucher No." %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Received From" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Purpose" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if recent_vouchers %}
                                    {% for voucher in recent_vouchers %}
                                        <tr>
                                            <td class="voucher-number">{{ voucher.voucher_number }}</td>
                                            <td>{{ voucher.date|date:"M d, Y" }}</td>
                                            <td>{{ voucher.received_from }}</td>
                                            <td>${{ voucher.amount|floatformat:2 }}</td>
                                            <td>{{ voucher.purpose|truncatechars:50 }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="viewVoucher({{ voucher.id }})">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="printVoucher({{ voucher.id }})">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="deleteVoucher({{ voucher.id }})">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No receipt vouchers found." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('voucherForm');
    const amountField = document.getElementById('amount');
    const amountDisplay = document.getElementById('amountDisplay');
    const amountWords = document.getElementById('amountWords');

    // Update preview in real-time
    function updatePreview() {
        // Update voucher number
        document.getElementById('previewVoucherNumber').textContent = document.getElementById('voucherNumber').value;
        
        // Update date
        const date = new Date(document.getElementById('voucherDate').value);
        document.getElementById('previewDate').textContent = date.toLocaleDateString();
        
        // Update received from
        const receivedFrom = document.getElementById('receivedFrom').value;
        document.getElementById('previewReceivedFrom').textContent = receivedFrom || '{% trans "Enter payer name" %}';
        
        // Update amount
        const amount = parseFloat(document.getElementById('amount').value) || 0;
        document.getElementById('previewAmount').textContent = amount.toFixed(2);
        amountDisplay.textContent = '$' + amount.toFixed(2);
        
        // Update amount in words (simplified)
        const amountInWords = numberToWords(amount);
        document.getElementById('previewAmountWords').textContent = amountInWords;
        amountWords.textContent = amountInWords;
        
        // Update purpose
        const purpose = document.getElementById('purpose').value;
        document.getElementById('previewPurpose').textContent = purpose || '{% trans "Enter payment purpose" %}';
        
        // Update payment method
        const paymentMethod = document.getElementById('paymentMethod');
        const paymentMethodText = paymentMethod.options[paymentMethod.selectedIndex].text;
        document.getElementById('previewPaymentMethod').textContent = paymentMethodText;
        
        // Update account
        const account = document.getElementById('account');
        const accountText = account.options[account.selectedIndex].text;
        document.getElementById('previewAccount').textContent = accountText;
        
        // Update reference
        const reference = document.getElementById('reference').value;
        document.getElementById('previewReference').textContent = reference || '-';
    }

    // Simple number to words conversion (basic implementation)
    function numberToWords(amount) {
        if (amount === 0) return '{% trans "Zero Dollars" %}';
        
        const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
        const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
        const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
        
        const dollars = Math.floor(amount);
        const cents = Math.round((amount - dollars) * 100);
        
        let result = '';
        
        if (dollars > 0) {
            if (dollars < 10) {
                result = ones[dollars];
            } else if (dollars < 20) {
                result = teens[dollars - 10];
            } else if (dollars < 100) {
                result = tens[Math.floor(dollars / 10)] + (dollars % 10 > 0 ? ' ' + ones[dollars % 10] : '');
            } else {
                // Simplified for hundreds
                result = dollars.toString();
            }
            result += dollars === 1 ? ' Dollar' : ' Dollars';
        }
        
        if (cents > 0) {
            if (result) result += ' and ';
            result += cents + (cents === 1 ? ' Cent' : ' Cents');
        }
        
        return result || '{% trans "Zero Dollars" %}';
    }

    // Add event listeners
    form.addEventListener('input', updatePreview);
    form.addEventListener('change', updatePreview);

    // Initialize preview
    updatePreview();
});

function printVoucher(id = null) {
    if (id) {
        window.open(`/finance/receipt-voucher/${id}/print/`, '_blank');
    } else {
        window.print();
    }
}

function saveVoucher() {
    const form = document.getElementById('voucherForm');
    const formData = new FormData(form);
    
    // Add form data
    formData.append('voucher_number', document.getElementById('voucherNumber').value);
    formData.append('date', document.getElementById('voucherDate').value);
    formData.append('received_from', document.getElementById('receivedFrom').value);
    formData.append('amount', document.getElementById('amount').value);
    formData.append('purpose', document.getElementById('purpose').value);
    formData.append('payment_method', document.getElementById('paymentMethod').value);
    formData.append('account', document.getElementById('account').value);
    formData.append('reference', document.getElementById('reference').value);
    
    // Here you would typically send the data to the server
    alert('{% trans "Voucher saved successfully!" %}');
}

function viewVoucher(id) {
    window.location.href = `/finance/receipt-voucher/${id}/`;
}

function deleteVoucher(id) {
    if (confirm('{% trans "Are you sure you want to delete this voucher?" %}')) {
        // Implement delete functionality
        console.log('Deleting voucher:', id);
    }
}
</script>
{% endblock %}
