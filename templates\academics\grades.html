{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load dashboard_tags %}

{% block title %}{% trans "Grades Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .grade-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .grade-card:hover {
        transform: translateY(-2px);
    }
    .grade-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .grade-excellent { background-color: #d4edda; color: #155724; }
    .grade-good { background-color: #d1ecf1; color: #0c5460; }
    .grade-average { background-color: #fff3cd; color: #856404; }
    .grade-poor { background-color: #f8d7da; color: #721c24; }
    .grade-badge {
        font-size: 1.1em;
        font-weight: bold;
        padding: 8px 12px;
        border-radius: 20px;
    }
    .student-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
    .subject-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        margin-right: 8px;
    }
    .math-icon { background-color: #007bff; }
    .science-icon { background-color: #28a745; }
    .english-icon { background-color: #fd7e14; }
    .arabic-icon { background-color: #e83e8c; }
    .art-icon { background-color: #6f42c1; }
    .pe-icon { background-color: #20c997; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-line me-2"></i>{% trans "Grades Management" %}
                    </h1>
                    <p class="text-muted">{% trans "Track student performance and academic progress" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:grade_create' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Grade" %}
                    </a>
                    <a href="{% url 'academics:grade_report' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-file-alt me-2"></i>{% trans "Generate Report" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card grade-card">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">{{ total_grades|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Grades" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card grade-card">
                <div class="card-body text-center">
                    <i class="fas fa-star text-warning mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-warning">{{ average_grade|floatformat:1|default:"0.0" }}</h4>
                    <p class="mb-0">{% trans "Average Grade" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card grade-card">
                <div class="card-body text-center">
                    <i class="fas fa-trophy text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">{{ excellent_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Excellent Grades" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card grade-card">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle text-danger mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-danger">{{ failing_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Needs Attention" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="class_filter" class="form-label">{% trans "Class" %}</label>
                            <select name="class" id="class_filter" class="form-select">
                                <option value="">{% trans "All Classes" %}</option>
                                {% for class in classes %}
                                    <option value="{{ class.id }}" {% if selected_class and class.id == selected_class.id %}selected{% endif %}>
                                        {{ class.grade.name }} - {{ class.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="subject_filter" class="form-label">{% trans "Subject" %}</label>
                            <select name="subject" id="subject_filter" class="form-select">
                                <option value="">{% trans "All Subjects" %}</option>
                                {% for subject in subjects %}
                                    <option value="{{ subject.id }}" {% if selected_subject and subject.id == selected_subject.id %}selected{% endif %}>
                                        {{ subject.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="semester_filter" class="form-label">{% trans "Semester" %}</label>
                            <select name="semester" id="semester_filter" class="form-select">
                                <option value="">{% trans "All Semesters" %}</option>
                                <option value="first" {% if selected_semester == 'first' %}selected{% endif %}>{% trans "First Semester" %}</option>
                                <option value="second" {% if selected_semester == 'second' %}selected{% endif %}>{% trans "Second Semester" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                                </button>
                                <a href="{% url 'academics:grades' %}" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-times me-2"></i>{% trans "Clear" %}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Grades List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Student Grades" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Subject" %}</th>
                                    <th>{% trans "Assessment Type" %}</th>
                                    <th>{% trans "Grade" %}</th>
                                    <th>{% trans "Max Grade" %}</th>
                                    <th>{% trans "Percentage" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if grades %}
                                    {% for grade in grades %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {% if grade.student.user.profile_picture %}
                                                        <img src="{{ grade.student.user.profile_picture.url }}" alt="{{ grade.student.user.get_full_name }}" class="student-avatar me-2">
                                                    {% else %}
                                                        <div class="student-avatar bg-secondary d-flex align-items-center justify-content-center me-2">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <div class="fw-bold">{{ grade.student.user.first_name }} {{ grade.student.user.last_name }}</div>
                                                        <small class="text-muted">{{ grade.student.student_id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="subject-icon 
                                                        {% if 'math' in grade.subject.name|lower %}math-icon
                                                        {% elif 'science' in grade.subject.name|lower %}science-icon
                                                        {% elif 'english' in grade.subject.name|lower %}english-icon
                                                        {% elif 'arabic' in grade.subject.name|lower %}arabic-icon
                                                        {% elif 'art' in grade.subject.name|lower %}art-icon
                                                        {% elif 'pe' in grade.subject.name|lower or 'physical' in grade.subject.name|lower %}pe-icon
                                                        {% else %}math-icon{% endif %}">
                                                        <i class="fas fa-book"></i>
                                                    </div>
                                                    {{ grade.subject.name }}
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    {% if grade.assessment_type == 'quiz' %}{% trans "Quiz" %}
                                                    {% elif grade.assessment_type == 'exam' %}{% trans "Exam" %}
                                                    {% elif grade.assessment_type == 'assignment' %}{% trans "Assignment" %}
                                                    {% elif grade.assessment_type == 'project' %}{% trans "Project" %}
                                                    {% else %}{{ grade.assessment_type }}{% endif %}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="fw-bold fs-5">{{ grade.grade|floatformat:1 }}</span>
                                            </td>
                                            <td>{{ grade.max_grade|default:100 }}</td>
                                            <td>
                                                {% with percentage=grade.grade|mul:100|div:grade.max_grade %}
                                                    <span class="grade-badge 
                                                        {% if percentage >= 90 %}grade-excellent
                                                        {% elif percentage >= 80 %}grade-good
                                                        {% elif percentage >= 70 %}grade-average
                                                        {% else %}grade-poor{% endif %}">
                                                        {{ percentage|floatformat:0 }}%
                                                    </span>
                                                {% endwith %}
                                            </td>
                                            <td>{{ grade.date_recorded|date:"M d, Y" }}</td>
                                            <td>
                                                {% with percentage=grade.grade|mul:100|div:grade.max_grade %}
                                                    {% if percentage >= 90 %}
                                                        <span class="badge bg-success">{% trans "Excellent" %}</span>
                                                    {% elif percentage >= 80 %}
                                                        <span class="badge bg-primary">{% trans "Good" %}</span>
                                                    {% elif percentage >= 70 %}
                                                        <span class="badge bg-warning">{% trans "Average" %}</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">{% trans "Needs Improvement" %}</span>
                                                    {% endif %}
                                                {% endwith %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{% url 'academics:grade_detail' grade.id %}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'academics:grade_update' grade.id %}" class="btn btn-sm btn-outline-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No grades found. Add grades to track student performance." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form on filter change
    document.querySelectorAll('#class_filter, #subject_filter, #semester_filter').forEach(function(element) {
        element.addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>
{% endblock %}
