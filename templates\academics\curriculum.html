{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Curriculum Management" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>
                        <i class="fas fa-book text-primary me-2"></i>{% trans "Curriculum Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage academic curriculum and subject mappings" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCurriculumModal">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Curriculum" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter options -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="grade" class="form-label">{% trans "Grade" %}</label>
                            <select class="form-select" id="grade" name="grade">
                                <option value="">{% trans "All Grades" %}</option>
                                <!-- Add grade options here -->
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="academic_year" class="form-label">{% trans "Academic Year" %}</label>
                            <select class="form-select" id="academic_year" name="academic_year">
                                <option value="">{% trans "All Academic Years" %}</option>
                                <!-- Add academic year options here -->
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="status" class="form-label">{% trans "Status" %}</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">{% trans "All" %}</option>
                                <option value="active">{% trans "Active" %}</option>
                                <option value="inactive">{% trans "Inactive" %}</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>{% trans "Apply Filters" %}
                            </button>
                            <a href="{% url 'academics:curriculum' %}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-redo me-2"></i>{% trans "Reset" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Curriculum list -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{% trans "Curriculum List" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Name" %}</th>
                                    <th>{% trans "Grade" %}</th>
                                    <th>{% trans "Academic Year" %}</th>
                                    <th>{% trans "Subjects" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Created By" %}</th>
                                    <th>{% trans "Created At" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Sample data - replace with actual data -->
                                <tr>
                                    <td>Grade 10 Science Curriculum</td>
                                    <td>Grade 10</td>
                                    <td>2024-2025</td>
                                    <td>8</td>
                                    <td>
                                        <span class="badge bg-success">{% trans "Active" %}</span>
                                    </td>
                                    <td>Admin User</td>
                                    <td>2024-07-01</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="#" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Grade 11 Arts Curriculum</td>
                                    <td>Grade 11</td>
                                    <td>2024-2025</td>
                                    <td>7</td>
                                    <td>
                                        <span class="badge bg-success">{% trans "Active" %}</span>
                                    </td>
                                    <td>Admin User</td>
                                    <td>2024-07-02</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="#" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Curriculum Modal -->
<div class="modal fade" id="addCurriculumModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add New Curriculum" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="curriculumName" class="form-label">{% trans "Curriculum Name" %}</label>
                            <input type="text" class="form-control" id="curriculumName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="curriculumNameAr" class="form-label">{% trans "Curriculum Name (Arabic)" %}</label>
                            <input type="text" class="form-control" id="curriculumNameAr">
                        </div>
                        <div class="col-md-6">
                            <label for="curriculumGrade" class="form-label">{% trans "Grade" %}</label>
                            <select class="form-select" id="curriculumGrade" required>
                                <option value="">{% trans "Select Grade" %}</option>
                                <!-- Add grade options here -->
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="curriculumYear" class="form-label">{% trans "Academic Year" %}</label>
                            <select class="form-select" id="curriculumYear" required>
                                <option value="">{% trans "Select Academic Year" %}</option>
                                <!-- Add academic year options here -->
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="curriculumDescription" class="form-label">{% trans "Description" %}</label>
                            <textarea class="form-control" id="curriculumDescription" rows="3"></textarea>
                        </div>
                        <div class="col-12">
                            <label for="curriculumObjectives" class="form-label">{% trans "Learning Objectives" %}</label>
                            <textarea class="form-control" id="curriculumObjectives" rows="3"></textarea>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="curriculumActive" checked>
                                <label class="form-check-label" for="curriculumActive">
                                    {% trans "Active" %}
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Save Curriculum" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}