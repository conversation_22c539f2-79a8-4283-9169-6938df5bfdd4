{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Student Dashboard" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .dashboard-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .dashboard-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    .quick-action-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        cursor: pointer;
        transition: transform 0.2s;
    }
    .quick-action-card:hover {
        transform: scale(1.05);
        color: white;
        text-decoration: none;
    }
    .recent-activity {
        max-height: 400px;
        overflow-y: auto;
    }
    .activity-item {
        border-left: 3px solid #007bff;
        padding-left: 1rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="mb-1">
                        <i class="fas fa-tachometer-alt text-primary me-2"></i>{% trans "Student Management Dashboard" %}
                    </h2>
                    <p class="text-muted">{% trans "Overview of student affairs and quick access to common tasks" %}</p>
                </div>
            </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ total_students|default:0 }}</h3>
                    <p class="mb-0">{% trans "Total Students" %}</p>
                    <small class="opacity-75">{% trans "All enrolled students" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-plus fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ new_admissions|default:0 }}</h3>
                    <p class="mb-0">{% trans "New Admissions" %}</p>
                    <small class="opacity-75">{% trans "This month" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ active_students|default:1180 }}</h3>
                    <p class="mb-0">{% trans "Active Students" %}</p>
                    <small class="opacity-75">{% trans "Currently enrolled" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ graduating_students|default:125 }}</h3>
                    <p class="mb-0">{% trans "Graduating" %}</p>
                    <small class="opacity-75">{% trans "This year" %}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <h5 class="mb-3">{% trans "Quick Actions" %}</h5>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'students:add_student' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-user-plus fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Add Student" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'students:list' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-list fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "View All" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'students:class_list' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-chalkboard fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Classes" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'students:bulk_import' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-upload fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Bulk Import" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'students:reports' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Reports" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'students:id_cards' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-id-card fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "ID Cards" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'students:certificates' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-certificate fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Certificates" %}</h6>
                </div>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Recent Students -->
        <div class="col-lg-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>{% trans "Recent Students" %}
                        </h5>
                        <a href="{% url 'students:list' %}" class="btn btn-sm btn-outline-primary">
                            {% trans "View All" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if recent_students %}
                        <div class="list-group list-group-flush">
                            {% for student in recent_students %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                            <span class="text-white fw-bold">{{ student.first_name|first }}{{ student.last_name|first }}</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ student.first_name }} {{ student.last_name }}</h6>
                                            <small class="text-muted">{{ student.current_class|default:"No class assigned" }}</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">{{ student.admission_date|date:"M d" }}</small>
                                        <br>
                                        <span class="badge bg-success">{% trans "Active" %}</span>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">{% trans "No recent students found" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-lg-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>{% trans "Recent Activities" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="recent-activity">
                        <div class="activity-item">
                            <div class="d-flex justify-content-between">
                                <strong>{% trans "New student registered" %}</strong>
                                <small class="text-muted">2 {% trans "hours ago" %}</small>
                            </div>
                            <p class="mb-0 text-muted">{% trans "Ahmed Mohamed Ali was added to Grade 1-A" %}</p>
                        </div>
                        <div class="activity-item">
                            <div class="d-flex justify-content-between">
                                <strong>{% trans "Student information updated" %}</strong>
                                <small class="text-muted">4 {% trans "hours ago" %}</small>
                            </div>
                            <p class="mb-0 text-muted">{% trans "Fatima Hassan's contact information was updated" %}</p>
                        </div>
                        <div class="activity-item">
                            <div class="d-flex justify-content-between">
                                <strong>{% trans "Bulk import completed" %}</strong>
                                <small class="text-muted">1 {% trans "day ago" %}</small>
                            </div>
                            <p class="mb-0 text-muted">{% trans "25 new students imported successfully" %}</p>
                        </div>
                        <div class="activity-item">
                            <div class="d-flex justify-content-between">
                                <strong>{% trans "ID cards generated" %}</strong>
                                <small class="text-muted">2 {% trans "days ago" %}</small>
                            </div>
                            <p class="mb-0 text-muted">{% trans "ID cards generated for Grade 2-B students" %}</p>
                        </div>
                        <div class="activity-item">
                            <div class="d-flex justify-content-between">
                                <strong>{% trans "Student transferred" %}</strong>
                                <small class="text-muted">3 {% trans "days ago" %}</small>
                            </div>
                            <p class="mb-0 text-muted">{% trans "Omar Khalid transferred to Grade 3-A" %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Class Distribution -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Students by Class" %}
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="classDistributionChart" style="height: 300px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Pending Tasks -->
        <div class="col-lg-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>{% trans "Pending Tasks" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Pending Admissions" %}
                            <span class="badge bg-warning rounded-pill">{{ pending_admissions|default:8 }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Document Verification" %}
                            <span class="badge bg-info rounded-pill">{{ pending_documents|default:12 }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Fee Payments" %}
                            <span class="badge bg-danger rounded-pill">{{ pending_fees|default:25 }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Transfer Requests" %}
                            <span class="badge bg-secondary rounded-pill">{{ transfer_requests|default:3 }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "ID Card Requests" %}
                            <span class="badge bg-primary rounded-pill">{{ id_card_requests|default:15 }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Class Distribution Chart
        const ctx = document.getElementById('classDistributionChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Grade 1-A', 'Grade 1-B', 'Grade 2-A', 'Grade 2-B', 'Grade 3-A', 'Grade 3-B'],
                datasets: [{
                    label: '{% trans "Number of Students" %}',
                    data: [32, 28, 30, 25, 35, 29],
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe',
                        '#00f2fe'
                    ],
                    borderRadius: 10,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
