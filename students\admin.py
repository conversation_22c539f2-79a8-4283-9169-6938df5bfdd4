from django.contrib import admin
from .models import (
    Grade, Class, Parent, Student, StudentDocument, VacationRequest,
    StudentInfraction, StudentTransfer, StudentAttachment, ElectronicRegistration
)


@admin.register(Grade)
class GradeAdmin(admin.ModelAdmin):
    """
    Grade admin
    """
    list_display = ('name', 'level', 'is_active')
    list_filter = ('is_active', 'level')
    search_fields = ('name', 'name_ar')
    ordering = ('level',)


@admin.register(Class)
class ClassAdmin(admin.ModelAdmin):
    """
    Class admin
    """
    list_display = ('name', 'grade', 'academic_year', 'class_teacher', 'current_students_count', 'max_students', 'is_active')
    list_filter = ('grade', 'academic_year', 'is_active')
    search_fields = ('name', 'grade__name', 'room_number')
    raw_id_fields = ('class_teacher',)


@admin.register(Parent)
class ParentAdmin(admin.ModelAdmin):
    """
    Parent admin
    """
    list_display = ('father_name', 'mother_name', 'father_phone', 'user', 'is_active')
    list_filter = ('is_active', 'created_at')
    search_fields = ('father_name', 'mother_name', 'father_phone', 'user__username')
    raw_id_fields = ('user',)


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    """
    Student admin
    """
    list_display = ('student_id', 'full_name', 'current_class', 'parent', 'admission_date', 'is_graduated', 'is_active')
    list_filter = ('gender', 'current_class', 'is_graduated', 'is_active', 'admission_date')
    search_fields = ('student_id', 'admission_number', 'first_name', 'last_name', 'first_name_ar', 'last_name_ar')
    raw_id_fields = ('user', 'parent', 'current_class')
    readonly_fields = ('age',)

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'student_id', 'admission_number', 'first_name', 'last_name', 'first_name_ar', 'last_name_ar')
        }),
        ('Personal Details', {
            'fields': ('date_of_birth', 'gender', 'nationality', 'national_id', 'passport_number', 'blood_type', 'photo')
        }),
        ('Academic Information', {
            'fields': ('parent', 'current_class', 'admission_date', 'previous_school')
        }),
        ('Medical Information', {
            'fields': ('medical_conditions', 'allergies', 'special_needs')
        }),
        ('Status', {
            'fields': ('is_graduated', 'graduation_date', 'is_active')
        }),
    )

    def age(self, obj):
        return obj.age
    age.short_description = 'Age'


@admin.register(StudentDocument)
class StudentDocumentAdmin(admin.ModelAdmin):
    """
    Student Document admin
    """
    list_display = ('student', 'document_type', 'title', 'is_verified', 'uploaded_by', 'created_at')
    list_filter = ('document_type', 'is_verified', 'created_at')
    search_fields = ('student__first_name', 'student__last_name', 'title')
    raw_id_fields = ('student', 'uploaded_by', 'verified_by')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(VacationRequest)
class VacationRequestAdmin(admin.ModelAdmin):
    """
    Vacation Request admin
    """
    list_display = ('student', 'start_date', 'end_date', 'status', 'requested_by', 'created_at')
    list_filter = ('status', 'start_date', 'created_at')
    search_fields = ('student__first_name', 'student__last_name', 'reason')
    raw_id_fields = ('student', 'requested_by', 'approved_by')
    readonly_fields = ('created_at', 'updated_at', 'duration_days')

    def duration_days(self, obj):
        return obj.duration_days
    duration_days.short_description = 'Duration (Days)'


@admin.register(StudentInfraction)
class StudentInfractionAdmin(admin.ModelAdmin):
    """
    Student Infraction admin
    """
    list_display = ('student', 'incident_date', 'severity', 'action_taken', 'reported_by', 'parent_notified')
    list_filter = ('severity', 'action_taken', 'parent_notified', 'incident_date')
    search_fields = ('student__first_name', 'student__last_name', 'description')
    raw_id_fields = ('student', 'reported_by', 'handled_by')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(StudentTransfer)
class StudentTransferAdmin(admin.ModelAdmin):
    """
    Student Transfer admin
    """
    list_display = ('student', 'transfer_type', 'transfer_date', 'status', 'requested_by')
    list_filter = ('transfer_type', 'status', 'transfer_date')
    search_fields = ('student__first_name', 'student__last_name', 'from_school', 'to_school')
    raw_id_fields = ('student', 'from_class', 'to_class', 'requested_by', 'approved_by')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(StudentAttachment)
class StudentAttachmentAdmin(admin.ModelAdmin):
    """
    Student Attachment admin
    """
    list_display = ('student', 'title', 'attachment_type', 'uploaded_by', 'is_public', 'created_at')
    list_filter = ('attachment_type', 'is_public', 'created_at')
    search_fields = ('student__first_name', 'student__last_name', 'title')
    raw_id_fields = ('student', 'uploaded_by')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(ElectronicRegistration)
class ElectronicRegistrationAdmin(admin.ModelAdmin):
    """
    Electronic Registration admin
    """
    list_display = ('full_name', 'desired_grade', 'status', 'submitted_at', 'reviewed_by')
    list_filter = ('status', 'desired_grade', 'submitted_at')
    search_fields = ('first_name', 'last_name', 'father_name', 'father_email')
    raw_id_fields = ('desired_grade', 'reviewed_by', 'student')
    readonly_fields = ('created_at', 'updated_at', 'full_name')

    fieldsets = (
        ('Student Information', {
            'fields': ('first_name', 'last_name', 'first_name_ar', 'last_name_ar', 'date_of_birth', 'gender', 'nationality')
        }),
        ('Parent Information', {
            'fields': ('father_name', 'father_phone', 'father_email', 'mother_name', 'mother_phone')
        }),
        ('Address', {
            'fields': ('home_address',)
        }),
        ('Academic Information', {
            'fields': ('desired_grade', 'previous_school')
        }),
        ('Registration Status', {
            'fields': ('status', 'submitted_at', 'reviewed_by', 'reviewed_at', 'student', 'notes')
        }),
    )
