{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load dashboard_tags %}

{% block title %}{% trans "Admin Dashboard" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .admin-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s, box-shadow 0.2s;
    }
    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }
    .admin-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .stat-icon {
        font-size: 3rem;
        opacity: 0.8;
    }
    .quick-action-btn {
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        border: 2px solid transparent;
        transition: all 0.3s;
    }
    .quick-action-btn:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    .system-status {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
    }
    .alert-item {
        border-left: 4px solid #dc3545;
        background-color: #f8d7da;
        border-radius: 5px;
        margin-bottom: 10px;
    }
    .chart-container {
        position: relative;
        height: 300px;
    }
    .activity-item {
        border-bottom: 1px solid #eee;
        padding: 10px 0;
    }
    .activity-item:last-child {
        border-bottom: none;
    }
    .activity-time {
        font-size: 0.8rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>{% trans "Admin Dashboard" %}
                    </h1>
                    <p class="text-muted">{% trans "System overview and administrative controls" %}</p>
                </div>
                <div>
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-circle me-1"></i>{% trans "System Online" %}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- System Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-users stat-icon text-primary mb-2"></i>
                    <h3 class="text-primary">{{ total_users|default:0 }}</h3>
                    <p class="mb-0">{% trans "Total Users" %}</p>
                    <small class="text-muted">{% trans "Active system users" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-graduate stat-icon text-success mb-2"></i>
                    <h3 class="text-success">{{ total_students|default:0 }}</h3>
                    <p class="mb-0">{% trans "Total Students" %}</p>
                    <small class="text-muted">{% trans "Enrolled students" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-chalkboard-teacher stat-icon text-warning mb-2"></i>
                    <h3 class="text-warning">{{ total_teachers|default:0 }}</h3>
                    <p class="mb-0">{% trans "Total Teachers" %}</p>
                    <small class="text-muted">{% trans "Active teachers" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card admin-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-tie stat-icon text-info mb-2"></i>
                    <h3 class="text-info">{{ total_staff|default:0 }}</h3>
                    <p class="mb-0">{% trans "Total Staff" %}</p>
                    <small class="text-muted">{% trans "Administrative staff" %}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions & System Status -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card admin-card">
                <div class="card-header admin-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <a href="{% url 'students:add_student' %}" class="btn btn-outline-primary quick-action-btn w-100">
                                <i class="fas fa-user-plus d-block mb-2"></i>
                                {% trans "Add Student" %}
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{% url 'hr:employee_create' %}" class="btn btn-outline-success quick-action-btn w-100">
                                <i class="fas fa-user-tie d-block mb-2"></i>
                                {% trans "Add Employee" %}
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{% url 'core:backup' %}" class="btn btn-outline-warning quick-action-btn w-100">
                                <i class="fas fa-database d-block mb-2"></i>
                                {% trans "Backup System" %}
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{% url 'accounts:user_create' %}" class="btn btn-outline-info quick-action-btn w-100">
                                <i class="fas fa-user-cog d-block mb-2"></i>
                                {% trans "Create User" %}
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{% url 'core:sync' %}" class="btn btn-outline-secondary quick-action-btn w-100">
                                <i class="fas fa-sync d-block mb-2"></i>
                                {% trans "Sync Data" %}
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{% url 'reports:dashboard' %}" class="btn btn-outline-dark quick-action-btn w-100">
                                <i class="fas fa-chart-bar d-block mb-2"></i>
                                {% trans "View Reports" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card admin-card system-status">
                <div class="card-body">
                    <h5 class="mb-3">
                        <i class="fas fa-server me-2"></i>{% trans "System Status" %}
                    </h5>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Database" %}</span>
                            <span class="badge bg-light text-dark">{% trans "Online" %}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Storage" %}</span>
                            <span class="badge bg-light text-dark">{{ storage_usage|default:"75%" }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Last Backup" %}</span>
                            <span class="badge bg-light text-dark">{{ last_backup|default:"2 hours ago" }}</span>
                        </div>
                    </div>
                    <div class="mb-0">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Active Sessions" %}</span>
                            <span class="badge bg-light text-dark">{{ active_sessions|default:12 }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Analytics -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card admin-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "User Registration Trends" %}</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="userTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card admin-card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "System Usage Analytics" %}</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="usageChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity & System Alerts -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>{% trans "Recent System Activity" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_activities %}
                        {% for activity in recent_activities %}
                            <div class="activity-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>{{ activity.user.get_full_name|default:activity.user.username }}</strong>
                                        <span class="text-muted">{{ activity.action }}</span>
                                    </div>
                                    <span class="activity-time">{{ activity.timestamp|timesince }} {% trans "ago" %}</span>
                                </div>
                                {% if activity.description %}
                                    <small class="text-muted">{{ activity.description }}</small>
                                {% endif %}
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">{% trans "No recent activity to display" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>{% trans "System Alerts" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if system_alerts %}
                        {% for alert in system_alerts %}
                            <div class="alert-item p-3">
                                <div class="d-flex justify-content-between">
                                    <strong>{{ alert.title }}</strong>
                                    <small class="text-muted">{{ alert.created_at|timesince }} {% trans "ago" %}</small>
                                </div>
                                <p class="mb-0 mt-1">{{ alert.message }}</p>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">{% trans "No system alerts" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // User Trends Chart
    const userTrendsCtx = document.getElementById('userTrendsChart').getContext('2d');
    const userTrendsChart = new Chart(userTrendsCtx, {
        type: 'line',
        data: {
            labels: {{ user_trend_labels|safe|default:"[]" }},
            datasets: [{
                label: '{% trans "New Users" %}',
                data: {{ user_trend_data|safe|default:"[]" }},
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Usage Chart
    const usageCtx = document.getElementById('usageChart').getContext('2d');
    const usageChart = new Chart(usageCtx, {
        type: 'doughnut',
        data: {
            labels: ['{% trans "Students" %}', '{% trans "Teachers" %}', '{% trans "Staff" %}', '{% trans "Admins" %}'],
            datasets: [{
                data: [
                    {{ total_students|default:0 }},
                    {{ total_teachers|default:0 }},
                    {{ total_staff|default:0 }},
                    {{ total_admins|default:0 }}
                ],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#17a2b8',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Auto-refresh system status every 30 seconds
    setInterval(function() {
        // You can implement AJAX calls here to update system status
        console.log('Refreshing system status...');
    }, 30000);
</script>
{% endblock %}
