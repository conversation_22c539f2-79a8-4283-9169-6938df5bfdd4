{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Approve Vacation Requests" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-check-circle text-primary me-2"></i>{% trans "Approve Vacation Requests" %}
                    </h2>
                    <p class="text-muted">{% trans "Review and approve pending vacation requests" %}</p>
                </div>
            </div>

            <!-- Pending Requests -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                {% trans "Pending Vacation Requests" %}
                                <span class="badge bg-warning">{{ pending_requests.count }}</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if pending_requests %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Student" %}</th>
                                                <th>{% trans "Start Date" %}</th>
                                                <th>{% trans "End Date" %}</th>
                                                <th>{% trans "Duration" %}</th>
                                                <th>{% trans "Reason" %}</th>
                                                <th>{% trans "Requested By" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for request in pending_requests %}
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                                <span class="text-white fw-bold">{{ request.student.first_name|slice:":1" }}{{ request.student.last_name|slice:":1" }}</span>
                                                            </div>
                                                            <div>
                                                                <h6 class="mb-0">{{ request.student.full_name }}</h6>
                                                                <small class="text-muted">{{ request.student.current_class }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{{ request.start_date }}</td>
                                                    <td>{{ request.end_date }}</td>
                                                    <td>{{ request.duration_days }} {% trans "days" %}</td>
                                                    <td>{{ request.reason|truncatechars:50 }}</td>
                                                    <td>{{ request.requested_by.get_full_name }}</td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#viewModal{{ request.id }}">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#approveModal{{ request.id }}">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#rejectModal{{ request.id }}">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>{% trans "No pending vacation requests to review." %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals for each request -->
{% for request in pending_requests %}
    <!-- View Modal -->
    <div class="modal fade" id="viewModal{{ request.id }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{% trans "Vacation Request Details" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>{% trans "Student:" %}</strong> {{ request.student.full_name }}</p>
                            <p><strong>{% trans "Class:" %}</strong> {{ request.student.current_class }}</p>
                            <p><strong>{% trans "Start Date:" %}</strong> {{ request.start_date }}</p>
                            <p><strong>{% trans "End Date:" %}</strong> {{ request.end_date }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{% trans "Duration:" %}</strong> {{ request.duration_days }} {% trans "days" %}</p>
                            <p><strong>{% trans "Requested By:" %}</strong> {{ request.requested_by.get_full_name }}</p>
                            <p><strong>{% trans "Request Date:" %}</strong> {{ request.created_at|date:"d/m/Y H:i" }}</p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <p><strong>{% trans "Reason:" %}</strong></p>
                        <p class="border p-3 rounded">{{ request.reason }}</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Approve Modal -->
    <div class="modal fade" id="approveModal{{ request.id }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{% trans "Approve Vacation Request" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post">
                    {% csrf_token %}
                    <input type="hidden" name="request_id" value="{{ request.id }}">
                    <input type="hidden" name="action" value="approve">
                    <div class="modal-body">
                        <p>{% trans "Are you sure you want to approve the vacation request for" %} <strong>{{ request.student.full_name }}</strong>?</p>
                        <p><strong>{% trans "Period:" %}</strong> {{ request.start_date }} {% trans "to" %} {{ request.end_date }} ({{ request.duration_days }} {% trans "days" %})</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check me-2"></i>{% trans "Approve Request" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div class="modal fade" id="rejectModal{{ request.id }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{% trans "Reject Vacation Request" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post">
                    {% csrf_token %}
                    <input type="hidden" name="request_id" value="{{ request.id }}">
                    <input type="hidden" name="action" value="reject">
                    <div class="modal-body">
                        <p>{% trans "Are you sure you want to reject the vacation request for" %} <strong>{{ request.student.full_name }}</strong>?</p>
                        
                        <div class="mb-3">
                            <label for="rejection_reason{{ request.id }}" class="form-label">{% trans "Rejection Reason" %} <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="rejection_reason{{ request.id }}" name="rejection_reason" rows="3" required placeholder="{% trans 'Please provide a reason for rejection...' %}"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-times me-2"></i>{% trans "Reject Request" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endfor %}
{% endblock %}