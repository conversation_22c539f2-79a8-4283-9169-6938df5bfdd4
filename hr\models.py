from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from core.models import BaseModel


class Department(BaseModel):
    """
    Department model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Department Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Department Name (Arabic)')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Department Code')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    head = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_departments',
        verbose_name=_('Department Head')
    )

    class Meta:
        verbose_name = _('Department')
        verbose_name_plural = _('Departments')
        ordering = ['name']

    def __str__(self):
        return self.name


class Position(BaseModel):
    """
    Job position model
    """
    title = models.CharField(
        max_length=100,
        verbose_name=_('Position Title')
    )

    title_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Position Title (Arabic)')
    )

    department = models.ForeignKey(
        Department,
        on_delete=models.CASCADE,
        related_name='positions',
        verbose_name=_('Department')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Job Description')
    )

    requirements = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Requirements')
    )

    min_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('Minimum Salary')
    )

    max_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('Maximum Salary')
    )

    class Meta:
        verbose_name = _('Position')
        verbose_name_plural = _('Positions')
        ordering = ['department', 'title']

    def __str__(self):
        return f"{self.title} - {self.department.name}"


class Employee(BaseModel):
    """
    Employee model
    """
    EMPLOYMENT_STATUS = (
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('terminated', _('Terminated')),
        ('resigned', _('Resigned')),
        ('retired', _('Retired')),
    )

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='employee_profile',
        verbose_name=_('User Account')
    )

    employee_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Employee ID')
    )

    position = models.ForeignKey(
        Position,
        on_delete=models.CASCADE,
        related_name='employees',
        verbose_name=_('Position')
    )

    hire_date = models.DateField(
        verbose_name=_('Hire Date')
    )

    termination_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Termination Date')
    )

    employment_status = models.CharField(
        max_length=20,
        choices=EMPLOYMENT_STATUS,
        default='active',
        verbose_name=_('Employment Status')
    )

    salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Salary')
    )

    emergency_contact_name = models.CharField(
        max_length=100,
        verbose_name=_('Emergency Contact Name')
    )

    emergency_contact_phone = models.CharField(
        max_length=20,
        verbose_name=_('Emergency Contact Phone')
    )

    emergency_contact_relationship = models.CharField(
        max_length=50,
        verbose_name=_('Emergency Contact Relationship')
    )

    class Meta:
        verbose_name = _('Employee')
        verbose_name_plural = _('Employees')
        ordering = ['user__first_name', 'user__last_name']

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} ({self.employee_id})"


class AttendanceRecord(BaseModel):
    """
    Employee attendance record model
    """
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='attendance_records',
        verbose_name=_('Employee')
    )

    date = models.DateField(
        verbose_name=_('Date')
    )

    check_in_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Check In Time')
    )

    check_out_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Check Out Time')
    )

    break_start_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Break Start Time')
    )

    break_end_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Break End Time')
    )

    status = models.CharField(
        max_length=20,
        choices=[
            ('present', _('Present')),
            ('absent', _('Absent')),
            ('late', _('Late')),
            ('half_day', _('Half Day')),
            ('sick_leave', _('Sick Leave')),
            ('vacation', _('Vacation')),
            ('permission', _('Permission')),
        ],
        default='present',
        verbose_name=_('Status')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    is_manual = models.BooleanField(
        default=False,
        verbose_name=_('Is Manual Entry')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_attendance_records',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Attendance Record')
        verbose_name_plural = _('Attendance Records')
        unique_together = ['employee', 'date']
        ordering = ['-date', 'employee']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.date} ({self.status})"

    @property
    def total_hours(self):
        """Calculate total working hours"""
        if self.check_in_time and self.check_out_time and self.date:
            from datetime import datetime, timedelta
            check_in = datetime.combine(self.date, self.check_in_time)
            check_out = datetime.combine(self.date, self.check_out_time)

            # Handle overnight shifts
            if check_out < check_in:
                check_out += timedelta(days=1)

            total_time = check_out - check_in

            # Subtract break time if available
            if self.break_start_time and self.break_end_time and self.date:
                break_start = datetime.combine(self.date, self.break_start_time)
                break_end = datetime.combine(self.date, self.break_end_time)
                break_duration = break_end - break_start
                total_time -= break_duration

            return total_time.total_seconds() / 3600  # Return hours as float
        return 0


class LeaveType(BaseModel):
    """
    Leave type model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Leave Type Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Leave Type Name (Arabic)')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Leave Type Code')
    )

    max_days_per_year = models.PositiveIntegerField(
        default=30,
        verbose_name=_('Max Days Per Year')
    )

    is_paid = models.BooleanField(
        default=True,
        verbose_name=_('Is Paid Leave')
    )

    requires_approval = models.BooleanField(
        default=True,
        verbose_name=_('Requires Approval')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    class Meta:
        verbose_name = _('Leave Type')
        verbose_name_plural = _('Leave Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class LeaveRequest(BaseModel):
    """
    Employee leave request model
    """
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('cancelled', _('Cancelled')),
    ]

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='leave_requests',
        verbose_name=_('Employee')
    )

    leave_type = models.ForeignKey(
        LeaveType,
        on_delete=models.CASCADE,
        related_name='leave_requests',
        verbose_name=_('Leave Type')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    reason = models.TextField(
        verbose_name=_('Reason')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_leave_requests',
        verbose_name=_('Approved By')
    )

    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Approved At')
    )

    rejection_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Rejection Reason')
    )

    attachment = models.FileField(
        upload_to='hr/leave_attachments/',
        blank=True,
        null=True,
        verbose_name=_('Attachment')
    )

    class Meta:
        verbose_name = _('Leave Request')
        verbose_name_plural = _('Leave Requests')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.leave_type.name} ({self.start_date} to {self.end_date})"

    @property
    def duration_days(self):
        """Calculate leave duration in days"""
        return (self.end_date - self.start_date).days + 1

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.start_date and self.end_date:
            if self.start_date > self.end_date:
                raise ValidationError(_('Start date cannot be after end date.'))


class PayrollPeriod(BaseModel):
    """
    Payroll period model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Period Name')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    is_closed = models.BooleanField(
        default=False,
        verbose_name=_('Is Closed')
    )

    closed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='closed_payroll_periods',
        verbose_name=_('Closed By')
    )

    closed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Closed At')
    )

    class Meta:
        verbose_name = _('Payroll Period')
        verbose_name_plural = _('Payroll Periods')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"


class Payroll(BaseModel):
    """
    Employee payroll model
    """
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='payrolls',
        verbose_name=_('Employee')
    )

    period = models.ForeignKey(
        PayrollPeriod,
        on_delete=models.CASCADE,
        related_name='payrolls',
        verbose_name=_('Payroll Period')
    )

    basic_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Basic Salary')
    )

    allowances = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Allowances')
    )

    overtime_hours = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name=_('Overtime Hours')
    )

    overtime_rate = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Overtime Rate')
    )

    deductions = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Deductions')
    )

    tax_deduction = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Tax Deduction')
    )

    insurance_deduction = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Insurance Deduction')
    )

    net_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Net Salary')
    )

    is_paid = models.BooleanField(
        default=False,
        verbose_name=_('Is Paid')
    )

    paid_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Paid Date')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Payroll')
        verbose_name_plural = _('Payrolls')
        unique_together = ['employee', 'period']
        ordering = ['-period__start_date', 'employee']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.period.name}"

    def save(self, *args, **kwargs):
        # Auto-calculate net salary
        overtime_pay = self.overtime_hours * self.overtime_rate
        gross_salary = self.basic_salary + self.allowances + overtime_pay
        total_deductions = self.deductions + self.tax_deduction + self.insurance_deduction
        self.net_salary = gross_salary - total_deductions
        super().save(*args, **kwargs)


class PerformanceEvaluation(BaseModel):
    """
    Employee performance evaluation model
    """
    RATING_CHOICES = [
        (1, _('Poor')),
        (2, _('Below Average')),
        (3, _('Average')),
        (4, _('Good')),
        (5, _('Excellent')),
    ]

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='performance_evaluations',
        verbose_name=_('Employee')
    )

    evaluator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='conducted_evaluations',
        verbose_name=_('Evaluator')
    )

    evaluation_period_start = models.DateField(
        verbose_name=_('Evaluation Period Start')
    )

    evaluation_period_end = models.DateField(
        verbose_name=_('Evaluation Period End')
    )

    # Performance criteria ratings
    quality_of_work = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Quality of Work')
    )

    productivity = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Productivity')
    )

    communication = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Communication')
    )

    teamwork = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Teamwork')
    )

    punctuality = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Punctuality')
    )

    initiative = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Initiative')
    )

    # Overall assessment
    overall_rating = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Overall Rating')
    )

    strengths = models.TextField(
        verbose_name=_('Strengths')
    )

    areas_for_improvement = models.TextField(
        verbose_name=_('Areas for Improvement')
    )

    goals_for_next_period = models.TextField(
        verbose_name=_('Goals for Next Period')
    )

    employee_comments = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Employee Comments')
    )

    is_finalized = models.BooleanField(
        default=False,
        verbose_name=_('Is Finalized')
    )

    finalized_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Finalized At')
    )

    class Meta:
        verbose_name = _('Performance Evaluation')
        verbose_name_plural = _('Performance Evaluations')
        ordering = ['-evaluation_period_end', 'employee']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.evaluation_period_start} to {self.evaluation_period_end}"

    @property
    def average_rating(self):
        """Calculate average rating across all criteria"""
        ratings = [
            self.quality_of_work,
            self.productivity,
            self.communication,
            self.teamwork,
            self.punctuality,
            self.initiative
        ]
        return sum(ratings) / len(ratings)


class EmployeeDocument(BaseModel):
    """
    Employee document model
    """
    DOCUMENT_TYPES = [
        ('contract', _('Employment Contract')),
        ('id_copy', _('ID Copy')),
        ('resume', _('Resume/CV')),
        ('certificate', _('Certificate')),
        ('medical', _('Medical Certificate')),
        ('reference', _('Reference Letter')),
        ('other', _('Other')),
    ]

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name=_('Employee')
    )

    document_type = models.CharField(
        max_length=20,
        choices=DOCUMENT_TYPES,
        verbose_name=_('Document Type')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('Document Title')
    )

    file = models.FileField(
        upload_to='hr/employee_documents/',
        verbose_name=_('Document File')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='uploaded_employee_documents',
        verbose_name=_('Uploaded By')
    )

    is_confidential = models.BooleanField(
        default=False,
        verbose_name=_('Is Confidential')
    )

    expiry_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Expiry Date')
    )

    class Meta:
        verbose_name = _('Employee Document')
        verbose_name_plural = _('Employee Documents')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.title}"

    @property
    def is_expired(self):
        """Check if document is expired"""
        if self.expiry_date:
            from datetime import date
            return self.expiry_date < date.today()
        return False
