from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, View
from django.http import HttpResponse, JsonResponse
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import datetime, timedelta
from students.models import Student, Class, Grade
from academics.models import Teacher, Subject, StudentGrade, StudentAttendance
from hr.models import Employee, Department
from finance.models import Payment, Account
from core.models import AcademicYear

def placeholder_view(request):
    return HttpResponse("Reports module - Coming soon!")

# Reports Dashboard
class ReportsDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Basic statistics
        context['total_students'] = Student.objects.filter(is_active=True).count()
        context['total_teachers'] = Teacher.objects.filter(is_active=True).count()
        context['total_employees'] = Employee.objects.filter(is_active=True).count()
        context['total_classes'] = Class.objects.filter(is_active=True).count()

        # Recent report activities
        context['recent_reports'] = []  # Will be implemented with actual report generation tracking

        # Available report categories
        context['report_categories'] = [
            {
                'name': 'Student Reports',
                'icon': 'user-graduate',
                'count': 8,
                'url': 'reports:students'
            },
            {
                'name': 'Academic Reports',
                'icon': 'graduation-cap',
                'count': 6,
                'url': 'reports:academic'
            },
            {
                'name': 'Financial Reports',
                'icon': 'chart-line',
                'count': 5,
                'url': 'reports:financial'
            },
            {
                'name': 'HR Reports',
                'icon': 'users',
                'count': 4,
                'url': 'reports:hr'
            }
        ]

        return context

# Student Reports
class StudentReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/students.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Student statistics by grade
        context['students_by_grade'] = Student.objects.filter(is_active=True).values(
            'current_class__grade__name'
        ).annotate(count=Count('id')).order_by('current_class__grade__level')

        # Student statistics by gender
        context['students_by_gender'] = Student.objects.filter(is_active=True).values(
            'gender'
        ).annotate(count=Count('id'))

        # Recent enrollments
        last_month = timezone.now() - timedelta(days=30)
        context['recent_enrollments'] = Student.objects.filter(
            admission_date__gte=last_month,
            is_active=True
        ).count()

        # Available student reports
        context['available_reports'] = [
            {'name': 'Classes Report', 'url': 'reports:classes_report', 'description': 'Student distribution by classes'},
            {'name': 'Attendance Report', 'url': 'reports:student_attendance', 'description': 'Student attendance analysis'},
            {'name': 'Performance Report', 'url': 'reports:student_performance', 'description': 'Academic performance metrics'},
            {'name': 'Enrollment Report', 'url': 'reports:enrollment', 'description': 'Enrollment trends and statistics'},
            {'name': 'Demographics Report', 'url': 'reports:demographics', 'description': 'Student demographic analysis'},
        ]

        return context

class ClassesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/classes_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Classes with student counts
        context['classes_data'] = Class.objects.filter(is_active=True).annotate(
            student_count=Count('students', filter=Q(students__is_active=True))
        ).select_related('grade').order_by('grade__level', 'name')

        # Class capacity utilization
        context['capacity_stats'] = {
            'total_capacity': Class.objects.filter(is_active=True).aggregate(
                total=Sum('max_students')
            )['total'] or 0,
            'current_enrollment': Student.objects.filter(is_active=True).count(),
        }

        return context

class UsersLoginDataView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/login_data.html'

class StudentEmployeeSonsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/employee_sons.html'

class BrotherReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/brothers.html'

class StudentAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/student_attendance.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current date for filtering
        today = timezone.now().date()

        # Get date from request or use today
        selected_date = self.request.GET.get('date')
        if selected_date:
            try:
                selected_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            except ValueError:
                selected_date = today
        else:
            selected_date = today

        context['selected_date'] = selected_date

        # Get class filter from request
        selected_class = self.request.GET.get('class')
        if selected_class:
            try:
                selected_class = Class.objects.get(id=selected_class)
                context['selected_class'] = selected_class
            except (Class.DoesNotExist, ValueError):
                selected_class = None
        else:
            selected_class = None

        # Get all classes for dropdown
        context['classes'] = Class.objects.filter(is_active=True).select_related('grade').order_by('grade__level', 'name')

        # Get attendance records for the selected date
        attendance_query = StudentAttendance.objects.filter(
            date=selected_date,
            is_active=True
        ).select_related(
            'student',
            'student__current_class',
            'student__current_class__grade'
        )

        # Apply class filter if selected
        if selected_class:
            attendance_query = attendance_query.filter(student__current_class=selected_class)

        # Get attendance records
        attendance_records = attendance_query.order_by('student__current_class__grade__level', 'student__current_class__name', 'student__last_name')
        context['attendance_records'] = attendance_records

        # Calculate attendance statistics
        total_records = attendance_records.count()
        context['present_count'] = attendance_records.filter(status='present').count()
        context['absent_count'] = attendance_records.filter(status='absent').count()
        context['late_count'] = attendance_records.filter(status='late').count()
        context['excused_count'] = attendance_records.filter(status='excused').count()

        # Calculate percentages
        if total_records > 0:
            context['present_percentage'] = round((context['present_count'] / total_records) * 100, 1)
            context['absent_percentage'] = round((context['absent_count'] / total_records) * 100, 1)
            context['late_percentage'] = round((context['late_count'] / total_records) * 100, 1)
            context['excused_percentage'] = round((context['excused_count'] / total_records) * 100, 1)
        else:
            context['present_percentage'] = 0
            context['absent_percentage'] = 0
            context['late_percentage'] = 0
            context['excused_percentage'] = 0

        # Get attendance trends for the last 5 school days
        last_5_days = []
        current_day = selected_date
        days_checked = 0
        day_names = {
            0: 'الإثنين',
            1: 'الثلاثاء',
            2: 'الأربعاء',
            3: 'الخميس',
            4: 'الجمعة',
            5: 'السبت',
            6: 'الأحد'
        }

        # Get 5 school days (excluding weekends)
        while len(last_5_days) < 5 and days_checked < 10:
            # Skip weekends (assuming Friday and Saturday are weekends)
            if current_day.weekday() not in [4, 5]:  # 4=Friday, 5=Saturday
                last_5_days.append(current_day)
            current_day = current_day - timedelta(days=1)
            days_checked += 1

        # Get attendance data for each day
        attendance_trends = []
        for day in last_5_days:
            day_records = StudentAttendance.objects.filter(date=day)
            total_day_records = day_records.count()

            if total_day_records > 0:
                present_rate = round((day_records.filter(status='present').count() / total_day_records) * 100, 1)
                absent_rate = round((day_records.filter(status='absent').count() / total_day_records) * 100, 1)
            else:
                present_rate = 0
                absent_rate = 0

            attendance_trends.append({
                'date': day,
                'day_name': day_names[day.weekday()],
                'present_rate': present_rate,
                'absent_rate': absent_rate
            })

        # Reverse to show in chronological order
        attendance_trends.reverse()
        context['attendance_trends'] = attendance_trends

        # Get best attendance class
        if context['classes'].exists():
            best_class = None
            best_attendance_rate = 0

            for class_obj in context['classes']:
                class_records = StudentAttendance.objects.filter(
                    date=selected_date,
                    student__current_class=class_obj
                )
                total_class_records = class_records.count()

                if total_class_records > 0:
                    class_present_rate = (class_records.filter(status='present').count() / total_class_records) * 100
                    if class_present_rate > best_attendance_rate:
                        best_attendance_rate = class_present_rate
                        best_class = class_obj

            if best_class:
                context['best_class'] = best_class
                context['best_class_rate'] = round(best_attendance_rate, 1)

        # Calculate average late minutes
        late_records = attendance_records.filter(status='late')
        if late_records.exists() and hasattr(late_records.first(), 'late_minutes'):
            total_late_minutes = sum(record.late_minutes or 0 for record in late_records)
            context['avg_late_minutes'] = round(total_late_minutes / late_records.count(), 1)
        else:
            context['avg_late_minutes'] = 0

        return context

class StudentPerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/student_performance.html'

class EnrollmentReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/enrollment.html'

class DemographicsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/demographics.html'

# Financial Reports
class FinancialReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/financial.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current month data
        current_month = timezone.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        # Calculate financial metrics
        from finance.models import Payment, JournalEntry, StudentFee, Invoice

        # Total revenue this month
        context['total_revenue'] = Payment.objects.filter(
            payment_date__gte=current_month,
            payment_date__lt=next_month
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Total expenses this month
        context['total_expenses'] = JournalEntry.objects.filter(
            entry_date__gte=current_month,
            entry_date__lt=next_month,
            debit_amount__gt=0,
            account__account_type__type='expense'
        ).aggregate(total=Sum('debit_amount'))['total'] or 0

        # Net profit
        context['net_profit'] = context['total_revenue'] - context['total_expenses']

        # Outstanding fees
        context['outstanding_fees'] = StudentFee.objects.filter(
            is_paid=False
        ).aggregate(
            total=Sum('amount') - Sum('discount_amount')
        )['total'] or 0

        # Recent transactions (last 10)
        recent_payments = Payment.objects.select_related('student').order_by('-payment_date')[:5]
        recent_expenses = JournalEntry.objects.filter(
            debit_amount__gt=0
        ).select_related('account').order_by('-entry_date')[:5]

        # Combine and sort transactions
        transactions = []

        for payment in recent_payments:
            transactions.append({
                'date': payment.payment_date,
                'description': f"رسوم دراسية - {payment.student.first_name} {payment.student.last_name}" if payment.student else "دفعة مالية",
                'type': 'revenue',
                'amount': payment.amount,
                'status': 'completed',
                'id': payment.id,
                'model': 'payment'
            })

        for expense in recent_expenses:
            transactions.append({
                'date': expense.entry_date,
                'description': expense.description or f"مصروف - {expense.account.name}",
                'type': 'expense',
                'amount': expense.debit_amount,
                'status': 'completed' if expense.is_posted else 'pending',
                'id': expense.id,
                'model': 'expense'
            })

        # Sort by date (most recent first)
        transactions.sort(key=lambda x: x['date'], reverse=True)
        context['recent_transactions'] = transactions[:10]

        return context

class RevenueReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/revenue.html'

class ExpensesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/expenses.html'

class ProfitLossReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/profit_loss.html'

class CashFlowReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/cash_flow.html'

class BudgetReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/budget.html'

class FeesCollectionReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/fees_collection.html'

class OutstandingFeesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/outstanding_fees.html'

class PaymentHistoryReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/payment_history.html'

# Academic Reports
class AcademicReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/academic.html'

class GradesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/grades.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current academic year
        current_year = AcademicYear.objects.filter(is_current=True).first()

        # Get all student grades for current academic year
        # Filter by exam date within current academic year
        if current_year:
            student_grades = StudentGrade.objects.filter(
                exam__exam_date__gte=current_year.start_date,
                exam__exam_date__lte=current_year.end_date,
                is_active=True
            ).select_related(
                'student',
                'student__current_class',
                'student__current_class__grade',
                'exam__class_subject__subject'
            )
        else:
            # If no current academic year, get recent grades
            student_grades = StudentGrade.objects.filter(
                is_active=True,
                graded_at__gte=timezone.now() - timedelta(days=365)
            ).select_related(
                'student',
                'student__current_class',
                'student__current_class__grade',
                'exam__class_subject__subject'
            )

        # Calculate statistics
        if student_grades.exists():
            # Average grade (using marks_obtained)
            context['average_grade'] = student_grades.aggregate(
                avg=Avg('marks_obtained')
            )['avg'] or 0

            # Students by performance level (using percentage field or calculated percentage)
            context['excellent_students'] = student_grades.filter(percentage__gte=90).values('student').distinct().count()
            context['good_students'] = student_grades.filter(percentage__gte=80, percentage__lt=90).values('student').distinct().count()
            context['average_students'] = student_grades.filter(percentage__gte=70, percentage__lt=80).values('student').distinct().count()
            context['below_average_students'] = student_grades.filter(percentage__lt=70).values('student').distinct().count()

            # Pass rate (assuming 60 is passing grade)
            total_students = student_grades.values('student').distinct().count()
            passing_students = student_grades.filter(percentage__gte=60).values('student').distinct().count()
            context['pass_rate'] = (passing_students / total_students * 100) if total_students > 0 else 0

            # Group grades by student for display
            student_grade_data = {}
            for grade in student_grades:
                student_id = grade.student.id
                if student_id not in student_grade_data:
                    student_grade_data[student_id] = {
                        'student': grade.student,
                        'grades': {},
                        'total_score': 0,
                        'subject_count': 0
                    }

                subject_name = grade.exam.class_subject.subject.name
                student_grade_data[student_id]['grades'][subject_name] = grade.marks_obtained
                student_grade_data[student_id]['total_score'] += grade.marks_obtained
                student_grade_data[student_id]['subject_count'] += 1

            # Calculate averages and grades
            for student_id, data in student_grade_data.items():
                if data['subject_count'] > 0:
                    data['average'] = data['total_score'] / data['subject_count']
                    # Determine grade level
                    if data['average'] >= 90:
                        data['grade_level'] = 'ممتاز'
                        data['grade_color'] = 'success'
                    elif data['average'] >= 80:
                        data['grade_level'] = 'جيد جداً'
                        data['grade_color'] = 'primary'
                    elif data['average'] >= 70:
                        data['grade_level'] = 'جيد'
                        data['grade_color'] = 'info'
                    elif data['average'] >= 60:
                        data['grade_level'] = 'مقبول'
                        data['grade_color'] = 'warning'
                    else:
                        data['grade_level'] = 'راسب'
                        data['grade_color'] = 'danger'
                else:
                    data['average'] = 0
                    data['grade_level'] = 'غير محدد'
                    data['grade_color'] = 'secondary'

            context['student_grades'] = list(student_grade_data.values())

        else:
            # Default values when no grades exist
            context['average_grade'] = 0
            context['excellent_students'] = 0
            context['good_students'] = 0
            context['average_students'] = 0
            context['below_average_students'] = 0
            context['pass_rate'] = 0
            context['student_grades'] = []

        # Get all subjects for table headers
        context['subjects'] = Subject.objects.filter(is_active=True).order_by('name')

        return context

class ExamResultsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/exam_results.html'

class SubjectPerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/subject_performance.html'

class TeacherPerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/teacher_performance.html'

class ClassPerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/class_performance.html'

class StudentPerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/student_performance.html'


class ClassPerformanceExportView(LoginRequiredMixin, View):
    """Export class performance report to PDF/Excel"""

    def get(self, request, *args, **kwargs):
        from django.http import HttpResponse
        import json

        # Get class ID from query parameters
        class_id = request.GET.get('class')
        export_format = request.GET.get('format', 'pdf')

        if export_format == 'pdf':
            # For now, return a simple response - you can implement actual PDF generation
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="class_performance_{class_id}.pdf"'
            response.write(b'PDF export functionality - to be implemented')
            return response
        elif export_format == 'excel':
            response = HttpResponse(content_type='application/vnd.ms-excel')
            response['Content-Disposition'] = f'attachment; filename="class_performance_{class_id}.xlsx"'
            response.write(b'Excel export functionality - to be implemented')
            return response
        else:
            return HttpResponse('Invalid export format', status=400)


class StudentPerformanceExportView(LoginRequiredMixin, View):
    """Export student performance report to PDF/Excel"""

    def get(self, request, *args, **kwargs):
        from django.http import HttpResponse

        # Get student ID from query parameters
        student_id = request.GET.get('student')
        export_format = request.GET.get('format', 'pdf')

        if export_format == 'pdf':
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="student_performance_{student_id}.pdf"'
            response.write(b'PDF export functionality - to be implemented')
            return response
        elif export_format == 'excel':
            response = HttpResponse(content_type='application/vnd.ms-excel')
            response['Content-Disposition'] = f'attachment; filename="student_performance_{student_id}.xlsx"'
            response.write(b'Excel export functionality - to be implemented')
            return response
        else:
            return HttpResponse('Invalid export format', status=400)

class CurriculumProgressReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/curriculum_progress.html'

# Attendance Reports
class AttendanceReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/attendance.html'

class DailyAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/daily_attendance.html'

class MonthlyAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/monthly_attendance.html'

class ClassWiseAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/class_wise_attendance.html'

class StudentWiseAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/student_wise_attendance.html'

class AbsenteesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/absentees.html'

class LateArrivalsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/late_arrivals.html'

# HR Reports
class HRReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/hr.html'

class EmployeeReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/employee.html'

class PayrollReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/payroll.html'

class EmployeeAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/employee_attendance.html'

class LeaveReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/leave.html'

class EmployeePerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/employee_performance.html'

# Custom Reports
class CustomReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/custom.html'

class ReportBuilderView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/report_builder.html'

class ReportDesignerView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/report_designer.html'

class ReportTemplatesView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/report_templates.html'

# Export and Print
class ExportReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/export.html'

class PrintReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/print.html'

# Report Scheduling
class ScheduleReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/schedule.html'

class ScheduledReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/scheduled.html'

# Analytics Dashboard
class AnalyticsDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/analytics.html'

class TrendsAnalysisView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/trends.html'

class PredictiveAnalysisView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/predictions.html'
