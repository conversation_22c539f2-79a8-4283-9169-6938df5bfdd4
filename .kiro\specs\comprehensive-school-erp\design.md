# Design Document

## Overview

The Comprehensive School ERP System is designed as a modern, scalable, and modular web application built with Django and Django REST Framework. The system follows a microservices-inspired modular architecture within a monolithic Django application, ensuring maintainability, scalability, and ease of deployment. The design emphasizes real-time capabilities, multi-tenancy, internationalization, and comprehensive API support.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Load Balancer (Nginx)                    │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                     Django Application Layer                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │   Core      │ │  Students   │ │  Academics  │ │  Finance    ││
│  │   Module    │ │   Module    │ │   Module    │ │   Module    ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │     HR      │ │  Library    │ │ Transport   │ │ Inventory   ││
│  │   Module    │ │   Module    │ │   Module    │ │   Module    ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                      Service Layer                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │   Redis     │ │   Celery    │ │ WebSocket   │ │    API      ││
│  │   Cache     │ │   Tasks     │ │   Server    │ │  Gateway    ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                      Data Layer                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │ PostgreSQL  │ │    Redis    │ │   File      │ │   Search    ││
│  │  Database   │ │   Session   │ │  Storage    │ │  Engine     ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

### Technology Stack

- **Backend Framework:** Django 5.2+ with Django REST Framework
- **Database:** PostgreSQL (primary), Redis (caching/sessions)
- **Task Queue:** Celery with Redis broker
- **Real-time:** Django Channels with WebSocket support
- **Frontend:** Modern responsive web interface with HTMX/Alpine.js
- **Caching:** Redis with Django-Redis
- **Search:** PostgreSQL full-text search with optional Elasticsearch
- **File Storage:** Local storage with S3 compatibility
- **Monitoring:** Django Debug Toolbar, logging, and metrics

## Components and Interfaces

### Core Module

The core module provides foundational services and shared functionality:

```python
# core/models.py
class School(models.Model):
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=20, unique=True)
    address = models.TextField()
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    logo = models.ImageField(upload_to='school_logos/')
    settings = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

class AcademicYear(models.Model):
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    start_date = models.DateField()
    end_date = models.DateField()
    is_current = models.BooleanField(default=False)

class BaseModel(models.Model):
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True)
    
    class Meta:
        abstract = True
```

### Student Information System (SIS)

```python
# students/models.py
class Student(BaseModel):
    student_id = models.CharField(max_length=20, unique=True)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    arabic_first_name = models.CharField(max_length=100, blank=True)
    arabic_last_name = models.CharField(max_length=100, blank=True)
    date_of_birth = models.DateField()
    gender = models.CharField(max_length=1, choices=[('M', 'Male'), ('F', 'Female')])
    nationality = models.CharField(max_length=100)
    photo = models.ImageField(upload_to='student_photos/', blank=True)
    admission_date = models.DateField()
    current_grade = models.ForeignKey('academics.Grade', on_delete=models.SET_NULL, null=True)
    status = models.CharField(max_length=20, default='active')
    
class Parent(BaseModel):
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='parents')
    relationship = models.CharField(max_length=20)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    phone = models.CharField(max_length=20)
    email = models.EmailField(blank=True)
    occupation = models.CharField(max_length=100, blank=True)
    
class StudentDocument(BaseModel):
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    document_type = models.CharField(max_length=50)
    document_file = models.FileField(upload_to='student_documents/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
```

### Academic Management System

```python
# academics/models.py
class Grade(BaseModel):
    name = models.CharField(max_length=50)
    arabic_name = models.CharField(max_length=50, blank=True)
    level = models.IntegerField()
    capacity = models.IntegerField(default=30)
    
class Subject(BaseModel):
    name = models.CharField(max_length=100)
    arabic_name = models.CharField(max_length=100, blank=True)
    code = models.CharField(max_length=20)
    grade = models.ForeignKey(Grade, on_delete=models.CASCADE)
    credits = models.IntegerField(default=1)
    
class Class(BaseModel):
    grade = models.ForeignKey(Grade, on_delete=models.CASCADE)
    section = models.CharField(max_length=10)
    academic_year = models.ForeignKey('core.AcademicYear', on_delete=models.CASCADE)
    class_teacher = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True)
    students = models.ManyToManyField(Student, through='StudentEnrollment')
    
class StudentEnrollment(BaseModel):
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    class_obj = models.ForeignKey(Class, on_delete=models.CASCADE, related_name='enrollments')
    enrollment_date = models.DateField()
    status = models.CharField(max_length=20, default='active')
```

### Financial Management System

```python
# finance/models.py
class Account(BaseModel):
    code = models.CharField(max_length=20)
    name = models.CharField(max_length=200)
    arabic_name = models.CharField(max_length=200, blank=True)
    account_type = models.CharField(max_length=20)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
class Transaction(BaseModel):
    transaction_id = models.CharField(max_length=50, unique=True)
    date = models.DateField()
    description = models.TextField()
    reference = models.CharField(max_length=100, blank=True)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    
class TransactionEntry(BaseModel):
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='entries')
    account = models.ForeignKey(Account, on_delete=models.CASCADE)
    debit_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    credit_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
class FeeStructure(BaseModel):
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    academic_year = models.ForeignKey('core.AcademicYear', on_delete=models.CASCADE)
    fee_type = models.CharField(max_length=50)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    due_date = models.DateField()
```

### Human Resources Management

```python
# hr/models.py
class Employee(BaseModel):
    employee_id = models.CharField(max_length=20, unique=True)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    arabic_first_name = models.CharField(max_length=100, blank=True)
    arabic_last_name = models.CharField(max_length=100, blank=True)
    email = models.EmailField(unique=True)
    phone = models.CharField(max_length=20)
    hire_date = models.DateField()
    department = models.CharField(max_length=100)
    position = models.CharField(max_length=100)
    salary = models.DecimalField(max_digits=10, decimal_places=2)
    user = models.OneToOneField('auth.User', on_delete=models.CASCADE, null=True)
    
class Attendance(BaseModel):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    date = models.DateField()
    check_in = models.TimeField()
    check_out = models.TimeField(null=True, blank=True)
    status = models.CharField(max_length=20, default='present')
    notes = models.TextField(blank=True)
```

### Communication System

```python
# communications/models.py
class NotificationTemplate(BaseModel):
    name = models.CharField(max_length=100)
    subject = models.CharField(max_length=200)
    body = models.TextField()
    notification_type = models.CharField(max_length=20)
    is_active = models.BooleanField(default=True)
    
class Notification(BaseModel):
    recipient_type = models.CharField(max_length=20)
    recipient_id = models.IntegerField()
    subject = models.CharField(max_length=200)
    message = models.TextField()
    notification_type = models.CharField(max_length=20)
    status = models.CharField(max_length=20, default='pending')
    sent_at = models.DateTimeField(null=True, blank=True)
```

## Data Models

### Database Schema Design

The system uses PostgreSQL as the primary database with the following key design principles:

1. **Multi-tenancy:** All models inherit from `BaseModel` which includes a `school` foreign key
2. **Audit Trail:** All models include `created_at`, `updated_at`, and `created_by` fields
3. **Soft Deletion:** Important models include `is_active` or `status` fields instead of hard deletion
4. **Internationalization:** Key text fields have Arabic counterparts
5. **Indexing:** Proper database indexes on frequently queried fields

### Key Relationships

```mermaid
erDiagram
    School ||--o{ Student : contains
    School ||--o{ Employee : employs
    School ||--o{ Grade : has
    Student ||--o{ Parent : "has parents"
    Student ||--o{ StudentEnrollment : "enrolled in"
    Class ||--o{ StudentEnrollment : contains
    Grade ||--o{ Class : "has classes"
    Grade ||--o{ Subject : "has subjects"
    Employee ||--o{ Attendance : "has attendance"
    Account ||--o{ TransactionEntry : "has entries"
    Transaction ||--o{ TransactionEntry : "has entries"
```

## Error Handling

### Exception Handling Strategy

```python
# core/exceptions.py
class SchoolERPException(Exception):
    """Base exception for School ERP system"""
    pass

class ValidationError(SchoolERPException):
    """Raised when data validation fails"""
    pass

class PermissionDeniedError(SchoolERPException):
    """Raised when user lacks required permissions"""
    pass

class BusinessLogicError(SchoolERPException):
    """Raised when business rules are violated"""
    pass

# core/middleware.py
class ErrorHandlingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            response = self.get_response(request)
        except SchoolERPException as e:
            # Log error and return appropriate response
            logger.error(f"School ERP Error: {str(e)}")
            return JsonResponse({'error': str(e)}, status=400)
        except Exception as e:
            # Log unexpected errors
            logger.exception("Unexpected error occurred")
            return JsonResponse({'error': 'Internal server error'}, status=500)
        
        return response
```

### API Error Responses

```python
# Standard error response format
{
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid data provided",
        "details": {
            "field_name": ["This field is required"]
        },
        "timestamp": "2025-01-28T10:00:00Z"
    }
}
```

## Testing Strategy

### Testing Pyramid

1. **Unit Tests (70%)**
   - Model validation and business logic
   - Utility functions and helpers
   - Individual view functions

2. **Integration Tests (20%)**
   - API endpoint testing
   - Database integration
   - Third-party service integration

3. **End-to-End Tests (10%)**
   - Critical user workflows
   - Cross-module functionality
   - UI interaction testing

### Test Implementation

```python
# tests/test_students.py
import pytest
from students.models import Student, School

@pytest.fixture
def school(db):
    return School.objects.create(name="Test School", code="TEST")

def test_student_creation(school):
    student = Student.objects.create(
        school=school,
        student_id="STU001",
        first_name="John",
        last_name="Doe",
        date_of_birth="2010-01-01",
        gender="M",
        nationality="US",
        admission_date="2025-01-01"
    )
    assert student.full_name == "John Doe"
    assert student.is_active is True

# tests/test_api.py
import pytest
from rest_framework.test import APIClient
from django.contrib.auth.models import User
from students.models import School

@pytest.fixture
def api_client(db):
    client = APIClient()
    user = User.objects.create_user(username="admin", password="test")
    client.force_authenticate(user=user)
    return client

@pytest.fixture
def school(db):
    return School.objects.create(name="Test School", code="TEST")

def test_create_student(api_client, school):
    data = {
        "student_id": "STU001",
        "first_name": "John",
        "last_name": "Doe",
        "date_of_birth": "2010-01-01",
        "gender": "M",
        "nationality": "US",
        "admission_date": "2025-01-01",
        "school": school.id
    }
    response = api_client.post('/api/students/', data)
    assert response.status_code == 201
```

### Performance Testing

```python
# tests/test_performance.py
import pytest
import time
from students.models import Student, School
from rest_framework.test import APIClient
from django.contrib.auth.models import User

@pytest.fixture
def setup_env(db):
    school = School.objects.create(name="Test School", code="TEST")
    user = User.objects.create_user(username="admin", password="test")
    client = APIClient()
    client.force_authenticate(user=user)
    return school, client

def test_student_list_performance(setup_env):
    school, client = setup_env
    students = [
        Student(
            school=school,
            student_id=f"STU{i:04d}",
            first_name="John",
            last_name="Doe",
            date_of_birth="2010-01-01",
            gender="M",
            nationality="US",
            admission_date="2025-01-01"
        )
        for i in range(1000)
    ]
    Student.objects.bulk_create(students)

    start_time = time.time()
    response = client.get('/api/students/')
    end_time = time.time()

    assert end_time - start_time < 1.0
    assert response.status_code == 200
```

## Security Considerations

### Authentication and Authorization

```python
# core/permissions.py
class SchoolPermission(BasePermission):
    """Ensure user can only access their school's data"""
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        # Check if user belongs to a school
        if hasattr(request.user, 'employee'):
            request.school = request.user.employee.school
            return True
        
        return request.user.is_superuser
    
    def has_object_permission(self, request, view, obj):
        if request.user.is_superuser:
            return True
        
        return obj.school == request.school

# Authentication settings
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
    'core.backends.SchoolAuthBackend',
]

# JWT Token configuration
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
}
```

### Data Encryption

```python
# core/encryption.py
from cryptography.fernet import Fernet
from django.conf import settings

class FieldEncryption:
    def __init__(self):
        self.cipher = Fernet(settings.ENCRYPTION_KEY)
    
    def encrypt(self, data):
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt(self, encrypted_data):
        return self.cipher.decrypt(encrypted_data.encode()).decode()

# Usage in models
class Student(BaseModel):
    ssn = models.CharField(max_length=255)  # Encrypted field
    
    def save(self, *args, **kwargs):
        if self.ssn and not self.ssn.startswith('gAAAAA'):  # Not already encrypted
            self.ssn = FieldEncryption().encrypt(self.ssn)
        super().save(*args, **kwargs)
```

### Audit Logging

```python
# core/audit.py
class AuditLog(models.Model):
    user = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True)
    action = models.CharField(max_length=50)
    model_name = models.CharField(max_length=100)
    object_id = models.IntegerField()
    changes = models.JSONField()
    ip_address = models.GenericIPAddressField()
    timestamp = models.DateTimeField(auto_now_add=True)

class AuditMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Log request details
        response = self.get_response(request)
        
        # Log response if it's a modification
        if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            self.log_action(request, response)
        
        return response
```

## Scalability and Performance

### Caching Strategy

```python
# core/cache.py
from django.core.cache import cache
from django.core.cache.utils import make_template_fragment_key

class CacheManager:
    @staticmethod
    def get_student_cache_key(student_id, school_id):
        return f"student:{school_id}:{student_id}"
    
    @staticmethod
    def cache_student_data(student):
        key = CacheManager.get_student_cache_key(student.student_id, student.school_id)
        cache.set(key, student, timeout=3600)  # Cache for 1 hour
    
    @staticmethod
    def get_cached_student(student_id, school_id):
        key = CacheManager.get_student_cache_key(student_id, school_id)
        return cache.get(key)

# Usage in views
class StudentViewSet(viewsets.ModelViewSet):
    def retrieve(self, request, pk=None):
        # Try cache first
        cached_student = CacheManager.get_cached_student(pk, request.school.id)
        if cached_student:
            serializer = StudentSerializer(cached_student)
            return Response(serializer.data)
        
        # Fallback to database
        student = self.get_object()
        CacheManager.cache_student_data(student)
        serializer = StudentSerializer(student)
        return Response(serializer.data)
```

### Database Optimization

```python
# core/querysets.py
class OptimizedQuerySet(models.QuerySet):
    def with_related(self):
        return self.select_related('school', 'created_by').prefetch_related('parents')
    
    def active(self):
        return self.filter(status='active')
    
    def for_school(self, school):
        return self.filter(school=school)

class Student(BaseModel):
    objects = OptimizedQuerySet.as_manager()
    
    class Meta:
        indexes = [
            models.Index(fields=['school', 'student_id']),
            models.Index(fields=['school', 'status']),
            models.Index(fields=['admission_date']),
        ]
```

### Asynchronous Task Processing

```python
# core/tasks.py
from celery import shared_task
from django.core.mail import send_mail

@shared_task
def send_notification_email(recipient_email, subject, message):
    try:
        send_mail(subject, message, '<EMAIL>', [recipient_email])
        return f"Email sent to {recipient_email}"
    except Exception as e:
        return f"Failed to send email: {str(e)}"

@shared_task
def generate_report(report_type, school_id, user_id):
    # Generate report asynchronously
    from reports.generators import ReportGenerator
    
    generator = ReportGenerator(report_type, school_id)
    report_path = generator.generate()
    
    # Notify user when complete
    send_notification_email.delay(
        user_email,
        "Report Ready",
        f"Your {report_type} report is ready for download."
    )
    
    return report_path
```

This design provides a solid foundation for a comprehensive, scalable school ERP system that can handle the complex requirements of modern educational institutions while maintaining performance, security, and usability.