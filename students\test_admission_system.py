import pytest
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone
from datetime import date, timedelta

from core.models import School, AcademicYear
from students.models import (
    Grade, Class, Parent, Student, StudentDocument, StudentEnrollment,
    VacationRequest, StudentInfraction, StudentTransfer, ElectronicRegistration,
    StudentSuspension, DisciplinaryAction, DisciplineReport
)

User = get_user_model()


@pytest.fixture
def school():
    """Create a test school"""
    return School.objects.create(
        name="Test School",
        code="TEST",
        address="123 Test St",
        phone="+1234567890",
        email="<EMAIL>",
        principal_name="Test Principal",
        established_date=date(2000, 1, 1)
    )


@pytest.fixture
def academic_year(school):
    """Create a test academic year"""
    return AcademicYear.objects.create(
        school=school,
        name="2024-2025",
        start_date=date(2024, 9, 1),
        end_date=date(2025, 6, 30),
        is_current=True
    )


@pytest.fixture
def grade(school):
    """Create a test grade"""
    return Grade.objects.create(
        school=school,
        name="Grade 1",
        level=1
    )


@pytest.fixture
def class_obj(school, grade, academic_year):
    """Create a test class"""
    return Class.objects.create(
        school=school,
        name="A",
        grade=grade,
        academic_year=academic_year,
        max_students=30
    )


@pytest.fixture
def admin_user():
    """Create an admin user"""
    return User.objects.create_user(
        username="admin",
        email="<EMAIL>",
        password="testpass123",
        is_staff=True
    )


@pytest.fixture
def parent_user():
    """Create a parent user"""
    return User.objects.create_user(
        username="parent1",
        email="<EMAIL>",
        password="testpass123",
        user_type="parent"
    )


@pytest.fixture
def parent(school, parent_user):
    """Create a test parent"""
    return Parent.objects.create(
        school=school,
        user=parent_user,
        father_name="John Doe",
        father_phone="+1234567890",
        home_address="123 Parent St"
    )


@pytest.mark.django_db
class TestElectronicRegistration:
    """Test cases for electronic registration"""

    def test_electronic_registration_creation(self, school, grade):
        """Test electronic registration creation"""
        registration = ElectronicRegistration.objects.create(
            school=school,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            father_name="John Doe Sr",
            father_phone="+1234567890",
            father_email="<EMAIL>",
            mother_name="Jane Doe",
            home_address="123 Test St",
            desired_grade=grade
        )
        
        assert registration.status == 'draft'
        assert registration.full_name == "John Doe"

    def test_electronic_registration_validation(self, school, grade):
        """Test electronic registration validation"""
        # Test invalid age (too young)
        with pytest.raises(ValidationError):
            registration = ElectronicRegistration(
                school=school,
                first_name="Baby",
                last_name="Doe",
                date_of_birth=date(2023, 1, 1),  # 2 years old
                gender="M",
                nationality="US",
                father_name="John Doe Sr",
                father_phone="+1234567890",
                father_email="<EMAIL>",
                home_address="123 Test St",
                desired_grade=grade
            )
            registration.full_clean()


@pytest.mark.django_db
class TestStudentIDGeneration:
    """Test cases for student ID generation"""

    def test_student_id_generation_uniqueness(self, school, parent):
        """Test that student ID generation creates unique IDs"""
        students = []
        for i in range(5):
            student_user = User.objects.create_user(
                username=f"student{i}",
                email=f"student{i}@test.com",
                password="testpass123",
                user_type="student"
            )
            
            student = Student.objects.create(
                school=school,
                user=student_user,
                first_name=f"Student{i}",
                last_name="Test",
                date_of_birth=date(2015, 1, i+1),
                gender="M",
                nationality="US",
                parent=parent,
                admission_date=date(2024, 9, i+1)
            )
            students.append(student)
        
        # Check that all student IDs are unique
        student_ids = [s.student_id for s in students]
        assert len(student_ids) == len(set(student_ids))

    def test_admission_number_generation(self, school, parent):
        """Test admission number generation"""
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=school,
            user=student_user,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )
        
        # Check admission number format
        assert student.admission_number.startswith("2024-")
        assert len(student.admission_number) == 9  # YYYY-NNNN format


@pytest.mark.django_db
class TestDocumentVerification:
    """Test cases for document verification"""

    def test_document_verification_workflow(self, school, parent, admin_user):
        """Test document verification workflow"""
        # Create student first
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=school,
            user=student_user,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )
        
        # Create a test file
        test_file = SimpleUploadedFile(
            "test.pdf",
            b"file_content",
            content_type="application/pdf"
        )
        
        # Create document
        document = StudentDocument.objects.create(
            school=school,
            student=student,
            document_type='birth_certificate',
            title='Birth Certificate',
            file=test_file,
            uploaded_by=admin_user
        )
        
        # Test verification
        assert not document.is_verified
        document.verify_document(admin_user)
        assert document.is_verified
        assert document.verified_by == admin_user
        assert document.verified_at is not None

    def test_document_without_student(self, school, admin_user):
        """Test document creation without student (for admission workflow)"""
        test_file = SimpleUploadedFile(
            "test.pdf",
            b"file_content",
            content_type="application/pdf"
        )
        
        # Create document without student (admission workflow)
        document = StudentDocument.objects.create(
            school=school,
            student=None,  # No student yet
            document_type='birth_certificate',
            title='Birth Certificate',
            file=test_file,
            uploaded_by=admin_user
        )
        
        assert document.student_id is None
        assert document.title == 'Birth Certificate'


@pytest.mark.django_db
class TestAdmissionWorkflow:
    """Integration tests for the complete admission workflow"""

    def test_complete_admission_workflow(self, school, grade, class_obj, admin_user):
        """Test the complete admission workflow from application to student creation"""
        # Step 1: Create electronic registration
        registration = ElectronicRegistration.objects.create(
            school=school,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            father_name="John Doe Sr",
            father_phone="+1234567890",
            father_email="<EMAIL>",
            mother_name="Jane Doe",
            home_address="123 Test St",
            desired_grade=grade,
            status='submitted'
        )
        
        # Step 2: Upload documents (simulated)
        test_file = SimpleUploadedFile(
            "birth_cert.pdf",
            b"birth certificate content",
            content_type="application/pdf"
        )
        
        document = StudentDocument.objects.create(
            school=school,
            student=None,  # Not linked to student yet
            document_type='birth_certificate',
            title=f"{registration.full_name} - Birth Certificate",
            file=test_file,
            description=f"Admission document for {registration.full_name}",
            uploaded_by=admin_user
        )
        
        # Step 3: Review and approve application
        registration.status = 'approved'
        registration.reviewed_by = admin_user
        registration.reviewed_at = timezone.now()
        registration.save()
        
        # Step 4: Verify that workflow completed successfully
        assert registration.status == 'approved'
        assert registration.reviewed_by == admin_user
        assert registration.reviewed_at is not None
        
        # Verify document exists
        assert StudentDocument.objects.filter(
            title__icontains=registration.full_name
        ).exists()


@pytest.mark.django_db
class TestStudentEnrollment:
    """Test cases for student enrollment"""

    def test_enrollment_creation(self, school, class_obj, academic_year, parent, admin_user):
        """Test enrollment creation with valid data"""
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=school,
            user=student_user,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )

        enrollment = StudentEnrollment.objects.create(
            school=school,
            student=student,
            class_obj=class_obj,
            academic_year=academic_year,
            enrollment_date=date(2024, 9, 1),
            enrolled_by=admin_user
        )
        
        assert enrollment.status == 'active'
        assert enrollment.roll_number is not None  # Auto-generated
        assert enrollment.is_active

    def test_enrollment_date_validation(self, school, class_obj, academic_year, parent, admin_user):
        """Test enrollment date validation"""
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=school,
            user=student_user,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )

        # Test enrollment date outside academic year
        with pytest.raises(ValidationError):
            enrollment = StudentEnrollment(
                school=school,
                student=student,
                class_obj=class_obj,
                academic_year=academic_year,
                enrollment_date=date(2023, 1, 1),  # Before academic year
                enrolled_by=admin_user
            )
            enrollment.full_clean()

    def test_class_capacity_validation(self, school, academic_year, parent, admin_user):
        """Test class capacity validation"""
        grade = Grade.objects.create(
            school=school,
            name="Grade 1",
            level=1
        )
        
        class_obj = Class.objects.create(
            school=school,
            name="A",
            grade=grade,
            academic_year=academic_year,
            max_students=2  # Small capacity for testing
        )
        
        # Create first student and enrollment
        student_user1 = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student1 = Student.objects.create(
            school=school,
            user=student_user1,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="F",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )

        StudentEnrollment.objects.create(
            school=school,
            student=student1,
            class_obj=class_obj,
            academic_year=academic_year,
            enrollment_date=date(2024, 9, 1),
            enrolled_by=admin_user
        )
        
        # Create second student and enrollment
        student_user2 = User.objects.create_user(
            username="student2",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student2 = Student.objects.create(
            school=school,
            user=student_user2,
            first_name="John",
            last_name="Smith",
            date_of_birth=date(2015, 2, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 2)
        )
        
        StudentEnrollment.objects.create(
            school=school,
            student=student2,
            class_obj=class_obj,
            academic_year=academic_year,
            enrollment_date=date(2024, 9, 1),
            enrolled_by=admin_user
        )
        
        # Try to create third enrollment (should fail due to capacity)
        student_user3 = User.objects.create_user(
            username="student3",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student3 = Student.objects.create(
            school=school,
            user=student_user3,
            first_name="Bob",
            last_name="Johnson",
            date_of_birth=date(2015, 3, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 3)
        )
        
        with pytest.raises(ValidationError):
            enrollment = StudentEnrollment(
                school=school,
                student=student3,
                class_obj=class_obj,
                academic_year=academic_year,
                enrollment_date=date(2024, 9, 1),
                enrolled_by=admin_user
            )
            enrollment.full_clean()

@pytest.mark.django_db
class TestDisciplinarySystem:
    """Test cases for the disciplinary system"""

    def test_student_suspension_creation(self, school, parent, admin_user):
        """Test student suspension creation"""
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=school,
            user=student_user,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )

        suspension = StudentSuspension.objects.create(
            school=school,
            student=student,
            suspension_type='out_of_school',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=3),
            reason="Disruptive behavior",
            issued_by=admin_user
        )
        
        assert suspension.student == student
        assert suspension.suspension_type == 'out_of_school'
        assert suspension.duration_days == 4  # 3 days + 1 (inclusive)
        assert suspension.status == 'active'

    def test_suspension_is_active_property(self, school, parent, admin_user):
        """Test suspension is_active property"""
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=school,
            user=student_user,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )

        # Active suspension
        active_suspension = StudentSuspension.objects.create(
            school=school,
            student=student,
            suspension_type='in_school',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=2),
            reason="Test suspension",
            issued_by=admin_user
        )
        
        assert active_suspension.is_active

        # Past suspension
        past_suspension = StudentSuspension.objects.create(
            school=school,
            student=student,
            suspension_type='in_school',
            start_date=date.today() - timedelta(days=5),
            end_date=date.today() - timedelta(days=3),
            reason="Past suspension",
            issued_by=admin_user
        )
        
        assert not past_suspension.is_active

    def test_disciplinary_action_creation(self, school, parent, admin_user):
        """Test disciplinary action creation"""
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=school,
            user=student_user,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )

        # Create infraction first
        infraction = StudentInfraction.objects.create(
            school=school,
            student=student,
            incident_date=date.today(),
            description="Talking in class",
            severity='minor',
            action_taken='warning',
            reported_by=admin_user
        )

        # Create disciplinary action
        action = DisciplinaryAction.objects.create(
            school=school,
            infraction=infraction,
            action_type='detention',
            description="After school detention for 1 hour",
            assigned_date=date.today(),
            due_date=date.today() + timedelta(days=1),
            assigned_by=admin_user
        )
        
        assert action.infraction == infraction
        assert action.action_type == 'detention'
        assert action.status == 'pending'
        assert not action.is_overdue  # Due tomorrow

    def test_disciplinary_action_overdue(self, school, parent, admin_user):
        """Test disciplinary action overdue property"""
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=school,
            user=student_user,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )

        infraction = StudentInfraction.objects.create(
            school=school,
            student=student,
            incident_date=date.today(),
            description="Test infraction",
            severity='minor',
            action_taken='warning',
            reported_by=admin_user
        )

        # Overdue action
        overdue_action = DisciplinaryAction.objects.create(
            school=school,
            infraction=infraction,
            action_type='community_service',
            description="Clean school grounds",
            assigned_date=date.today() - timedelta(days=5),
            due_date=date.today() - timedelta(days=2),
            assigned_by=admin_user
        )
        
        assert overdue_action.is_overdue

    def test_discipline_report_generation(self, school, parent, admin_user):
        """Test discipline report generation"""
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=school,
            user=student_user,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )

        # Create some infractions
        for i in range(3):
            StudentInfraction.objects.create(
                school=school,
                student=student,
                incident_date=date.today() - timedelta(days=i),
                description=f"Test infraction {i}",
                severity='minor' if i < 2 else 'major',
                action_taken='warning',
                reported_by=admin_user
            )

        # Create report
        report = DisciplineReport.objects.create(
            school=school,
            report_type='student_summary',
            title='Test Report',
            start_date=date.today() - timedelta(days=30),
            end_date=date.today(),
            generated_by=admin_user
        )

        # Generate report data
        report_data = report.generate_report_data()
        
        assert report_data['summary']['total_infractions'] == 3
        assert report_data['summary']['unique_students'] == 1
        assert 'severity_breakdown' in report_data
        assert 'action_breakdown' in report_data
        assert 'monthly_trends' in report_data

    def test_suspension_notification(self, school, parent, admin_user):
        """Test suspension parent notification"""
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        student = Student.objects.create(
            school=school,
            user=student_user,
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            parent=parent,
            admission_date=date(2024, 9, 1)
        )

        suspension = StudentSuspension.objects.create(
            school=school,
            student=student,
            suspension_type='out_of_school',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=2),
            reason="Test suspension",
            issued_by=admin_user
        )
        
        # Initially not notified
        assert not suspension.parent_notified
        assert suspension.parent_notified_at is None
        
        # Notify parent
        suspension.notify_parent()
        
        assert suspension.parent_notified
        assert suspension.parent_notified_at is not None