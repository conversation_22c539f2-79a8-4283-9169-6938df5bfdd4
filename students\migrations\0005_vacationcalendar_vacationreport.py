# Generated by Django 5.2.4 on 2025-07-30 08:52

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("students", "0004_transferdocument_academichistory"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="VacationCalendar",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "name",
                    models.Char<PERSON>ield(
                        max_length=200, verbose_name="Vacation/Holiday Name"
                    ),
                ),
                (
                    "vacation_type",
                    models.CharField(
                        choices=[
                            ("school_holiday", "School Holiday"),
                            ("public_holiday", "Public Holiday"),
                            ("exam_period", "Exam Period"),
                            ("maintenance", "Maintenance Period"),
                            ("special_event", "Special Event"),
                        ],
                        max_length=20,
                        verbose_name="Type",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="Start Date")),
                ("end_date", models.DateField(verbose_name="End Date")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "affects_attendance",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this period affects student attendance calculations",
                        verbose_name="Affects Attendance",
                    ),
                ),
                (
                    "is_recurring",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this vacation repeats annually",
                        verbose_name="Is Recurring",
                    ),
                ),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vacation_calendar",
                        to="core.academicyear",
                        verbose_name="Academic Year",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Vacation Calendar",
                "verbose_name_plural": "Vacation Calendar",
                "ordering": ["start_date"],
            },
        ),
        migrations.CreateModel(
            name="VacationReport",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("monthly", "Monthly Report"),
                            ("quarterly", "Quarterly Report"),
                            ("annual", "Annual Report"),
                            ("custom", "Custom Period Report"),
                        ],
                        max_length=20,
                        verbose_name="Report Type",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="Report Start Date")),
                ("end_date", models.DateField(verbose_name="Report End Date")),
                (
                    "report_data",
                    models.JSONField(default=dict, verbose_name="Report Data"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="generated_vacation_reports",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Generated By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Vacation Report",
                "verbose_name_plural": "Vacation Reports",
                "ordering": ["-created_at"],
            },
        ),
    ]
