{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Document Receiver" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-inbox text-primary me-2"></i>{% trans "Document Receiver" %}
                    </h2>
                    <p class="text-muted">{% trans "Review and verify student documents" %}</p>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <h3 class="mb-1">{{ pending_documents.count }}</h3>
                            <p class="mb-0">{% trans "Pending Documents" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h3 class="mb-1">0</h3>
                            <p class="mb-0">{% trans "Verified Today" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                            <h3 class="mb-1">0</h3>
                            <p class="mb-0">{% trans "Total Documents" %}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Documents -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Pending Document Verification" %}</h5>
                        </div>
                        <div class="card-body">
                            {% if pending_documents %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Student" %}</th>
                                                <th>{% trans "Document Type" %}</th>
                                                <th>{% trans "Title" %}</th>
                                                <th>{% trans "Uploaded By" %}</th>
                                                <th>{% trans "Upload Date" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for document in pending_documents %}
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                                <span class="text-white fw-bold">{{ document.student.first_name|slice:":1" }}{{ document.student.last_name|slice:":1" }}</span>
                                                            </div>
                                                            <div>
                                                                <h6 class="mb-0">{{ document.student.full_name }}</h6>
                                                                <small class="text-muted">{{ document.student.student_id }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">{{ document.get_document_type_display }}</span>
                                                    </td>
                                                    <td>{{ document.title }}</td>
                                                    <td>{{ document.uploaded_by.get_full_name }}</td>
                                                    <td>{{ document.created_at|date:"d/m/Y H:i" }}</td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#documentModal{{ document.id }}">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <form method="post" style="display: inline;">
                                                                {% csrf_token %}
                                                                <input type="hidden" name="document_id" value="{{ document.id }}">
                                                                <input type="hidden" name="action" value="verify">
                                                                <button type="submit" class="btn btn-sm btn-outline-success" onclick="return confirm('{% trans "Are you sure you want to verify this document?" %}')">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            </form>
                                                            <form method="post" style="display: inline;">
                                                                {% csrf_token %}
                                                                <input type="hidden" name="document_id" value="{{ document.id }}">
                                                                <input type="hidden" name="action" value="reject">
                                                                <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('{% trans "Are you sure you want to reject this document?" %}')">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>{% trans "No pending documents to review." %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Document Detail Modals -->
{% for document in pending_documents %}
    <div class="modal fade" id="documentModal{{ document.id }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{% trans "Document Details" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>{% trans "Student:" %}</strong> {{ document.student.full_name }}</p>
                            <p><strong>{% trans "Document Type:" %}</strong> {{ document.get_document_type_display }}</p>
                            <p><strong>{% trans "Title:" %}</strong> {{ document.title }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{% trans "Uploaded By:" %}</strong> {{ document.uploaded_by.get_full_name }}</p>
                            <p><strong>{% trans "Upload Date:" %}</strong> {{ document.created_at|date:"d/m/Y H:i" }}</p>
                            <p><strong>{% trans "File Size:" %}</strong> {{ document.file.size|filesizeformat }}</p>
                        </div>
                    </div>
                    
                    {% if document.description %}
                        <div class="mb-3">
                            <p><strong>{% trans "Description:" %}</strong></p>
                            <p>{{ document.description }}</p>
                        </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <p><strong>{% trans "File:" %}</strong></p>
                        <a href="{{ document.file.url }}" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-download me-2"></i>{% trans "Download File" %}
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                    <form method="post" style="display: inline;">
                        {% csrf_token %}
                        <input type="hidden" name="document_id" value="{{ document.id }}">
                        <input type="hidden" name="action" value="verify">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check me-2"></i>{% trans "Verify Document" %}
                        </button>
                    </form>
                    <form method="post" style="display: inline;">
                        {% csrf_token %}
                        <input type="hidden" name="document_id" value="{{ document.id }}">
                        <input type="hidden" name="action" value="reject">
                        <button type="submit" class="btn btn-danger" onclick="return confirm('{% trans "Are you sure you want to reject this document?" %}')">
                            <i class="fas fa-times me-2"></i>{% trans "Reject Document" %}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endfor %}
{% endblock %}