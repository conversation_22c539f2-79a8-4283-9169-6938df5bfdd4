{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load dashboard_tags %}

{% block title %}{% trans "Leave Management Report" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .leave-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .leave-card:hover {
        transform: translateY(-2px);
    }
    .leave-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .employee-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
    .leave-type-annual { background-color: #e3f2fd; color: #1565c0; }
    .leave-type-sick { background-color: #ffebee; color: #c62828; }
    .leave-type-emergency { background-color: #fff3e0; color: #ef6c00; }
    .leave-type-maternity { background-color: #f3e5f5; color: #7b1fa2; }
    .leave-type-paternity { background-color: #e8f5e8; color: #2e7d32; }
    .status-pending { background-color: #fff3cd; color: #856404; }
    .status-approved { background-color: #d4edda; color: #155724; }
    .status-rejected { background-color: #f8d7da; color: #721c24; }
    .status-cancelled { background-color: #e2e3e5; color: #383d41; }
    .chart-container {
        position: relative;
        height: 300px;
    }
    .balance-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-calendar-minus me-2"></i>{% trans "Leave Management Report" %}
                    </h1>
                    <p class="text-muted">{% trans "Comprehensive leave analytics and balance tracking" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>{% trans "Export Report" %}
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printReport()">
                        <i class="fas fa-print me-2"></i>{% trans "Print" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-2">
                            <label for="department_filter" class="form-label">{% trans "Department" %}</label>
                            <select name="department" id="department_filter" class="form-select">
                                <option value="">{% trans "All Departments" %}</option>
                                {% for dept in departments %}
                                    <option value="{{ dept.id }}" {% if selected_department and dept.id == selected_department.id %}selected{% endif %}>
                                        {{ dept.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="leave_type_filter" class="form-label">{% trans "Leave Type" %}</label>
                            <select name="leave_type" id="leave_type_filter" class="form-select">
                                <option value="">{% trans "All Types" %}</option>
                                <option value="annual" {% if selected_leave_type == 'annual' %}selected{% endif %}>{% trans "Annual Leave" %}</option>
                                <option value="sick" {% if selected_leave_type == 'sick' %}selected{% endif %}>{% trans "Sick Leave" %}</option>
                                <option value="emergency" {% if selected_leave_type == 'emergency' %}selected{% endif %}>{% trans "Emergency Leave" %}</option>
                                <option value="maternity" {% if selected_leave_type == 'maternity' %}selected{% endif %}>{% trans "Maternity Leave" %}</option>
                                <option value="paternity" {% if selected_leave_type == 'paternity' %}selected{% endif %}>{% trans "Paternity Leave" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status_filter" class="form-label">{% trans "Status" %}</label>
                            <select name="status" id="status_filter" class="form-select">
                                <option value="">{% trans "All Status" %}</option>
                                <option value="pending" {% if selected_status == 'pending' %}selected{% endif %}>{% trans "Pending" %}</option>
                                <option value="approved" {% if selected_status == 'approved' %}selected{% endif %}>{% trans "Approved" %}</option>
                                <option value="rejected" {% if selected_status == 'rejected' %}selected{% endif %}>{% trans "Rejected" %}</option>
                                <option value="cancelled" {% if selected_status == 'cancelled' %}selected{% endif %}>{% trans "Cancelled" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="start_date" class="form-label">{% trans "Start Date" %}</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-2">
                            <label for="end_date" class="form-label">{% trans "End Date" %}</label>
                            <input type="date" name="end_date" id="end_date" class="form-control" value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card leave-card">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">{{ total_requests|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Requests" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card leave-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">{{ approved_requests|default:0 }}</h4>
                    <p class="mb-0">{% trans "Approved" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card leave-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock text-warning mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-warning">{{ pending_requests|default:0 }}</h4>
                    <p class="mb-0">{% trans "Pending" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card leave-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-day text-info mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-info">{{ total_days_taken|default:0 }}</h4>
                    <p class="mb-0">{% trans "Days Taken" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Leave Requests by Type" %}</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="leaveTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Monthly Leave Trends" %}</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="monthlyTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Balance Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Employee Leave Balances" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if employee_balances %}
                            {% for balance in employee_balances %}
                                <div class="col-md-4 mb-3">
                                    <div class="card balance-card">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-2">
                                                {% if balance.employee.user.profile_picture %}
                                                    <img src="{{ balance.employee.user.profile_picture.url }}" alt="{{ balance.employee.user.get_full_name }}" class="employee-avatar me-2">
                                                {% else %}
                                                    <div class="employee-avatar bg-light d-flex align-items-center justify-content-center me-2">
                                                        <i class="fas fa-user text-dark"></i>
                                                    </div>
                                                {% endif %}
                                                <div>
                                                    <h6 class="mb-0">{{ balance.employee.user.first_name }} {{ balance.employee.user.last_name }}</h6>
                                                    <small>{{ balance.employee.department.name|default:"N/A" }}</small>
                                                </div>
                                            </div>
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <div class="fw-bold">{{ balance.annual_balance|default:0 }}</div>
                                                    <small>{% trans "Annual" %}</small>
                                                </div>
                                                <div class="col-4">
                                                    <div class="fw-bold">{{ balance.sick_balance|default:0 }}</div>
                                                    <small>{% trans "Sick" %}</small>
                                                </div>
                                                <div class="col-4">
                                                    <div class="fw-bold">{{ balance.emergency_balance|default:0 }}</div>
                                                    <small>{% trans "Emergency" %}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    {% trans "No employee balance data available." %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Requests Details -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Leave Requests Details" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="leaveTable">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Employee" %}</th>
                                    <th>{% trans "Leave Type" %}</th>
                                    <th>{% trans "Start Date" %}</th>
                                    <th>{% trans "End Date" %}</th>
                                    <th>{% trans "Days" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Applied Date" %}</th>
                                    <th>{% trans "Approved By" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if leave_requests %}
                                    {% for request in leave_requests %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {% if request.employee.user.profile_picture %}
                                                        <img src="{{ request.employee.user.profile_picture.url }}" alt="{{ request.employee.user.get_full_name }}" class="employee-avatar me-2">
                                                    {% else %}
                                                        <div class="employee-avatar bg-secondary d-flex align-items-center justify-content-center me-2">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <div class="fw-bold">{{ request.employee.user.first_name }} {{ request.employee.user.last_name }}</div>
                                                        <small class="text-muted">{{ request.employee.employee_id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge 
                                                    {% if request.leave_type == 'annual' %}bg-primary
                                                    {% elif request.leave_type == 'sick' %}bg-danger
                                                    {% elif request.leave_type == 'emergency' %}bg-warning
                                                    {% elif request.leave_type == 'maternity' %}bg-info
                                                    {% elif request.leave_type == 'paternity' %}bg-success
                                                    {% else %}bg-secondary{% endif %}">
                                                    {% if request.leave_type == 'annual' %}{% trans "Annual" %}
                                                    {% elif request.leave_type == 'sick' %}{% trans "Sick" %}
                                                    {% elif request.leave_type == 'emergency' %}{% trans "Emergency" %}
                                                    {% elif request.leave_type == 'maternity' %}{% trans "Maternity" %}
                                                    {% elif request.leave_type == 'paternity' %}{% trans "Paternity" %}
                                                    {% else %}{{ request.leave_type }}{% endif %}
                                                </span>
                                            </td>
                                            <td>{{ request.start_date|date:"M d, Y" }}</td>
                                            <td>{{ request.end_date|date:"M d, Y" }}</td>
                                            <td>
                                                <span class="fw-bold">{{ request.total_days|default:0 }}</span>
                                            </td>
                                            <td>
                                                <span class="badge 
                                                    {% if request.status == 'pending' %}bg-warning
                                                    {% elif request.status == 'approved' %}bg-success
                                                    {% elif request.status == 'rejected' %}bg-danger
                                                    {% elif request.status == 'cancelled' %}bg-secondary
                                                    {% else %}bg-info{% endif %}">
                                                    {% if request.status == 'pending' %}{% trans "Pending" %}
                                                    {% elif request.status == 'approved' %}{% trans "Approved" %}
                                                    {% elif request.status == 'rejected' %}{% trans "Rejected" %}
                                                    {% elif request.status == 'cancelled' %}{% trans "Cancelled" %}
                                                    {% else %}{{ request.status }}{% endif %}
                                                </span>
                                            </td>
                                            <td>{{ request.applied_date|date:"M d, Y" }}</td>
                                            <td>
                                                {% if request.approved_by %}
                                                    {{ request.approved_by.first_name }} {{ request.approved_by.last_name }}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{% url 'hr:leave_detail' request.id %}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    {% if request.status == 'pending' %}
                                                        <a href="{% url 'hr:leave_approve' request.id %}" class="btn btn-sm btn-outline-success">
                                                            <i class="fas fa-check"></i>
                                                        </a>
                                                        <a href="{% url 'hr:leave_reject' request.id %}" class="btn btn-sm btn-outline-danger">
                                                            <i class="fas fa-times"></i>
                                                        </a>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No leave requests found for the selected criteria." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Leave Type Chart
    const leaveTypeCtx = document.getElementById('leaveTypeChart').getContext('2d');
    const leaveTypeChart = new Chart(leaveTypeCtx, {
        type: 'doughnut',
        data: {
            labels: {{ leave_type_labels|safe }},
            datasets: [{
                data: {{ leave_type_data|safe }},
                backgroundColor: [
                    '#007bff', '#dc3545', '#ffc107', '#17a2b8', '#28a745'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Monthly Trend Chart
    const monthlyTrendCtx = document.getElementById('monthlyTrendChart').getContext('2d');
    const monthlyTrendChart = new Chart(monthlyTrendCtx, {
        type: 'line',
        data: {
            labels: {{ monthly_labels|safe }},
            datasets: [{
                label: '{% trans "Leave Requests" %}',
                data: {{ monthly_data|safe }},
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Export and Print Functions
    function exportReport() {
        alert('{% trans "Export functionality will be implemented" %}');
    }

    function printReport() {
        window.print();
    }

    // Auto-submit form on filter change
    document.querySelectorAll('#department_filter, #leave_type_filter, #status_filter').forEach(function(element) {
        element.addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>
{% endblock %}
