{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "User Permissions Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .permissions-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .permissions-card:hover {
        transform: translateY(-2px);
    }
    .permissions-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .user-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
    }
    .permission-group {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        background-color: #f8f9fa;
    }
    .permission-item {
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }
    .permission-item:last-child {
        border-bottom: none;
    }
    .role-badge {
        font-size: 0.9em;
        padding: 5px 10px;
        border-radius: 20px;
    }
    .role-admin { background-color: #dc3545; color: white; }
    .role-teacher { background-color: #28a745; color: white; }
    .role-student { background-color: #007bff; color: white; }
    .role-staff { background-color: #6c757d; color: white; }
    .permission-switch {
        transform: scale(1.2);
    }
    .search-box {
        border-radius: 25px;
        padding: 10px 20px;
        border: 2px solid #e9ecef;
    }
    .search-box:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-shield me-2"></i>{% trans "User Permissions Management" %}
                    </h1>
                    <p class="text-muted">{% trans "Manage user roles and permissions across the system" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#createRoleModal">
                        <i class="fas fa-plus me-2"></i>{% trans "Create Role" %}
                    </button>
                    <button class="btn btn-outline-secondary" onclick="exportPermissions()">
                        <i class="fas fa-download me-2"></i>{% trans "Export" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card permissions-card">
                <div class="card-body text-center">
                    <i class="fas fa-users text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">{{ total_users|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Users" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card permissions-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-cog text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">{{ total_roles|default:0 }}</h4>
                    <p class="mb-0">{% trans "Active Roles" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card permissions-card">
                <div class="card-body text-center">
                    <i class="fas fa-key text-warning mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-warning">{{ total_permissions|default:0 }}</h4>
                    <p class="mb-0">{% trans "Permissions" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card permissions-card">
                <div class="card-body text-center">
                    <i class="fas fa-shield-alt text-info mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-info">{{ active_sessions|default:0 }}</h4>
                    <p class="mb-0">{% trans "Active Sessions" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <input type="text" name="search" class="form-control search-box" 
                                   placeholder="{% trans 'Search users...' %}" value="{{ request.GET.search }}">
                        </div>
                        <div class="col-md-3">
                            <select name="role" class="form-select">
                                <option value="">{% trans "All Roles" %}</option>
                                <option value="admin" {% if request.GET.role == 'admin' %}selected{% endif %}>{% trans "Admin" %}</option>
                                <option value="teacher" {% if request.GET.role == 'teacher' %}selected{% endif %}>{% trans "Teacher" %}</option>
                                <option value="student" {% if request.GET.role == 'student' %}selected{% endif %}>{% trans "Student" %}</option>
                                <option value="staff" {% if request.GET.role == 'staff' %}selected{% endif %}>{% trans "Staff" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">{% trans "All Status" %}</option>
                                <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>{% trans "Active" %}</option>
                                <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>{% trans "Inactive" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Users and Permissions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card permissions-card">
                <div class="card-header permissions-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "Users and Permissions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "User" %}</th>
                                    <th>{% trans "Role" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Last Login" %}</th>
                                    <th>{% trans "Permissions" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if users %}
                                    {% for user in users %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {% if user.profile_picture %}
                                                        <img src="{{ user.profile_picture.url }}" alt="{{ user.get_full_name }}" class="user-avatar me-3">
                                                    {% else %}
                                                        <div class="user-avatar bg-secondary d-flex align-items-center justify-content-center me-3">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <h6 class="mb-0">{{ user.get_full_name|default:user.username }}</h6>
                                                        <small class="text-muted">{{ user.email }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="role-badge role-{{ user.user_type|default:'staff' }}">
                                                    {% if user.user_type == 'admin' %}{% trans "Admin" %}
                                                    {% elif user.user_type == 'teacher' %}{% trans "Teacher" %}
                                                    {% elif user.user_type == 'student' %}{% trans "Student" %}
                                                    {% else %}{% trans "Staff" %}{% endif %}
                                                </span>
                                            </td>
                                            <td>
                                                {% if user.is_active %}
                                                    <span class="badge bg-success">{% trans "Active" %}</span>
                                                {% else %}
                                                    <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if user.last_login %}
                                                    {{ user.last_login|timesince }} {% trans "ago" %}
                                                {% else %}
                                                    <span class="text-muted">{% trans "Never" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info" 
                                                        onclick="showPermissions({{ user.id }}, '{{ user.get_full_name|default:user.username }}')">
                                                    <i class="fas fa-key me-1"></i>{% trans "View" %}
                                                </button>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary" 
                                                            onclick="editUser({{ user.id }})">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning" 
                                                            onclick="managePermissions({{ user.id }})">
                                                        <i class="fas fa-cog"></i>
                                                    </button>
                                                    {% if user.is_active %}
                                                        <button class="btn btn-sm btn-outline-danger" 
                                                                onclick="deactivateUser({{ user.id }})">
                                                            <i class="fas fa-ban"></i>
                                                        </button>
                                                    {% else %}
                                                        <button class="btn btn-sm btn-outline-success" 
                                                                onclick="activateUser({{ user.id }})">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No users found matching your criteria." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Permissions Modal -->
<div class="modal fade" id="permissionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "User Permissions" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="permissionsContent">
                <!-- Permissions content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <button type="button" class="btn btn-primary" onclick="savePermissions()">{% trans "Save Changes" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Create Role Modal -->
<div class="modal fade" id="createRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Create New Role" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createRoleForm">
                    <div class="mb-3">
                        <label for="roleName" class="form-label">{% trans "Role Name" %}</label>
                        <input type="text" class="form-control" id="roleName" required>
                    </div>
                    <div class="mb-3">
                        <label for="roleDescription" class="form-label">{% trans "Description" %}</label>
                        <textarea class="form-control" id="roleDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{% trans "Permissions" %}</label>
                        <div class="permission-group">
                            <h6>{% trans "Academic Management" %}</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="perm_view_grades">
                                <label class="form-check-label" for="perm_view_grades">{% trans "View Grades" %}</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="perm_edit_grades">
                                <label class="form-check-label" for="perm_edit_grades">{% trans "Edit Grades" %}</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" onclick="createRole()">{% trans "Create Role" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function showPermissions(userId, userName) {
        document.querySelector('#permissionsModal .modal-title').textContent = 
            '{% trans "Permissions for" %} ' + userName;
        
        // Load permissions via AJAX
        fetch(`/accounts/user/${userId}/permissions/`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('permissionsContent').innerHTML = data.html;
                new bootstrap.Modal(document.getElementById('permissionsModal')).show();
            })
            .catch(error => {
                console.error('Error loading permissions:', error);
                alert('{% trans "Error loading permissions" %}');
            });
    }

    function editUser(userId) {
        window.location.href = `/accounts/user/${userId}/edit/`;
    }

    function managePermissions(userId) {
        window.location.href = `/accounts/user/${userId}/permissions/edit/`;
    }

    function deactivateUser(userId) {
        if (confirm('{% trans "Are you sure you want to deactivate this user?" %}')) {
            // Implement deactivation logic
            console.log('Deactivating user:', userId);
        }
    }

    function activateUser(userId) {
        if (confirm('{% trans "Are you sure you want to activate this user?" %}')) {
            // Implement activation logic
            console.log('Activating user:', userId);
        }
    }

    function savePermissions() {
        // Implement save permissions logic
        alert('{% trans "Permissions saved successfully" %}');
        bootstrap.Modal.getInstance(document.getElementById('permissionsModal')).hide();
    }

    function createRole() {
        const form = document.getElementById('createRoleForm');
        if (form.checkValidity()) {
            // Implement create role logic
            alert('{% trans "Role created successfully" %}');
            bootstrap.Modal.getInstance(document.getElementById('createRoleModal')).hide();
        }
    }

    function exportPermissions() {
        window.location.href = '/accounts/permissions/export/';
    }
</script>
{% endblock %}
