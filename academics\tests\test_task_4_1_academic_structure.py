"""
Test cases for Task 4.1: Create Academic Structure Models

This test file specifically validates the implementation of:
- Grade model with capacity management
- Subject model with credit system
- Class model with teacher assignments
- Curriculum planning capabilities
- Prerequisite management

Requirements: 3.1
"""

import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from datetime import date, timedelta
from decimal import Decimal

from core.models import School, AcademicYear, Semester
from students.models import Grade, Class, Student, Parent
from academics.models import (
    Subject, Teacher, ClassSubject, Schedule, Exam, StudentGrade,
    StudentAttendance, CurriculumPlan, CurriculumSubject, 
    GradeCapacityManagement
)

User = get_user_model()


class Task41AcademicStructureTest(TestCase):
    """
    Comprehensive test cases for Task 4.1 Academic Structure Models
    """
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.semester = Semester.objects.create(
            school=self.school,
            academic_year=self.academic_year,
            name="First Semester",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1,
            max_capacity=60
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )

    def test_grade_model_with_capacity_management(self):
        """Test Grade model with capacity management functionality"""
        # Test basic grade creation
        grade = Grade.objects.create(
            school=self.school,
            name="Grade 2",
            name_ar="الصف الثاني",
            level=2,
            max_capacity=50,
            min_age=6,
            max_age=8
        )
        
        self.assertEqual(grade.name, "Grade 2")
        self.assertEqual(grade.name_ar, "الصف الثاني")
        self.assertEqual(grade.level, 2)
        self.assertEqual(grade.max_capacity, 50)
        self.assertEqual(grade.min_age, 6)
        self.assertEqual(grade.max_age, 8)
        
        # Test capacity management creation
        capacity_mgmt = GradeCapacityManagement.objects.create(
            school=self.school,
            grade=grade,
            academic_year=self.academic_year,
            total_capacity=50,
            minimum_class_size=15,
            maximum_class_size=25,
            auto_create_sections=True
        )
        
        # Test capacity management properties
        self.assertEqual(capacity_mgmt.total_capacity, 50)
        self.assertEqual(capacity_mgmt.available_capacity, 50)
        self.assertEqual(capacity_mgmt.enrollment_percentage, 0.0)
        self.assertFalse(capacity_mgmt.is_full)
        
        # Test enrollment eligibility
        can_enroll, message = capacity_mgmt.can_enroll_student()
        self.assertTrue(can_enroll)
        
        # Test section creation logic
        suggested_sections = capacity_mgmt.get_suggested_sections_count()
        self.assertEqual(suggested_sections, 1)
        
        # Test new section creation
        new_section = capacity_mgmt.create_new_section("Section A")
        self.assertEqual(new_section.name, "Section A")
        self.assertEqual(new_section.grade, grade)
        self.assertEqual(new_section.max_students, 25)

    def test_subject_model_with_credit_system(self):
        """Test Subject model with comprehensive credit system"""
        # Test basic subject creation with credit system
        subject = Subject.objects.create(
            school=self.school,
            name="Advanced Mathematics",
            name_ar="الرياضيات المتقدمة",
            code="MATH201",
            description="Advanced mathematical concepts",
            subject_type="mathematics",
            credit_hours=4,
            weekly_hours=6,
            minimum_grade_required=70.0,
            max_students_per_class=25,
            requires_lab=False,
            requires_special_equipment=True,
            equipment_requirements="Graphing calculators, geometric tools",
            learning_objectives="Master advanced algebra and calculus",
            assessment_methods="Tests, assignments, projects",
            textbook_required="Advanced Math Textbook 2024",
            created_by=self.user
        )
        
        # Test subject properties
        self.assertEqual(subject.name, "Advanced Mathematics")
        self.assertEqual(subject.code, "MATH201")
        self.assertEqual(subject.credit_hours, 4)
        self.assertEqual(subject.weekly_hours, 6)
        self.assertEqual(subject.subject_type, "mathematics")
        self.assertTrue(subject.requires_special_equipment)
        self.assertEqual(str(subject), "MATH201 - Advanced Mathematics")
        
        # Test workload estimation
        workload = subject.get_workload_estimate()
        expected_workload = 4 * 2.5 + 6  # credit_hours * 2.5 + weekly_hours
        self.assertEqual(workload, expected_workload)
        
        # Test difficulty level calculation
        difficulty = subject.get_difficulty_level()
        self.assertEqual(difficulty, 'Beginner')  # No prerequisites
        
        # Test capacity calculation
        capacity = subject.get_available_capacity(self.academic_year)
        self.assertIsInstance(capacity, int)

    def test_prerequisite_management(self):
        """Test comprehensive prerequisite management system"""
        # Create prerequisite subjects
        basic_math = Subject.objects.create(
            school=self.school,
            name="Basic Mathematics",
            code="MATH101",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        
        intermediate_math = Subject.objects.create(
            school=self.school,
            name="Intermediate Mathematics",
            code="MATH102",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        
        # Create advanced subject with prerequisites
        advanced_math = Subject.objects.create(
            school=self.school,
            name="Advanced Mathematics",
            code="MATH201",
            credit_hours=4,
            weekly_hours=5,
            created_by=self.user
        )
        
        # Set up prerequisite chain
        intermediate_math.prerequisites.add(basic_math)
        advanced_math.prerequisites.add(intermediate_math)
        
        # Test prerequisite chain
        chain = advanced_math.get_prerequisite_chain()
        self.assertIn(intermediate_math, chain)
        self.assertIn(basic_math, chain)
        
        # Test difficulty level with prerequisites
        self.assertEqual(basic_math.get_difficulty_level(), 'Beginner')
        self.assertEqual(intermediate_math.get_difficulty_level(), 'Intermediate')
        self.assertEqual(advanced_math.get_difficulty_level(), 'Advanced')
        
        # Test circular prerequisite detection
        self.assertFalse(advanced_math.detect_circular_prerequisites())
        
        # Create circular dependency and test detection
        basic_math.prerequisites.add(advanced_math)
        self.assertTrue(basic_math.detect_circular_prerequisites())

    def test_class_model_with_teacher_assignments(self):
        """Test Class model with teacher assignment functionality"""
        # Create teacher user and teacher
        teacher_user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        teacher = Teacher.objects.create(
            school=self.school,
            user=teacher_user,
            employee_id="T001",
            hire_date=date.today(),
            qualification="Master's in Mathematics",
            experience_years=5,
            department="Mathematics"
        )
        
        # Create subject
        subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH101",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        
        # Add subject to teacher's qualifications
        teacher.subjects.add(subject)
        
        # Create class
        class_obj = Class.objects.create(
            school=self.school,
            name="Section A",
            grade=self.grade,
            academic_year=self.academic_year,
            class_teacher=teacher_user,
            max_students=30,
            room_number="101"
        )
        
        # Test class properties
        self.assertEqual(class_obj.name, "Section A")
        self.assertEqual(class_obj.grade, self.grade)
        self.assertEqual(class_obj.class_teacher, teacher_user)
        self.assertEqual(class_obj.max_students, 30)
        self.assertEqual(class_obj.room_number, "101")
        
        # Create class-subject assignment
        class_subject = ClassSubject.objects.create(
            school=self.school,
            class_obj=class_obj,
            subject=subject,
            teacher=teacher,
            academic_year=self.academic_year,
            semester=self.semester,
            weekly_hours=4,
            max_students=25,
            room_preference="Math Lab",
            requires_special_setup=True,
            setup_requirements="Projector and whiteboard"
        )
        
        # Test class-subject assignment properties
        self.assertEqual(class_subject.class_obj, class_obj)
        self.assertEqual(class_subject.subject, subject)
        self.assertEqual(class_subject.teacher, teacher)
        self.assertEqual(class_subject.weekly_hours, 4)
        self.assertEqual(class_subject.effective_max_students, 25)
        self.assertEqual(class_subject.current_enrollment, 0)
        self.assertEqual(class_subject.available_capacity, 25)
        self.assertFalse(class_subject.is_full)
        
        # Test teacher workload calculation
        workload = class_subject.get_teacher_workload()
        self.assertGreater(workload, 4)  # Should include preparation and grading time
        
        # Test resource requirements
        requirements = class_subject.get_resource_requirements()
        self.assertEqual(requirements['capacity'], 25)
        self.assertEqual(requirements['setup_time'], 15)

    def test_curriculum_planning_capabilities(self):
        """Test comprehensive curriculum planning functionality"""
        # Create curriculum plan
        curriculum = CurriculumPlan.objects.create(
            school=self.school,
            name="Primary Mathematics Curriculum",
            code="PMC2024",
            curriculum_type="national",
            academic_year=self.academic_year,
            objectives="Develop fundamental mathematical skills",
            description="Comprehensive primary mathematics curriculum",
            total_credit_hours=120,
            minimum_credit_hours=100,
            core_subjects_required=5,
            elective_subjects_required=2,
            effective_date=date.today(),
            created_by=self.user
        )
        
        # Add grade to curriculum
        curriculum.grades.add(self.grade)
        
        # Test curriculum properties
        self.assertEqual(curriculum.name, "Primary Mathematics Curriculum")
        self.assertEqual(curriculum.code, "PMC2024")
        self.assertEqual(curriculum.total_credit_hours, 120)
        self.assertEqual(curriculum.minimum_credit_hours, 100)
        self.assertTrue(curriculum.is_current)
        
        # Create subjects for curriculum
        math_subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH001",
            subject_type="mathematics",
            credit_hours=4,
            weekly_hours=5,
            created_by=self.user
        )
        
        science_subject = Subject.objects.create(
            school=self.school,
            name="Science",
            code="SCI001",
            subject_type="science",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        
        # Create curriculum subject mappings
        curriculum_math = CurriculumSubject.objects.create(
            school=self.school,
            curriculum=curriculum,
            subject=math_subject,
            credit_hours=4,
            weekly_hours=5,
            semester="first",
            sequence_order=1,
            learning_outcomes="Master basic arithmetic operations",
            assessment_criteria="Tests, quizzes, and assignments",
            assessment_breakdown={"midterm": 30, "final": 40, "assignments": 30}
        )
        
        curriculum_science = CurriculumSubject.objects.create(
            school=self.school,
            curriculum=curriculum,
            subject=science_subject,
            credit_hours=3,
            weekly_hours=4,
            semester="first",
            sequence_order=2,
            learning_outcomes="Understand basic scientific concepts",
            assessment_criteria="Lab work and theoretical tests",
            assessment_breakdown={"midterm": 25, "final": 45, "lab": 30}
        )
        
        # Add grades to curriculum subjects
        curriculum_math.applicable_grades.add(self.grade)
        curriculum_science.applicable_grades.add(self.grade)
        
        # Test curriculum subject properties
        self.assertEqual(curriculum_math.curriculum, curriculum)
        self.assertEqual(curriculum_math.subject, math_subject)
        self.assertEqual(curriculum_math.credit_hours, 4)
        self.assertEqual(curriculum_math.semester, "first")
        
        # Test workload calculation
        workload = curriculum_math.calculate_workload()
        self.assertEqual(workload['weekly_teaching_hours'], 5)
        self.assertEqual(workload['credit_hours'], 4)
        self.assertEqual(workload['weekly_study_hours'], 12.5)  # 5 * 2.5
        
        # Test assessment schedule generation
        schedule = curriculum_math.generate_assessment_schedule(self.academic_year)
        self.assertEqual(len(schedule), 3)  # midterm, final, assignments
        
        # Test curriculum structure validation
        errors = curriculum.validate_curriculum_structure()
        self.assertIsInstance(errors, list)
        
        # Test subject distribution
        distribution = curriculum.get_subject_distribution()
        self.assertIn('mathematics', distribution)
        self.assertIn('science', distribution)

    def test_integration_academic_structure(self):
        """Test complete integration of all academic structure components"""
        # Create complete academic structure
        
        # 1. Create teacher
        teacher_user = User.objects.create_user(
            username="math_teacher",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        teacher = Teacher.objects.create(
            school=self.school,
            user=teacher_user,
            employee_id="T001",
            hire_date=date.today(),
            qualification="Master's in Mathematics"
        )
        
        # 2. Create subjects with prerequisites
        basic_math = Subject.objects.create(
            school=self.school,
            name="Basic Mathematics",
            code="MATH101",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        
        advanced_math = Subject.objects.create(
            school=self.school,
            name="Advanced Mathematics",
            code="MATH201",
            credit_hours=4,
            weekly_hours=5,
            created_by=self.user
        )
        
        advanced_math.prerequisites.add(basic_math)
        teacher.subjects.add(basic_math, advanced_math)
        
        # 3. Create grade with capacity management
        capacity_mgmt = GradeCapacityManagement.objects.create(
            school=self.school,
            grade=self.grade,
            academic_year=self.academic_year,
            total_capacity=60,
            maximum_class_size=30
        )
        
        # 4. Create class
        class_obj = Class.objects.create(
            school=self.school,
            name="Section A",
            grade=self.grade,
            academic_year=self.academic_year,
            class_teacher=teacher_user,
            max_students=30
        )
        
        # 5. Create curriculum
        curriculum = CurriculumPlan.objects.create(
            school=self.school,
            name="Mathematics Curriculum",
            code="MATHCUR2024",
            academic_year=self.academic_year,
            objectives="Mathematics education",
            total_credit_hours=60,
            effective_date=date.today(),
            created_by=self.user
        )
        
        curriculum.grades.add(self.grade)
        
        # 6. Create curriculum subjects
        curriculum_basic = CurriculumSubject.objects.create(
            school=self.school,
            curriculum=curriculum,
            subject=basic_math,
            credit_hours=3,
            weekly_hours=4,
            semester="first",
            learning_outcomes="Basic math skills",
            assessment_criteria="Tests and assignments",
            assessment_breakdown={"midterm": 30, "final": 40, "assignments": 30}
        )
        
        curriculum_advanced = CurriculumSubject.objects.create(
            school=self.school,
            curriculum=curriculum,
            subject=advanced_math,
            credit_hours=4,
            weekly_hours=5,
            semester="second",
            learning_outcomes="Advanced math skills",
            assessment_criteria="Tests and projects",
            assessment_breakdown={"midterm": 25, "final": 45, "projects": 30}
        )
        
        curriculum_basic.applicable_grades.add(self.grade)
        curriculum_advanced.applicable_grades.add(self.grade)
        
        # 7. Create class subject assignments
        class_subject_basic = ClassSubject.objects.create(
            school=self.school,
            class_obj=class_obj,
            subject=basic_math,
            teacher=teacher,
            academic_year=self.academic_year,
            semester=self.semester,
            weekly_hours=4
        )
        
        # Test complete integration
        self.assertEqual(teacher.subjects.count(), 2)
        self.assertEqual(curriculum.grades.count(), 1)
        self.assertEqual(capacity_mgmt.total_capacity, 60)
        self.assertEqual(class_subject_basic.teacher, teacher)
        self.assertEqual(advanced_math.prerequisites.count(), 1)
        
        # Test prerequisite chain
        chain = advanced_math.get_prerequisite_chain()
        self.assertIn(basic_math, chain)
        
        # Test curriculum validation
        errors = curriculum.validate_curriculum_structure()
        self.assertIsInstance(errors, list)
        
        # Test capacity management
        self.assertTrue(capacity_mgmt.can_enroll_student()[0])
        
        # Test class subject functionality
        self.assertEqual(class_subject_basic.available_capacity, 30)
        self.assertFalse(class_subject_basic.is_full)

    def test_validation_and_constraints(self):
        """Test validation rules and constraints"""
        # Test subject validation
        with self.assertRaises(ValidationError):
            subject = Subject(
                school=self.school,
                name="Invalid Subject",
                code="INV001",
                credit_hours=15,  # Exceeds maximum
                weekly_hours=5,
                created_by=self.user
            )
            subject.full_clean()
        
        # Test capacity management validation
        with self.assertRaises(ValidationError):
            capacity_mgmt = GradeCapacityManagement(
                school=self.school,
                grade=self.grade,
                academic_year=self.academic_year,
                total_capacity=60,
                minimum_class_size=35,  # Greater than maximum
                maximum_class_size=30
            )
            capacity_mgmt.full_clean()
        
        # Test curriculum validation
        with self.assertRaises(ValidationError):
            curriculum = CurriculumPlan(
                school=self.school,
                name="Invalid Curriculum",
                code="INV2024",
                academic_year=self.academic_year,
                total_credit_hours=100,
                minimum_credit_hours=120,  # Greater than total
                effective_date=date.today(),
                created_by=self.user
            )
            curriculum.full_clean()


@pytest.mark.django_db
class TestTask41Integration:
    """Integration tests for Task 4.1 Academic Structure Models"""
    
    def test_complete_academic_structure_workflow(self):
        """Test complete workflow of academic structure setup"""
        # Create school and academic year
        school = School.objects.create(
            name="Integration Test School",
            code="ITS",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        academic_year = AcademicYear.objects.create(
            school=school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        semester = Semester.objects.create(
            school=school,
            academic_year=academic_year,
            name="First Semester",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        # Create grade with capacity management
        grade = Grade.objects.create(
            school=school,
            name="Grade 1",
            level=1,
            max_capacity=60
        )
        
        capacity_mgmt = GradeCapacityManagement.objects.create(
            school=school,
            grade=grade,
            academic_year=academic_year,
            total_capacity=60,
            maximum_class_size=30
        )
        
        # Create subjects with credit system and prerequisites
        user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )
        
        basic_subject = Subject.objects.create(
            school=school,
            name="Basic Mathematics",
            code="MATH101",
            credit_hours=3,
            weekly_hours=4,
            subject_type="mathematics",
            created_by=user
        )
        
        advanced_subject = Subject.objects.create(
            school=school,
            name="Advanced Mathematics",
            code="MATH201",
            credit_hours=4,
            weekly_hours=5,
            subject_type="mathematics",
            created_by=user
        )
        
        advanced_subject.prerequisites.add(basic_subject)
        
        # Create teacher and assign subjects
        teacher_user = User.objects.create_user(
            username="teacher",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        teacher = Teacher.objects.create(
            school=school,
            user=teacher_user,
            employee_id="T001",
            hire_date=date.today(),
            qualification="Master's in Mathematics"
        )
        
        teacher.subjects.add(basic_subject, advanced_subject)
        
        # Create class with teacher assignment
        class_obj = Class.objects.create(
            school=school,
            name="Section A",
            grade=grade,
            academic_year=academic_year,
            class_teacher=teacher_user,
            max_students=30
        )
        
        # Create curriculum plan
        curriculum = CurriculumPlan.objects.create(
            school=school,
            name="Primary Mathematics Curriculum",
            code="PMC2024",
            academic_year=academic_year,
            objectives="Primary math education",
            total_credit_hours=120,
            effective_date=date.today(),
            created_by=user
        )
        
        curriculum.grades.add(grade)
        
        # Create curriculum subjects
        curriculum_basic = CurriculumSubject.objects.create(
            school=school,
            curriculum=curriculum,
            subject=basic_subject,
            credit_hours=3,
            weekly_hours=4,
            semester="first",
            learning_outcomes="Basic math skills",
            assessment_criteria="Tests and assignments",
            assessment_breakdown={"midterm": 30, "final": 40, "assignments": 30}
        )
        
        curriculum_basic.applicable_grades.add(grade)
        
        # Create class subject assignment
        class_subject = ClassSubject.objects.create(
            school=school,
            class_obj=class_obj,
            subject=basic_subject,
            teacher=teacher,
            academic_year=academic_year,
            semester=semester,
            weekly_hours=4
        )
        
        # Verify complete integration
        assert school.name == "Integration Test School"
        assert grade.max_capacity == 60
        assert capacity_mgmt.total_capacity == 60
        assert basic_subject.credit_hours == 3
        assert advanced_subject.prerequisites.count() == 1
        assert teacher.subjects.count() == 2
        assert class_obj.class_teacher == teacher_user
        assert curriculum.grades.count() == 1
        assert curriculum_basic.curriculum == curriculum
        assert class_subject.teacher == teacher
        
        # Test functionality
        assert capacity_mgmt.can_enroll_student()[0] is True
        assert basic_subject.get_difficulty_level() == 'Beginner'
        assert advanced_subject.get_difficulty_level() == 'Intermediate'
        assert class_subject.available_capacity == 30
        assert curriculum.is_current is True
        
        workload = curriculum_basic.calculate_workload()
        assert workload['credit_hours'] == 3
        assert workload['weekly_teaching_hours'] == 4