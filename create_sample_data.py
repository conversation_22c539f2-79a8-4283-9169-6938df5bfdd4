#!/usr/bin/env python
"""
<PERSON><PERSON>t to create sample data for the School ERP system
"""
import os
import sys
import django
from datetime import date, datetime, timedelta
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from accounts.models import User, UserProfile
from core.models import School, AcademicYear, Semester
from students.models import Grade, Class, Parent, Student
from academics.models import Subject, Teacher, ClassSubject, Schedule
from hr.models import Department, Position, Employee
from finance.models import AccountType, Account, FeeType, GradeFee, StudentFee, Payment, PaymentItem


def create_sample_data():
    print("Creating sample data for School ERP system...")
    
    # Create School
    school, created = School.objects.get_or_create(
        code='SCH001',
        defaults={
            'name': 'Greenwood International School',
            'name_ar': 'مدرسة جرينوود الدولية',
            'address': '123 Education Street, Knowledge City',
            'phone': '******-0123',
            'email': '<EMAIL>',
            'website': 'https://greenwood.edu',
            'principal_name': 'Dr. <PERSON>',
            'established_date': date(2010, 9, 1),
            'license_number': 'EDU-2010-001',
            'tax_number': 'TAX-*********'
        }
    )
    print(f"✓ School: {school.name}")
    
    # Create Academic Year
    current_year = date.today().year
    academic_year, created = AcademicYear.objects.get_or_create(
        name=f'{current_year}-{current_year + 1}',
        defaults={
            'start_date': date(current_year, 9, 1),
            'end_date': date(current_year + 1, 6, 30),
            'is_current': True
        }
    )
    print(f"✓ Academic Year: {academic_year.name}")
    
    # Create Semesters
    semester1, created = Semester.objects.get_or_create(
        academic_year=academic_year,
        name='First Semester',
        defaults={
            'start_date': date(current_year, 9, 1),
            'end_date': date(current_year, 12, 20),
            'is_current': True
        }
    )
    
    semester2, created = Semester.objects.get_or_create(
        academic_year=academic_year,
        name='Second Semester',
        defaults={
            'start_date': date(current_year + 1, 1, 10),
            'end_date': date(current_year + 1, 6, 30),
            'is_current': False
        }
    )
    print(f"✓ Semesters: {semester1.name}, {semester2.name}")
    
    # Create Grades
    grades_data = [
        (1, 'Grade 1', 'الصف الأول'),
        (2, 'Grade 2', 'الصف الثاني'),
        (3, 'Grade 3', 'الصف الثالث'),
        (4, 'Grade 4', 'الصف الرابع'),
        (5, 'Grade 5', 'الصف الخامس'),
        (6, 'Grade 6', 'الصف السادس'),
    ]
    
    grades = []
    for level, name, name_ar in grades_data:
        grade, created = Grade.objects.get_or_create(
            level=level,
            defaults={'name': name, 'name_ar': name_ar}
        )
        grades.append(grade)
    print(f"✓ Grades: {len(grades)} created")
    
    # Create Subjects
    subjects_data = [
        ('MATH', 'Mathematics', 'الرياضيات'),
        ('ENG', 'English Language', 'اللغة الإنجليزية'),
        ('ARA', 'Arabic Language', 'اللغة العربية'),
        ('SCI', 'Science', 'العلوم'),
        ('HIST', 'History', 'التاريخ'),
        ('GEO', 'Geography', 'الجغرافيا'),
        ('ART', 'Art', 'الفنون'),
        ('PE', 'Physical Education', 'التربية البدنية'),
    ]
    
    subjects = []
    for code, name, name_ar in subjects_data:
        subject, created = Subject.objects.get_or_create(
            code=code,
            defaults={'name': name, 'name_ar': name_ar, 'credit_hours': 3}
        )
        # Add subject to all grades
        subject.grades.set(grades)
        subjects.append(subject)
    print(f"✓ Subjects: {len(subjects)} created")
    
    # Create Users for different roles
    users_data = [
        ('teacher1', 'teacher', 'John', 'Smith', '<EMAIL>'),
        ('teacher2', 'teacher', 'Emily', 'Davis', '<EMAIL>'),
        ('teacher3', 'teacher', 'Ahmed', 'Hassan', '<EMAIL>'),
        ('parent1', 'parent', 'Michael', 'Johnson', '<EMAIL>'),
        ('parent2', 'parent', 'Sarah', 'Williams', '<EMAIL>'),
        ('student1', 'student', 'Alex', 'Johnson', '<EMAIL>'),
        ('student2', 'student', 'Emma', 'Williams', '<EMAIL>'),
        ('accountant1', 'accountant', 'Lisa', 'Brown', '<EMAIL>'),
    ]
    
    created_users = {}
    for username, user_type, first_name, last_name, email in users_data:
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'user_type': user_type,
                'first_name': first_name,
                'last_name': last_name,
                'email': email,
                'is_active': True
            }
        )
        if created:
            user.set_password('password123')
            user.save()
        created_users[username] = user
    print(f"✓ Users: {len(created_users)} created")
    
    # Create Teachers
    teacher_users = [u for u in created_users.values() if u.user_type == 'teacher']
    teachers = []
    for i, user in enumerate(teacher_users, 1):
        teacher, created = Teacher.objects.get_or_create(
            user=user,
            defaults={
                'employee_id': f'T{i:03d}',
                'hire_date': date(2020, 9, 1),
                'qualification': 'Bachelor of Education',
                'experience_years': 5,
                'salary': Decimal('50000.00'),
                'department': 'Academic'
            }
        )
        # Assign subjects to teachers
        teacher.subjects.set(subjects[:3])  # Each teacher gets first 3 subjects
        teachers.append(teacher)
    print(f"✓ Teachers: {len(teachers)} created")
    
    # Create Classes
    classes = []
    for grade in grades[:3]:  # Create classes for first 3 grades
        for section in ['A', 'B']:
            class_obj, created = Class.objects.get_or_create(
                name=section,
                grade=grade,
                academic_year=academic_year,
                defaults={
                    'class_teacher': teachers[0].user if teachers else None,
                    'max_students': 25,
                    'room_number': f'{grade.level}{section}'
                }
            )
            classes.append(class_obj)
    print(f"✓ Classes: {len(classes)} created")
    
    # Create Parents
    parent_users = [u for u in created_users.values() if u.user_type == 'parent']
    parents = []
    for user in parent_users:
        parent, created = Parent.objects.get_or_create(
            user=user,
            defaults={
                'father_name': user.first_name + ' ' + user.last_name,
                'mother_name': 'Jane ' + user.last_name,
                'father_phone': '******-0100',
                'mother_phone': '******-0101',
                'father_occupation': 'Engineer',
                'mother_occupation': 'Teacher',
                'home_address': '456 Family Street, Hometown'
            }
        )
        parents.append(parent)
    print(f"✓ Parents: {len(parents)} created")
    
    # Create Students
    student_users = [u for u in created_users.values() if u.user_type == 'student']
    students = []
    for i, user in enumerate(student_users):
        student, created = Student.objects.get_or_create(
            user=user,
            defaults={
                'student_id': f'S{2025}{i+1:04d}',
                'admission_number': f'ADM{2025}{i+1:04d}',
                'first_name': user.first_name,
                'last_name': user.last_name,
                'date_of_birth': date(2010, 1, 15),
                'gender': 'M' if i % 2 == 0 else 'F',
                'nationality': 'American',
                'parent': parents[i] if i < len(parents) else parents[0],
                'current_class': classes[i] if i < len(classes) else classes[0],
                'admission_date': date(2024, 9, 1)
            }
        )
        students.append(student)
    print(f"✓ Students: {len(students)} created")
    
    # Create Account Types and Accounts
    account_types_data = [
        ('asset', 'Assets', 'الأصول'),
        ('liability', 'Liabilities', 'الخصوم'),
        ('equity', 'Equity', 'حقوق الملكية'),
        ('revenue', 'Revenue', 'الإيرادات'),
        ('expense', 'Expenses', 'المصروفات'),
    ]
    
    account_types = {}
    for type_code, name, name_ar in account_types_data:
        account_type, created = AccountType.objects.get_or_create(
            type=type_code,
            defaults={'name': name, 'name_ar': name_ar}
        )
        account_types[type_code] = account_type
    
    # Create main accounts
    accounts_data = [
        ('1000', 'Cash', 'النقد', 'asset'),
        ('1100', 'Accounts Receivable', 'الذمم المدينة', 'asset'),
        ('2000', 'Accounts Payable', 'الذمم الدائنة', 'liability'),
        ('3000', 'Capital', 'رأس المال', 'equity'),
        ('4000', 'Tuition Revenue', 'إيرادات الرسوم الدراسية', 'revenue'),
        ('5000', 'Salary Expenses', 'مصروفات الرواتب', 'expense'),
    ]
    
    accounts = {}
    for code, name, name_ar, type_code in accounts_data:
        account, created = Account.objects.get_or_create(
            code=code,
            defaults={
                'name': name,
                'name_ar': name_ar,
                'account_type': account_types[type_code]
            }
        )
        accounts[code] = account
    print(f"✓ Accounts: {len(accounts)} created")
    
    # Create Fee Types
    fee_types_data = [
        ('TUITION', 'Tuition Fee', 'الرسوم الدراسية', '4000'),
        ('TRANSPORT', 'Transportation Fee', 'رسوم النقل', '4000'),
        ('BOOKS', 'Books Fee', 'رسوم الكتب', '4000'),
        ('ACTIVITY', 'Activity Fee', 'رسوم الأنشطة', '4000'),
    ]
    
    fee_types = []
    for code, name, name_ar, account_code in fee_types_data:
        fee_type, created = FeeType.objects.get_or_create(
            name=name,
            defaults={
                'name_ar': name_ar,
                'account': accounts[account_code]
            }
        )
        fee_types.append(fee_type)
    print(f"✓ Fee Types: {len(fee_types)} created")
    
    # Create Grade Fees
    for grade in grades:
        for fee_type in fee_types:
            base_amount = 1000 + (grade.level * 100)  # Higher grades cost more
            if fee_type.name == 'Transportation Fee':
                base_amount = 500
            elif fee_type.name == 'Books Fee':
                base_amount = 200
            elif fee_type.name == 'Activity Fee':
                base_amount = 150
                
            grade_fee, created = GradeFee.objects.get_or_create(
                grade=grade,
                fee_type=fee_type,
                academic_year=academic_year,
                defaults={
                    'amount': Decimal(str(base_amount)),
                    'due_date': date(current_year, 10, 1)
                }
            )
    print("✓ Grade Fees created")
    
    print("\n🎉 Sample data creation completed successfully!")
    print("\nTest Accounts:")
    print("- Admin: admin / admin123")
    print("- Teacher: teacher1 / password123")
    print("- Parent: parent1 / password123")
    print("- Student: student1 / password123")
    print("- Accountant: accountant1 / password123")
    print("\nAccess the system at: http://127.0.0.1:8000")


if __name__ == '__main__':
    create_sample_data()
