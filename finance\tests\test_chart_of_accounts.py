"""
Tests for Chart of Accounts functionality
"""
import pytest
from decimal import Decimal
from datetime import date, timedelta
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.db import IntegrityError

from core.models import School, AcademicYear
from finance.models import Account, AccountType, JournalEntry
from finance.services import ChartOfAccountsService, AccountValidationService

User = get_user_model()


@pytest.mark.django_db
class TestAccountModel:
    """Test Account model functionality"""
    
    @pytest.fixture
    def school(self):
        return School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="+**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
    
    @pytest.fixture
    def user(self):
        return User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
    
    @pytest.fixture
    def account_type(self, school):
        return AccountType.objects.create(
            school=school,
            name="Assets",
            type="asset"
        )
    
    @pytest.fixture
    def parent_account(self, school, account_type):
        return Account.objects.create(
            school=school,
            code="1000",
            name="Assets",
            account_type=account_type,
            is_header=True
        )
    
    def test_account_creation(self, school, account_type):
        """Test basic account creation"""
        account = Account.objects.create(
            school=school,
            code="1100",
            name="Current Assets",
            account_type=account_type,
            is_header=True
        )
        
        assert account.code == "1100"
        assert account.name == "Current Assets"
        assert account.level == 0
        assert account.path == "1100"
        assert account.is_active is True
        assert account.is_archived is False
    
    def test_account_hierarchy(self, school, account_type, parent_account):
        """Test account hierarchy functionality"""
        child_account = Account.objects.create(
            school=school,
            code="1110",
            name="Cash",
            account_type=account_type,
            parent=parent_account
        )
        
        assert child_account.level == 1
        assert child_account.path == "1000/1110"
        assert child_account.parent == parent_account
        assert child_account in parent_account.children.all()
    
    def test_account_code_uniqueness(self, school, account_type):
        """Test account code uniqueness within school"""
        Account.objects.create(
            school=school,
            code="1100",
            name="Current Assets",
            account_type=account_type
        )
        
        with pytest.raises(IntegrityError):
            Account.objects.create(
                school=school,
                code="1100",
                name="Duplicate Code",
                account_type=account_type
            )
    
    def test_account_validation(self, school, account_type):
        """Test account validation rules"""
        # Test invalid code format
        account = Account(
            school=school,
            code="ABC123",
            name="Invalid Code",
            account_type=account_type
        )
        
        with pytest.raises(ValidationError):
            account.full_clean()
    
    def test_circular_reference_prevention(self, school, account_type):
        """Test prevention of circular references"""
        parent = Account.objects.create(
            school=school,
            code="1000",
            name="Parent",
            account_type=account_type,
            is_header=True
        )
        
        child = Account.objects.create(
            school=school,
            code="1100",
            name="Child",
            account_type=account_type,
            parent=parent,
            is_header=True
        )
        
        # Try to make parent a child of child (circular reference)
        parent.parent = child
        
        with pytest.raises(ValidationError):
            parent.full_clean()
    
    def test_account_balance_calculation(self, school, account_type, user):
        """Test account balance calculation"""
        account = Account.objects.create(
            school=school,
            code="1110",
            name="Cash",
            account_type=account_type,
            opening_balance=Decimal('1000.00')
        )
        
        # Create journal entries
        JournalEntry.objects.create(
            school=school,
            reference_number="JE001",
            entry_date=date.today(),
            description="Test debit entry",
            account=account,
            debit_amount=Decimal('500.00'),
            is_posted=True,
            created_by=user
        )
        
        JournalEntry.objects.create(
            school=school,
            reference_number="JE002",
            entry_date=date.today(),
            description="Test credit entry",
            account=account,
            credit_amount=Decimal('200.00'),
            is_posted=True,
            created_by=user
        )
        
        # For asset account (debit balance type): opening + debits - credits
        expected_balance = Decimal('1000.00') + Decimal('500.00') - Decimal('200.00')
        assert account.get_balance() == expected_balance
    
    def test_account_archiving(self, school, account_type, user):
        """Test account archiving functionality"""
        account = Account.objects.create(
            school=school,
            code="1110",
            name="Cash",
            account_type=account_type
        )
        
        # Test archiving
        can_archive, error = account.can_be_archived()
        assert can_archive is True
        assert error is None
        
        account.archive(user, "Test archiving")
        
        assert account.is_archived is True
        assert account.archived_by == user
        assert account.archive_reason == "Test archiving"
        assert account.is_active is False
        
        # Test unarchiving
        account.unarchive()
        
        assert account.is_archived is False
        assert account.archived_by is None
        assert account.archive_reason is None
        assert account.is_active is True
    
    def test_account_deletion_validation(self, school, account_type, user):
        """Test account deletion validation"""
        account = Account.objects.create(
            school=school,
            code="1110",
            name="Cash",
            account_type=account_type
        )
        
        # Account without entries can be deleted
        can_delete, error = account.can_be_deleted()
        assert can_delete is True
        assert error is None
        
        # Add journal entry
        JournalEntry.objects.create(
            school=school,
            reference_number="JE001",
            entry_date=date.today(),
            description="Test entry",
            account=account,
            debit_amount=Decimal('100.00'),
            created_by=user
        )
        
        # Account with entries cannot be deleted
        can_delete, error = account.can_be_deleted()
        assert can_delete is False
        assert "journal entries" in str(error)


@pytest.mark.django_db
class TestChartOfAccountsService:
    """Test Chart of Accounts service functionality"""
    
    @pytest.fixture
    def school(self):
        return School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="+**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
    
    @pytest.fixture
    def user(self):
        return User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
    
    def test_create_default_accounts(self, school):
        """Test creation of default chart of accounts"""
        accounts = ChartOfAccountsService.create_default_accounts(school)
        
        assert len(accounts) > 0
        assert '1000' in accounts  # Assets
        assert '2000' in accounts  # Liabilities
        assert '3000' in accounts  # Equity
        assert '4000' in accounts  # Revenue
        assert '5000' in accounts  # Expenses
        
        # Verify hierarchy
        assets_account = accounts['1000']
        current_assets = accounts['1100']
        
        assert current_assets.parent == assets_account
        assert current_assets.level == 1
        assert assets_account.is_header is True
        assert assets_account.is_system_account is True
    
    def test_account_tree_generation(self, school):
        """Test account tree generation"""
        ChartOfAccountsService.create_default_accounts(school)
        
        tree = ChartOfAccountsService.get_account_tree(school)
        
        assert len(tree) == 5  # 5 root account types
        
        # Check tree structure
        assets_node = next((node for node in tree if node['account'].code == '1000'), None)
        assert assets_node is not None
        assert len(assets_node['children']) > 0
        
        # Check child structure
        current_assets = next(
            (child for child in assets_node['children'] if child['account'].code == '1100'), 
            None
        )
        assert current_assets is not None
        assert len(current_assets['children']) > 0
    
    def test_account_balance_calculation(self, school, user):
        """Test account balance calculation service"""
        ChartOfAccountsService.create_default_accounts(school)
        
        cash_account = Account.objects.get(school=school, code='1110')
        
        # Create journal entries
        JournalEntry.objects.create(
            school=school,
            reference_number="JE001",
            entry_date=date.today(),
            description="Test entry",
            account=cash_account,
            debit_amount=Decimal('1000.00'),
            is_posted=True,
            created_by=user
        )
        
        balances = ChartOfAccountsService.get_account_balances(school)
        
        assert '1110' in balances
        assert balances['1110'] == Decimal('1000.00')
    
    def test_trial_balance_report(self, school, user):
        """Test trial balance report generation"""
        ChartOfAccountsService.create_default_accounts(school)
        
        # Create some journal entries
        cash_account = Account.objects.get(school=school, code='1110')
        revenue_account = Account.objects.get(school=school, code='4100')
        
        JournalEntry.objects.create(
            school=school,
            reference_number="JE001",
            entry_date=date.today(),
            description="Cash receipt",
            account=cash_account,
            debit_amount=Decimal('1000.00'),
            is_posted=True,
            created_by=user
        )
        
        JournalEntry.objects.create(
            school=school,
            reference_number="JE002",
            entry_date=date.today(),
            description="Revenue recognition",
            account=revenue_account,
            credit_amount=Decimal('1000.00'),
            is_posted=True,
            created_by=user
        )
        
        report = ChartOfAccountsService.generate_account_report(school, 'trial_balance')
        
        assert report['title'] == 'Trial Balance'
        assert report['total_debits'] == report['total_credits']
        assert len(report['accounts']) >= 2
    
    def test_balance_sheet_report(self, school, user):
        """Test balance sheet report generation"""
        ChartOfAccountsService.create_default_accounts(school)
        
        # Create some journal entries
        cash_account = Account.objects.get(school=school, code='1110')
        capital_account = Account.objects.get(school=school, code='3100')
        
        JournalEntry.objects.create(
            school=school,
            reference_number="JE001",
            entry_date=date.today(),
            description="Initial capital",
            account=cash_account,
            debit_amount=Decimal('10000.00'),
            is_posted=True,
            created_by=user
        )
        
        JournalEntry.objects.create(
            school=school,
            reference_number="JE002",
            entry_date=date.today(),
            description="Initial capital",
            account=capital_account,
            credit_amount=Decimal('10000.00'),
            is_posted=True,
            created_by=user
        )
        
        report = ChartOfAccountsService.generate_account_report(school, 'balance_sheet')
        
        assert report['title'] == 'Balance Sheet'
        assert report['total_assets'] == report['total_equity']
        assert len(report['assets']) >= 1
        assert len(report['equity']) >= 1
    
    def test_income_statement_report(self, school, user):
        """Test income statement report generation"""
        ChartOfAccountsService.create_default_accounts(school)
        
        # Create revenue and expense entries
        revenue_account = Account.objects.get(school=school, code='4100')
        expense_account = Account.objects.get(school=school, code='5100')
        
        JournalEntry.objects.create(
            school=school,
            reference_number="JE001",
            entry_date=date.today(),
            description="Tuition revenue",
            account=revenue_account,
            credit_amount=Decimal('5000.00'),
            is_posted=True,
            created_by=user
        )
        
        JournalEntry.objects.create(
            school=school,
            reference_number="JE002",
            entry_date=date.today(),
            description="Salary expense",
            account=expense_account,
            debit_amount=Decimal('3000.00'),
            is_posted=True,
            created_by=user
        )
        
        report = ChartOfAccountsService.generate_account_report(school, 'income_statement')
        
        assert report['title'] == 'Income Statement'
        assert report['total_revenue'] == Decimal('5000.00')
        assert report['total_expenses'] == Decimal('3000.00')
        assert report['net_income'] == Decimal('2000.00')


@pytest.mark.django_db
class TestAccountValidationService:
    """Test Account validation service"""
    
    @pytest.fixture
    def school(self):
        return School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="+**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
    
    @pytest.fixture
    def account_type(self, school):
        return AccountType.objects.create(
            school=school,
            name="Assets",
            type="asset"
        )
    
    def test_account_code_validation(self, school, account_type):
        """Test account code validation"""
        # Valid code
        is_valid, error = AccountValidationService.validate_account_code(school, "1100")
        assert is_valid is True
        assert error is None
        
        # Invalid format (non-numeric)
        is_valid, error = AccountValidationService.validate_account_code(school, "ABC123")
        assert is_valid is False
        assert "digits" in str(error)
        
        # Invalid length (too short)
        is_valid, error = AccountValidationService.validate_account_code(school, "12")
        assert is_valid is False
        assert "between 3 and 10" in str(error)
        
        # Create account and test uniqueness
        Account.objects.create(
            school=school,
            code="1100",
            name="Test Account",
            account_type=account_type
        )
        
        is_valid, error = AccountValidationService.validate_account_code(school, "1100")
        assert is_valid is False
        assert "already exists" in str(error)
    
    def test_account_deletion_validation(self, school, account_type):
        """Test account deletion validation"""
        account = Account.objects.create(
            school=school,
            code="1100",
            name="Test Account",
            account_type=account_type
        )
        
        # Can delete account without entries
        is_valid, error = AccountValidationService.validate_account_deletion(account)
        assert is_valid is True
        assert error is None
        
        # Cannot delete system account
        account.is_system_account = True
        account.save()
        
        is_valid, error = AccountValidationService.validate_account_deletion(account)
        assert is_valid is False
        assert "System accounts" in str(error)
    
    def test_journal_entry_validation(self, school, account_type):
        """Test journal entry account validation"""
        # Regular account - should allow entries
        account = Account.objects.create(
            school=school,
            code="1100",
            name="Test Account",
            account_type=account_type
        )
        
        is_valid, error = AccountValidationService.validate_journal_entry_account(account)
        assert is_valid is True
        assert error is None
        
        # Header account - should not allow entries
        account.is_header = True
        account.save()
        
        is_valid, error = AccountValidationService.validate_journal_entry_account(account)
        assert is_valid is False
        assert "header accounts" in str(error)
        
        # Account with manual entries disabled
        account.is_header = False
        account.allow_manual_entries = False
        account.save()
        
        is_valid, error = AccountValidationService.validate_journal_entry_account(account)
        assert is_valid is False
        assert "Manual entries not allowed" in str(error)


class TestAccountIntegration(TestCase):
    """Integration tests for Account functionality"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="+**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
    
    def test_complete_account_workflow(self):
        """Test complete account management workflow"""
        # 1. Create default chart of accounts
        accounts = ChartOfAccountsService.create_default_accounts(self.school)
        self.assertGreater(len(accounts), 0)
        
        # 2. Create custom account
        asset_type = AccountType.objects.get(school=self.school, type='asset')
        parent_account = Account.objects.get(school=self.school, code='1100')
        
        custom_account = Account.objects.create(
            school=self.school,
            code="1115",
            name="Petty Cash",
            account_type=asset_type,
            parent=parent_account,
            opening_balance=Decimal('500.00')
        )
        
        # 3. Validate account hierarchy
        self.assertEqual(custom_account.level, 2)
        self.assertEqual(custom_account.path, "1000/1100/1115")
        
        # 4. Create journal entries
        JournalEntry.objects.create(
            school=self.school,
            reference_number="JE001",
            entry_date=date.today(),
            description="Petty cash replenishment",
            account=custom_account,
            debit_amount=Decimal('200.00'),
            is_posted=True,
            created_by=self.user
        )
        
        # 5. Check balance calculation
        expected_balance = Decimal('500.00') + Decimal('200.00')
        self.assertEqual(custom_account.get_balance(), expected_balance)
        
        # 6. Generate reports
        trial_balance = ChartOfAccountsService.generate_account_report(
            self.school, 'trial_balance'
        )
        self.assertIn('accounts', trial_balance)
        self.assertGreater(len(trial_balance['accounts']), 0)
        
        # 7. Archive account (should fail due to journal entries)
        can_archive, error = custom_account.can_be_archived()
        self.assertTrue(can_archive)  # Should be able to archive even with entries
        
        custom_account.archive(self.user, "Test archiving")
        self.assertTrue(custom_account.is_archived)
        
        # 8. Unarchive account
        custom_account.unarchive()
        self.assertFalse(custom_account.is_archived)
    
    def test_account_tree_operations(self):
        """Test account tree operations"""
        # Create default accounts
        ChartOfAccountsService.create_default_accounts(self.school)
        
        # Get account tree
        tree = ChartOfAccountsService.get_account_tree(self.school)
        
        # Verify tree structure
        self.assertEqual(len(tree), 5)  # 5 main account types
        
        # Find assets node
        assets_node = next((node for node in tree if node['account'].code == '1000'), None)
        self.assertIsNotNone(assets_node)
        self.assertGreater(len(assets_node['children']), 0)
        
        # Verify child relationships
        current_assets = next(
            (child for child in assets_node['children'] if child['account'].code == '1100'),
            None
        )
        self.assertIsNotNone(current_assets)
        self.assertGreater(len(current_assets['children']), 0)