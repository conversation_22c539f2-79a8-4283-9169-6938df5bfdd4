# Generated by Django 5.2.4 on 2025-07-14 16:19

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('academics', '0002_initial'),
        ('core', '0001_initial'),
        ('students', '0002_electronicregistration_studentattachment_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Curriculum',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('name', models.CharField(max_length=200, verbose_name='Curriculum Name')),
                ('name_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Curriculum Name (Arabic)')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('objectives', models.TextField(blank=True, null=True, verbose_name='Learning Objectives')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='curriculums', to='core.academicyear', verbose_name='Academic Year')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_curriculums', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('grade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='curriculums', to='students.grade', verbose_name='Grade')),
            ],
            options={
                'verbose_name': 'Curriculum',
                'verbose_name_plural': 'Curriculums',
                'ordering': ['grade', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Exam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=200, verbose_name='Exam Name')),
                ('name_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Exam Name (Arabic)')),
                ('exam_type', models.CharField(choices=[('quiz', 'Quiz'), ('midterm', 'Midterm'), ('final', 'Final'), ('assignment', 'Assignment'), ('project', 'Project'), ('oral', 'Oral Exam')], max_length=20, verbose_name='Exam Type')),
                ('exam_date', models.DateField(verbose_name='Exam Date')),
                ('start_time', models.TimeField(verbose_name='Start Time')),
                ('end_time', models.TimeField(verbose_name='End Time')),
                ('duration_minutes', models.PositiveIntegerField(verbose_name='Duration (Minutes)')),
                ('total_marks', models.PositiveIntegerField(verbose_name='Total Marks')),
                ('passing_marks', models.PositiveIntegerField(verbose_name='Passing Marks')),
                ('room_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='Room Number')),
                ('instructions', models.TextField(blank=True, null=True, verbose_name='Instructions')),
                ('is_published', models.BooleanField(default=False, verbose_name='Is Published')),
                ('class_subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exams', to='academics.classsubject', verbose_name='Class Subject')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_exams', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'Exam',
                'verbose_name_plural': 'Exams',
                'ordering': ['-exam_date', '-start_time'],
            },
        ),
        migrations.CreateModel(
            name='CurriculumSubject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('weekly_hours', models.PositiveIntegerField(verbose_name='Weekly Hours')),
                ('semester', models.CharField(choices=[('first', 'First Semester'), ('second', 'Second Semester'), ('full_year', 'Full Year')], default='full_year', max_length=20, verbose_name='Semester')),
                ('is_mandatory', models.BooleanField(default=True, verbose_name='Is Mandatory')),
                ('learning_outcomes', models.TextField(blank=True, null=True, verbose_name='Learning Outcomes')),
                ('assessment_criteria', models.TextField(blank=True, null=True, verbose_name='Assessment Criteria')),
                ('curriculum', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='curriculum_subjects', to='academics.curriculum', verbose_name='Curriculum')),
                ('subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='curriculum_mappings', to='academics.subject', verbose_name='Subject')),
            ],
            options={
                'verbose_name': 'Curriculum Subject',
                'verbose_name_plural': 'Curriculum Subjects',
                'ordering': ['curriculum', 'subject'],
                'unique_together': {('curriculum', 'subject')},
            },
        ),
        migrations.CreateModel(
            name='StudentAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('date', models.DateField(verbose_name='Date')),
                ('status', models.CharField(choices=[('present', 'Present'), ('absent', 'Absent'), ('late', 'Late'), ('excused', 'Excused')], max_length=10, verbose_name='Status')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('class_subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='academics.classsubject', verbose_name='Class Subject')),
                ('marked_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='marked_attendance', to=settings.AUTH_USER_MODEL, verbose_name='Marked By')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='academic_attendance', to='students.student', verbose_name='Student')),
            ],
            options={
                'verbose_name': 'Student Attendance',
                'verbose_name_plural': 'Student Attendance',
                'ordering': ['-date', 'student'],
                'unique_together': {('student', 'class_subject', 'date')},
            },
        ),
        migrations.CreateModel(
            name='StudentGrade',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('marks_obtained', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Marks Obtained')),
                ('grade_letter', models.CharField(blank=True, max_length=2, null=True, verbose_name='Grade Letter')),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Percentage')),
                ('remarks', models.TextField(blank=True, null=True, verbose_name='Remarks')),
                ('graded_at', models.DateTimeField(auto_now_add=True, verbose_name='Graded At')),
                ('exam', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_grades', to='academics.exam', verbose_name='Exam')),
                ('graded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='graded_assessments', to=settings.AUTH_USER_MODEL, verbose_name='Graded By')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='academic_grades', to='students.student', verbose_name='Student')),
            ],
            options={
                'verbose_name': 'Student Grade',
                'verbose_name_plural': 'Student Grades',
                'ordering': ['-graded_at'],
                'unique_together': {('student', 'exam')},
            },
        ),
    ]
