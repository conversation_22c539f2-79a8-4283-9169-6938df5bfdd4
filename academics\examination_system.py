"""
Advanced Examination System for Task 4.4: Implement Examination System

This module provides:
- Exam scheduling and management
- Exam room allocation system
- Invigilation assignment
- Exam result processing
- Exam analytics and reporting
"""

from datetime import datetime, timedelta, time, date
from typing import List, Dict, Tuple, Optional, Set
from django.db.models import Q, Count, Avg, Sum, Max, Min
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.db import transaction

from .models import (
    Exam, ClassSubject, Teacher, Room, Schedule, StudentGrade, 
    Student, Subject, ExamSession, ExamInvigilator, ExamSeating,
    ExamResult, ExamAnalytics
)
from core.models import AcademicYear, Semester


class ExamSchedulingConflict:
    """Represents an exam scheduling conflict"""
    
    def __init__(self, conflict_type: str, message: str, exams: List[Exam] = None):
        self.conflict_type = conflict_type  # 'teacher', 'room', 'student', 'time'
        self.message = message
        self.exams = exams or []
    
    def __str__(self):
        return f"{self.conflict_type}: {self.message}"


class ExamScheduler:
    """
    Advanced exam scheduling system with conflict detection and optimization
    """
    
    def __init__(self, school, academic_year: AcademicYear):
        self.school = school
        self.academic_year = academic_year
        self.conflicts = []
    
    def schedule_exam(self, exam: Exam, preferred_rooms: List[Room] = None) -> Dict:
        """Schedule a single exam with room allocation and conflict checking"""
        result = {
            'success': False,
            'exam': exam,
            'allocated_room': None,
            'conflicts': [],
            'suggestions': []
        }
        
        # Check for conflicts
        conflicts = self.detect_exam_conflicts(exam)
        if conflicts:
            result['conflicts'] = conflicts
            result['suggestions'] = self._generate_conflict_resolutions(exam, conflicts)
            return result
        
        # Allocate room
        allocated_room = self.allocate_exam_room(exam, preferred_rooms)
        if allocated_room:
            exam.room = allocated_room
            exam.save()
            result['allocated_room'] = allocated_room
            result['success'] = True
        else:
            result['conflicts'].append(ExamSchedulingConflict(
                'room',
                'No suitable room available for the exam',
                [exam]
            ))
        
        return result
    
    def detect_exam_conflicts(self, exam: Exam) -> List[ExamSchedulingConflict]:
        """Detect all conflicts for an exam"""
        conflicts = []
        
        # Teacher conflicts (if teacher is assigned as invigilator)
        teacher_conflicts = self._check_teacher_conflicts(exam)
        conflicts.extend(teacher_conflicts)
        
        # Room conflicts
        room_conflicts = self._check_room_conflicts(exam)
        conflicts.extend(room_conflicts)
        
        # Student conflicts (same students having multiple exams)
        student_conflicts = self._check_student_conflicts(exam)
        conflicts.extend(student_conflicts)
        
        # Time conflicts with regular classes
        class_conflicts = self._check_class_schedule_conflicts(exam)
        conflicts.extend(class_conflicts)
        
        return conflicts
    
    def _check_teacher_conflicts(self, exam: Exam) -> List[ExamSchedulingConflict]:
        """Check for teacher scheduling conflicts"""
        conflicts = []
        
        # Check if the subject teacher has other commitments
        subject_teacher = exam.class_subject.teacher
        
        # Check for other exams at the same time
        conflicting_exams = Exam.objects.filter(
            school=self.school,
            exam_date=exam.exam_date,
            start_time__lt=exam.end_time,
            end_time__gt=exam.start_time,
            class_subject__teacher=subject_teacher
        ).exclude(pk=exam.pk if exam.pk else None)
        
        if conflicting_exams.exists():
            conflicts.append(ExamSchedulingConflict(
                'teacher',
                f'Teacher {subject_teacher} has conflicting exam schedules',
                list(conflicting_exams)
            ))
        
        # Check for regular class schedules
        conflicting_schedules = Schedule.objects.filter(
            school=self.school,
            class_subject__teacher=subject_teacher,
            day_of_week=exam.exam_date.strftime('%A').lower(),
            start_time__lt=exam.end_time,
            end_time__gt=exam.start_time,
            status='active'
        )
        
        if conflicting_schedules.exists():
            conflicts.append(ExamSchedulingConflict(
                'teacher',
                f'Teacher {subject_teacher} has regular classes during exam time',
                [exam]
            ))
        
        return conflicts
    
    def _check_room_conflicts(self, exam: Exam) -> List[ExamSchedulingConflict]:
        """Check for room allocation conflicts"""
        conflicts = []
        
        if not hasattr(exam, 'room') or not exam.room:
            return conflicts
        
        # Check for other exams in the same room
        conflicting_exams = Exam.objects.filter(
            school=self.school,
            room=exam.room,
            exam_date=exam.exam_date,
            start_time__lt=exam.end_time,
            end_time__gt=exam.start_time
        ).exclude(pk=exam.pk if exam.pk else None)
        
        if conflicting_exams.exists():
            conflicts.append(ExamSchedulingConflict(
                'room',
                f'Room {exam.room} has conflicting exam schedules',
                list(conflicting_exams)
            ))
        
        # Check for regular class schedules in the room
        conflicting_schedules = Schedule.objects.filter(
            school=self.school,
            room=exam.room,
            day_of_week=exam.exam_date.strftime('%A').lower(),
            start_time__lt=exam.end_time,
            end_time__gt=exam.start_time,
            status='active'
        )
        
        if conflicting_schedules.exists():
            conflicts.append(ExamSchedulingConflict(
                'room',
                f'Room {exam.room} has regular classes during exam time',
                [exam]
            ))
        
        return conflicts
    
    def _check_student_conflicts(self, exam: Exam) -> List[ExamSchedulingConflict]:
        """Check for student scheduling conflicts"""
        conflicts = []
        
        # Get students in the class
        students = exam.class_subject.class_obj.students.filter(is_active=True)
        
        # Check for other exams for the same students
        for student in students:
            conflicting_exams = Exam.objects.filter(
                class_subject__class_obj__students=student,
                exam_date=exam.exam_date,
                start_time__lt=exam.end_time,
                end_time__gt=exam.start_time
            ).exclude(pk=exam.pk if exam.pk else None)
            
            if conflicting_exams.exists():
                conflicts.append(ExamSchedulingConflict(
                    'student',
                    f'Students have conflicting exam schedules',
                    list(conflicting_exams)
                ))
                break  # One conflict is enough to flag the issue
        
        return conflicts
    
    def _check_class_schedule_conflicts(self, exam: Exam) -> List[ExamSchedulingConflict]:
        """Check for conflicts with regular class schedules"""
        conflicts = []
        
        # Check if the class has regular schedules during exam time
        conflicting_schedules = Schedule.objects.filter(
            school=self.school,
            class_subject__class_obj=exam.class_subject.class_obj,
            day_of_week=exam.exam_date.strftime('%A').lower(),
            start_time__lt=exam.end_time,
            end_time__gt=exam.start_time,
            status='active'
        )
        
        if conflicting_schedules.exists():
            conflicts.append(ExamSchedulingConflict(
                'class',
                f'Class {exam.class_subject.class_obj} has regular schedules during exam time',
                [exam]
            ))
        
        return conflicts
    
    def _generate_conflict_resolutions(self, exam: Exam, conflicts: List[ExamSchedulingConflict]) -> List[Dict]:
        """Generate suggestions to resolve conflicts"""
        suggestions = []
        
        for conflict in conflicts:
            if conflict.conflict_type == 'room':
                # Suggest alternative rooms
                alternative_rooms = self.find_alternative_rooms(exam)
                if alternative_rooms:
                    suggestions.append({
                        'type': 'room_change',
                        'message': 'Consider using alternative rooms',
                        'options': alternative_rooms
                    })
            
            elif conflict.conflict_type == 'time':
                # Suggest alternative time slots
                alternative_times = self.find_alternative_time_slots(exam)
                if alternative_times:
                    suggestions.append({
                        'type': 'time_change',
                        'message': 'Consider alternative time slots',
                        'options': alternative_times
                    })
        
        return suggestions
    
    def allocate_exam_room(self, exam: Exam, preferred_rooms: List[Room] = None) -> Optional[Room]:
        """Allocate the best available room for an exam"""
        # Get class size
        class_size = exam.class_subject.class_obj.students.filter(is_active=True).count()
        
        # Find suitable rooms
        suitable_rooms = Room.objects.filter(
            school=self.school,
            is_available=True,
            capacity__gte=class_size
        )
        
        # Filter by preferred rooms if provided
        if preferred_rooms:
            suitable_rooms = suitable_rooms.filter(id__in=[r.id for r in preferred_rooms])
        
        # Check availability for each room
        available_rooms = []
        for room in suitable_rooms:
            if self._is_room_available(room, exam.exam_date, exam.start_time, exam.end_time):
                available_rooms.append(room)
        
        if not available_rooms:
            return None
        
        # Score and rank rooms
        room_scores = []
        for room in available_rooms:
            score = self._calculate_room_suitability_score(room, exam)
            room_scores.append((room, score))
        
        # Sort by score (highest first)
        room_scores.sort(key=lambda x: x[1], reverse=True)
        
        return room_scores[0][0] if room_scores else None
    
    def _is_room_available(self, room: Room, exam_date: date, start_time: time, end_time: time) -> bool:
        """Check if a room is available for the given time slot"""
        # Check for exam conflicts
        exam_conflicts = Exam.objects.filter(
            room=room,
            exam_date=exam_date,
            start_time__lt=end_time,
            end_time__gt=start_time
        ).exists()
        
        if exam_conflicts:
            return False
        
        # Check for regular schedule conflicts
        day_of_week = exam_date.strftime('%A').lower()
        schedule_conflicts = Schedule.objects.filter(
            room=room,
            day_of_week=day_of_week,
            start_time__lt=end_time,
            end_time__gt=start_time,
            status='active'
        ).exists()
        
        return not schedule_conflicts
    
    def _calculate_room_suitability_score(self, room: Room, exam: Exam) -> float:
        """Calculate suitability score for a room"""
        score = 0.0
        
        # Capacity match (prefer rooms that are not too large)
        class_size = exam.class_subject.class_obj.students.filter(is_active=True).count()
        capacity_ratio = room.capacity / max(class_size, 1)
        
        if 1.0 <= capacity_ratio <= 1.5:
            score += 10.0
        elif 1.5 < capacity_ratio <= 2.0:
            score += 7.0
        elif capacity_ratio > 2.0:
            score += 3.0
        
        # Equipment suitability
        if exam.exam_type in ['computer', 'online'] and room.has_computer:
            score += 5.0
        
        if room.has_projector:
            score += 2.0
        
        # Accessibility
        if room.is_accessible:
            score += 2.0
        
        # Room type preference for exams
        if room.room_type in ['classroom', 'auditorium']:
            score += 3.0
        
        return score
    
    def find_alternative_rooms(self, exam: Exam) -> List[Room]:
        """Find alternative rooms for an exam"""
        class_size = exam.class_subject.class_obj.students.filter(is_active=True).count()
        
        alternative_rooms = Room.objects.filter(
            school=self.school,
            is_available=True,
            capacity__gte=class_size
        )
        
        available_alternatives = []
        for room in alternative_rooms:
            if self._is_room_available(room, exam.exam_date, exam.start_time, exam.end_time):
                available_alternatives.append(room)
        
        return available_alternatives[:5]  # Return top 5 alternatives
    
    def find_alternative_time_slots(self, exam: Exam) -> List[Dict]:
        """Find alternative time slots for an exam"""
        alternatives = []
        
        # Try different times on the same day
        base_date = exam.exam_date
        duration = exam.duration_minutes
        
        # Common exam time slots
        time_slots = [
            (time(8, 0), time(10, 0)),
            (time(10, 30), time(12, 30)),
            (time(14, 0), time(16, 0)),
            (time(16, 30), time(18, 30))
        ]
        
        for start_time, end_time in time_slots:
            # Skip if it's the current time
            if start_time == exam.start_time:
                continue
            
            # Check if this time slot would work
            temp_exam = Exam(
                class_subject=exam.class_subject,
                exam_date=base_date,
                start_time=start_time,
                end_time=end_time,
                duration_minutes=duration
            )
            
            conflicts = self.detect_exam_conflicts(temp_exam)
            if not conflicts:
                alternatives.append({
                    'date': base_date,
                    'start_time': start_time,
                    'end_time': end_time,
                    'conflicts': 0
                })
        
        return alternatives[:3]  # Return top 3 alternatives
    
    def generate_exam_schedule(self, exam_period_start: date, exam_period_end: date) -> Dict:
        """Generate complete exam schedule for a period"""
        result = {
            'total_exams': 0,
            'scheduled_exams': 0,
            'failed_exams': 0,
            'conflicts_detected': 0,
            'schedule': [],
            'conflicts': [],
            'warnings': []
        }
        
        # Get all exams in the period
        exams = Exam.objects.filter(
            school=self.school,
            class_subject__academic_year=self.academic_year,
            exam_date__range=[exam_period_start, exam_period_end]
        ).order_by('exam_date', 'start_time')
        
        result['total_exams'] = exams.count()
        
        for exam in exams:
            schedule_result = self.schedule_exam(exam)
            
            if schedule_result['success']:
                result['scheduled_exams'] += 1
                result['schedule'].append({
                    'exam': exam,
                    'room': schedule_result['allocated_room'],
                    'status': 'scheduled'
                })
            else:
                result['failed_exams'] += 1
                result['conflicts'].extend(schedule_result['conflicts'])
                result['warnings'].append(
                    f'Failed to schedule {exam}: {[str(c) for c in schedule_result["conflicts"]]}'
                )
        
        result['conflicts_detected'] = len(result['conflicts'])
        
        return result


class InvigilationManager:
    """
    Manages teacher assignment for exam invigilation
    """
    
    def __init__(self, school, academic_year: AcademicYear):
        self.school = school
        self.academic_year = academic_year
    
    def assign_invigilators(self, exam: Exam, num_invigilators: int = None) -> Dict:
        """Assign invigilators to an exam"""
        if not num_invigilators:
            # Calculate based on class size (1 invigilator per 30 students)
            class_size = exam.class_subject.class_obj.students.filter(is_active=True).count()
            num_invigilators = max(1, (class_size + 29) // 30)
        
        result = {
            'success': False,
            'assigned_invigilators': [],
            'conflicts': [],
            'warnings': []
        }
        
        # Find available teachers
        available_teachers = self.find_available_teachers(exam)
        
        if len(available_teachers) < num_invigilators:
            result['warnings'].append(
                f'Only {len(available_teachers)} teachers available, need {num_invigilators}'
            )
            num_invigilators = len(available_teachers)
        
        # Assign teachers based on priority
        assigned_teachers = self.select_best_invigilators(exam, available_teachers, num_invigilators)
        
        # Create invigilation assignments
        # We need to get a user for assigned_by - use the first available admin user
        from django.contrib.auth import get_user_model
        User = get_user_model()
        admin_user = User.objects.filter(is_staff=True).first()
        if not admin_user:
            admin_user = User.objects.first()  # Fallback to any user
        
        for i, teacher in enumerate(assigned_teachers):
            invigilator = ExamInvigilator.objects.create(
                school=self.school,
                exam=exam,
                teacher=teacher,
                role='chief' if i == 0 else 'assistant',
                assigned_by=admin_user,
                assigned_at=datetime.now()
            )
            result['assigned_invigilators'].append(invigilator)
        
        result['success'] = len(assigned_teachers) > 0
        
        return result
    
    def find_available_teachers(self, exam: Exam) -> List[Teacher]:
        """Find teachers available for invigilation"""
        # Get all teachers
        all_teachers = Teacher.objects.filter(school=self.school)
        
        available_teachers = []
        
        for teacher in all_teachers:
            if self.is_teacher_available(teacher, exam):
                available_teachers.append(teacher)
        
        return available_teachers
    
    def is_teacher_available(self, teacher: Teacher, exam: Exam) -> bool:
        """Check if a teacher is available for invigilation"""
        # Check if teacher has other exam invigilation duties
        conflicting_invigilation = ExamInvigilator.objects.filter(
            teacher=teacher,
            exam__exam_date=exam.exam_date,
            exam__start_time__lt=exam.end_time,
            exam__end_time__gt=exam.start_time
        ).exists()
        
        if conflicting_invigilation:
            return False
        
        # Check if teacher has regular classes
        day_of_week = exam.exam_date.strftime('%A').lower()
        conflicting_schedule = Schedule.objects.filter(
            class_subject__teacher=teacher,
            day_of_week=day_of_week,
            start_time__lt=exam.end_time,
            end_time__gt=exam.start_time,
            status='active'
        ).exists()
        
        if conflicting_schedule:
            return False
        
        # Check if teacher is on leave (if leave system is implemented)
        # This would integrate with HR leave management
        
        return True
    
    def select_best_invigilators(self, exam: Exam, available_teachers: List[Teacher], 
                                num_needed: int) -> List[Teacher]:
        """Select the best teachers for invigilation based on criteria"""
        teacher_scores = []
        
        for teacher in available_teachers:
            score = self.calculate_invigilation_score(teacher, exam)
            teacher_scores.append((teacher, score))
        
        # Sort by score (highest first)
        teacher_scores.sort(key=lambda x: x[1], reverse=True)
        
        return [teacher for teacher, score in teacher_scores[:num_needed]]
    
    def calculate_invigilation_score(self, teacher: Teacher, exam: Exam) -> float:
        """Calculate suitability score for teacher invigilation"""
        score = 0.0
        
        # Prefer teachers who teach the same subject
        if teacher.subjects.filter(id=exam.class_subject.subject.id).exists():
            score += 10.0
        
        # Prefer teachers with less invigilation load
        current_load = ExamInvigilator.objects.filter(
            teacher=teacher,
            exam__exam_date=exam.exam_date
        ).count()
        
        score += max(0, 5 - current_load)
        
        # Prefer senior teachers for chief invigilator role
        score += min(teacher.experience_years or 0, 10) * 0.5
        
        # Prefer teachers from the same department
        if hasattr(teacher, 'department') and hasattr(exam.class_subject.teacher, 'department'):
            if teacher.department == exam.class_subject.teacher.department:
                score += 3.0
        
        return score
    
    def get_invigilation_schedule(self, start_date: date, end_date: date) -> Dict:
        """Get invigilation schedule for a period"""
        invigilators = ExamInvigilator.objects.filter(
            school=self.school,
            exam__exam_date__range=[start_date, end_date]
        ).select_related('teacher', 'exam', 'exam__class_subject').order_by(
            'exam__exam_date', 'exam__start_time'
        )
        
        schedule = {}
        for invigilator in invigilators:
            date_key = invigilator.exam.exam_date.strftime('%Y-%m-%d')
            if date_key not in schedule:
                schedule[date_key] = []
            
            schedule[date_key].append({
                'exam': invigilator.exam,
                'teacher': invigilator.teacher,
                'role': invigilator.role,
                'start_time': invigilator.exam.start_time,
                'end_time': invigilator.exam.end_time,
                'room': invigilator.exam.room
            })
        
        return schedule


class ExamResultProcessor:
    """
    Processes exam results and generates analytics
    """
    
    def __init__(self, school, academic_year: AcademicYear):
        self.school = school
        self.academic_year = academic_year
    
    def process_exam_results(self, exam: Exam) -> Dict:
        """Process and analyze exam results"""
        result = {
            'exam': exam,
            'total_students': 0,
            'students_appeared': 0,
            'students_passed': 0,
            'pass_percentage': 0.0,
            'average_marks': 0.0,
            'highest_marks': 0.0,
            'lowest_marks': 0.0,
            'grade_distribution': {},
            'analytics': {}
        }
        
        # Get all students in the class
        students = exam.class_subject.class_obj.students.filter(is_active=True)
        result['total_students'] = students.count()
        
        # Get student grades for this exam
        grades = StudentGrade.objects.filter(exam=exam)
        result['students_appeared'] = grades.count()
        
        if grades.exists():
            # Calculate statistics
            marks_list = list(grades.values_list('marks_obtained', flat=True))
            
            result['average_marks'] = sum(marks_list) / len(marks_list)
            result['highest_marks'] = max(marks_list)
            result['lowest_marks'] = min(marks_list)
            
            # Calculate pass percentage
            passed_students = grades.filter(marks_obtained__gte=exam.passing_marks).count()
            result['students_passed'] = passed_students
            result['pass_percentage'] = (passed_students / result['students_appeared']) * 100
            
            # Grade distribution
            grade_distribution = grades.values('grade_letter').annotate(
                count=Count('id')
            ).order_by('grade_letter')
            
            for grade in grade_distribution:
                result['grade_distribution'][grade['grade_letter']] = grade['count']
            
            # Advanced analytics
            result['analytics'] = self.generate_exam_analytics(exam, grades)
        
        return result
    
    def generate_exam_analytics(self, exam: Exam, grades) -> Dict:
        """Generate advanced analytics for exam results"""
        analytics = {}
        
        # Performance distribution
        marks_list = list(grades.values_list('marks_obtained', flat=True))
        
        if marks_list:
            # Quartiles
            sorted_marks = sorted(marks_list)
            n = len(sorted_marks)
            
            analytics['quartiles'] = {
                'q1': sorted_marks[n // 4] if n > 0 else 0,
                'q2': sorted_marks[n // 2] if n > 0 else 0,
                'q3': sorted_marks[3 * n // 4] if n > 0 else 0
            }
            
            # Standard deviation
            mean = sum(float(x) for x in marks_list) / len(marks_list)
            variance = sum((float(x) - mean) ** 2 for x in marks_list) / len(marks_list)
            analytics['standard_deviation'] = variance ** 0.5
            
            # Performance categories
            total_marks = exam.total_marks
            analytics['performance_categories'] = {
                'excellent': grades.filter(marks_obtained__gte=total_marks * 0.9).count(),
                'good': grades.filter(
                    marks_obtained__gte=total_marks * 0.75,
                    marks_obtained__lt=total_marks * 0.9
                ).count(),
                'average': grades.filter(
                    marks_obtained__gte=total_marks * 0.6,
                    marks_obtained__lt=total_marks * 0.75
                ).count(),
                'below_average': grades.filter(
                    marks_obtained__gte=exam.passing_marks,
                    marks_obtained__lt=total_marks * 0.6
                ).count(),
                'failed': grades.filter(marks_obtained__lt=exam.passing_marks).count()
            }
        
        return analytics
    
    def compare_exam_performance(self, exam1: Exam, exam2: Exam) -> Dict:
        """Compare performance between two exams"""
        result1 = self.process_exam_results(exam1)
        result2 = self.process_exam_results(exam2)
        
        comparison = {
            'exam1': result1,
            'exam2': result2,
            'improvements': {},
            'declines': {},
            'insights': []
        }
        
        # Compare key metrics
        metrics = ['pass_percentage', 'average_marks', 'students_appeared']
        
        for metric in metrics:
            value1 = result1.get(metric, 0)
            value2 = result2.get(metric, 0)
            
            if value2 > value1:
                comparison['improvements'][metric] = {
                    'from': value1,
                    'to': value2,
                    'change': value2 - value1,
                    'percentage_change': ((value2 - value1) / value1 * 100) if value1 > 0 else 0
                }
            elif value2 < value1:
                comparison['declines'][metric] = {
                    'from': value1,
                    'to': value2,
                    'change': value1 - value2,
                    'percentage_change': ((value1 - value2) / value1 * 100) if value1 > 0 else 0
                }
        
        # Generate insights
        if comparison['improvements']:
            comparison['insights'].append('Performance has improved in some areas')
        if comparison['declines']:
            comparison['insights'].append('Performance has declined in some areas')
        
        return comparison
    
    def generate_class_performance_report(self, class_obj, semester: Semester = None) -> Dict:
        """Generate comprehensive performance report for a class"""
        # Get all exams for the class
        exams_query = Exam.objects.filter(
            class_subject__class_obj=class_obj,
            class_subject__academic_year=self.academic_year
        )
        
        if semester:
            exams_query = exams_query.filter(class_subject__semester=semester)
        
        exams = exams_query.order_by('exam_date')
        
        report = {
            'class': class_obj,
            'semester': semester,
            'total_exams': exams.count(),
            'exam_results': [],
            'overall_performance': {},
            'subject_wise_performance': {},
            'student_performance': {},
            'trends': {}
        }
        
        # Process each exam
        for exam in exams:
            exam_result = self.process_exam_results(exam)
            report['exam_results'].append(exam_result)
        
        # Calculate overall performance
        if report['exam_results']:
            total_pass_rate = sum(r['pass_percentage'] for r in report['exam_results'])
            report['overall_performance']['average_pass_rate'] = total_pass_rate / len(report['exam_results'])
            
            total_avg_marks = sum(r['average_marks'] for r in report['exam_results'])
            report['overall_performance']['overall_average'] = total_avg_marks / len(report['exam_results'])
        
        # Subject-wise performance
        subjects = set(exam.class_subject.subject for exam in exams)
        for subject in subjects:
            subject_exams = [r for r in report['exam_results'] if r['exam'].class_subject.subject == subject]
            if subject_exams:
                avg_pass_rate = sum(r['pass_percentage'] for r in subject_exams) / len(subject_exams)
                avg_marks = sum(r['average_marks'] for r in subject_exams) / len(subject_exams)
                
                report['subject_wise_performance'][subject.name] = {
                    'average_pass_rate': avg_pass_rate,
                    'average_marks': avg_marks,
                    'exam_count': len(subject_exams)
                }
        
        return report


class ExamSeatingManager:
    """
    Manages exam seating arrangements
    """
    
    def __init__(self, school, academic_year: AcademicYear):
        self.school = school
        self.academic_year = academic_year
    
    def generate_seating_arrangement(self, exam: Exam, room: Room = None) -> Dict:
        """Generate seating arrangement for an exam"""
        if not room:
            room = exam.room
        
        if not room:
            return {'success': False, 'error': 'No room assigned to exam'}
        
        # Get students for the exam
        students = exam.class_subject.class_obj.students.filter(is_active=True).order_by('?')  # Random order
        
        # Calculate seating capacity
        total_seats = room.capacity
        students_count = students.count()
        
        if students_count > total_seats:
            return {
                'success': False,
                'error': f'Not enough seats. Need {students_count}, available {total_seats}'
            }
        
        # Generate seating arrangement
        seating_arrangement = []
        
        # Simple grid arrangement (can be enhanced based on room layout)
        rows = int((total_seats ** 0.5)) + 1
        cols = (total_seats + rows - 1) // rows
        
        seat_number = 1
        for student in students:
            row = ((seat_number - 1) // cols) + 1
            col = ((seat_number - 1) % cols) + 1
            
            seating = ExamSeating.objects.create(
                school=self.school,
                exam=exam,
                student=student,
                room=room,
                seat_number=f"R{row}C{col}",
                row_number=row,
                column_number=col
            )
            
            seating_arrangement.append(seating)
            seat_number += 1
        
        return {
            'success': True,
            'seating_arrangement': seating_arrangement,
            'total_students': students_count,
            'room_capacity': total_seats,
            'utilization': (students_count / total_seats) * 100
        }
    
    def get_seating_chart(self, exam: Exam) -> Dict:
        """Get seating chart for an exam"""
        seatings = ExamSeating.objects.filter(exam=exam).select_related('student')
        
        if not seatings.exists():
            return {'success': False, 'error': 'No seating arrangement found'}
        
        # Organize by rows and columns
        seating_chart = {}
        for seating in seatings:
            row = seating.row_number
            if row not in seating_chart:
                seating_chart[row] = {}
            
            seating_chart[row][seating.column_number] = {
                'student': seating.student,
                'seat_number': seating.seat_number,
                'student_id': seating.student.student_id,
                'student_name': f"{seating.student.first_name} {seating.student.last_name}"
            }
        
        return {
            'success': True,
            'exam': exam,
            'room': exam.room,
            'seating_chart': seating_chart,
            'total_students': seatings.count()
        }


class ExamAnalyticsEngine:
    """
    Advanced analytics engine for examination system
    """
    
    def __init__(self, school, academic_year: AcademicYear):
        self.school = school
        self.academic_year = academic_year
    
    def generate_comprehensive_analytics(self) -> Dict:
        """Generate comprehensive exam analytics"""
        analytics = {
            'overview': self.get_exam_overview(),
            'performance_trends': self.get_performance_trends(),
            'subject_analysis': self.get_subject_analysis(),
            'teacher_effectiveness': self.get_teacher_effectiveness(),
            'room_utilization': self.get_room_utilization(),
            'scheduling_efficiency': self.get_scheduling_efficiency()
        }
        
        return analytics
    
    def get_exam_overview(self) -> Dict:
        """Get overall exam statistics"""
        exams = Exam.objects.filter(
            school=self.school,
            class_subject__academic_year=self.academic_year
        )
        
        total_exams = exams.count()
        # Calculate completed exams manually since is_completed is a property
        completed_exams = 0
        for exam in exams:
            if exam.is_completed:
                completed_exams += 1
        
        # Get grade statistics
        grades = StudentGrade.objects.filter(
            exam__school=self.school,
            exam__class_subject__academic_year=self.academic_year
        )
        
        overview = {
            'total_exams': total_exams,
            'completed_exams': completed_exams,
            'pending_exams': total_exams - completed_exams,
            'total_students_examined': grades.values('student').distinct().count(),
            'total_grades_recorded': grades.count(),
            'average_performance': grades.aggregate(avg=Avg('percentage'))['avg'] or 0
        }
        
        return overview
    
    def get_performance_trends(self) -> Dict:
        """Analyze performance trends over time"""
        exams = Exam.objects.filter(
            school=self.school,
            class_subject__academic_year=self.academic_year,
            is_completed=True
        ).order_by('exam_date')
        
        trends = {
            'monthly_performance': {},
            'exam_type_performance': {},
            'improvement_trends': []
        }
        
        # Monthly performance
        for exam in exams:
            month_key = exam.exam_date.strftime('%Y-%m')
            if month_key not in trends['monthly_performance']:
                trends['monthly_performance'][month_key] = {
                    'exam_count': 0,
                    'total_performance': 0,
                    'average_performance': 0
                }
            
            exam_avg = exam.student_grades.aggregate(avg=Avg('percentage'))['avg'] or 0
            trends['monthly_performance'][month_key]['exam_count'] += 1
            trends['monthly_performance'][month_key]['total_performance'] += exam_avg
        
        # Calculate averages
        for month_data in trends['monthly_performance'].values():
            if month_data['exam_count'] > 0:
                month_data['average_performance'] = month_data['total_performance'] / month_data['exam_count']
        
        # Exam type performance
        exam_types = exams.values('exam_type').distinct()
        for exam_type_data in exam_types:
            exam_type = exam_type_data['exam_type']
            type_exams = exams.filter(exam_type=exam_type)
            
            avg_performance = StudentGrade.objects.filter(
                exam__in=type_exams
            ).aggregate(avg=Avg('percentage'))['avg'] or 0
            
            trends['exam_type_performance'][exam_type] = {
                'exam_count': type_exams.count(),
                'average_performance': avg_performance
            }
        
        return trends
    
    def get_subject_analysis(self) -> Dict:
        """Analyze performance by subject"""
        subjects = Subject.objects.filter(
            school=self.school,
            class_subjects__academic_year=self.academic_year
        ).distinct()
        
        analysis = {}
        
        for subject in subjects:
            subject_exams = Exam.objects.filter(
                class_subject__subject=subject,
                class_subject__academic_year=self.academic_year
            )
            
            subject_grades = StudentGrade.objects.filter(exam__in=subject_exams)
            
            if subject_grades.exists():
                analysis[subject.name] = {
                    'total_exams': subject_exams.count(),
                    'total_students': subject_grades.values('student').distinct().count(),
                    'average_performance': subject_grades.aggregate(avg=Avg('percentage'))['avg'],
                    'pass_rate': (subject_grades.filter(
                        marks_obtained__gte=models.F('exam__passing_marks')
                    ).count() / subject_grades.count()) * 100,
                    'grade_distribution': dict(
                        subject_grades.values('grade_letter').annotate(
                            count=Count('id')
                        ).values_list('grade_letter', 'count')
                    )
                }
        
        return analysis
    
    def get_teacher_effectiveness(self) -> Dict:
        """Analyze teacher effectiveness based on exam results"""
        teachers = Teacher.objects.filter(
            school=self.school,
            class_subjects__academic_year=self.academic_year
        ).distinct()
        
        effectiveness = {}
        
        for teacher in teachers:
            teacher_exams = Exam.objects.filter(
                class_subject__teacher=teacher,
                class_subject__academic_year=self.academic_year
            )
            
            teacher_grades = StudentGrade.objects.filter(exam__in=teacher_exams)
            
            if teacher_grades.exists():
                effectiveness[f"{teacher.user.first_name} {teacher.user.last_name}"] = {
                    'total_exams': teacher_exams.count(),
                    'students_taught': teacher_grades.values('student').distinct().count(),
                    'average_performance': teacher_grades.aggregate(avg=Avg('percentage'))['avg'],
                    'pass_rate': (teacher_grades.filter(
                        marks_obtained__gte=models.F('exam__passing_marks')
                    ).count() / teacher_grades.count()) * 100,
                    'subjects_taught': list(
                        teacher.subjects.filter(
                            class_subjects__academic_year=self.academic_year
                        ).values_list('name', flat=True).distinct()
                    )
                }
        
        return effectiveness
    
    def get_room_utilization(self) -> Dict:
        """Analyze exam room utilization"""
        rooms = Room.objects.filter(school=self.school, is_available=True)
        
        utilization = {}
        
        for room in rooms:
            room_exams = Exam.objects.filter(
                room=room,
                class_subject__academic_year=self.academic_year
            )
            
            if room_exams.exists():
                total_hours = sum(
                    exam.duration_minutes for exam in room_exams
                ) / 60
                
                utilization[room.name] = {
                    'total_exams': room_exams.count(),
                    'total_hours_used': total_hours,
                    'capacity': room.capacity,
                    'average_occupancy': room_exams.aggregate(
                        avg_students=Avg('class_subject__class_obj__students__count')
                    )['avg_students'] or 0,
                    'utilization_rate': (total_hours / (8 * 5)) * 100  # Assuming 8 hours/day, 5 days/week
                }
        
        return utilization
    
    def get_scheduling_efficiency(self) -> Dict:
        """Analyze exam scheduling efficiency"""
        exams = Exam.objects.filter(
            school=self.school,
            class_subject__academic_year=self.academic_year
        )
        
        # Count conflicts
        total_conflicts = 0
        for exam in exams:
            scheduler = ExamScheduler(self.school, self.academic_year)
            conflicts = scheduler.detect_exam_conflicts(exam)
            total_conflicts += len(conflicts)
        
        efficiency = {
            'total_exams': exams.count(),
            'total_conflicts': total_conflicts,
            'conflict_rate': (total_conflicts / exams.count()) * 100 if exams.count() > 0 else 0,
            'scheduling_efficiency': max(0, 100 - ((total_conflicts / exams.count()) * 100)) if exams.count() > 0 else 100,
            'room_distribution': dict(
                exams.values('room__name').annotate(
                    count=Count('id')
                ).values_list('room__name', 'count')
            ),
            'time_distribution': dict(
                exams.extra(
                    select={'hour': 'EXTRACT(hour FROM start_time)'}
                ).values('hour').annotate(
                    count=Count('id')
                ).values_list('hour', 'count')
            )
        }
        
        return efficiency