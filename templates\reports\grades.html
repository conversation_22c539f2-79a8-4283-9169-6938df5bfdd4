{% extends 'base.html' %}
{% load static %}
{% load dashboard_tags %}

{% block title %}تقرير الدرجات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">تقرير الدرجات</h1>
            <p class="text-muted">تفاصيل درجات الطلاب في جميع المواد</p>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-success">{{ average_grade|floatformat:1|default:0 }}%</h4>
                    <p class="mb-0">متوسط الدرجات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">{{ excellent_students|default:0 }}</h4>
                    <p class="mb-0">طلاب ممتازون</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-info">{{ good_students|default:0 }}</h4>
                    <p class="mb-0">طلاب جيد جداً</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning">{{ pass_rate|floatformat:1|default:0 }}%</h4>
                    <p class="mb-0">معدل النجاح</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">درجات الطلاب</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الطالب</th>
                                    <th>الفصل</th>
                                    {% for subject in subjects %}
                                        <th>{{ subject.name }}</th>
                                    {% endfor %}
                                    <th>المعدل</th>
                                    <th>التقدير</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if student_grades %}
                                    {% for student_data in student_grades %}
                                        <tr>
                                            <td>{{ student_data.student.first_name }} {{ student_data.student.last_name }}</td>
                                            <td>{{ student_data.student.current_class.grade.name }} {{ student_data.student.current_class.name }}</td>

                                            {% for subject in subjects %}
                                                <td>
                                                    {% with grade=student_data.grades|get_item:subject.name %}
                                                        {% if grade %}
                                                            <span class="text-{% if grade >= 90 %}success{% elif grade >= 80 %}primary{% elif grade >= 70 %}info{% elif grade >= 60 %}warning{% else %}danger{% endif %}">
                                                                {{ grade }}
                                                            </span>
                                                        {% else %}
                                                            <span class="text-muted">-</span>
                                                        {% endif %}
                                                    {% endwith %}
                                                </td>
                                            {% endfor %}

                                            <td>
                                                <span class="text-{{ student_data.grade_color }}">
                                                    {{ student_data.average|floatformat:2 }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ student_data.grade_color }}">
                                                    {{ student_data.grade_level }}
                                                </span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                لا توجد درجات مسجلة حالياً
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
