# Generated by Django 5.2.4 on 2025-07-13 11:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('academics', '0001_initial'),
        ('students', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='classsubject',
            name='class_obj',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='class_subjects', to='students.class', verbose_name='Class'),
        ),
        migrations.AddField(
            model_name='schedule',
            name='class_subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='academics.classsubject', verbose_name='Class Subject'),
        ),
        migrations.AddField(
            model_name='subject',
            name='grades',
            field=models.ManyToManyField(related_name='subjects', to='students.grade', verbose_name='Grades'),
        ),
        migrations.AddField(
            model_name='classsubject',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='class_subjects', to='academics.subject', verbose_name='Subject'),
        ),
        migrations.AddField(
            model_name='teacher',
            name='subjects',
            field=models.ManyToManyField(related_name='teachers', to='academics.subject', verbose_name='Subjects'),
        ),
        migrations.AddField(
            model_name='teacher',
            name='user',
            field=models.OneToOneField(limit_choices_to={'user_type': 'teacher'}, on_delete=django.db.models.deletion.CASCADE, related_name='teacher_profile', to=settings.AUTH_USER_MODEL, verbose_name='User Account'),
        ),
        migrations.AddField(
            model_name='classsubject',
            name='teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='class_subjects', to='academics.teacher', verbose_name='Teacher'),
        ),
        migrations.AlterUniqueTogether(
            name='classsubject',
            unique_together={('class_obj', 'subject', 'academic_year')},
        ),
    ]
