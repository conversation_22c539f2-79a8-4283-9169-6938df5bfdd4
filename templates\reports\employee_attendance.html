{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load dashboard_tags %}

{% block title %}{% trans "Employee Attendance Report" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .report-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .report-card:hover {
        transform: translateY(-2px);
    }
    .report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .employee-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
    .attendance-rate-excellent { color: #28a745; }
    .attendance-rate-good { color: #17a2b8; }
    .attendance-rate-average { color: #ffc107; }
    .attendance-rate-poor { color: #dc3545; }
    .status-present { background-color: #d4edda; color: #155724; }
    .status-absent { background-color: #f8d7da; color: #721c24; }
    .status-late { background-color: #fff3cd; color: #856404; }
    .status-leave { background-color: #d1ecf1; color: #0c5460; }
    .chart-container {
        position: relative;
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Employee Attendance Report" %}
                    </h1>
                    <p class="text-muted">{% trans "Comprehensive attendance analytics and insights" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>{% trans "Export Report" %}
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printReport()">
                        <i class="fas fa-print me-2"></i>{% trans "Print" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="department_filter" class="form-label">{% trans "Department" %}</label>
                            <select name="department" id="department_filter" class="form-select">
                                <option value="">{% trans "All Departments" %}</option>
                                {% for dept in departments %}
                                    <option value="{{ dept.id }}" {% if selected_department and dept.id == selected_department.id %}selected{% endif %}>
                                        {{ dept.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">{% trans "Start Date" %}</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">{% trans "End Date" %}</label>
                            <input type="date" name="end_date" id="end_date" class="form-control" value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>{% trans "Generate Report" %}
                                </button>
                                <a href="{% url 'reports:employee_attendance' %}" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-refresh me-2"></i>{% trans "Reset" %}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card report-card">
                <div class="card-body text-center">
                    <i class="fas fa-users text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">{{ total_employees|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Employees" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card">
                <div class="card-body text-center">
                    <i class="fas fa-percentage text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">{{ overall_attendance_rate|floatformat:1|default:"0.0" }}%</h4>
                    <p class="mb-0">{% trans "Overall Attendance" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock text-warning mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-warning">{{ total_late_arrivals|default:0 }}</h4>
                    <p class="mb-0">{% trans "Late Arrivals" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-times text-danger mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-danger">{{ total_absences|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Absences" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Attendance Trends" %}</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="attendanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Department Comparison" %}</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="departmentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Attendance Details -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Employee Attendance Details" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="attendanceTable">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Employee" %}</th>
                                    <th>{% trans "Department" %}</th>
                                    <th>{% trans "Position" %}</th>
                                    <th>{% trans "Working Days" %}</th>
                                    <th>{% trans "Present Days" %}</th>
                                    <th>{% trans "Absent Days" %}</th>
                                    <th>{% trans "Late Arrivals" %}</th>
                                    <th>{% trans "Attendance Rate" %}</th>
                                    <th>{% trans "Status" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if employee_attendance %}
                                    {% for emp in employee_attendance %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {% if emp.employee.user.profile_picture %}
                                                        <img src="{{ emp.employee.user.profile_picture.url }}" alt="{{ emp.employee.user.get_full_name }}" class="employee-avatar me-2">
                                                    {% else %}
                                                        <div class="employee-avatar bg-secondary d-flex align-items-center justify-content-center me-2">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <div class="fw-bold">{{ emp.employee.user.first_name }} {{ emp.employee.user.last_name }}</div>
                                                        <small class="text-muted">{{ emp.employee.employee_id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ emp.employee.department.name|default:"N/A" }}</td>
                                            <td>{{ emp.employee.position|default:"N/A" }}</td>
                                            <td>{{ emp.working_days|default:0 }}</td>
                                            <td>
                                                <span class="badge bg-success">{{ emp.present_days|default:0 }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-danger">{{ emp.absent_days|default:0 }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning">{{ emp.late_arrivals|default:0 }}</span>
                                            </td>
                                            <td>
                                                {% with rate=emp.attendance_rate|default:0 %}
                                                    <span class="fw-bold 
                                                        {% if rate >= 95 %}attendance-rate-excellent
                                                        {% elif rate >= 85 %}attendance-rate-good
                                                        {% elif rate >= 75 %}attendance-rate-average
                                                        {% else %}attendance-rate-poor{% endif %}">
                                                        {{ rate|floatformat:1 }}%
                                                    </span>
                                                {% endwith %}
                                            </td>
                                            <td>
                                                {% with rate=emp.attendance_rate|default:0 %}
                                                    {% if rate >= 95 %}
                                                        <span class="badge bg-success">{% trans "Excellent" %}</span>
                                                    {% elif rate >= 85 %}
                                                        <span class="badge bg-primary">{% trans "Good" %}</span>
                                                    {% elif rate >= 75 %}
                                                        <span class="badge bg-warning">{% trans "Average" %}</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">{% trans "Needs Attention" %}</span>
                                                    {% endif %}
                                                {% endwith %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No attendance data found for the selected period." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Attendance Trends Chart
    const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
    const attendanceChart = new Chart(attendanceCtx, {
        type: 'line',
        data: {
            labels: {{ attendance_dates|safe }},
            datasets: [{
                label: '{% trans "Present" %}',
                data: {{ present_data|safe }},
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }, {
                label: '{% trans "Absent" %}',
                data: {{ absent_data|safe }},
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Department Comparison Chart
    const departmentCtx = document.getElementById('departmentChart').getContext('2d');
    const departmentChart = new Chart(departmentCtx, {
        type: 'bar',
        data: {
            labels: {{ department_names|safe }},
            datasets: [{
                label: '{% trans "Attendance Rate %" %}',
                data: {{ department_rates|safe }},
                backgroundColor: [
                    '#007bff', '#28a745', '#ffc107', '#dc3545', 
                    '#17a2b8', '#6f42c1', '#fd7e14', '#20c997'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Export and Print Functions
    function exportReport() {
        // Implementation for exporting report
        alert('{% trans "Export functionality will be implemented" %}');
    }

    function printReport() {
        window.print();
    }

    // Auto-submit form on date change
    document.querySelectorAll('#department_filter, #start_date, #end_date').forEach(function(element) {
        element.addEventListener('change', function() {
            // Auto-submit after a short delay to allow for date range selection
            setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    });
</script>
{% endblock %}
