#!/bin/bash

# School ERP Installation Script
# This script sets up the School ERP system on a new device

set -e

echo "🏫 School ERP Installation Script"
echo "================================="

# Check if Python 3.8+ is installed
python_version=$(python3 --version 2>&1 | grep -Po '(?<=Python )\d+\.\d+' || echo "0.0")
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.8 or higher is required. Found: $python_version"
    exit 1
fi

echo "✅ Python version: $python_version"

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed"
    exit 1
fi

echo "✅ pip3 is available"

# Install pipenv if not available
if ! command -v pipenv &> /dev/null; then
    echo "📦 Installing pipenv..."
    pip3 install pipenv
fi

echo "✅ pipenv is available"

# Create virtual environment and install dependencies
echo "📦 Installing Python dependencies..."
pipenv install

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p static media logs locale templates

# Set up environment variables
if [ ! -f .env ]; then
    echo "⚙️ Creating environment file..."
    cat > .env << EOF
DEBUG=True
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///db.sqlite3
REDIS_URL=redis://localhost:6379/0
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
EOF
    echo "📝 Please edit .env file with your configuration"
fi

# Run migrations
echo "🗄️ Setting up database..."
pipenv run python manage.py makemigrations
pipenv run python manage.py migrate

# Create superuser
echo "👤 Creating superuser..."
echo "Please create an admin user:"
pipenv run python manage.py createsuperuser

# Collect static files
echo "📦 Collecting static files..."
pipenv run python manage.py collectstatic --noinput

# Generate translation files
echo "🌐 Setting up translations..."
pipenv run python manage.py makemessages -l ar
pipenv run python manage.py compilemessages

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "To start the development server:"
echo "  pipenv run python manage.py runserver"
echo ""
echo "To start with Docker:"
echo "  docker-compose up"
echo ""
echo "Access the application at: http://localhost:8000"
echo "Admin panel: http://localhost:8000/admin"
echo ""
echo "📖 For more information, check the README.md file"
