"""
Management command to set up initial school data
"""
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.db import transaction
from datetime import date, timedelta
from core.models import School, AcademicYear, Semester

User = get_user_model()


class Command(BaseCommand):
    help = 'Set up initial school data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--school-name',
            type=str,
            required=True,
            help='Name of the school'
        )
        parser.add_argument(
            '--school-code',
            type=str,
            required=True,
            help='Unique code for the school'
        )
        parser.add_argument(
            '--admin-email',
            type=str,
            required=True,
            help='Email for the admin user'
        )
        parser.add_argument(
            '--admin-password',
            type=str,
            default='admin123',
            help='Password for the admin user (default: admin123)'
        )

    def handle(self, *args, **options):
        school_name = options['school_name']
        school_code = options['school_code']
        admin_email = options['admin_email']
        admin_password = options['admin_password']

        try:
            with transaction.atomic():
                # Create school
                school, created = School.objects.get_or_create(
                    code=school_code,
                    defaults={
                        'name': school_name,
                        'address': 'Default Address',
                        'phone': '+1234567890',
                        'email': admin_email,
                        'principal_name': 'Principal Name',
                        'established_date': date.today(),
                        'currency': 'USD',
                        'timezone': 'UTC'
                    }
                )

                if created:
                    self.stdout.write(
                        self.style.SUCCESS(f'Successfully created school: {school_name}')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'School {school_name} already exists')
                    )

                # Create admin user
                admin_user, user_created = User.objects.get_or_create(
                    email=admin_email,
                    defaults={
                        'username': f'admin_{school_code.lower()}',
                        'first_name': 'Admin',
                        'last_name': 'User',
                        'is_staff': True,
                        'is_superuser': True
                    }
                )

                if user_created:
                    admin_user.set_password(admin_password)
                    admin_user.save()
                    self.stdout.write(
                        self.style.SUCCESS(f'Successfully created admin user: {admin_email}')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Admin user {admin_email} already exists')
                    )

                # Create current academic year
                current_year = date.today().year
                start_date = date(current_year, 9, 1)  # September 1st
                end_date = date(current_year + 1, 6, 30)  # June 30th next year

                academic_year, ay_created = AcademicYear.objects.get_or_create(
                    school=school,
                    name=f'{current_year}-{current_year + 1}',
                    defaults={
                        'start_date': start_date,
                        'end_date': end_date,
                        'is_current': True,
                        'registration_start_date': start_date - timedelta(days=30),
                        'registration_end_date': start_date + timedelta(days=30)
                    }
                )

                if ay_created:
                    self.stdout.write(
                        self.style.SUCCESS(f'Successfully created academic year: {academic_year.name}')
                    )

                # Create semesters
                semester1, s1_created = Semester.objects.get_or_create(
                    school=school,
                    academic_year=academic_year,
                    name='Fall Semester',
                    defaults={
                        'start_date': start_date,
                        'end_date': date(current_year + 1, 1, 31),
                        'is_current': True,
                        'exam_start_date': date(current_year + 1, 1, 15),
                        'exam_end_date': date(current_year + 1, 1, 31)
                    }
                )

                semester2, s2_created = Semester.objects.get_or_create(
                    school=school,
                    academic_year=academic_year,
                    name='Spring Semester',
                    defaults={
                        'start_date': date(current_year + 1, 2, 1),
                        'end_date': end_date,
                        'is_current': False,
                        'exam_start_date': date(current_year + 1, 6, 15),
                        'exam_end_date': end_date
                    }
                )

                if s1_created:
                    self.stdout.write(
                        self.style.SUCCESS('Successfully created Fall semester')
                    )
                if s2_created:
                    self.stdout.write(
                        self.style.SUCCESS('Successfully created Spring semester')
                    )

                self.stdout.write(
                    self.style.SUCCESS(
                        f'\nSchool setup completed successfully!\n'
                        f'School: {school.name} ({school.code})\n'
                        f'Admin: {admin_email}\n'
                        f'Password: {admin_password}\n'
                        f'Academic Year: {academic_year.name}'
                    )
                )

        except Exception as e:
            raise CommandError(f'Error setting up school: {str(e)}')