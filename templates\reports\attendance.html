{% extends 'base.html' %}
{% load static %}

{% block title %}تقارير الحضور{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" rel="stylesheet">
<style>
    .attendance-card {
        transition: transform 0.2s;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .attendance-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .stat-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    .chart-container {
        position: relative;
        height: 300px;
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    .attendance-rate {
        font-size: 1.5rem;
        font-weight: bold;
    }
    .present { color: #28a745; }
    .absent { color: #dc3545; }
    .late { color: #ffc107; }
    .excused { color: #17a2b8; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">تقارير الحضور</h1>
                    <p class="text-muted">تقارير شاملة عن حضور الطلاب والموظفين</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2">
                        <i class="fas fa-download me-2"></i>تصدير التقرير
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">الفترة الزمنية</label>
                <input type="text" class="form-control" id="daterange" placeholder="اختر الفترة">
            </div>
            <div class="col-md-3">
                <label class="form-label">الفصل</label>
                <select class="form-select">
                    <option value="">جميع الفصول</option>
                    <option value="1">الصف الأول</option>
                    <option value="2">الصف الثاني</option>
                    <option value="3">الصف الثالث</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع التقرير</label>
                <select class="form-select">
                    <option value="daily">يومي</option>
                    <option value="weekly">أسبوعي</option>
                    <option value="monthly">شهري</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-check stat-icon present"></i>
                    <h4 class="attendance-rate present">{{ present_count|default:0 }}</h4>
                    <p class="mb-0">حاضر</p>
                    <small class="text-muted">{{ present_percentage|default:0 }}%</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-times stat-icon absent"></i>
                    <h4 class="attendance-rate absent">{{ absent_count|default:0 }}</h4>
                    <p class="mb-0">غائب</p>
                    <small class="text-muted">{{ absent_percentage|default:0 }}%</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-clock stat-icon late"></i>
                    <h4 class="attendance-rate late">{{ late_count|default:0 }}</h4>
                    <p class="mb-0">متأخر</p>
                    <small class="text-muted">{{ late_percentage|default:0 }}%</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-shield stat-icon excused"></i>
                    <h4 class="attendance-rate excused">{{ excused_count|default:0 }}</h4>
                    <p class="mb-0">غياب بعذر</p>
                    <small class="text-muted">{{ excused_percentage|default:0 }}%</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Reports -->
    <div class="row">
        <!-- Attendance Chart -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">رسم بياني للحضور</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="attendanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Reports -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">تقارير سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'reports:daily_attendance' %}" class="btn btn-outline-primary">
                            <i class="fas fa-calendar-day me-2"></i>الحضور اليومي
                        </a>
                        <a href="{% url 'reports:monthly_attendance' %}" class="btn btn-outline-success">
                            <i class="fas fa-calendar-alt me-2"></i>الحضور الشهري
                        </a>
                        <a href="{% url 'reports:class_wise_attendance' %}" class="btn btn-outline-info">
                            <i class="fas fa-school me-2"></i>حضور الفصول
                        </a>
                        <a href="{% url 'reports:student_wise_attendance' %}" class="btn btn-outline-warning">
                            <i class="fas fa-user-graduate me-2"></i>حضور الطلاب
                        </a>
                        <a href="{% url 'reports:absentees' %}" class="btn btn-outline-danger">
                            <i class="fas fa-user-times me-2"></i>قائمة الغياب
                        </a>
                        <a href="{% url 'reports:late_arrivals' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-clock me-2"></i>المتأخرين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Attendance Table -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">تفاصيل الحضور</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-filter me-1"></i>فلترة
                        </button>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-sort me-1"></i>ترتيب
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الطالب</th>
                                    <th>الفصل</th>
                                    <th>التاريخ</th>
                                    <th>وقت الوصول</th>
                                    <th>الحالة</th>
                                    <th>ملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="Student">
                                            <div>
                                                <div class="fw-bold">أحمد محمد علي</div>
                                                <small class="text-muted">ID: 12345</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>الصف الأول أ</td>
                                    <td>2025-01-15</td>
                                    <td>08:00 ص</td>
                                    <td><span class="badge bg-success">حاضر</span></td>
                                    <td>-</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">تفاصيل</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="Student">
                                            <div>
                                                <div class="fw-bold">فاطمة أحمد حسن</div>
                                                <small class="text-muted">ID: 12346</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>الصف الأول أ</td>
                                    <td>2025-01-15</td>
                                    <td>08:15 ص</td>
                                    <td><span class="badge bg-warning">متأخر</span></td>
                                    <td>تأخر 15 دقيقة</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">تفاصيل</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="Student">
                                            <div>
                                                <div class="fw-bold">محمد عبدالله سالم</div>
                                                <small class="text-muted">ID: 12347</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>الصف الأول أ</td>
                                    <td>2025-01-15</td>
                                    <td>-</td>
                                    <td><span class="badge bg-danger">غائب</span></td>
                                    <td>غياب بدون عذر</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">تفاصيل</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">السابق</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">التالي</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script>
    // Initialize date range picker
    $('#daterange').daterangepicker({
        startDate: moment().subtract(29, 'days'),
        endDate: moment(),
        locale: {
            format: 'YYYY-MM-DD',
            separator: ' إلى ',
            applyLabel: 'تطبيق',
            cancelLabel: 'إلغاء',
            fromLabel: 'من',
            toLabel: 'إلى',
            customRangeLabel: 'مخصص',
            weekLabel: 'أ',
            daysOfWeek: ['أح', 'إث', 'ث', 'أر', 'خ', 'ج', 'س'],
            monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            firstDay: 1
        }
    });

    // Initialize attendance chart
    const ctx = document.getElementById('attendanceChart').getContext('2d');
    const attendanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس'],
            datasets: [{
                label: 'حاضر',
                data: [85, 90, 88, 92, 87],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }, {
                label: 'غائب',
                data: [15, 10, 12, 8, 13],
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
</script>
{% endblock %}
