// Service Worker for School ERP System
// Provides offline functionality and caching

const CACHE_NAME = 'school-erp-v1';
const STATIC_CACHE = 'school-erp-static-v1';
const DYNAMIC_CACHE = 'school-erp-dynamic-v1';

// Files to cache for offline use
const STATIC_FILES = [
    '/',
    '/accounts/login/',
    '/accounts/dashboard/',
    '/static/css/style.css',
    '/static/js/main.js',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
    'https://code.jquery.com/jquery-3.6.0.min.js'
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Caching static files...');
                return cache.addAll(STATIC_FILES);
            })
            .catch(error => {
                console.error('Failed to cache static files:', error);
            })
    );
    
    // Skip waiting to activate immediately
    self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    
    // Take control of all pages immediately
    self.clients.claim();
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http requests
    if (!request.url.startsWith('http')) {
        return;
    }
    
    event.respondWith(
        caches.match(request)
            .then(cachedResponse => {
                // Return cached version if available
                if (cachedResponse) {
                    return cachedResponse;
                }
                
                // Otherwise fetch from network
                return fetch(request)
                    .then(networkResponse => {
                        // Don't cache if not a valid response
                        if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== 'basic') {
                            return networkResponse;
                        }
                        
                        // Clone the response
                        const responseToCache = networkResponse.clone();
                        
                        // Cache dynamic content
                        if (shouldCache(request)) {
                            caches.open(DYNAMIC_CACHE)
                                .then(cache => {
                                    cache.put(request, responseToCache);
                                });
                        }
                        
                        return networkResponse;
                    })
                    .catch(error => {
                        console.log('Fetch failed, serving offline page:', error);
                        
                        // Serve offline page for navigation requests
                        if (request.destination === 'document') {
                            return caches.match('/accounts/login/');
                        }
                        
                        // For other requests, return a generic offline response
                        return new Response('Offline', {
                            status: 503,
                            statusText: 'Service Unavailable'
                        });
                    });
            })
    );
});

// Determine if request should be cached
function shouldCache(request) {
    const url = new URL(request.url);
    
    // Cache same-origin requests
    if (url.origin === location.origin) {
        // Don't cache admin pages
        if (url.pathname.startsWith('/admin/')) {
            return false;
        }
        
        // Don't cache API endpoints that change frequently
        if (url.pathname.startsWith('/api/')) {
            return false;
        }
        
        // Cache other pages
        return true;
    }
    
    // Cache external static resources
    if (url.hostname.includes('cdn.jsdelivr.net') || 
        url.hostname.includes('cdnjs.cloudflare.com') ||
        url.hostname.includes('code.jquery.com')) {
        return true;
    }
    
    return false;
}

// Background sync for offline form submissions
self.addEventListener('sync', event => {
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === 'form-sync') {
        event.waitUntil(syncFormData());
    }
});

// Sync form data when back online
async function syncFormData() {
    try {
        // Get queued form data from IndexedDB or localStorage
        const queuedData = await getQueuedData();
        
        for (const item of queuedData) {
            try {
                const response = await fetch(item.url, {
                    method: item.method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': item.csrfToken
                    },
                    body: JSON.stringify(item.data)
                });
                
                if (response.ok) {
                    // Remove from queue
                    await removeFromQueue(item.id);
                    
                    // Notify user of successful sync
                    self.registration.showNotification('Data synced successfully!', {
                        icon: '/static/images/icon-192x192.png',
                        badge: '/static/images/badge-72x72.png'
                    });
                }
            } catch (error) {
                console.error('Failed to sync item:', error);
            }
        }
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

// Get queued data (placeholder - implement with IndexedDB)
async function getQueuedData() {
    // This would typically use IndexedDB
    // For now, return empty array
    return [];
}

// Remove item from queue (placeholder - implement with IndexedDB)
async function removeFromQueue(id) {
    // This would typically remove from IndexedDB
    console.log('Removing from queue:', id);
}

// Push notification event
self.addEventListener('push', event => {
    console.log('Push notification received:', event);
    
    const options = {
        body: event.data ? event.data.text() : 'New notification',
        icon: '/static/images/icon-192x192.png',
        badge: '/static/images/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View',
                icon: '/static/images/checkmark.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/static/images/xmark.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('School ERP', options)
    );
});

// Notification click event
self.addEventListener('notificationclick', event => {
    console.log('Notification clicked:', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        // Open the app
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Message event for communication with main thread
self.addEventListener('message', event => {
    console.log('Service Worker received message:', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CACHE_URLS') {
        event.waitUntil(
            caches.open(DYNAMIC_CACHE)
                .then(cache => cache.addAll(event.data.payload))
        );
    }
});

// Periodic background sync (if supported)
self.addEventListener('periodicsync', event => {
    console.log('Periodic sync triggered:', event.tag);
    
    if (event.tag === 'content-sync') {
        event.waitUntil(syncContent());
    }
});

// Sync content periodically
async function syncContent() {
    try {
        // Sync critical data like attendance, grades, etc.
        const response = await fetch('/api/sync/');
        if (response.ok) {
            const data = await response.json();
            // Store in cache or IndexedDB
            console.log('Content synced:', data);
        }
    } catch (error) {
        console.error('Periodic sync failed:', error);
    }
}

// Error handling
self.addEventListener('error', event => {
    console.error('Service Worker error:', event.error);
});

self.addEventListener('unhandledrejection', event => {
    console.error('Service Worker unhandled rejection:', event.reason);
});

console.log('Service Worker loaded successfully');
