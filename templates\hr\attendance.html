{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Attendance Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .attendance-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .attendance-card:hover {
        transform: translateY(-2px);
    }
    .attendance-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .status-present {
        background-color: #d4edda;
        color: #155724;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .status-absent {
        background-color: #f8d7da;
        color: #721c24;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .status-late {
        background-color: #fff3cd;
        color: #856404;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .status-leave {
        background-color: #d1ecf1;
        color: #0c5460;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .quick-action-btn {
        border-radius: 25px;
        padding: 0.5rem 1.5rem;
        font-weight: 500;
        border: 2px solid;
        transition: all 0.3s ease;
    }
    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    .attendance-calendar {
        background: white;
        border-radius: 15px;
        padding: 1rem;
    }
    .calendar-day {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 2px;
        cursor: pointer;
        transition: all 0.2s;
    }
    .calendar-day:hover {
        background-color: #e9ecef;
    }
    .calendar-day.present {
        background-color: #28a745;
        color: white;
    }
    .calendar-day.absent {
        background-color: #dc3545;
        color: white;
    }
    .calendar-day.late {
        background-color: #ffc107;
        color: #212529;
    }
    .calendar-day.leave {
        background-color: #17a2b8;
        color: white;
    }
    .calendar-day.today {
        border: 2px solid #007bff;
        font-weight: bold;
    }
    .time-tracker {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
    }
    .time-display {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 1rem 0;
    }
    .employee-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-calendar-check text-primary me-2"></i>{% trans "Attendance Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Track and manage employee attendance records" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#markAttendanceModal">
                        <i class="fas fa-plus me-2"></i>{% trans "Mark Attendance" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export Report" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                    <h3 class="mb-1">{{ present_today|default:0 }}</h3>
                    <p class="mb-0">{% trans "Present Today" %}</p>
                    <small class="text-muted">{% trans "Out of" %} {{ total_employees|default:0 }} {% trans "employees" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-times fa-2x text-danger mb-2"></i>
                    <h3 class="mb-1">{{ absent_today|default:0 }}</h3>
                    <p class="mb-0">{% trans "Absent Today" %}</p>
                    <small class="text-muted">{{ absence_rate|default:6 }}% {% trans "absence rate" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h3 class="mb-1">{{ late_today|default:2 }}</h3>
                    <p class="mb-0">{% trans "Late Arrivals" %}</p>
                    <small class="text-muted">{% trans "Today" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-percentage fa-2x text-info mb-2"></i>
                    <h3 class="mb-1">{{ attendance_rate|default:92 }}%</h3>
                    <p class="mb-0">{% trans "Attendance Rate" %}</p>
                    <small class="text-muted">{% trans "This month" %}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Time Tracker -->
        <div class="col-lg-4 mb-4">
            <div class="time-tracker">
                <h5 class="mb-3">
                    <i class="fas fa-stopwatch me-2"></i>{% trans "Current Time" %}
                </h5>
                <div class="time-display" id="currentTime">--:--:--</div>
                <p class="mb-3">{{ today|date:"l, F d, Y" }}</p>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-light quick-action-btn" onclick="markAttendance('check_in')">
                        <i class="fas fa-sign-in-alt me-2"></i>{% trans "Check In" %}
                    </button>
                    <button class="btn btn-outline-light quick-action-btn" onclick="markAttendance('check_out')">
                        <i class="fas fa-sign-out-alt me-2"></i>{% trans "Check Out" %}
                    </button>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card attendance-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#bulkAttendanceModal">
                            <i class="fas fa-users me-2"></i>{% trans "Bulk Attendance" %}
                        </button>
                        <button class="btn btn-outline-success">
                            <i class="fas fa-chart-bar me-2"></i>{% trans "Attendance Report" %}
                        </button>
                        <button class="btn btn-outline-info">
                            <i class="fas fa-calendar-alt me-2"></i>{% trans "Monthly Summary" %}
                        </button>
                        <button class="btn btn-outline-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Absent Alerts" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance List -->
        <div class="col-lg-8">
            <div class="card attendance-card">
                <div class="attendance-header card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>{% trans "Today's Attendance" %}
                        </h5>
                        <div>
                            <button class="btn btn-light btn-sm me-2">
                                <i class="fas fa-filter me-1"></i>{% trans "Filter" %}
                            </button>
                            <button class="btn btn-light btn-sm">
                                <i class="fas fa-search me-1"></i>{% trans "Search" %}
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filter Row -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-select" name="department">
                                <option value="">{% trans "All Departments" %}</option>
                                <option value="teaching">{% trans "Teaching" %}</option>
                                <option value="administration">{% trans "Administration" %}</option>
                                <option value="support">{% trans "Support Staff" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="status">
                                <option value="">{% trans "All Status" %}</option>
                                <option value="present">{% trans "Present" %}</option>
                                <option value="absent">{% trans "Absent" %}</option>
                                <option value="late">{% trans "Late" %}</option>
                                <option value="leave">{% trans "On Leave" %}</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="{% trans 'Search employee...' %}">
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Employee" %}</th>
                                    <th>{% trans "Department" %}</th>
                                    <th>{% trans "Check In" %}</th>
                                    <th>{% trans "Check Out" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Working Hours" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if attendance_records %}
                                    {% for record in attendance_records %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {% if record.employee.user.profile_picture %}
                                                        <img src="{{ record.employee.user.profile_picture.url }}" alt="{{ record.employee.user.get_full_name }}" class="employee-avatar me-3">
                                                    {% else %}
                                                        <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                                    {% endif %}
                                                    <div>
                                                        <h6 class="mb-1">{{ record.employee.user.first_name }} {{ record.employee.user.last_name }}</h6>
                                                        <small class="text-muted">{{ record.employee.employee_id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ record.employee.department.name|default:"N/A" }}</td>
                                            <td>{{ record.check_in_time|time:"H:i"|default:"--:--" }}</td>
                                            <td>{{ record.check_out_time|time:"H:i"|default:"--:--" }}</td>
                                            <td>
                                                <span class="status-{{ record.status|lower }}">
                                                    {% if record.status == 'present' %}{% trans "Present" %}
                                                    {% elif record.status == 'absent' %}{% trans "Absent" %}
                                                    {% elif record.status == 'late' %}{% trans "Late" %}
                                                    {% else %}{{ record.status }}{% endif %}
                                                </span>
                                            </td>
                                            <td>{{ record.working_hours|default:"--" }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" title="{% trans 'Edit' %}" onclick="editAttendance({{ record.id }})">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" title="{% trans 'Mark Out' %}" onclick="markOut({{ record.id }})">
                                                        <i class="fas fa-sign-out-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No attendance records found for today." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                            <div>
                                                <h6 class="mb-1">فاطمة أحمد حسن</h6>
                                                <small class="text-muted">EMP002</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{% trans "Teaching" %}</td>
                                    <td>08:45 AM</td>
                                    <td>--:--</td>
                                    <td><span class="status-late">{% trans "Late" %}</span></td>
                                    <td>3h 30m</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'Edit' %}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Mark Out' %}">
                                                <i class="fas fa-sign-out-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                            <div>
                                                <h6 class="mb-1">محمد عبدالله سالم</h6>
                                                <small class="text-muted">EMP003</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{% trans "Administration" %}</td>
                                    <td>--:--</td>
                                    <td>--:--</td>
                                    <td><span class="status-absent">{% trans "Absent" %}</span></td>
                                    <td>0h 0m</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'Mark Present' %}">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" title="{% trans 'Add Note' %}">
                                                <i class="fas fa-sticky-note"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar me-3">
                                            <div>
                                                <h6 class="mb-1">سارة محمد أحمد</h6>
                                                <small class="text-muted">EMP004</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{% trans "Teaching" %}</td>
                                    <td>--:--</td>
                                    <td>--:--</td>
                                    <td><span class="status-leave">{% trans "On Leave" %}</span></td>
                                    <td>0h 0m</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" title="{% trans 'View Leave' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mark Attendance Modal -->
<div class="modal fade" id="markAttendanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Mark Attendance" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="employee" class="form-label">{% trans "Employee" %}</label>
                        <select class="form-select" id="employee" required>
                            <option value="">{% trans "Select Employee" %}</option>
                            {% for employee in employees %}
                                <option value="{{ employee.id }}">{{ employee.user.first_name }} {{ employee.user.last_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">{% trans "Status" %}</label>
                        <select class="form-select" id="status" required>
                            <option value="present">{% trans "Present" %}</option>
                            <option value="absent">{% trans "Absent" %}</option>
                            <option value="late">{% trans "Late" %}</option>
                            <option value="leave">{% trans "On Leave" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="time" class="form-label">{% trans "Time" %}</label>
                        <input type="time" class="form-control" id="time" required>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Mark Attendance" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Attendance Modal -->
<div class="modal fade" id="bulkAttendanceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Bulk Attendance" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="bulkDate" class="form-label">{% trans "Date" %}</label>
                    <input type="date" class="form-control" id="bulkDate" value="{{ today|date:'Y-m-d' }}">
                </div>
                <div class="mb-3">
                    <label for="bulkDepartment" class="form-label">{% trans "Department" %}</label>
                    <select class="form-select" id="bulkDepartment">
                        <option value="">{% trans "All Departments" %}</option>
                        <option value="teaching">{% trans "Teaching" %}</option>
                        <option value="administration">{% trans "Administration" %}</option>
                    </select>
                </div>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{% trans "Employee" %}</th>
                                <th>{% trans "Present" %}</th>
                                <th>{% trans "Absent" %}</th>
                                <th>{% trans "Late" %}</th>
                                <th>{% trans "Leave" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if employees %}
                                {% for employee in employees %}
                                    <tr>
                                        <td>{{ employee.user.first_name }} {{ employee.user.last_name }}</td>
                                        <td><input type="radio" name="emp{{ employee.id }}" value="present" {% if forloop.first %}checked{% endif %}></td>
                                        <td><input type="radio" name="emp{{ employee.id }}" value="absent"></td>
                                        <td><input type="radio" name="emp{{ employee.id }}" value="late"></td>
                                        <td><input type="radio" name="emp{{ employee.id }}" value="leave"></td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="alert alert-info mb-0">
                                            <i class="fas fa-info-circle me-2"></i>
                                            {% trans "No employees found for bulk attendance." %}
                                        </div>
                                    </td>
                                </tr>
                            {% endif %}
                                <td><input type="radio" name="emp2" value="absent"></td>
                                <td><input type="radio" name="emp2" value="late" checked></td>
                                <td><input type="radio" name="emp2" value="leave"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Save Attendance" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Update current time
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    document.getElementById('currentTime').textContent = timeString;
}

// Update time every second
setInterval(updateTime, 1000);
updateTime();

// Mark attendance function
function markAttendance(type) {
    const action = type === 'check_in' ? '{% trans "Check In" %}' : '{% trans "Check Out" %}';
    if (confirm(`{% trans "Are you sure you want to" %} ${action}?`)) {
        // Here you would make an AJAX call to mark attendance
        alert(`${action} {% trans "recorded successfully!" %}`);
    }
}

// Auto-submit filters
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('select[name="department"], select[name="status"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            // Here you would filter the attendance list
            console.log('Filter changed:', this.name, this.value);
        });
    });
});
</script>
{% endblock %}
