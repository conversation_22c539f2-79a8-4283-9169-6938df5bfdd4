# Generated by Django 5.2.4 on 2025-07-31 14:12

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        (
            "finance",
            "0004_account_allow_manual_entries_account_archive_reason_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "transaction_id",
                    models.CharField(
                        help_text="Unique transaction identifier",
                        max_length=50,
                        unique=True,
                        verbose_name="Transaction ID",
                    ),
                ),
                ("transaction_date", models.DateField(verbose_name="Transaction Date")),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("manual", "Manual Transaction"),
                            ("automatic", "Automatic Transaction"),
                            ("adjustment", "Adjustment Transaction"),
                            ("closing", "Closing Transaction"),
                            ("payment", "Payment Transaction"),
                            ("receipt", "Receipt Transaction"),
                            ("journal", "Journal Transaction"),
                        ],
                        default="manual",
                        max_length=20,
                        verbose_name="Transaction Type",
                    ),
                ),
                ("description", models.TextField(verbose_name="Description")),
                (
                    "reference",
                    models.CharField(
                        blank=True,
                        help_text="External reference number or document",
                        max_length=100,
                        null=True,
                        verbose_name="Reference",
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total transaction amount (should equal sum of debits/credits)",
                        max_digits=15,
                        verbose_name="Total Amount",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("pending_approval", "Pending Approval"),
                            ("approved", "Approved"),
                            ("posted", "Posted"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "requires_approval",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this transaction requires approval before posting",
                        verbose_name="Requires Approval",
                    ),
                ),
                (
                    "approved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Approved At"
                    ),
                ),
                (
                    "approval_notes",
                    models.TextField(
                        blank=True, null=True, verbose_name="Approval Notes"
                    ),
                ),
                (
                    "posted_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Posted At"
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_transactions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Approved By",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_transactions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="modified_transactions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Modified By",
                    ),
                ),
                (
                    "posted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="posted_transactions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Posted By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transaction",
                "verbose_name_plural": "Transactions",
                "ordering": ["-transaction_date", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TransactionAuditLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("created", "Created"),
                            ("modified", "Modified"),
                            ("approved", "Approved"),
                            ("posted", "Posted"),
                            ("cancelled", "Cancelled"),
                            ("deleted", "Deleted"),
                        ],
                        max_length=20,
                        verbose_name="Action",
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="Timestamp"),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="Notes"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP Address"
                    ),
                ),
                (
                    "changes",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="JSON representation of changes made",
                        verbose_name="Changes",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "transaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="audit_logs",
                        to="finance.transaction",
                        verbose_name="Transaction",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transaction_audit_logs",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transaction Audit Log",
                "verbose_name_plural": "Transaction Audit Logs",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="TransactionEntry",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "entry_date",
                    models.DateField(
                        help_text="Should match transaction date",
                        verbose_name="Entry Date",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        help_text="Specific description for this entry",
                        verbose_name="Description",
                    ),
                ),
                (
                    "debit_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Debit Amount",
                    ),
                ),
                (
                    "credit_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Credit Amount",
                    ),
                ),
                (
                    "reference",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Entry Reference",
                    ),
                ),
                (
                    "is_posted",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this entry has been posted to the account",
                        verbose_name="Is Posted",
                    ),
                ),
                (
                    "posted_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Posted At"
                    ),
                ),
                (
                    "account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transaction_entries",
                        to="finance.account",
                        verbose_name="Account",
                    ),
                ),
                (
                    "cost_center",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="transaction_entries",
                        to="finance.costcenter",
                        verbose_name="Cost Center",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "transaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="entries",
                        to="finance.transaction",
                        verbose_name="Transaction",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transaction Entry",
                "verbose_name_plural": "Transaction Entries",
                "ordering": ["transaction", "id"],
            },
        ),
        migrations.AddIndex(
            model_name="transaction",
            index=models.Index(
                fields=["school", "transaction_date"],
                name="finance_tra_school__1c9517_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="transaction",
            index=models.Index(
                fields=["school", "status"], name="finance_tra_school__831fb2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="transaction",
            index=models.Index(
                fields=["school", "transaction_type"],
                name="finance_tra_school__6cf992_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="transaction",
            index=models.Index(
                fields=["transaction_id"], name="finance_tra_transac_840e6f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="transactionentry",
            index=models.Index(
                fields=["school", "account", "entry_date"],
                name="finance_tra_school__bfae01_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="transactionentry",
            index=models.Index(
                fields=["school", "is_posted"], name="finance_tra_school__c563a2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="transactionentry",
            index=models.Index(
                fields=["transaction", "account"], name="finance_tra_transac_76eb7b_idx"
            ),
        ),
    ]
