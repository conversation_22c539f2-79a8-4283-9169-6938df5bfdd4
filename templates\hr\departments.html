{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Department Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .dept-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
        overflow: hidden;
    }
    .dept-card:hover {
        transform: translateY(-2px);
    }
    .dept-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        text-align: center;
    }
    .dept-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
    }
    .dept-stats {
        padding: 1rem;
    }
    .stat-item {
        text-align: center;
        padding: 0.5rem;
    }
    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #495057;
    }
    .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
    }
    .dept-actions {
        padding: 1rem;
        border-top: 1px solid #e9ecef;
        background: #f8f9fa;
    }
    .head-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
    .org-chart {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .org-node {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        margin: 0.5rem;
        min-width: 150px;
    }
    .org-line {
        border-left: 2px solid #dee2e6;
        height: 30px;
        margin: 0 auto;
        width: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-building text-primary me-2"></i>{% trans "Department Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage organizational departments and structure" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#newDepartmentModal">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Department" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-sitemap me-2"></i>{% trans "Org Chart" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Cards -->
    <div class="row">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card dept-card">
                <div class="dept-header">
                    <div class="dept-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <h5 class="mb-1">{% trans "Teaching Department" %}</h5>
                    <small>TEACH-001</small>
                </div>
                <div class="dept-stats">
                    <div class="row">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number">45</div>
                                <div class="stat-label">{% trans "Employees" %}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number">8</div>
                                <div class="stat-label">{% trans "Positions" %}</div>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="d-flex align-items-center">
                        <img src="{% static 'images/default-avatar.png' %}" alt="Head" class="head-avatar me-2">
                        <div>
                            <small class="text-muted">{% trans "Department Head" %}</small><br>
                            <strong>أحمد محمد علي</strong>
                        </div>
                    </div>
                </div>
                <div class="dept-actions">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> {% trans "View" %}
                        </button>
                        <button class="btn btn-outline-success btn-sm">
                            <i class="fas fa-edit"></i> {% trans "Edit" %}
                        </button>
                        <button class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash"></i> {% trans "Delete" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card dept-card">
                <div class="dept-header">
                    <div class="dept-icon">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <h5 class="mb-1">{% trans "Administration" %}</h5>
                    <small>ADMIN-001</small>
                </div>
                <div class="dept-stats">
                    <div class="row">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number">12</div>
                                <div class="stat-label">{% trans "Employees" %}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number">5</div>
                                <div class="stat-label">{% trans "Positions" %}</div>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="d-flex align-items-center">
                        <img src="{% static 'images/default-avatar.png' %}" alt="Head" class="head-avatar me-2">
                        <div>
                            <small class="text-muted">{% trans "Department Head" %}</small><br>
                            <strong>فاطمة أحمد حسن</strong>
                        </div>
                    </div>
                </div>
                <div class="dept-actions">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> {% trans "View" %}
                        </button>
                        <button class="btn btn-outline-success btn-sm">
                            <i class="fas fa-edit"></i> {% trans "Edit" %}
                        </button>
                        <button class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash"></i> {% trans "Delete" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card dept-card">
                <div class="dept-header">
                    <div class="dept-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h5 class="mb-1">{% trans "Support Staff" %}</h5>
                    <small>SUPP-001</small>
                </div>
                <div class="dept-stats">
                    <div class="row">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number">18</div>
                                <div class="stat-label">{% trans "Employees" %}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number">6</div>
                                <div class="stat-label">{% trans "Positions" %}</div>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="d-flex align-items-center">
                        <img src="{% static 'images/default-avatar.png' %}" alt="Head" class="head-avatar me-2">
                        <div>
                            <small class="text-muted">{% trans "Department Head" %}</small><br>
                            <strong>محمد عبدالله سالم</strong>
                        </div>
                    </div>
                </div>
                <div class="dept-actions">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> {% trans "View" %}
                        </button>
                        <button class="btn btn-outline-success btn-sm">
                            <i class="fas fa-edit"></i> {% trans "Edit" %}
                        </button>
                        <button class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash"></i> {% trans "Delete" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card dept-card">
                <div class="dept-header">
                    <div class="dept-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h5 class="mb-1">{% trans "Management" %}</h5>
                    <small>MGMT-001</small>
                </div>
                <div class="dept-stats">
                    <div class="row">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number">8</div>
                                <div class="stat-label">{% trans "Employees" %}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number">4</div>
                                <div class="stat-label">{% trans "Positions" %}</div>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="d-flex align-items-center">
                        <img src="{% static 'images/default-avatar.png' %}" alt="Head" class="head-avatar me-2">
                        <div>
                            <small class="text-muted">{% trans "Department Head" %}</small><br>
                            <strong>سارة محمد أحمد</strong>
                        </div>
                    </div>
                </div>
                <div class="dept-actions">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> {% trans "View" %}
                        </button>
                        <button class="btn btn-outline-success btn-sm">
                            <i class="fas fa-edit"></i> {% trans "Edit" %}
                        </button>
                        <button class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash"></i> {% trans "Delete" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Organizational Chart -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="org-chart">
                <h5 class="text-center mb-4">
                    <i class="fas fa-sitemap me-2"></i>{% trans "Organizational Structure" %}
                </h5>
                
                <div class="text-center">
                    <!-- Principal -->
                    <div class="org-node d-inline-block">
                        <i class="fas fa-user-tie fa-2x mb-2"></i>
                        <h6>{% trans "Principal" %}</h6>
                        <small>{% trans "School Director" %}</small>
                    </div>
                    
                    <div class="org-line"></div>
                    
                    <!-- Department Heads -->
                    <div class="d-flex justify-content-center flex-wrap">
                        <div class="org-node">
                            <i class="fas fa-chalkboard-teacher mb-2"></i>
                            <h6>{% trans "Teaching" %}</h6>
                            <small>45 {% trans "employees" %}</small>
                        </div>
                        <div class="org-node">
                            <i class="fas fa-users-cog mb-2"></i>
                            <h6>{% trans "Administration" %}</h6>
                            <small>12 {% trans "employees" %}</small>
                        </div>
                        <div class="org-node">
                            <i class="fas fa-tools mb-2"></i>
                            <h6>{% trans "Support" %}</h6>
                            <small>18 {% trans "employees" %}</small>
                        </div>
                        <div class="org-node">
                            <i class="fas fa-chart-line mb-2"></i>
                            <h6>{% trans "Management" %}</h6>
                            <small>8 {% trans "employees" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Department Modal -->
<div class="modal fade" id="newDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add New Department" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="deptName" class="form-label">{% trans "Department Name" %}</label>
                        <input type="text" class="form-control" id="deptName" required>
                    </div>
                    <div class="mb-3">
                        <label for="deptNameAr" class="form-label">{% trans "Department Name (Arabic)" %}</label>
                        <input type="text" class="form-control" id="deptNameAr">
                    </div>
                    <div class="mb-3">
                        <label for="deptCode" class="form-label">{% trans "Department Code" %}</label>
                        <input type="text" class="form-control" id="deptCode" required>
                    </div>
                    <div class="mb-3">
                        <label for="deptHead" class="form-label">{% trans "Department Head" %}</label>
                        <select class="form-select" id="deptHead">
                            <option value="">{% trans "Select Head" %}</option>
                            <option value="1">أحمد محمد علي</option>
                            <option value="2">فاطمة أحمد حسن</option>
                            <option value="3">محمد عبدالله سالم</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="deptDescription" class="form-label">{% trans "Description" %}</label>
                        <textarea class="form-control" id="deptDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Create Department" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate department code
    const deptName = document.getElementById('deptName');
    const deptCode = document.getElementById('deptCode');
    
    if (deptName && deptCode) {
        deptName.addEventListener('input', function() {
            const name = this.value.toUpperCase().replace(/\s+/g, '');
            if (name.length >= 3) {
                deptCode.value = name.substring(0, 4) + '-001';
            }
        });
    }
});
</script>
{% endblock %}
