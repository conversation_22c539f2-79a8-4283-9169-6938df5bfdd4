﻿
angular.module("SchoolApp_Services").factory("NotificationServices", () => {

    ///Save the status in local storage
    
    let isSoundOn = localStorage.getItem('isSoundOn') === 'true';
    let isslider = localStorage.getItem('isslider') === 'true';
    let iscolored = localStorage.getItem('iscolored') === 'true';

    $('#soundToggle').on('click', function () {
        isSoundOn = !isSoundOn;
        isslider = !isslider;
        iscolored = !iscolored;
        updateSoundLabel();
        updateSliderPosition();
        localStorage.setItem('isSoundOn', isSoundOn);
        localStorage.setItem('isslider', isslider);
        localStorage.setItem('iscolored', iscolored);
    });
    function updateSoundLabel() {
        if (isSoundOn) {
            $('#soundToggle .SoundTurnOff').text('Sound On');
            $('#soundToggle .SoundTurnOff').css('color', 'green');
        } else {
            $('#soundToggle .SoundTurnOff').text('Sound Off');
            $('#soundToggle .SoundTurnOff').css('color', 'red');

        }
    }
    function updateSliderPosition() {
        const slider = $('#slider');
        const newPosition = isSoundOn ? 'calc(100% - 20px)' : '0';
        slider.css('transform', 'translateX(' + newPosition + ')');

    }

    updateSoundLabel();
    updateSliderPosition();


    pushNotification = (type, message) => {
        $.notifyClose();
        $.notify(message, {
            type: type,
            placement: {
                from: "top",
                align: "left"
            },
            offset: 20,
            spacing: 10,
            z_index: 99999999,
            delay: 5000,
            timer: 1000,
            url_target: '_blank',
            mouse_over: null,
            animate: {
                enter: 'animated bounceInDown',
                exit: 'animated zoomOutUp'
            }
        });

        if (isSoundOn) {
            $.playSound("/assets/alert-sound.wav");
        }
    };

    return {
        pushNotification: pushNotification
    };
});



$(function () {
    let isSoundOn = localStorage.getItem('isSoundOn') === 'true';
    let isslider = localStorage.getItem('isslider') === 'true';
    let iscolored = localStorage.getItem('iscolored') === 'true';

    $('#soundToggle').on('click', function () {
        isSoundOn = !isSoundOn;
        isslider = !isslider;
        iscolored = !iscolored;

        updateSoundLabel();
        updateSliderPosition();
        localStorage.setItem('isSoundOn', isSoundOn);
        localStorage.setItem('isslider', isslider);
        localStorage.setItem('iscolored', iscolored);

    });
    function updateSoundLabel() {
        if (isSoundOn) {
            $('#soundToggle .SoundTurnOff').text('Sound On');
            $('#soundToggle .SoundTurnOff').css('color', 'green');
        } else {
            $('#soundToggle .SoundTurnOff').text('Sound Off');
            $('#soundToggle .SoundTurnOff').css('color', 'red');

        }
    }
    function updateSliderPosition() {
        const slider = $('#slider');
        const newPosition = isSoundOn ? 'calc(100% - 20px)' : '0';
        slider.css('transform', 'translateX(' + newPosition + ')');

    }

    updateSoundLabel();
    updateSliderPosition();

    pushNotification = (type, message) => {
        $.notifyClose();
        $.notify(message, {
            type: type,
            placement: {
                from: "top",
                align: "left"
            },
            offset: 20,
            spacing: 10,
            z_index: 99999999,
            delay: 5000,
            timer: 1000,
            url_target: '_blank',
            mouse_over: null,
            animate: {
                enter: 'animated bounceInDown',
                exit: 'animated zoomOutUp'
            }
        });
        if (isSoundOn) {
            $.playSound("/assets/alert-sound.wav");
        }
    };
});

