﻿

const FirbasePublicKey = "BGtlQpLvL9QMiyt6gVNrrIvt4svsuYK0_MJa1ZasmkC_MnkQhE7DvhQCvDyc7ZGFWwbNFaVJXhzmXLXpTt4GIqQ";
const firebaseConfig = {

    apiKey: "AIzaSyCG8HlnCmqeAsO8kIEi6du2iI15pN1wxK8",
    authDomain: "pio-schoolonline.firebaseapp.com",
    projectId: "pio-schoolonline",
    storageBucket: "pio-schoolonline.appspot.com",
    messagingSenderId: "650654650704",
    appId: "1:650654650704:web:901882efd9659545be5c55",
    measurementId: "G-1HCYRSJRPR"
};

// Initialize Firebase
//firebase.initializeApp(config);

const FireBaseApp = firebase.initializeApp(firebaseConfig);
const messaging = FireBaseApp.messaging();
$(document).ready(() => {
    // Import the functions you need from the SDKs you need
    //import {initializeApp} from "https://www.gstatic.com/firebasejs/9.1.2/firebase-app.js";
    //import {getAnalytics} from "https://www.gstatic.com/firebasejs/9.1.2/firebase-analytics.js";
    // TODO: Add SDKs for Firebase products that you want to use
    // https://firebase.google.com/docs/web/setup#available-libraries

    // Your web app's Firebase configuration
    // For Firebase JS SDK v7.20.0 and later, measurementId is optional

    //const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
    //await navigator.serviceWorker.ready;
   
    if ('serviceWorker' in navigator) {

        navigator.serviceWorker.register('/firebase-messaging-sw.js')
            .then(function (registration) {

                console.log("Service Worker Registered");
                messaging.useServiceWorker(registration);
            }).catch((err) => {
            });
    }
    //ClearNotificationToken()
    messaging.requestPermission().then(function () {

        /*  ClearNotificationToken()*/
        if (isTokenSentToServer()) {
            //ClearNotificationToken();

        } else {

            getRegtoken();
        }

    }).catch((err) => {

    });
    messaging.onMessage(function (payload) {
        
        notificationTitle = payload.notification.title;
        notificationOptions = {
            body: payload.notification.body,
            icon: payload.notification.icon
        };
        var notification = new Notification(notificationTitle, notificationOptions);
        notification.onclick((reee) => {

        })
        pushNotification("success", "تسجيل حضور موظف ");
    });
    messaging.onTokenRefresh(function () {

        messaging.getToken()
            .then(function (refreshedToken, ew) {

                alert("sa")
                console.log('Token refreshed.');
                // Indicate that the new Instance ID token has not yet been sent to the
                // app server.
                setTokenSentToServer(false);
                // Send Instance ID token to app server.
                sendTokenToServer(refreshedToken);
                // ...
            })
            .catch(function (err) {

                sendTokenToServer("");
                alert("sas")

                console.log('Unable to retrieve refreshed token ', err);
                showToken('Unable to retrieve refreshed token ', err);
            });
    });
})
function getRegtoken() {

    messaging.getToken().then((currentToken) => {
        if (currentToken) {
            //setTokenSentToServer(true);
            //sendTokenToServer(currentToken);
            subscribeTokenToTopic(currentToken, "AttendenceTopic")
        } else {
            console.log('No Instance ID token available. Request permission to generate one.');
        }
    }).catch((err) => {

        console.log('An error occurred while retrieving token. ', err);
        setTokenSentToServer(false);
    });

}
function subscribeTokenToTopic(token, topic) {
    $.post(`/api/web/Firebase/subscribeTokenToTopic?token=${token}&topic=${topic}`, JSON.stringify({
        token: token,
        topic: topic
    }), (res) => {
        if (res.Status == true) {
            setTokenSentToServer(true);

        }
    })
    //fetch('https://iid.googleapis.com/iid/v1/' + token + '/rel/topics/' + topic, {
    //    method: 'POST',
    //    headers: new Headers({
    //        'Authorization': 'key=' + FirbasePublicKey
    //    })
    //}).then(response => {
    //    debugger
    //    if (response.status < 200 || response.status >= 400) {
    //        throw 'Error subscribing to topic: ' + response.status + ' - ' + response.text();
    //    }
    //    console.log('Subscribed to "' + topic + '"');
    //}).catch(error => {
    //    console.error(error);
    //})
}

function setTokenSentToServer(sent) {
    window.localStorage.setItem('sentToServer', sent ? 1 : 0);
}
function isTokenSentToServer() {
    return window.localStorage.getItem('sentToServer') === '1';
}
function sendTokenToServer(token) {
    setTokenSentToServer(true);

    //$.post("/api/Firebase/SaveToken", { Token: token } , (res) => {
    //    if (res.Status) {
    //        setTokenSentToServer(true);
    //    }
    //    else {
    //        setTokenSentToServer(false);
    //    }
    //})
}


function deleteTokenFromserver(token) {

    //$.post("/api/Firebase/RemoveToken", { Token: token }, (res) => {

    //})
}


$(document).on("ClearNotificationToken", function (event, callBack, arg2) {
    callBack();

    //let token = "";
    ////Frist We Get Curent Token 
    //messaging.getToken().then((currentToken) => {
    //    if (currentToken) {
    //        messaging.deleteToken().then(function (token, ds) {

    //            if (token) {
    //                deleteTokenFromserver(currentToken)

    //                setTokenSentToServer(false);
    //                callBack();
    //            } 
    //        }).catch((err) => { });
    //    } 
    //}).catch((err) => {
    //});
});
function ClearNotificationToken() {
    //navigator.serviceWorker.getRegistrations().then(function (registrations) {

    //    angular.forEach(registrations, function (registration,as) {

    //        registration.unregister();
    //        //setTokenSentToServer(false);

    //    });
    //});

    messaging.deleteToken().then(function (token, ds) {
        setTokenSentToServer(false);

        
        /*  ClearNotificationToken()*/
        if (isTokenSentToServer()) {
        } else {

            getRegtoken();
        }
    }).catch((err) => {
        debugger
    });
}
$(function () {

  

  
    //const analytics = getAnalytics(app);
   

   
    
    //self.addEventListener("notificationclick", function (event) {
    //    debugger
    //    //var urlToRedirect = event.notification.data.url;
    //    event.notification.close();
    //    event.waitUntil(self.clients.openWindow("/bla/bla"));
    //    event.waitUntil(clients.matchAll({
    //        type: "window"
    //    }).then(function (clientList) {
    //        for (var i = 0; i < clientList.length; i++) {
    //            var client = clientList[i];
    //            if (client.url == self.registration.scope && 'focus' in client) {
    //                return client.focus();
    //            }
    //        }
    //        if (clients.openWindow) {
    //            return clients.openWindow('/');
    //        }
    //    }));
    //});

});
