{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Create User" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .user-form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .form-card-body {
        background: white;
        border-radius: 0 0 15px 15px;
        color: #333;
    }
    .form-header {
        padding: 1.5rem;
        border-radius: 15px 15px 0 0;
    }
    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    .btn-secondary {
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
    }
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-plus me-2"></i>{% trans "Create New User" %}
                    </h1>
                    <p class="text-muted">{% trans "Add a new user to the system" %}</p>
                </div>
                <div>
                    <a href="{% url 'core:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Dashboard" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card user-form-card">
                <div class="form-header">
                    <h4 class="mb-0">
                        <i class="fas fa-user-cog me-2"></i>
                        {% trans "User Information" %}
                    </h4>
                </div>
                <div class="card-body form-card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- Username -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label required-field">
                                    {% trans "Username" %}
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger mt-1">
                                        {{ form.username.errors }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    {% trans "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only." %}
                                </small>
                            </div>

                            <!-- Email (if available) -->
                            {% if form.email %}
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    {% trans "Email" %}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger mt-1">
                                        {{ form.email.errors }}
                                    </div>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <!-- Password -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label required-field">
                                    {% trans "Password" %}
                                </label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                    <div class="text-danger mt-1">
                                        {{ form.password1.errors }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    {% trans "Your password must contain at least 8 characters." %}
                                </small>
                            </div>

                            <!-- Password Confirmation -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label required-field">
                                    {% trans "Password Confirmation" %}
                                </label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                    <div class="text-danger mt-1">
                                        {{ form.password2.errors }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    {% trans "Enter the same password as before, for verification." %}
                                </small>
                            </div>
                        </div>

                        <!-- First Name and Last Name (if available) -->
                        {% if form.first_name or form.last_name %}
                        <div class="row">
                            {% if form.first_name %}
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                    {% trans "First Name" %}
                                </label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger mt-1">
                                        {{ form.first_name.errors }}
                                    </div>
                                {% endif %}
                            </div>
                            {% endif %}

                            {% if form.last_name %}
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                    {% trans "Last Name" %}
                                </label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger mt-1">
                                        {{ form.last_name.errors }}
                                    </div>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{% url 'core:dashboard' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% trans "Create User" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = ['{{ form.username.id_for_label }}', '{{ form.password1.id_for_label }}', '{{ form.password2.id_for_label }}'];
        
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field && !field.value.trim()) {
                isValid = false;
                field.classList.add('is-invalid');
            } else if (field) {
                field.classList.remove('is-invalid');
            }
        });
        
        // Check password match
        const password1 = document.getElementById('{{ form.password1.id_for_label }}');
        const password2 = document.getElementById('{{ form.password2.id_for_label }}');
        
        if (password1 && password2 && password1.value !== password2.value) {
            isValid = false;
            password2.classList.add('is-invalid');
            alert('{% trans "Passwords do not match." %}');
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
