from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from core.models import BaseModel


class ReportTemplate(BaseModel):
    """
    Report template model for custom reports
    """
    REPORT_TYPES = (
        ('student', _('Student Report')),
        ('financial', _('Financial Report')),
        ('academic', _('Academic Report')),
        ('attendance', _('Attendance Report')),
        ('hr', _('HR Report')),
        ('custom', _('Custom Report')),
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Report Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Report Name (Arabic)')
    )

    report_type = models.CharField(
        max_length=20,
        choices=REPORT_TYPES,
        verbose_name=_('Report Type')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    query = models.TextField(
        verbose_name=_('SQL Query')
    )

    parameters = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Parameters')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_reports',
        verbose_name=_('Created By')
    )

    is_public = models.BooleanField(
        default=False,
        verbose_name=_('Is Public')
    )

    class Meta:
        verbose_name = _('Report Template')
        verbose_name_plural = _('Report Templates')
        ordering = ['name']

    def __str__(self):
        return self.name


class ReportExecution(BaseModel):
    """
    Report execution history model
    """
    template = models.ForeignKey(
        ReportTemplate,
        on_delete=models.CASCADE,
        related_name='executions',
        verbose_name=_('Report Template')
    )

    executed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='executed_reports',
        verbose_name=_('Executed By')
    )

    parameters_used = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Parameters Used')
    )

    execution_time = models.DurationField(
        verbose_name=_('Execution Time')
    )

    row_count = models.PositiveIntegerField(
        verbose_name=_('Row Count')
    )

    file_path = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        verbose_name=_('Generated File Path')
    )

    class Meta:
        verbose_name = _('Report Execution')
        verbose_name_plural = _('Report Executions')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.template.name} - {self.executed_by} ({self.created_at})"


class Dashboard(BaseModel):
    """
    Dashboard configuration model
    """
    name = models.CharField(
        max_length=200,
        verbose_name=_('Dashboard Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Dashboard Name (Arabic)')
    )

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='dashboards',
        verbose_name=_('User')
    )

    layout = models.JSONField(
        default=dict,
        verbose_name=_('Dashboard Layout')
    )

    widgets = models.JSONField(
        default=list,
        verbose_name=_('Dashboard Widgets')
    )

    is_default = models.BooleanField(
        default=False,
        verbose_name=_('Is Default Dashboard')
    )

    class Meta:
        verbose_name = _('Dashboard')
        verbose_name_plural = _('Dashboards')
        ordering = ['user', 'name']

    def __str__(self):
        return f"{self.user.username} - {self.name}"


class Analytics(BaseModel):
    """
    Analytics data model for storing computed metrics
    """
    METRIC_TYPES = (
        ('enrollment', _('Enrollment')),
        ('attendance', _('Attendance')),
        ('financial', _('Financial')),
        ('academic', _('Academic Performance')),
        ('hr', _('Human Resources')),
    )

    metric_type = models.CharField(
        max_length=20,
        choices=METRIC_TYPES,
        verbose_name=_('Metric Type')
    )

    metric_name = models.CharField(
        max_length=100,
        verbose_name=_('Metric Name')
    )

    value = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Value')
    )

    date = models.DateField(
        verbose_name=_('Date')
    )

    metadata = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Metadata')
    )

    class Meta:
        verbose_name = _('Analytics')
        verbose_name_plural = _('Analytics')
        unique_together = ['metric_type', 'metric_name', 'date']
        ordering = ['-date', 'metric_type']

    def __str__(self):
        return f"{self.metric_name} - {self.date} ({self.value})"
