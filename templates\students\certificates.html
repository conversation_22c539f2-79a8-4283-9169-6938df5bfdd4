{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Student Certificates" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .certificate-preview {
        border: 1px solid #ddd;
        padding: 10px;
        border-radius: 5px;
        background-color: #f9f9f9;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
    }
    .certificate-card {
        transition: transform 0.2s;
    }
    .certificate-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-certificate text-primary me-2"></i>{% trans "Student Certificates" %}
                            </h2>
                            <p class="text-muted">{% trans "Generate and manage student certificates" %}</p>
                        </div>
                        <div>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#generateCertificateModal">
                                <i class="fas fa-plus me-2"></i>{% trans "Generate Certificate" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Certificate Templates -->
            <div class="row mb-4">
                <div class="col-12">
                    <h4>{% trans "Certificate Templates" %}</h4>
                </div>
                
                <!-- Enrollment Certificate -->
                <div class="col-md-4 mb-4">
                    <div class="card certificate-card h-100">
                        <div class="certificate-preview">
                            <img src="{% static 'img/certificate-enrollment.png' %}" alt="Enrollment Certificate" class="img-fluid" style="max-height: 180px;" onerror="this.src='https://via.placeholder.com/300x200?text=Enrollment+Certificate'">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Enrollment Certificate" %}</h5>
                            <p class="card-text">{% trans "Certifies that a student is currently enrolled in the school." %}</p>
                            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#generateCertificateModal" data-certificate-type="enrollment">
                                <i class="fas fa-file-alt me-2"></i>{% trans "Generate" %}
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Completion Certificate -->
                <div class="col-md-4 mb-4">
                    <div class="card certificate-card h-100">
                        <div class="certificate-preview">
                            <img src="{% static 'img/certificate-completion.png' %}" alt="Completion Certificate" class="img-fluid" style="max-height: 180px;" onerror="this.src='https://via.placeholder.com/300x200?text=Completion+Certificate'">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Completion Certificate" %}</h5>
                            <p class="card-text">{% trans "Certifies that a student has completed a specific grade level." %}</p>
                            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#generateCertificateModal" data-certificate-type="completion">
                                <i class="fas fa-file-alt me-2"></i>{% trans "Generate" %}
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Achievement Certificate -->
                <div class="col-md-4 mb-4">
                    <div class="card certificate-card h-100">
                        <div class="certificate-preview">
                            <img src="{% static 'img/certificate-achievement.png' %}" alt="Achievement Certificate" class="img-fluid" style="max-height: 180px;" onerror="this.src='https://via.placeholder.com/300x200?text=Achievement+Certificate'">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Achievement Certificate" %}</h5>
                            <p class="card-text">{% trans "Recognizes a student's special achievement or excellence." %}</p>
                            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#generateCertificateModal" data-certificate-type="achievement">
                                <i class="fas fa-file-alt me-2"></i>{% trans "Generate" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Certificates -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Recent Certificates" %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Certificate ID" %}</th>
                                            <th>{% trans "Student" %}</th>
                                            <th>{% trans "Type" %}</th>
                                            <th>{% trans "Issue Date" %}</th>
                                            <th>{% trans "Created By" %}</th>
                                            <th>{% trans "Actions" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if certificates %}
                                            {% for cert in certificates %}
                                            <tr>
                                                <td>{{ cert.certificate_id }}</td>
                                                <td>{{ cert.student.full_name }}</td>
                                                <td>{{ cert.get_certificate_type_display }}</td>
                                                <td>{{ cert.issue_date }}</td>
                                                <td>{{ cert.created_by.get_full_name }}</td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewCertificate('{{ cert.certificate_id }}')">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadCertificate('{{ cert.certificate_id }}')">
                                                            <i class="fas fa-download"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-info" onclick="printCertificate('{{ cert.certificate_id }}')">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="6" class="text-center">{% trans "No certificates found" %}</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Generate Certificate Modal -->
<div class="modal fade" id="generateCertificateModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Generate Certificate" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="certificateForm" action="{% url 'students:certificates' %}" method="post">
                    {% csrf_token %}
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="certificateType" class="form-label">{% trans "Certificate Type" %}</label>
                            <select class="form-select" id="certificateType" name="certificate_type" required>
                                <option value="">{% trans "Select Certificate Type" %}</option>
                                <option value="enrollment">{% trans "Enrollment Certificate" %}</option>
                                <option value="completion">{% trans "Completion Certificate" %}</option>
                                <option value="achievement">{% trans "Achievement Certificate" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="student" class="form-label">{% trans "Student" %}</label>
                            <select class="form-select" id="student" name="student_id" required>
                                <option value="">{% trans "Select Student" %}</option>
                                {% for student in students %}
                                    <option value="{{ student.id }}">{{ student.full_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="issueDate" class="form-label">{% trans "Issue Date" %}</label>
                            <input type="date" class="form-control" id="issueDate" name="issue_date" value="{% now 'Y-m-d' %}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="language" class="form-label">{% trans "Language" %}</label>
                            <select class="form-select" id="language" name="language">
                                <option value="en">{% trans "English" %}</option>
                                <option value="ar">{% trans "Arabic" %}</option>
                                <option value="both">{% trans "Bilingual" %}</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="additionalNotes" class="form-label">{% trans "Additional Notes" %}</label>
                        <textarea class="form-control" id="additionalNotes" name="additional_notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="previewCertificateBtn">{% trans "Preview" %}</button>
                <button type="submit" form="certificateForm" class="btn btn-success">{% trans "Generate" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Certificate type change handler
    const certificateTypeButtons = document.querySelectorAll('[data-certificate-type]');
    
    certificateTypeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const type = this.getAttribute('data-certificate-type');
            document.getElementById('certificateType').value = type;
        });
    });
    
    // Preview button handler
    document.getElementById('previewCertificateBtn').addEventListener('click', function() {
        const certificateType = document.getElementById('certificateType').value;
        const studentId = document.getElementById('student').value;
        
        if (!certificateType || !studentId) {
            alert('Please select a certificate type and student first.');
            return;
        }
        
        // Open preview in new window
        window.open(`{% url 'students:certificate_preview' %}?type=${certificateType}&student=${studentId}`, '_blank');
    });
    
    // View certificate function
    window.viewCertificate = function(certificateId) {
        window.open(`{% url 'students:certificate_view' certificate_id='PLACEHOLDER' %}`.replace('PLACEHOLDER', certificateId), '_blank');
    };
    
    // Download certificate function
    window.downloadCertificate = function(certificateId) {
        window.location.href = `{% url 'students:certificate_download' certificate_id='PLACEHOLDER' %}`.replace('PLACEHOLDER', certificateId);
    };
    
    // Print certificate function
    window.printCertificate = function(certificateId) {
        const printWindow = window.open(`{% url 'students:certificate_print' certificate_id='PLACEHOLDER' %}`.replace('PLACEHOLDER', certificateId), '_blank');
        printWindow.addEventListener('load', function() {
            printWindow.print();
        });
    };
});
</script>
{% endblock %}