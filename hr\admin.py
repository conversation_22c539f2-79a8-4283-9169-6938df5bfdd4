from django.contrib import admin
from .models import (
    Department, Position, Employee, AttendanceRecord, LeaveType, LeaveRequest,
    PayrollPeriod, Payroll, PerformanceEvaluation, EmployeeDocument
)


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    """
    Department admin
    """
    list_display = ('name', 'code', 'head', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'name_ar', 'code')
    raw_id_fields = ('head',)
    ordering = ('name',)


@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    """
    Position admin
    """
    list_display = ('title', 'department', 'is_active')
    list_filter = ('department', 'is_active')
    search_fields = ('title', 'title_ar')
    raw_id_fields = ('department',)
    ordering = ('title',)


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    """
    Employee admin
    """
    list_display = ('employee_id', 'user', 'position', 'hire_date', 'employment_status', 'is_active')
    list_filter = ('employment_status', 'position__department', 'hire_date', 'is_active')
    search_fields = ('employee_id', 'user__first_name', 'user__last_name', 'user__email')
    raw_id_fields = ('user', 'position')
    date_hierarchy = 'hire_date'
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'employee_id', 'position', 'employment_status')
        }),
        ('Employment Details', {
            'fields': ('hire_date', 'termination_date', 'salary')
        }),
        ('Emergency Contact', {
            'fields': ('emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship')
        }),
        ('System', {
            'fields': ('is_active', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(AttendanceRecord)
class AttendanceRecordAdmin(admin.ModelAdmin):
    """
    Attendance Record admin
    """
    list_display = ('employee', 'date', 'check_in_time', 'check_out_time', 'status', 'total_hours', 'is_manual')
    list_filter = ('status', 'is_manual', 'date', 'employee__position__department')
    search_fields = ('employee__user__first_name', 'employee__user__last_name', 'employee__employee_id')
    raw_id_fields = ('employee', 'created_by')
    date_hierarchy = 'date'
    readonly_fields = ('total_hours', 'created_at', 'updated_at')

    def total_hours(self, obj):
        return f"{obj.total_hours:.2f}" if obj.total_hours else "0.00"
    total_hours.short_description = 'Total Hours'


@admin.register(LeaveType)
class LeaveTypeAdmin(admin.ModelAdmin):
    """
    Leave Type admin
    """
    list_display = ('name', 'code', 'max_days_per_year', 'is_paid', 'requires_approval', 'is_active')
    list_filter = ('is_paid', 'requires_approval', 'is_active')
    search_fields = ('name', 'name_ar', 'code')
    ordering = ('name',)


@admin.register(LeaveRequest)
class LeaveRequestAdmin(admin.ModelAdmin):
    """
    Leave Request admin
    """
    list_display = ('employee', 'leave_type', 'start_date', 'end_date', 'duration_days', 'status', 'approved_by')
    list_filter = ('status', 'leave_type', 'start_date', 'employee__position__department')
    search_fields = ('employee__user__first_name', 'employee__user__last_name', 'reason')
    raw_id_fields = ('employee', 'leave_type', 'approved_by')
    date_hierarchy = 'start_date'
    readonly_fields = ('duration_days', 'created_at', 'updated_at')

    def duration_days(self, obj):
        return obj.duration_days
    duration_days.short_description = 'Duration (Days)'


@admin.register(PayrollPeriod)
class PayrollPeriodAdmin(admin.ModelAdmin):
    """
    Payroll Period admin
    """
    list_display = ('name', 'start_date', 'end_date', 'is_closed', 'closed_by', 'is_active')
    list_filter = ('is_closed', 'is_active')
    search_fields = ('name',)
    raw_id_fields = ('closed_by',)
    date_hierarchy = 'start_date'
    readonly_fields = ('closed_at', 'created_at', 'updated_at')


@admin.register(Payroll)
class PayrollAdmin(admin.ModelAdmin):
    """
    Payroll admin
    """
    list_display = ('employee', 'period', 'basic_salary', 'net_salary', 'is_paid', 'paid_date')
    list_filter = ('is_paid', 'period', 'employee__position__department')
    search_fields = ('employee__user__first_name', 'employee__user__last_name', 'employee__employee_id')
    raw_id_fields = ('employee', 'period')
    readonly_fields = ('net_salary', 'created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('employee', 'period')
        }),
        ('Salary Components', {
            'fields': ('basic_salary', 'allowances', 'overtime_hours', 'overtime_rate')
        }),
        ('Deductions', {
            'fields': ('deductions', 'tax_deduction', 'insurance_deduction')
        }),
        ('Payment', {
            'fields': ('net_salary', 'is_paid', 'paid_date', 'notes')
        }),
    )


@admin.register(PerformanceEvaluation)
class PerformanceEvaluationAdmin(admin.ModelAdmin):
    """
    Performance Evaluation admin
    """
    list_display = ('employee', 'evaluator', 'evaluation_period_start', 'evaluation_period_end', 'overall_rating', 'average_rating', 'is_finalized')
    list_filter = ('overall_rating', 'is_finalized', 'evaluation_period_end', 'employee__position__department')
    search_fields = ('employee__user__first_name', 'employee__user__last_name', 'evaluator__first_name', 'evaluator__last_name')
    raw_id_fields = ('employee', 'evaluator')
    date_hierarchy = 'evaluation_period_end'
    readonly_fields = ('average_rating', 'finalized_at', 'created_at', 'updated_at')

    def average_rating(self, obj):
        return f"{obj.average_rating:.1f}"
    average_rating.short_description = 'Average Rating'

    fieldsets = (
        ('Basic Information', {
            'fields': ('employee', 'evaluator', 'evaluation_period_start', 'evaluation_period_end')
        }),
        ('Performance Ratings', {
            'fields': ('quality_of_work', 'productivity', 'communication', 'teamwork', 'punctuality', 'initiative', 'overall_rating')
        }),
        ('Assessment', {
            'fields': ('strengths', 'areas_for_improvement', 'goals_for_next_period', 'employee_comments')
        }),
        ('Status', {
            'fields': ('is_finalized', 'finalized_at')
        }),
    )


@admin.register(EmployeeDocument)
class EmployeeDocumentAdmin(admin.ModelAdmin):
    """
    Employee Document admin
    """
    list_display = ('employee', 'document_type', 'title', 'uploaded_by', 'is_confidential', 'expiry_date', 'is_expired')
    list_filter = ('document_type', 'is_confidential', 'expiry_date', 'created_at')
    search_fields = ('employee__user__first_name', 'employee__user__last_name', 'title')
    raw_id_fields = ('employee', 'uploaded_by')
    readonly_fields = ('is_expired', 'created_at', 'updated_at')

    def is_expired(self, obj):
        return obj.is_expired
    is_expired.boolean = True
    is_expired.short_description = 'Is Expired'
