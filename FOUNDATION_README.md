# School ERP System - Foundation Setup

This document describes the foundational infrastructure that has been implemented for the School ERP System.

## 🏗️ Infrastructure Components

### 1. Core Architecture
- **Django 5.2+** with Django REST Framework
- **Multi-tenancy** support with school-based data isolation
- **PostgreSQL** database with SQLite fallback for development
- **Redis** for caching and session management
- **Celery** for asynchronous task processing
- **WebSocket** support via Django Channels
- **Comprehensive logging** system

### 2. Database Models
- **School**: Root model for multi-tenancy
- **AcademicYear**: Academic year management
- **Semester**: Semester management within academic years
- **BaseModel**: Abstract base with common fields (school, timestamps, audit)
- **AuditLog**: Comprehensive audit trail

### 3. Security & Permissions
- **Multi-factor authentication** ready
- **Role-based access control** (RBAC)
- **Field-level encryption** support
- **Comprehensive audit logging**
- **Security headers** middleware
- **School-based data isolation**

### 4. Utilities & Services
- **CacheManager**: Centralized cache operations
- **EncryptionManager**: Field-level encryption
- **IDGenerator**: Automatic ID generation (student, employee, etc.)
- **SecurityUtils**: Password hashing, token generation
- **ValidationUtils**: Common validation functions

### 5. Real-time Features
- **WebSocket consumers** for notifications, chat, attendance
- **Real-time notifications** system
- **Live updates** for critical operations

### 6. Task Processing
- **Celery integration** with Redis broker
- **Scheduled tasks** (attendance reports, cleanup, backups)
- **Email notifications** (async)
- **Report generation** (async)
- **Data synchronization** tasks

## 🚀 Quick Start

### Prerequisites
- Python 3.10+
- pipenv
- PostgreSQL (optional, SQLite fallback available)
- Redis (optional, database cache fallback available)

### Installation

1. **Clone and setup**:
   ```bash
   git clone <repository>
   cd school-erp
   python setup.py
   ```

2. **Manual setup** (alternative):
   ```bash
   pipenv install --dev
   cp .env.example .env
   pipenv run python manage.py init_db
   ```

3. **Create a school**:
   ```bash
   pipenv run python manage.py setup_school \
     --school-name="My School" \
     --school-code="MYSCH" \
     --admin-email="<EMAIL>"
   ```

4. **Run the server**:
   ```bash
   pipenv run python manage.py runserver
   ```

## 🧪 Testing

### Run Tests
```bash
# All core tests
pipenv run python manage.py test core

# Specific test modules
pipenv run python manage.py test core.tests
pipenv run python manage.py test core.test_integration

# With coverage
pipenv run pytest --cov=core
```

### Test Coverage
- **Unit tests**: Model validation, utilities, permissions
- **Integration tests**: Multi-component workflows
- **Task tests**: Celery task execution
- **Middleware tests**: Request/response processing

## 🔧 Management Commands

### Database Management
```bash
# Initialize database
python manage.py init_db

# Reset database (WARNING: Deletes all data)
python manage.py init_db --reset
```

### Security
```bash
# Generate encryption key
python manage.py generate_key --type=fernet

# Generate Django secret key
python manage.py generate_key --type=secret
```

### School Setup
```bash
# Create new school with admin user
python manage.py setup_school \
  --school-name="School Name" \
  --school-code="CODE" \
  --admin-email="<EMAIL>" \
  --admin-password="secure_password"
```

### Health Monitoring
```bash
# System health check
python manage.py health_check
```

## 📁 Project Structure

```
school_erp/
├── core/                          # Core functionality
│   ├── models.py                  # Base models (School, AcademicYear, etc.)
│   ├── utils.py                   # Utility functions
│   ├── permissions.py             # Permission classes
│   ├── middleware.py              # Custom middleware
│   ├── tasks.py                   # Celery tasks
│   ├── consumers.py               # WebSocket consumers
│   ├── routing.py                 # WebSocket routing
│   ├── logging_config.py          # Logging configuration
│   ├── management/commands/       # Management commands
│   ├── tests.py                   # Unit tests
│   └── test_integration.py        # Integration tests
├── school_erp/
│   ├── settings.py                # Django settings
│   ├── asgi.py                    # ASGI configuration
│   └── urls.py                    # URL configuration
├── logs/                          # Log files
├── media/                         # User uploads
├── static/                        # Static files
├── templates/                     # HTML templates
├── .env.example                   # Environment variables template
├── Pipfile                        # Python dependencies
├── pytest.ini                     # Test configuration
└── setup.py                       # Setup script
```

## 🔐 Security Features

### Authentication & Authorization
- JWT token authentication
- Session-based authentication
- Role-based permissions
- School-based data isolation

### Data Protection
- Field-level encryption for sensitive data
- Secure password hashing
- CSRF protection
- XSS protection
- Security headers

### Audit & Monitoring
- Comprehensive audit logging
- User action tracking
- IP address logging
- Failed login attempts
- Data change tracking

## 📊 Monitoring & Logging

### Log Files
- `logs/django.log`: General application logs
- `logs/error.log`: Error logs
- `logs/security.log`: Security-related events
- `logs/celery.log`: Celery task logs

### Health Monitoring
- Database connectivity
- Cache system status
- Redis connectivity
- File system permissions
- Environment variables

## 🔄 Asynchronous Tasks

### Configured Tasks
- **Daily attendance reports**: Automated daily summaries
- **Email notifications**: Async email sending
- **Database cleanup**: Old log cleanup
- **Report generation**: Large report processing
- **Data synchronization**: External system sync

### Task Management
```bash
# Start Celery worker
pipenv run celery -A school_erp worker -l info

# Start Celery beat (scheduler)
pipenv run celery -A school_erp beat -l info

# Monitor tasks
pipenv run celery -A school_erp flower
```

## 🌐 Real-time Features

### WebSocket Endpoints
- `/ws/notifications/<school_code>/`: Real-time notifications
- `/ws/chat/<room_name>/`: Chat functionality
- `/ws/attendance/<school_code>/`: Live attendance updates

### Usage Example
```javascript
// Connect to notifications
const socket = new WebSocket('ws://localhost:8000/ws/notifications/MYSCH/');

socket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Notification:', data.message);
};
```

## 🔧 Configuration

### Environment Variables
Key variables in `.env`:
```bash
# Django
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database
DB_ENGINE=django.db.backends.postgresql
DB_NAME=school_erp
DB_USER=postgres
DB_PASSWORD=your-password

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Security
ENCRYPTION_KEY=your-fernet-key

# Development Options
USE_SQLITE=True  # Use SQLite instead of PostgreSQL
USE_DB_CACHE=True  # Use database cache instead of Redis
```

## 🚀 Next Steps

This foundation provides:
1. ✅ **Complete project setup** with virtual environment
2. ✅ **Database models** and migrations
3. ✅ **Authentication & permissions** system
4. ✅ **Caching & session management**
5. ✅ **Asynchronous task processing**
6. ✅ **Real-time WebSocket support**
7. ✅ **Comprehensive testing** framework
8. ✅ **Logging & monitoring** system
9. ✅ **Management commands** for administration
10. ✅ **Security features** and audit trail

**Ready for module development!** You can now proceed to implement specific modules (Students, Academics, Finance, etc.) on top of this solid foundation.

## 📚 Additional Resources

- [Django Documentation](https://docs.djangoproject.com/)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [Celery Documentation](https://docs.celeryproject.org/)
- [Django Channels](https://channels.readthedocs.io/)
- [Redis Documentation](https://redis.io/documentation)