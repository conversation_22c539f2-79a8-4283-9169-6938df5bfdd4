# Environment variables for School ERP System

# Django Settings
SECRET_KEY=your-secret-key-here-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DB_ENGINE=django.db.backends.postgresql
DB_NAME=school_erp
DB_USER=postgres
DB_PASSWORD=your-db-password
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# File Storage (Optional - for production)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
AWS_S3_REGION_NAME=us-east-1

# Security
ENCRYPTION_KEY=your-fernet-encryption-key-here

# Development Options (set to True for development without PostgreSQL/Redis)
USE_SQLITE=False
USE_DB_CACHE=False

# Multi-tenancy
DEFAULT_SCHOOL_CODE=MAIN

# Internationalization
LANGUAGE_CODE=en
TIME_ZONE=UTC

# File Upload Settings
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes

# Cache Settings
CACHE_TTL=300  # 5 minutes

# Logging Level
LOG_LEVEL=INFO

# External Services (Optional)
SMS_API_KEY=your-sms-api-key
SMS_API_URL=https://api.sms-provider.com/send

# Backup Settings
BACKUP_ENABLED=True
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30

# Sync Settings
SYNC_ENABLED=True
SYNC_INTERVAL=300  # 5 minutes