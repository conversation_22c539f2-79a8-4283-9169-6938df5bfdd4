# Generated by Django 5.2.4 on 2025-07-13 11:56

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Grade',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=50, verbose_name='Grade Name')),
                ('name_ar', models.Char<PERSON><PERSON>(blank=True, max_length=50, null=True, verbose_name='Grade Name (Arabic)')),
                ('level', models.PositiveIntegerField(verbose_name='Grade Level')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Grade',
                'verbose_name_plural': 'Grades',
                'ordering': ['level'],
            },
        ),
        migrations.CreateModel(
            name='Class',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=50, verbose_name='Class Name')),
                ('max_students', models.PositiveIntegerField(default=30, verbose_name='Maximum Students')),
                ('room_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='Room Number')),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='classes', to='core.academicyear', verbose_name='Academic Year')),
                ('class_teacher', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'teacher'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_classes', to=settings.AUTH_USER_MODEL, verbose_name='Class Teacher')),
                ('grade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='classes', to='students.grade', verbose_name='Grade')),
            ],
            options={
                'verbose_name': 'Class',
                'verbose_name_plural': 'Classes',
                'ordering': ['grade__level', 'name'],
                'unique_together': {('name', 'grade', 'academic_year')},
            },
        ),
        migrations.CreateModel(
            name='Parent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('father_name', models.CharField(max_length=100, verbose_name='Father Name')),
                ('mother_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='Mother Name')),
                ('father_phone', models.CharField(max_length=20, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$')], verbose_name='Father Phone')),
                ('mother_phone', models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$')], verbose_name='Mother Phone')),
                ('father_occupation', models.CharField(blank=True, max_length=100, null=True, verbose_name='Father Occupation')),
                ('mother_occupation', models.CharField(blank=True, max_length=100, null=True, verbose_name='Mother Occupation')),
                ('father_workplace', models.CharField(blank=True, max_length=200, null=True, verbose_name='Father Workplace')),
                ('mother_workplace', models.CharField(blank=True, max_length=200, null=True, verbose_name='Mother Workplace')),
                ('home_address', models.TextField(verbose_name='Home Address')),
                ('emergency_contact', models.CharField(blank=True, max_length=100, null=True, verbose_name='Emergency Contact')),
                ('emergency_phone', models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$')], verbose_name='Emergency Phone')),
                ('user', models.OneToOneField(limit_choices_to={'user_type': 'parent'}, on_delete=django.db.models.deletion.CASCADE, related_name='parent_profile', to=settings.AUTH_USER_MODEL, verbose_name='User Account')),
            ],
            options={
                'verbose_name': 'Parent',
                'verbose_name_plural': 'Parents',
            },
        ),
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('student_id', models.CharField(max_length=20, unique=True, verbose_name='Student ID')),
                ('admission_number', models.CharField(max_length=20, unique=True, verbose_name='Admission Number')),
                ('first_name', models.CharField(max_length=50, verbose_name='First Name')),
                ('last_name', models.CharField(max_length=50, verbose_name='Last Name')),
                ('first_name_ar', models.CharField(blank=True, max_length=50, null=True, verbose_name='First Name (Arabic)')),
                ('last_name_ar', models.CharField(blank=True, max_length=50, null=True, verbose_name='Last Name (Arabic)')),
                ('date_of_birth', models.DateField(verbose_name='Date of Birth')),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female')], max_length=1, verbose_name='Gender')),
                ('nationality', models.CharField(max_length=50, verbose_name='Nationality')),
                ('national_id', models.CharField(blank=True, max_length=20, null=True, verbose_name='National ID')),
                ('passport_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='Passport Number')),
                ('blood_type', models.CharField(blank=True, choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-')], max_length=3, null=True, verbose_name='Blood Type')),
                ('admission_date', models.DateField(verbose_name='Admission Date')),
                ('previous_school', models.CharField(blank=True, max_length=200, null=True, verbose_name='Previous School')),
                ('medical_conditions', models.TextField(blank=True, null=True, verbose_name='Medical Conditions')),
                ('allergies', models.TextField(blank=True, null=True, verbose_name='Allergies')),
                ('special_needs', models.TextField(blank=True, null=True, verbose_name='Special Needs')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='students/photos/', verbose_name='Photo')),
                ('is_graduated', models.BooleanField(default=False, verbose_name='Is Graduated')),
                ('graduation_date', models.DateField(blank=True, null=True, verbose_name='Graduation Date')),
                ('current_class', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='students', to='students.class', verbose_name='Current Class')),
                ('parent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='children', to='students.parent', verbose_name='Parent')),
                ('user', models.OneToOneField(limit_choices_to={'user_type': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='student_profile', to=settings.AUTH_USER_MODEL, verbose_name='User Account')),
            ],
            options={
                'verbose_name': 'Student',
                'verbose_name_plural': 'Students',
                'ordering': ['first_name', 'last_name'],
            },
        ),
    ]
