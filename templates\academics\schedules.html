{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load dashboard_tags %}

{% block title %}{% trans "Schedules Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .schedule-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .schedule-card:hover {
        transform: translateY(-2px);
    }
    .schedule-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .time-slot {
        background-color: #f8f9fa;
        font-weight: bold;
        text-align: center;
        vertical-align: middle;
    }
    .subject-cell {
        text-align: center;
        vertical-align: middle;
        padding: 10px;
        border: 1px solid #dee2e6;
    }
    .subject-math { background-color: #e3f2fd; }
    .subject-science { background-color: #e8f5e8; }
    .subject-english { background-color: #fff3e0; }
    .subject-arabic { background-color: #fce4ec; }
    .subject-art { background-color: #f3e5f5; }
    .subject-pe { background-color: #e0f2f1; }
    .break-cell { background-color: #fff8e1; }
    .empty-cell { background-color: #f5f5f5; color: #999; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>{% trans "Schedules Management" %}
                    </h1>
                    <p class="text-muted">{% trans "Manage class schedules and timetables" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:schedule_create' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Schedule" %}
                    </a>
                    <a href="{% url 'academics:timetable' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-table me-2"></i>{% trans "View Timetable" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card schedule-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-check text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">{{ total_schedules|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Schedules" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card schedule-card">
                <div class="card-body text-center">
                    <i class="fas fa-chalkboard-teacher text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">{{ active_classes|default:0 }}</h4>
                    <p class="mb-0">{% trans "Active Classes" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card schedule-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock text-warning mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-warning">{{ weekly_hours|default:0 }}</h4>
                    <p class="mb-0">{% trans "Weekly Hours" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card schedule-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-graduate text-info mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-info">{{ total_subjects|default:0 }}</h4>
                    <p class="mb-0">{% trans "Subjects" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="class_filter" class="form-label">{% trans "Class" %}</label>
                            <select name="class" id="class_filter" class="form-select">
                                <option value="">{% trans "All Classes" %}</option>
                                {% for class in classes %}
                                    <option value="{{ class.id }}" {% if selected_class and class.id == selected_class.id %}selected{% endif %}>
                                        {{ class.grade.name }} - {{ class.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="subject_filter" class="form-label">{% trans "Subject" %}</label>
                            <select name="subject" id="subject_filter" class="form-select">
                                <option value="">{% trans "All Subjects" %}</option>
                                {% for subject in subjects %}
                                    <option value="{{ subject.id }}" {% if selected_subject and subject.id == selected_subject.id %}selected{% endif %}>
                                        {{ subject.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="day_filter" class="form-label">{% trans "Day" %}</label>
                            <select name="day" id="day_filter" class="form-select">
                                <option value="">{% trans "All Days" %}</option>
                                <option value="sunday" {% if selected_day == 'sunday' %}selected{% endif %}>{% trans "Sunday" %}</option>
                                <option value="monday" {% if selected_day == 'monday' %}selected{% endif %}>{% trans "Monday" %}</option>
                                <option value="tuesday" {% if selected_day == 'tuesday' %}selected{% endif %}>{% trans "Tuesday" %}</option>
                                <option value="wednesday" {% if selected_day == 'wednesday' %}selected{% endif %}>{% trans "Wednesday" %}</option>
                                <option value="thursday" {% if selected_day == 'thursday' %}selected{% endif %}>{% trans "Thursday" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                                </button>
                                <a href="{% url 'academics:schedules' %}" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-times me-2"></i>{% trans "Clear" %}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedules List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Class Schedules" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Class" %}</th>
                                    <th>{% trans "Subject" %}</th>
                                    <th>{% trans "Teacher" %}</th>
                                    <th>{% trans "Day" %}</th>
                                    <th>{% trans "Time" %}</th>
                                    <th>{% trans "Room" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if schedules %}
                                    {% for schedule in schedules %}
                                        <tr>
                                            <td>
                                                <div class="fw-bold">{{ schedule.class_subject.class_name.grade.name }} - {{ schedule.class_subject.class_name.name }}</div>
                                                <small class="text-muted">{{ schedule.class_subject.class_name.students.count }} {% trans "students" %}</small>
                                            </td>
                                            <td>{{ schedule.class_subject.subject.name }}</td>
                                            <td>
                                                {% if schedule.class_subject.teacher %}
                                                    {{ schedule.class_subject.teacher.user.first_name }} {{ schedule.class_subject.teacher.user.last_name }}
                                                {% else %}
                                                    <span class="text-muted">{% trans "Not assigned" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    {% if schedule.day_of_week == 'sunday' %}{% trans "Sunday" %}
                                                    {% elif schedule.day_of_week == 'monday' %}{% trans "Monday" %}
                                                    {% elif schedule.day_of_week == 'tuesday' %}{% trans "Tuesday" %}
                                                    {% elif schedule.day_of_week == 'wednesday' %}{% trans "Wednesday" %}
                                                    {% elif schedule.day_of_week == 'thursday' %}{% trans "Thursday" %}
                                                    {% else %}{{ schedule.day_of_week }}{% endif %}
                                                </span>
                                            </td>
                                            <td>
                                                {% if schedule.start_time and schedule.end_time %}
                                                    {{ schedule.start_time|time:"H:i" }} - {{ schedule.end_time|time:"H:i" }}
                                                {% else %}
                                                    <span class="text-muted">{% trans "Not set" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if schedule.room %}
                                                    {{ schedule.room }}
                                                {% else %}
                                                    <span class="text-muted">{% trans "Not assigned" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if schedule.is_active %}
                                                    <span class="badge bg-success">{% trans "Active" %}</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{% url 'academics:schedule_detail' schedule.id %}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'academics:schedule_update' schedule.id %}" class="btn btn-sm btn-outline-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No schedules found. Create your first schedule to get started." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form on filter change
    document.querySelectorAll('#class_filter, #subject_filter, #day_filter').forEach(function(element) {
        element.addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>
{% endblock %}
