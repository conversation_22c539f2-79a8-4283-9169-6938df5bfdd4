{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Journal Entries" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .journal-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .journal-card:hover {
        transform: translateY(-2px);
    }
    .journal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .entry-row {
        border-bottom: 1px solid #eee;
        transition: background-color 0.2s;
    }
    .entry-row:hover {
        background-color: #f8f9fa;
    }
    .debit-amount {
        color: #dc3545;
        font-weight: bold;
    }
    .credit-amount {
        color: #28a745;
        font-weight: bold;
    }
    .entry-status {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
    }
    .status-posted {
        background-color: #d4edda;
        color: #155724;
    }
    .status-draft {
        background-color: #fff3cd;
        color: #856404;
    }
    .status-pending {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    .journal-form {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
    }
    .account-line {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 0.5rem;
    }
    .balance-indicator {
        font-weight: bold;
        padding: 0.5rem;
        border-radius: 8px;
        text-align: center;
    }
    .balanced {
        background: #d4edda;
        color: #155724;
    }
    .unbalanced {
        background: #f8d7da;
        color: #721c24;
    }
    .stats-widget {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-book text-primary me-2"></i>{% trans "Journal Entries" %}
                    </h2>
                    <p class="text-muted">{% trans "Record and manage financial transactions" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#newEntryModal">
                        <i class="fas fa-plus me-2"></i>{% trans "New Entry" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export Journal" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-book-open fa-2x mb-2"></i>
                <h3 class="mb-1">1,247</h3>
                <p class="mb-0">{% trans "Total Entries" %}</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h3 class="mb-1">1,198</h3>
                <p class="mb-0">{% trans "Posted Entries" %}</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-edit fa-2x mb-2"></i>
                <h3 class="mb-1">49</h3>
                <p class="mb-0">{% trans "Draft Entries" %}</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                <h3 class="mb-1">$2.4M</h3>
                <p class="mb-0">{% trans "Total Amount" %}</p>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card journal-card">
                <div class="card-body">
                    <h5 class="card-title mb-3">{% trans "Filter Entries" %}</h5>
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                            <input type="date" class="form-control" id="date_from" name="date_from">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                            <input type="date" class="form-control" id="date_to" name="date_to">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">{% trans "Status" %}</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">{% trans "All Status" %}</option>
                                <option value="posted">{% trans "Posted" %}</option>
                                <option value="draft">{% trans "Draft" %}</option>
                                <option value="pending">{% trans "Pending" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="reference" class="form-label">{% trans "Reference" %}</label>
                            <input type="text" class="form-control" id="reference" name="reference" placeholder="{% trans 'Search by reference' %}">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Journal Entries Table -->
    <div class="row">
        <div class="col-12">
            <div class="card journal-card">
                <div class="journal-header card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "Journal Entries" %}
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Reference" %}</th>
                                    <th>{% trans "Description" %}</th>
                                    <th>{% trans "Account" %}</th>
                                    <th class="text-end">{% trans "Debit" %}</th>
                                    <th class="text-end">{% trans "Credit" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Sample journal entries -->
                                <tr class="entry-row">
                                    <td>2025-01-13</td>
                                    <td><strong>JE-2025-001</strong></td>
                                    <td>{% trans "Student tuition payment received" %}</td>
                                    <td>1110 - {% trans "Cash" %}</td>
                                    <td class="text-end debit-amount">$1,500.00</td>
                                    <td class="text-end">-</td>
                                    <td><span class="entry-status status-posted">{% trans "Posted" %}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Edit' %}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" title="{% trans 'Print' %}">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="entry-row">
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>4100 - {% trans "Tuition Revenue" %}</td>
                                    <td class="text-end">-</td>
                                    <td class="text-end credit-amount">$1,500.00</td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                
                                <tr class="entry-row">
                                    <td>2025-01-12</td>
                                    <td><strong>JE-2025-002</strong></td>
                                    <td>{% trans "Office supplies purchase" %}</td>
                                    <td>5200 - {% trans "Office Expenses" %}</td>
                                    <td class="text-end debit-amount">$350.00</td>
                                    <td class="text-end">-</td>
                                    <td><span class="entry-status status-posted">{% trans "Posted" %}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Edit' %}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" title="{% trans 'Print' %}">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="entry-row">
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>1110 - {% trans "Cash" %}</td>
                                    <td class="text-end">-</td>
                                    <td class="text-end credit-amount">$350.00</td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                
                                <tr class="entry-row">
                                    <td>2025-01-11</td>
                                    <td><strong>JE-2025-003</strong></td>
                                    <td>{% trans "Teacher salary payment" %}</td>
                                    <td>5100 - {% trans "Salaries" %}</td>
                                    <td class="text-end debit-amount">$2,800.00</td>
                                    <td class="text-end">-</td>
                                    <td><span class="entry-status status-draft">{% trans "Draft" %}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Edit' %}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" title="{% trans 'Post' %}">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="entry-row">
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>1110 - {% trans "Cash" %}</td>
                                    <td class="text-end">-</td>
                                    <td class="text-end credit-amount">$2,800.00</td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">{% trans "Showing 3 entries" %}</span>
                        <nav>
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item disabled">
                                    <span class="page-link">{% trans "Previous" %}</span>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link">1</span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">2</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">{% trans "Next" %}</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Journal Entry Modal -->
<div class="modal fade" id="newEntryModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "New Journal Entry" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form class="journal-form">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="entryDate" class="form-label">{% trans "Date" %}</label>
                            <input type="date" class="form-control" id="entryDate" required>
                        </div>
                        <div class="col-md-4">
                            <label for="reference" class="form-label">{% trans "Reference" %}</label>
                            <input type="text" class="form-control" id="reference" placeholder="{% trans 'Auto-generated' %}" readonly>
                        </div>
                        <div class="col-md-4">
                            <label for="description" class="form-label">{% trans "Description" %}</label>
                            <input type="text" class="form-control" id="description" placeholder="{% trans 'Entry description' %}" required>
                        </div>
                    </div>

                    <h6>{% trans "Account Lines" %}</h6>
                    <div id="accountLines">
                        <!-- Account Line 1 -->
                        <div class="account-line">
                            <div class="row">
                                <div class="col-md-5">
                                    <label class="form-label">{% trans "Account" %}</label>
                                    <select class="form-select" required>
                                        <option value="">{% trans "Select account" %}</option>
                                        <option value="1110">1110 - {% trans "Cash" %}</option>
                                        <option value="1120">1120 - {% trans "Accounts Receivable" %}</option>
                                        <option value="4100">4100 - {% trans "Tuition Revenue" %}</option>
                                        <option value="5100">5100 - {% trans "Salaries" %}</option>
                                        <option value="5200">5200 - {% trans "Office Expenses" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">{% trans "Debit" %}</label>
                                    <input type="number" class="form-control debit-input" step="0.01" min="0">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">{% trans "Credit" %}</label>
                                    <input type="number" class="form-control credit-input" step="0.01" min="0">
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-outline-danger btn-sm remove-line">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Account Line 2 -->
                        <div class="account-line">
                            <div class="row">
                                <div class="col-md-5">
                                    <label class="form-label">{% trans "Account" %}</label>
                                    <select class="form-select" required>
                                        <option value="">{% trans "Select account" %}</option>
                                        <option value="1110">1110 - {% trans "Cash" %}</option>
                                        <option value="1120">1120 - {% trans "Accounts Receivable" %}</option>
                                        <option value="4100">4100 - {% trans "Tuition Revenue" %}</option>
                                        <option value="5100">5100 - {% trans "Salaries" %}</option>
                                        <option value="5200">5200 - {% trans "Office Expenses" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">{% trans "Debit" %}</label>
                                    <input type="number" class="form-control debit-input" step="0.01" min="0">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">{% trans "Credit" %}</label>
                                    <input type="number" class="form-control credit-input" step="0.01" min="0">
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-outline-danger btn-sm remove-line">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-primary" id="addLine">
                                <i class="fas fa-plus me-2"></i>{% trans "Add Line" %}
                            </button>
                        </div>
                        <div class="col-md-6">
                            <div class="balance-indicator unbalanced" id="balanceIndicator">
                                {% trans "Unbalanced" %}: $0.00
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-outline-primary">{% trans "Save as Draft" %}</button>
                <button type="button" class="btn btn-success">{% trans "Post Entry" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    document.getElementById('entryDate').value = new Date().toISOString().split('T')[0];
    
    // Generate reference number
    document.getElementById('reference').value = 'JE-' + new Date().getFullYear() + '-' + String(Math.floor(Math.random() * 1000)).padStart(3, '0');

    // Balance calculation
    function calculateBalance() {
        let totalDebits = 0;
        let totalCredits = 0;
        
        document.querySelectorAll('.debit-input').forEach(input => {
            totalDebits += parseFloat(input.value) || 0;
        });
        
        document.querySelectorAll('.credit-input').forEach(input => {
            totalCredits += parseFloat(input.value) || 0;
        });
        
        const difference = totalDebits - totalCredits;
        const balanceIndicator = document.getElementById('balanceIndicator');
        
        if (Math.abs(difference) < 0.01) {
            balanceIndicator.className = 'balance-indicator balanced';
            balanceIndicator.textContent = '{% trans "Balanced" %}';
        } else {
            balanceIndicator.className = 'balance-indicator unbalanced';
            balanceIndicator.textContent = `{% trans "Unbalanced" %}: $${Math.abs(difference).toFixed(2)}`;
        }
    }

    // Add event listeners for balance calculation
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('debit-input') || e.target.classList.contains('credit-input')) {
            calculateBalance();
        }
    });

    // Prevent entering both debit and credit on same line
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('debit-input') && e.target.value) {
            const creditInput = e.target.closest('.account-line').querySelector('.credit-input');
            creditInput.value = '';
        } else if (e.target.classList.contains('credit-input') && e.target.value) {
            const debitInput = e.target.closest('.account-line').querySelector('.debit-input');
            debitInput.value = '';
        }
    });

    // Add new account line
    document.getElementById('addLine').addEventListener('click', function() {
        const accountLines = document.getElementById('accountLines');
        const newLine = document.querySelector('.account-line').cloneNode(true);
        
        // Clear values
        newLine.querySelectorAll('input, select').forEach(input => {
            input.value = '';
        });
        
        accountLines.appendChild(newLine);
    });

    // Remove account line
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-line')) {
            const accountLines = document.querySelectorAll('.account-line');
            if (accountLines.length > 2) {
                e.target.closest('.account-line').remove();
                calculateBalance();
            } else {
                alert('{% trans "At least two account lines are required" %}');
            }
        }
    });

    // Handle action buttons
    document.querySelectorAll('.btn-outline-primary').forEach(btn => {
        btn.addEventListener('click', function() {
            if (this.title === '{% trans "View" %}') {
                alert('{% trans "View journal entry functionality would be implemented here" %}');
            }
        });
    });

    document.querySelectorAll('.btn-outline-success').forEach(btn => {
        btn.addEventListener('click', function() {
            if (this.title === '{% trans "Edit" %}') {
                alert('{% trans "Edit journal entry functionality would be implemented here" %}');
            }
        });
    });

    document.querySelectorAll('.btn-outline-info').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "Print journal entry functionality would be implemented here" %}');
        });
    });
});
</script>
{% endblock %}
