{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Certificate Preview" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-eye text-primary me-2"></i>{% trans "Certificate Preview" %}
                            </h2>
                            <p class="text-muted">{% trans "Preview certificate before generation" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:certificates' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Certificates" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Certificate Preview -->
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body text-center p-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                            <div class="certificate-content">
                                <h1 class="display-4 mb-4">{% trans "Certificate" %}</h1>
                                
                                {% if certificate_type == 'enrollment' %}
                                    <h3 class="mb-4">{% trans "Certificate of Enrollment" %}</h3>
                                    <p class="lead">{% trans "This is to certify that" %}</p>
                                    <h2 class="my-4 text-warning">
                                        {% if student %}
                                            {{ student.full_name }}
                                        {% else %}
                                            [{% trans "Student Name" %}]
                                        {% endif %}
                                    </h2>
                                    <p class="lead">{% trans "is currently enrolled as a student at our institution" %}</p>
                                    {% if student and student.current_class %}
                                        <p>{% trans "Class:" %} {{ student.current_class.name }}</p>
                                        <p>{% trans "Grade:" %} {{ student.current_class.grade.name }}</p>
                                    {% endif %}
                                    
                                {% elif certificate_type == 'completion' %}
                                    <h3 class="mb-4">{% trans "Certificate of Completion" %}</h3>
                                    <p class="lead">{% trans "This is to certify that" %}</p>
                                    <h2 class="my-4 text-warning">
                                        {% if student %}
                                            {{ student.full_name }}
                                        {% else %}
                                            [{% trans "Student Name" %}]
                                        {% endif %}
                                    </h2>
                                    <p class="lead">{% trans "has successfully completed the academic requirements" %}</p>
                                    {% if student and student.current_class %}
                                        <p>{% trans "for Grade:" %} {{ student.current_class.grade.name }}</p>
                                    {% endif %}
                                    
                                {% elif certificate_type == 'achievement' %}
                                    <h3 class="mb-4">{% trans "Certificate of Achievement" %}</h3>
                                    <p class="lead">{% trans "This is to certify that" %}</p>
                                    <h2 class="my-4 text-warning">
                                        {% if student %}
                                            {{ student.full_name }}
                                        {% else %}
                                            [{% trans "Student Name" %}]
                                        {% endif %}
                                    </h2>
                                    <p class="lead">{% trans "has demonstrated exceptional achievement and excellence" %}</p>
                                    {% if student and student.current_class %}
                                        <p>{% trans "in Grade:" %} {{ student.current_class.grade.name }}</p>
                                    {% endif %}
                                {% endif %}
                                
                                <div class="mt-5">
                                    <p>{% trans "Date:" %} {% now "F d, Y" %}</p>
                                </div>
                                
                                <div class="row mt-5">
                                    <div class="col-6">
                                        <div class="signature-line">
                                            <hr style="border-color: white;">
                                            <p class="small">{% trans "Principal" %}</p>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="signature-line">
                                            <hr style="border-color: white;">
                                            <p class="small">{% trans "Academic Director" %}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-success btn-lg me-3" onclick="generateCertificate()">
                            <i class="fas fa-check me-2"></i>{% trans "Generate Certificate" %}
                        </button>
                        <a href="{% url 'students:certificates' %}" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function generateCertificate() {
    // In a real application, this would submit the certificate generation request
    alert('{% trans "Certificate generation feature will be implemented soon." %}');
    window.location.href = '{% url "students:certificates" %}';
}
</script>
{% endblock %}