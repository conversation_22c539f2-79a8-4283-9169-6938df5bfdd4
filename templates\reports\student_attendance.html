{% extends 'base.html' %}
{% load static %}
{% load dashboard_tags %}

{% block title %}تقرير حضور الطلاب{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .attendance-card {
        transition: transform 0.2s;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .attendance-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .present { color: #28a745; }
    .absent { color: #dc3545; }
    .late { color: #ffc107; }
    .excused { color: #17a2b8; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">تقرير حضور الطلاب</h1>
                    <p class="text-muted">تقرير مفصل عن حضور وغياب الطلاب</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2">
                        <i class="fas fa-download me-2"></i>تصدير التقرير
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-check text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="present">{{ present_count|default:0 }}</h4>
                    <p class="mb-0">حاضر</p>
                    <small class="text-muted">{{ present_percentage|default:0 }}% من الطلاب</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-times text-danger mb-2" style="font-size: 2rem;"></i>
                    <h4 class="absent">{{ absent_count|default:0 }}</h4>
                    <p class="mb-0">غائب</p>
                    <small class="text-muted">{{ absent_percentage|default:0 }}% من الطلاب</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-clock text-warning mb-2" style="font-size: 2rem;"></i>
                    <h4 class="late">{{ late_count|default:0 }}</h4>
                    <p class="mb-0">متأخر</p>
                    <small class="text-muted">{{ late_percentage|default:0 }}% من الطلاب</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-shield text-info mb-2" style="font-size: 2rem;"></i>
                    <h4 class="excused">{{ excused_count|default:0 }}</h4>
                    <p class="mb-0">غياب بعذر</p>
                    <small class="text-muted">{{ excused_percentage|default:0 }}% من الطلاب</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Attendance Details -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">تفاصيل حضور الطلاب</h5>
                    <div>
                        <form method="get" class="d-inline-flex align-items-center">
                            <select name="class" class="form-select form-select-sm d-inline-block w-auto me-2" onchange="this.form.submit()">
                                <option value="">جميع الفصول</option>
                                {% for class in classes %}
                                    <option value="{{ class.id }}" {% if selected_class and class.id == selected_class.id %}selected{% endif %}>
                                        {{ class.grade.name }} {{ class.name }}
                                    </option>
                                {% endfor %}
                            </select>
                            <input type="date" name="date" class="form-control form-control-sm d-inline-block w-auto"
                                   value="{{ selected_date|date:'Y-m-d' }}" onchange="this.form.submit()">
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الطالب</th>
                                    <th>الفصل</th>
                                    <th>وقت الوصول</th>
                                    <th>وقت المغادرة</th>
                                    <th>الحالة</th>
                                    <th>ملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if attendance_records %}
                                    {% for record in attendance_records %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="{% if record.student.photo %}{{ record.student.photo.url }}{% else %}https://via.placeholder.com/32{% endif %}"
                                                         class="rounded-circle me-2" alt="Student" style="width: 32px; height: 32px;">
                                                    <div>
                                                        <div class="fw-bold">{{ record.student.first_name }} {{ record.student.last_name }}</div>
                                                        <small class="text-muted">ID: {{ record.student.student_id|default:record.student.id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ record.student.current_class.grade.name }} {{ record.student.current_class.name }}</td>
                                            <td>
                                                {% if record.check_in_time %}
                                                    {{ record.check_in_time|time:"H:i" }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if record.check_out_time %}
                                                    {{ record.check_out_time|time:"H:i" }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if record.status == 'present' %}
                                                    <span class="badge bg-success">حاضر</span>
                                                {% elif record.status == 'absent' %}
                                                    <span class="badge bg-danger">غائب</span>
                                                {% elif record.status == 'late' %}
                                                    <span class="badge bg-warning">متأخر</span>
                                                {% elif record.status == 'excused' %}
                                                    <span class="badge bg-info">غياب بعذر</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">غير محدد</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ record.notes|default:"-" }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"
                                                        onclick="showAttendanceDetails({{ record.id }})">تفاصيل</button>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                لا توجد سجلات حضور للتاريخ المحدد
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">السابق</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">التالي</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Trends -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">اتجاهات الحضور</h5>
                </div>
                <div class="card-body">
                    <canvas id="attendanceTrendsChart" style="height: 300px;"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إحصائيات الحضور</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            معدل الحضور العام
                            <span class="badge bg-success rounded-pill">{{ present_percentage|default:0 }}%</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            أفضل فصل حضوراً
                            <span class="badge bg-primary rounded-pill">
                                {% if best_class %}
                                    {{ best_class.grade.name }} {{ best_class.name }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            التاريخ المحدد
                            <span class="badge bg-info rounded-pill">{{ selected_date|date:"Y-m-d" }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            متوسط التأخير
                            <span class="badge bg-warning rounded-pill">{{ avg_late_minutes|default:0 }} دقيقة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Attendance Trends Chart
    const ctx = document.getElementById('attendanceTrendsChart').getContext('2d');

    // Get data from Django context
    const attendanceTrends = {{ attendance_trends|json_script:"attendance-trends" }};
    const trendsData = JSON.parse(document.getElementById('attendance-trends').textContent);

    const labels = trendsData.map(item => item.day_name);
    const presentData = trendsData.map(item => item.present_rate);
    const absentData = trendsData.map(item => item.absent_rate);

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels.length > 0 ? labels : ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس'],
            datasets: [{
                label: 'معدل الحضور (%)',
                data: presentData.length > 0 ? presentData : [0, 0, 0, 0, 0],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'معدل الغياب (%)',
                data: absentData.length > 0 ? absentData : [0, 0, 0, 0, 0],
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });
</script>
{% endblock %}
