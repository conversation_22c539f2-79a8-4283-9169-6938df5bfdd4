{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% trans "Take Attendance" %} - {{ block.super }}
{% endblock %}

{% block extra_css %}
<style>
    .attendance-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .attendance-body {
        background: white;
        border-radius: 0 0 15px 15px;
        color: #333;
    }
    .attendance-header {
        padding: 1.5rem;
        border-radius: 15px 15px 0 0;
    }
    .class-selection {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    .student-list {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .student-row {
        padding: 1rem;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .student-row:last-child {
        border-bottom: none;
    }
    .student-info {
        display: flex;
        align-items: center;
    }
    .student-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-right: 1rem;
    }
    .attendance-buttons {
        display: flex;
        gap: 0.5rem;
    }
    .btn-attendance {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        border: none;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .btn-present {
        background-color: #d4edda;
        color: #155724;
    }
    .btn-present.active {
        background-color: #28a745;
        color: white;
    }
    .btn-absent {
        background-color: #f8d7da;
        color: #721c24;
    }
    .btn-absent.active {
        background-color: #dc3545;
        color: white;
    }
    .btn-late {
        background-color: #fff3cd;
        color: #856404;
    }
    .btn-late.active {
        background-color: #ffc107;
        color: #212529;
    }
    .btn-excused {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    .btn-excused.active {
        background-color: #17a2b8;
        color: white;
    }
    .debug-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        font-family: monospace;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-check me-2"></i>
                        {% trans "Take Attendance" %}
                    </h1>
                    <p class="text-muted">{% trans "Mark attendance for students in class" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:attendance' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Attendance" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Debug Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="debug-info">
                <h6><i class="fas fa-bug me-2"></i>Debug Information</h6>
                <p><strong>Available Class Subjects:</strong> 
                    {% if class_subjects %}
                        {{ class_subjects.count }} found
                        <ul>
                        {% for cs in class_subjects %}
                            <li>{{ cs.class_obj }} - {{ cs.subject }} ({{ cs.teacher }})</li>
                        {% endfor %}
                        </ul>
                    {% else %}
                        <span class="text-danger">None found - This is likely the issue!</span>
                    {% endif %}
                </p>
                <p><strong>Available Classes:</strong> 
                    {% if classes %}
                        {{ classes.count }} found
                        <ul>
                        {% for class in classes %}
                            <li>{{ class.grade }} - {{ class.name }} ({{ class.current_students_count }} students)</li>
                        {% endfor %}
                        </ul>
                    {% else %}
                        <span class="text-danger">None found</span>
                    {% endif %}
                </p>
                <p><strong>Current Academic Year:</strong> 
                    {% if current_academic_year %}
                        {{ current_academic_year.name }} ({{ current_academic_year.start_date }} - {{ current_academic_year.end_date }})
                    {% else %}
                        <span class="text-danger">None set - This could be the issue!</span>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

    <!-- Class Selection -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="class-selection">
                <h5 class="mb-3">
                    <i class="fas fa-chalkboard-teacher me-2"></i>{% trans "Select Class and Subject" %}
                </h5>
                <form method="get" id="classSelectionForm">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="class_subject" class="form-label">{% trans "Class Subject" %}</label>
                            <select name="class_subject" id="class_subject" class="form-select">
                                <option value="">{% trans "Select Class Subject" %}</option>
                                {% for cs in class_subjects %}
                                    <option value="{{ cs.id }}" {% if request.GET.class_subject == cs.id|stringformat:"s" %}selected{% endif %}>
                                        {{ cs.class_obj }} - {{ cs.subject }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="date" class="form-label">{% trans "Date" %}</label>
                            <input type="date" name="date" id="date" class="form-control" value="{{ selected_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="fas fa-search me-2"></i>{% trans "Load Students" %}
                            </button>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <a href="{% url 'academics:attendance_create' %}" class="btn btn-outline-primary d-block">
                                <i class="fas fa-plus me-2"></i>{% trans "Individual" %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Student List -->
    {% if selected_class_subject and students %}
    <div class="row">
        <div class="col-12">
            <div class="card attendance-card">
                <div class="attendance-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        {% trans "Students in" %} {{ selected_class_subject.class_obj }} - {{ selected_class_subject.subject }}
                    </h5>
                    <small>{{ selected_date|date:"F d, Y" }}</small>
                </div>
                <div class="card-body attendance-body">
                    <form method="post" id="bulkAttendanceForm">
                        {% csrf_token %}
                        <input type="hidden" name="class_subject" value="{{ selected_class_subject.id }}">
                        <input type="hidden" name="date" value="{{ selected_date|date:'Y-m-d' }}">
                        
                        <div class="student-list">
                            {% for student in students %}
                            <div class="student-row">
                                <div class="student-info">
                                    <div class="student-avatar">
                                        {{ student.first_name.0 }}{{ student.last_name.0 }}
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ student.full_name }}</h6>
                                        <small class="text-muted">{{ student.student_id }}</small>
                                    </div>
                                </div>
                                <div class="attendance-buttons">
                                    <input type="hidden" name="student_{{ student.id }}" value="">
                                    <button type="button" class="btn-attendance btn-present" data-student="{{ student.id }}" data-status="present">
                                        <i class="fas fa-check me-1"></i>{% trans "Present" %}
                                    </button>
                                    <button type="button" class="btn-attendance btn-absent" data-student="{{ student.id }}" data-status="absent">
                                        <i class="fas fa-times me-1"></i>{% trans "Absent" %}
                                    </button>
                                    <button type="button" class="btn-attendance btn-late" data-student="{{ student.id }}" data-status="late">
                                        <i class="fas fa-clock me-1"></i>{% trans "Late" %}
                                    </button>
                                    <button type="button" class="btn-attendance btn-excused" data-student="{{ student.id }}" data-status="excused">
                                        <i class="fas fa-user-shield me-1"></i>{% trans "Excused" %}
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <button type="button" class="btn btn-outline-success" id="markAllPresent">
                                <i class="fas fa-check-double me-2"></i>{% trans "Mark All Present" %}
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% trans "Save Attendance" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% elif selected_class_subject %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                {% trans "No students found in the selected class." %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Attendance button handling
    const attendanceButtons = document.querySelectorAll('.btn-attendance');
    
    attendanceButtons.forEach(button => {
        button.addEventListener('click', function() {
            const studentId = this.dataset.student;
            const status = this.dataset.status;
            
            // Remove active class from all buttons for this student
            const studentButtons = document.querySelectorAll(`[data-student="${studentId}"]`);
            studentButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Update hidden input
            const hiddenInput = document.querySelector(`input[name="student_${studentId}"]`);
            if (hiddenInput) {
                hiddenInput.value = status;
            }
        });
    });
    
    // Mark all present functionality
    const markAllPresentBtn = document.getElementById('markAllPresent');
    if (markAllPresentBtn) {
        markAllPresentBtn.addEventListener('click', function() {
            const presentButtons = document.querySelectorAll('.btn-present');
            presentButtons.forEach(btn => btn.click());
        });
    }
    
    // Set today's date as default
    const dateInput = document.getElementById('date');
    if (dateInput && !dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }
});
</script>
{% endblock %}