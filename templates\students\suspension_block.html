{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Suspension & Block" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-ban text-danger me-2"></i>{% trans "Suspension & Block Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage student suspensions and account blocks" %}</p>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-clock me-2"></i>{% trans "Suspend Student" %}
                            </h5>
                        </div>
                        <div class="card-body">
                            <p>{% trans "Temporarily suspend a student from attending classes." %}</p>
                            <form method="post" action="#" class="mb-3">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="suspendStudent" class="form-label">{% trans "Select Student" %}</label>
                                    <select class="form-select" id="suspendStudent" name="student_id" required>
                                        <option value="">{% trans "Choose student..." %}</option>
                                        <!-- Add student options here -->
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="suspensionReason" class="form-label">{% trans "Reason" %}</label>
                                    <textarea class="form-control" id="suspensionReason" name="reason" rows="3" required></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="suspensionStart" class="form-label">{% trans "Start Date" %}</label>
                                        <input type="date" class="form-control" id="suspensionStart" name="start_date" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="suspensionEnd" class="form-label">{% trans "End Date" %}</label>
                                        <input type="date" class="form-control" id="suspensionEnd" name="end_date" required>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-warning mt-3">
                                    <i class="fas fa-user-clock me-2"></i>{% trans "Suspend Student" %}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-slash me-2"></i>{% trans "Block Student Account" %}
                            </h5>
                        </div>
                        <div class="card-body">
                            <p>{% trans "Permanently block a student's account access." %}</p>
                            <form method="post" action="#" class="mb-3">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="blockStudent" class="form-label">{% trans "Select Student" %}</label>
                                    <select class="form-select" id="blockStudent" name="student_id" required>
                                        <option value="">{% trans "Choose student..." %}</option>
                                        <!-- Add student options here -->
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="blockReason" class="form-label">{% trans "Reason" %}</label>
                                    <textarea class="form-control" id="blockReason" name="reason" rows="3" required></textarea>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="confirmBlock" required>
                                    <label class="form-check-label text-danger" for="confirmBlock">
                                        {% trans "I understand this action cannot be easily undone" %}
                                    </label>
                                </div>
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-user-slash me-2"></i>{% trans "Block Account" %}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Suspensions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Current Suspensions" %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Student" %}</th>
                                            <th>{% trans "Reason" %}</th>
                                            <th>{% trans "Start Date" %}</th>
                                            <th>{% trans "End Date" %}</th>
                                            <th>{% trans "Days Remaining" %}</th>
                                            <th>{% trans "Actions" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Sample data - replace with actual data -->
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                        <span class="text-white fw-bold">JD</span>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">John Doe</h6>
                                                        <small class="text-muted">Grade 10-A</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>Repeated misconduct</td>
                                            <td>2025-01-20</td>
                                            <td>2025-01-27</td>
                                            <td>
                                                <span class="badge bg-warning">3 days</span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-undo"></i> {% trans "Lift" %}
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i> {% trans "Modify" %}
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Blocked Accounts -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Blocked Accounts" %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Student" %}</th>
                                            <th>{% trans "Reason" %}</th>
                                            <th>{% trans "Blocked Date" %}</th>
                                            <th>{% trans "Blocked By" %}</th>
                                            <th>{% trans "Actions" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Sample data - replace with actual data -->
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                        <span class="text-white fw-bold">JS</span>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">Jane Smith</h6>
                                                        <small class="text-muted">Grade 11-B</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>Serious violation of school policy</td>
                                            <td>2025-01-15</td>
                                            <td>Principal</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="return confirm('{% trans "Are you sure you want to unblock this account?" %}')">
                                                    <i class="fas fa-unlock"></i> {% trans "Unblock" %}
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today for suspension dates
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('suspensionStart').setAttribute('min', today);
    document.getElementById('suspensionEnd').setAttribute('min', today);
    
    // Update end date minimum when start date changes
    document.getElementById('suspensionStart').addEventListener('change', function() {
        document.getElementById('suspensionEnd').setAttribute('min', this.value);
    });
});
</script>
{% endblock %}