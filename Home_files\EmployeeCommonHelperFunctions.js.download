﻿//EmployeeClass --- Common_Employee_list
//ManagementClass  --- Common_ManageDepartment_list
//Department Class --- Common_Department_list
//jobTitle Class --- Common_JobTitle_list
function Employee_JobTitle_Department_ManageDepartment(BaseSelectListContainerSelector) {

    let JobTitleID = $(BaseSelectListContainerSelector + " " + "[name='JobtitleID']").val();
    let ManageDepartmentID = $(BaseSelectListContainerSelector + " " + "[name='ManageDepartmentID']").val();
    let DepartmentID = $(BaseSelectListContainerSelector + " " + "[name='DepartmentID']").val();
    let EmployeeID = $(BaseSelectListContainerSelector + " " + "[name='EmployeeID']").val();

    $.get(`/Admission/Employee/GetEmployeeMetaData?JobTitleID=${JobTitleID}&ManageDepartmentID=${ManageDepartmentID}&DepartmentID=${DepartmentID}`, (res) => {
        ReplaceOptionsInDropDownlist($(BaseSelectListContainerSelector + " [name='EmployeeID']"), res.ListOfEmployees);
        ReplaceOptionsInDropDownlist($(BaseSelectListContainerSelector + " [name='DepartmentID']"), res.ListOfDepartments);
        ReplaceOptionsInDropDownlist($(BaseSelectListContainerSelector + " [name='ManageDepartmentID']"), res.ListOfManageDepartments);
        //ReplaceOptionsInDropDownlist($(BaseSelectListContainerSelector + " [name='JobtitleID']"), res.ListOfJobTitls);
        //$(BaseSelectListContainerSelector + " " + "[name='JobtitleID']").val(JobTitleID);
        $(BaseSelectListContainerSelector + " " + "[name='ManageDepartmentID']").val(ManageDepartmentID);
        $(BaseSelectListContainerSelector + " " + "[name='DepartmentID']").val(DepartmentID);
        $(BaseSelectListContainerSelector + " " + "[name='EmployeeID']").val(EmployeeID > 0 && res.ListOfEmployees.map(x => x.ID).indexOf(parseInt(EmployeeID)) >= 0 ? EmployeeID : "");
        $(BaseSelectListContainerSelector + " " + "[name='EmployeeID']").trigger("chosen:updated");
    });
}

$(document).on("change", ".Common_Employee_list", (e) => {
    
    var parent = $(e.currentTarget).parents("form");
    let EmployeeID = $(e.currentTarget).val();
    if (EmployeeID > 0) {
        $.get("/Admission/Employee/GetEmployeeShortinfo?EmployeeID=" + EmployeeID, (res) => {

            $(parent).find(".Common_ManageDepartment_list").val(res.ManageDepartmentID)
            $(parent).find(".Common_Department_list").val(res.DepartmentID)
            $(parent).find(".Common_JobTitle_list").val(res.JobTitleID)
        })
    }
});
$(document).on("change", ".Common_ManageDepartment_list", (e) => {
    

    var parent = $(e.currentTarget).parents("form");

    let ManageDepartmentID = $(e.currentTarget).val();
    let JobTitleID = $(parent).find(".Common_JobTitle_list").val();

    let DepartmentID = $(parent).find(".Common_Department_list").val();
    let EmployeeID = $(parent).find(".Common_Employee_list").val();
    $.get(`/Admission/Employee/GetEmployees_ByManagementDepartment?WorkSystemID=null&JobTitleID=${JobTitleID}&ManageDepartmentID=${ManageDepartmentID}&DepartmentID=${DepartmentID}`, (res) => {
        ReplaceOptionsInDropDownlist($(parent).find(".Common_Department_list"), res.ListOfDepartments);

        ReplaceOptionsInDropDownlist($(parent).find(".Common_Employee_list"), res.ListOfEmployees); 
        $(parent).find(".Common_Employee_list").val(EmployeeID > 0 && res.ListOfEmployees.map(x => x.ID).indexOf(parseInt(EmployeeID)) >= 0 ? EmployeeID : "");
        $(parent).find(".Common_Department_list").val(DepartmentID > 0 && res.ListOfDepartments.map(x => x.ID).indexOf(parseInt(DepartmentID)) >= 0 ? EmployeeID : "");
        $(parent).find(".Common_Employee_list").trigger("chosen:updated");
    });

});
$(document).on("change", ".Common_JobTitle_list , .Common_Department_list", (e) => {
      

    var parent = $(e.currentTarget).parents("form");

    let ManageDepartmentID = $(parent).find(".Common_ManageDepartment_list").val();
    let JobTitleID = $(parent).find(".Common_JobTitle_list").val();

    let DepartmentID = $(parent).find(".Common_Department_list").val();
    let EmployeeID = $(parent).find(".Common_Employee_list").val();
    $.get(`/Admission/Employee/GetEmployeesDropDownByConditions?WorkSystemID=null&JobTitleID=${JobTitleID}&ManageDepartmentID=${ManageDepartmentID}&DepartmentID=${DepartmentID}`, (res) => {
        ReplaceOptionsInDropDownlist($(parent).find(".Common_Employee_list"), res.ListOfEmployees);
        $(parent).find(".Common_Employee_list").val(EmployeeID > 0 && res.ListOfEmployees.map(x => x.ID).indexOf(parseInt(EmployeeID)) >= 0 ? EmployeeID : "");
        $(parent).find(".Common_Employee_list").trigger("chosen:updated");
    });
});
