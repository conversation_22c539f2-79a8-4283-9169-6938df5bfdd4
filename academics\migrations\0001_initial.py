# Generated by Django 5.2.4 on 2025-07-13 11:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Schedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('day_of_week', models.CharField(choices=[('monday', 'Monday'), ('tuesday', 'Tuesday'), ('wednesday', 'Wednesday'), ('thursday', 'Thursday'), ('friday', 'Friday'), ('saturday', 'Saturday'), ('sunday', 'Sunday')], max_length=10, verbose_name='Day of Week')),
                ('start_time', models.TimeField(verbose_name='Start Time')),
                ('end_time', models.TimeField(verbose_name='End Time')),
                ('room_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='Room Number')),
            ],
            options={
                'verbose_name': 'Schedule',
                'verbose_name_plural': 'Schedules',
                'ordering': ['day_of_week', 'start_time'],
            },
        ),
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=100, verbose_name='Subject Name')),
                ('name_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Subject Name (Arabic)')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Subject Code')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_mandatory', models.BooleanField(default=True, verbose_name='Is Mandatory')),
                ('credit_hours', models.PositiveIntegerField(default=1, verbose_name='Credit Hours')),
            ],
            options={
                'verbose_name': 'Subject',
                'verbose_name_plural': 'Subjects',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Teacher',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='Employee ID')),
                ('hire_date', models.DateField(verbose_name='Hire Date')),
                ('qualification', models.CharField(max_length=200, verbose_name='Qualification')),
                ('experience_years', models.PositiveIntegerField(default=0, verbose_name='Experience Years')),
                ('salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Salary')),
                ('department', models.CharField(blank=True, max_length=100, null=True, verbose_name='Department')),
            ],
            options={
                'verbose_name': 'Teacher',
                'verbose_name_plural': 'Teachers',
                'ordering': ['user__first_name', 'user__last_name'],
            },
        ),
        migrations.CreateModel(
            name='ClassSubject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('weekly_hours', models.PositiveIntegerField(default=1, verbose_name='Weekly Hours')),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='class_subjects', to='core.academicyear', verbose_name='Academic Year')),
            ],
            options={
                'verbose_name': 'Class Subject',
                'verbose_name_plural': 'Class Subjects',
                'ordering': ['class_obj', 'subject'],
            },
        ),
    ]
