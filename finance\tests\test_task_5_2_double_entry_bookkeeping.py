"""
Tests for Task 5.2: Develop Double-Entry Bookkeeping
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime

from core.models import School
from finance.models import (
    Account, AccountType, Transaction, TransactionEntry, TransactionAuditLog,
    CostCenter
)
from finance.services import DoubleEntryBookkeepingService, AccountValidationService

User = get_user_model()


class TestDoubleEntryBookkeepingTask(TestCase):
    """Test Double-Entry Bookkeeping implementation for Task 5.2"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="+**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create account types
        self.asset_type = AccountType.objects.create(
            school=self.school,
            name="Assets",
            type="asset"
        )
        
        self.revenue_type = AccountType.objects.create(
            school=self.school,
            name="Revenue",
            type="revenue"
        )
        
        # Create test accounts
        self.cash_account = Account.objects.create(
            school=self.school,
            code="1110",
            name="Cash",
            account_type=self.asset_type,
            opening_balance=Decimal('1000.00')
        )
        
        self.revenue_account = Account.objects.create(
            school=self.school,
            code="4100",
            name="Tuition Revenue",
            account_type=self.revenue_type
        )
        
        # Create cost center
        self.cost_center = CostCenter.objects.create(
            school=self.school,
            code="CC001",
            name="Administration",
            budget_amount=Decimal('10000.00')
        )
    
    def test_transaction_model_creation(self):
        """Test Transaction model creation and validation"""
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Test transaction',
            reference='REF001',
            total_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Test transaction properties
        self.assertEqual(transaction.school, self.school)
        self.assertEqual(transaction.transaction_type, 'manual')
        self.assertEqual(transaction.status, 'draft')
        self.assertEqual(transaction.total_amount, Decimal('500.00'))
        self.assertIsNotNone(transaction.transaction_id)
        self.assertTrue(transaction.transaction_id.startswith('TXN-'))
    
    def test_transaction_entry_model_creation(self):
        """Test TransactionEntry model creation and validation"""
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Test transaction',
            total_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Create debit entry
        debit_entry = TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.cash_account,
            entry_date=date.today(),
            description='Cash receipt',
            debit_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Create credit entry
        credit_entry = TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.revenue_account,
            entry_date=date.today(),
            description='Revenue recognition',
            credit_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Test entry properties
        self.assertEqual(debit_entry.amount, Decimal('500.00'))
        self.assertEqual(debit_entry.entry_type, 'debit')
        self.assertEqual(credit_entry.amount, Decimal('500.00'))
        self.assertEqual(credit_entry.entry_type, 'credit')
        self.assertFalse(debit_entry.is_posted)
        self.assertFalse(credit_entry.is_posted)
    
    def test_transaction_entry_validation(self):
        """Test TransactionEntry validation rules"""
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Test transaction',
            total_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Test validation: cannot have both debit and credit
        with self.assertRaises(ValidationError):
            entry = TransactionEntry(
                school=self.school,
                transaction=transaction,
                account=self.cash_account,
                entry_date=date.today(),
                description='Invalid entry',
                debit_amount=Decimal('100.00'),
                credit_amount=Decimal('100.00'),
                created_by=self.user
            )
            entry.full_clean()
        
        # Test validation: must have either debit or credit
        with self.assertRaises(ValidationError):
            entry = TransactionEntry(
                school=self.school,
                transaction=transaction,
                account=self.cash_account,
                entry_date=date.today(),
                description='Invalid entry',
                debit_amount=Decimal('0.00'),
                credit_amount=Decimal('0.00'),
                created_by=self.user
            )
            entry.full_clean()
    
    def test_transaction_balance_validation(self):
        """Test transaction balance validation"""
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Test transaction',
            total_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Create balanced entries
        TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.cash_account,
            entry_date=date.today(),
            description='Cash receipt',
            debit_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.revenue_account,
            entry_date=date.today(),
            description='Revenue recognition',
            credit_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Test balance validation
        self.assertTrue(transaction.is_balanced)
        self.assertEqual(transaction.total_debits, Decimal('500.00'))
        self.assertEqual(transaction.total_credits, Decimal('500.00'))
    
    def test_transaction_posting(self):
        """Test transaction posting functionality"""
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Test transaction',
            total_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Create entries
        debit_entry = TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.cash_account,
            entry_date=date.today(),
            description='Cash receipt',
            debit_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        credit_entry = TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.revenue_account,
            entry_date=date.today(),
            description='Revenue recognition',
            credit_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Test posting
        can_post, error = transaction.can_be_posted()
        self.assertTrue(can_post)
        self.assertIsNone(error)
        
        # Post transaction
        transaction.post(self.user)
        
        # Verify posting
        self.assertEqual(transaction.status, 'posted')
        self.assertEqual(transaction.posted_by, self.user)
        self.assertIsNotNone(transaction.posted_at)
        
        # Verify entries are posted
        debit_entry.refresh_from_db()
        credit_entry.refresh_from_db()
        self.assertTrue(debit_entry.is_posted)
        self.assertTrue(credit_entry.is_posted)
        
        # Verify account balances updated
        self.cash_account.refresh_from_db()
        self.assertEqual(self.cash_account.current_balance, Decimal('1500.00'))  # 1000 + 500
    
    def test_transaction_approval_workflow(self):
        """Test transaction approval workflow"""
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Test transaction requiring approval',
            total_amount=Decimal('500.00'),
            requires_approval=True,
            created_by=self.user
        )
        
        # Create entries
        TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.cash_account,
            entry_date=date.today(),
            description='Cash receipt',
            debit_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.revenue_account,
            entry_date=date.today(),
            description='Revenue recognition',
            credit_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Set status to pending approval
        transaction.status = 'pending_approval'
        transaction.save()
        
        # Test approval
        transaction.approve(self.user, 'Approved for testing')
        
        # Verify approval
        self.assertEqual(transaction.status, 'approved')
        self.assertEqual(transaction.approved_by, self.user)
        self.assertEqual(transaction.approval_notes, 'Approved for testing')
        self.assertIsNotNone(transaction.approved_at)
        
        # Now should be able to post
        can_post, error = transaction.can_be_posted()
        self.assertTrue(can_post)
    
    def test_transaction_cancellation(self):
        """Test transaction cancellation"""
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Test transaction',
            total_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Cancel transaction
        transaction.cancel(self.user, 'Cancelled for testing')
        
        # Verify cancellation
        self.assertEqual(transaction.status, 'cancelled')
        
        # Should not be able to post cancelled transaction
        can_post, error = transaction.can_be_posted()
        self.assertFalse(can_post)
        self.assertIn('Cancelled', str(error))
    
    def test_transaction_audit_log(self):
        """Test transaction audit logging"""
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Test transaction',
            total_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Create audit log entry
        transaction.create_audit_log('created', self.user, 'Transaction created')
        
        # Verify audit log
        audit_logs = transaction.audit_logs.all()
        self.assertEqual(audit_logs.count(), 1)
        
        audit_log = audit_logs.first()
        self.assertEqual(audit_log.action, 'created')
        self.assertEqual(audit_log.user, self.user)
        self.assertEqual(audit_log.notes, 'Transaction created')
    
    def test_double_entry_service_create_transaction(self):
        """Test DoubleEntryBookkeepingService.create_transaction"""
        transaction_data = {
            'transaction_date': date.today(),
            'transaction_type': 'manual',
            'description': 'Service test transaction',
            'reference': 'SVC001',
            'total_amount': Decimal('750.00'),
            'requires_approval': False
        }
        
        entries_data = [
            {
                'account_id': self.cash_account.id,
                'description': 'Cash receipt',
                'debit_amount': Decimal('750.00'),
                'cost_center_id': self.cost_center.id
            },
            {
                'account_id': self.revenue_account.id,
                'description': 'Revenue recognition',
                'credit_amount': Decimal('750.00')
            }
        ]
        
        # Create transaction using service
        transaction = DoubleEntryBookkeepingService.create_transaction(
            self.school, self.user, transaction_data, entries_data
        )
        
        # Verify transaction creation
        self.assertEqual(transaction.description, 'Service test transaction')
        self.assertEqual(transaction.total_amount, Decimal('750.00'))
        self.assertEqual(transaction.entries.count(), 2)
        self.assertTrue(transaction.is_balanced)
        
        # Verify entries
        debit_entry = transaction.entries.filter(debit_amount__gt=0).first()
        credit_entry = transaction.entries.filter(credit_amount__gt=0).first()
        
        self.assertEqual(debit_entry.account, self.cash_account)
        self.assertEqual(debit_entry.cost_center, self.cost_center)
        self.assertEqual(credit_entry.account, self.revenue_account)
    
    def test_double_entry_service_validation(self):
        """Test DoubleEntryBookkeepingService validation"""
        transaction_data = {
            'transaction_date': date.today(),
            'transaction_type': 'manual',
            'description': 'Unbalanced transaction',
            'total_amount': Decimal('500.00'),
            'requires_approval': False
        }
        
        # Create unbalanced entries
        entries_data = [
            {
                'account_id': self.cash_account.id,
                'description': 'Cash receipt',
                'debit_amount': Decimal('500.00')
            },
            {
                'account_id': self.revenue_account.id,
                'description': 'Revenue recognition',
                'credit_amount': Decimal('300.00')  # Unbalanced!
            }
        ]
        
        # Should raise validation error for unbalanced transaction
        with self.assertRaises(ValidationError) as context:
            DoubleEntryBookkeepingService.create_transaction(
                self.school, self.user, transaction_data, entries_data
            )
        
        self.assertIn('not balanced', str(context.exception))
    
    def test_double_entry_service_post_transaction(self):
        """Test DoubleEntryBookkeepingService.post_transaction"""
        # Create transaction
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Test transaction',
            total_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Create entries
        TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.cash_account,
            entry_date=date.today(),
            description='Cash receipt',
            debit_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.revenue_account,
            entry_date=date.today(),
            description='Revenue recognition',
            credit_amount=Decimal('500.00'),
            created_by=self.user
        )
        
        # Post using service
        result = DoubleEntryBookkeepingService.post_transaction(transaction, self.user)
        
        # Verify posting
        self.assertTrue(result)
        transaction.refresh_from_db()
        self.assertEqual(transaction.status, 'posted')
    
    def test_cost_center_integration(self):
        """Test cost center integration with transactions"""
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Cost center transaction',
            total_amount=Decimal('200.00'),
            created_by=self.user
        )
        
        # Create entry with cost center
        entry = TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.cash_account,
            entry_date=date.today(),
            description='Expense with cost center',
            debit_amount=Decimal('200.00'),
            cost_center=self.cost_center,
            created_by=self.user
        )
        
        # Verify cost center assignment
        self.assertEqual(entry.cost_center, self.cost_center)
        
        # Test cost center expense calculation
        initial_expenses = self.cost_center.total_expenses
        
        # Post the transaction
        TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.revenue_account,
            entry_date=date.today(),
            description='Revenue recognition',
            credit_amount=Decimal('200.00'),
            created_by=self.user
        )
        
        transaction.post(self.user)
        
        # Verify cost center expense tracking
        self.cost_center.refresh_from_db()
        # Note: This would require the cost center to track expenses from posted entries
    
    def test_transaction_id_generation(self):
        """Test automatic transaction ID generation"""
        transaction1 = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='First transaction',
            total_amount=Decimal('100.00'),
            created_by=self.user
        )
        
        transaction2 = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Second transaction',
            total_amount=Decimal('200.00'),
            created_by=self.user
        )
        
        # Verify unique transaction IDs
        self.assertIsNotNone(transaction1.transaction_id)
        self.assertIsNotNone(transaction2.transaction_id)
        self.assertNotEqual(transaction1.transaction_id, transaction2.transaction_id)
        
        # Verify format
        self.assertTrue(transaction1.transaction_id.startswith('TXN-'))
        self.assertTrue(transaction2.transaction_id.startswith('TXN-'))
    
    def test_header_account_validation(self):
        """Test that header accounts cannot be used in transactions"""
        # Create header account
        header_account = Account.objects.create(
            school=self.school,
            code="1000",
            name="Assets Header",
            account_type=self.asset_type,
            is_header=True
        )
        
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Test transaction',
            total_amount=Decimal('100.00'),
            created_by=self.user
        )
        
        # Should not be able to create entry with header account
        with self.assertRaises(ValidationError):
            entry = TransactionEntry(
                school=self.school,
                transaction=transaction,
                account=header_account,
                entry_date=date.today(),
                description='Invalid entry',
                debit_amount=Decimal('100.00'),
                created_by=self.user
            )
            entry.full_clean()