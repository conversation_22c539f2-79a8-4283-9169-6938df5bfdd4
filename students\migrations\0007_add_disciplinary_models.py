# Generated by Django 5.2.4 on 2025-07-30 09:26

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("students", "0006_make_student_optional_in_document"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DisciplinaryAction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("verbal_warning", "Verbal Warning"),
                            ("written_warning", "Written Warning"),
                            ("detention", "Detention"),
                            ("community_service", "Community Service"),
                            ("parent_conference", "Parent Conference"),
                            ("counseling", "Counseling"),
                            ("suspension", "Suspension"),
                            ("expulsion", "Expulsion"),
                        ],
                        max_length=30,
                        verbose_name="Action Type",
                    ),
                ),
                ("description", models.TextField(verbose_name="Action Description")),
                ("assigned_date", models.DateField(verbose_name="Assigned Date")),
                (
                    "due_date",
                    models.DateField(blank=True, null=True, verbose_name="Due Date"),
                ),
                (
                    "completed_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Completed Date"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "completion_notes",
                    models.TextField(
                        blank=True, null=True, verbose_name="Completion Notes"
                    ),
                ),
                (
                    "assigned_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assigned_disciplinary_actions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Assigned By",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "infraction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="disciplinary_actions",
                        to="students.studentinfraction",
                        verbose_name="Related Infraction",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "supervised_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="supervised_disciplinary_actions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Supervised By",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Disciplinary Action",
                "verbose_name_plural": "Disciplinary Actions",
                "ordering": ["-assigned_date"],
            },
        ),
        migrations.CreateModel(
            name="DisciplineReport",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("student_summary", "Student Discipline Summary"),
                            ("class_summary", "Class Discipline Summary"),
                            ("school_summary", "School Discipline Summary"),
                            ("infraction_trends", "Infraction Trends"),
                            ("action_effectiveness", "Action Effectiveness"),
                        ],
                        max_length=30,
                        verbose_name="Report Type",
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="Report Title"),
                ),
                ("start_date", models.DateField(verbose_name="Start Date")),
                ("end_date", models.DateField(verbose_name="End Date")),
                (
                    "filters",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Report Filters"
                    ),
                ),
                (
                    "report_data",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Report Data"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="generated_discipline_reports",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Generated By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Discipline Report",
                "verbose_name_plural": "Discipline Reports",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="StudentSuspension",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "suspension_type",
                    models.CharField(
                        choices=[
                            ("in_school", "In-School Suspension"),
                            ("out_of_school", "Out-of-School Suspension"),
                            ("indefinite", "Indefinite Suspension"),
                        ],
                        max_length=20,
                        verbose_name="Suspension Type",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="Start Date")),
                (
                    "end_date",
                    models.DateField(blank=True, null=True, verbose_name="End Date"),
                ),
                ("reason", models.TextField(verbose_name="Reason for Suspension")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "parent_notified",
                    models.BooleanField(default=False, verbose_name="Parent Notified"),
                ),
                (
                    "parent_notified_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Parent Notified At"
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, null=True, verbose_name="Additional Notes"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "issued_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="issued_suspensions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Issued By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="suspensions",
                        to="students.student",
                        verbose_name="Student",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Student Suspension",
                "verbose_name_plural": "Student Suspensions",
                "ordering": ["-start_date"],
            },
        ),
    ]
