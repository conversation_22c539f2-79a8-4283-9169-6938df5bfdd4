# Generated by Django 5.2.4 on 2025-07-31 09:10

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0009_biometricdevice_alter_studentattendance_options_and_more"),
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("students", "0008_grade_max_age_grade_max_capacity_grade_min_age"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Assignment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.<PERSON><PERSON>anField(default=True, verbose_name="Is Active"),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="Assignment Title"),
                ),
                (
                    "description",
                    models.TextField(
                        help_text="Detailed description of the assignment",
                        verbose_name="Description",
                    ),
                ),
                (
                    "assignment_type",
                    models.CharField(
                        choices=[
                            ("homework", "Homework"),
                            ("project", "Project"),
                            ("essay", "Essay"),
                            ("research", "Research Paper"),
                            ("presentation", "Presentation"),
                            ("lab_report", "Lab Report"),
                            ("case_study", "Case Study"),
                            ("group_work", "Group Work"),
                            ("quiz", "Quiz"),
                            ("other", "Other"),
                        ],
                        default="homework",
                        max_length=20,
                        verbose_name="Assignment Type",
                    ),
                ),
                (
                    "difficulty_level",
                    models.CharField(
                        choices=[
                            ("easy", "Easy"),
                            ("medium", "Medium"),
                            ("hard", "Hard"),
                            ("expert", "Expert"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="Difficulty Level",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("published", "Published"),
                            ("closed", "Closed"),
                            ("graded", "Graded"),
                            ("archived", "Archived"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "instructions",
                    models.TextField(
                        blank=True,
                        help_text="Specific instructions for completing the assignment",
                        null=True,
                        verbose_name="Instructions",
                    ),
                ),
                (
                    "max_score",
                    models.DecimalField(
                        decimal_places=2,
                        default=100.0,
                        max_digits=6,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(1000),
                        ],
                        verbose_name="Maximum Score",
                    ),
                ),
                (
                    "weight_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=10.0,
                        help_text="Percentage weight in final grade calculation",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Weight Percentage",
                    ),
                ),
                (
                    "assigned_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Assigned Date"
                    ),
                ),
                ("due_date", models.DateTimeField(verbose_name="Due Date")),
                (
                    "late_submission_allowed",
                    models.BooleanField(
                        default=True, verbose_name="Allow Late Submission"
                    ),
                ),
                (
                    "late_penalty_per_day",
                    models.DecimalField(
                        decimal_places=2,
                        default=5.0,
                        help_text="Percentage penalty per day for late submission",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Late Penalty Per Day (%)",
                    ),
                ),
                (
                    "max_late_days",
                    models.PositiveIntegerField(
                        default=3,
                        help_text="Maximum number of days late submission is accepted",
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(30),
                        ],
                        verbose_name="Maximum Late Days",
                    ),
                ),
                (
                    "allow_resubmission",
                    models.BooleanField(
                        default=False, verbose_name="Allow Resubmission"
                    ),
                ),
                (
                    "max_resubmissions",
                    models.PositiveIntegerField(
                        default=1,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(10),
                        ],
                        verbose_name="Maximum Resubmissions",
                    ),
                ),
                (
                    "group_assignment",
                    models.BooleanField(default=False, verbose_name="Group Assignment"),
                ),
                (
                    "max_group_size",
                    models.PositiveIntegerField(
                        default=1,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(20),
                        ],
                        verbose_name="Maximum Group Size",
                    ),
                ),
                (
                    "submission_format",
                    models.CharField(
                        default="pdf",
                        help_text="Required file format(s) for submission (e.g., pdf, docx, txt)",
                        max_length=100,
                        verbose_name="Submission Format",
                    ),
                ),
                (
                    "max_file_size_mb",
                    models.PositiveIntegerField(
                        default=10,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Maximum File Size (MB)",
                    ),
                ),
                (
                    "plagiarism_check_enabled",
                    models.BooleanField(
                        default=True, verbose_name="Enable Plagiarism Check"
                    ),
                ),
                (
                    "plagiarism_threshold",
                    models.DecimalField(
                        decimal_places=2,
                        default=20.0,
                        help_text="Similarity percentage threshold for plagiarism detection",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Plagiarism Threshold (%)",
                    ),
                ),
                (
                    "auto_grade_enabled",
                    models.BooleanField(
                        default=False,
                        help_text="Enable automatic grading for objective assignments",
                        verbose_name="Enable Auto Grading",
                    ),
                ),
                (
                    "rubric",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="JSON structure defining grading criteria and points",
                        verbose_name="Grading Rubric",
                    ),
                ),
                (
                    "resources",
                    models.TextField(
                        blank=True,
                        help_text="Additional resources, links, or references for the assignment",
                        null=True,
                        verbose_name="Resources",
                    ),
                ),
                (
                    "estimated_duration_hours",
                    models.DecimalField(
                        decimal_places=1,
                        default=2.0,
                        max_digits=4,
                        validators=[
                            django.core.validators.MinValueValidator(0.1),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                        verbose_name="Estimated Duration (Hours)",
                    ),
                ),
                (
                    "learning_objectives",
                    models.TextField(
                        blank=True,
                        help_text="What students should learn from this assignment",
                        null=True,
                        verbose_name="Learning Objectives",
                    ),
                ),
                (
                    "class_subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assignments",
                        to="academics.classsubject",
                        verbose_name="Class Subject",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Assignment",
                "verbose_name_plural": "Assignments",
                "ordering": ["-assigned_date"],
            },
        ),
        migrations.CreateModel(
            name="AssignmentAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "analytics_type",
                    models.CharField(
                        choices=[
                            ("assignment", "Assignment Analytics"),
                            ("student", "Student Performance"),
                            ("class", "Class Performance"),
                            ("subject", "Subject Analytics"),
                        ],
                        max_length=20,
                        verbose_name="Analytics Type",
                    ),
                ),
                ("data", models.JSONField(default=dict, verbose_name="Analytics Data")),
                (
                    "generated_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Generated At"
                    ),
                ),
                (
                    "insights",
                    models.JSONField(default=list, verbose_name="Generated Insights"),
                ),
                (
                    "recommendations",
                    models.JSONField(default=list, verbose_name="Recommendations"),
                ),
                (
                    "assignment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="academics.assignment",
                        verbose_name="Assignment",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Assignment Analytics",
                "verbose_name_plural": "Assignment Analytics",
                "ordering": ["-generated_at"],
            },
        ),
        migrations.CreateModel(
            name="AssignmentGroup",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Group Name")),
                (
                    "is_finalized",
                    models.BooleanField(
                        default=False,
                        help_text="Whether group membership is finalized",
                        verbose_name="Is Finalized",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "assignment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="groups",
                        to="academics.assignment",
                        verbose_name="Assignment",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "leader",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="led_groups",
                        to="students.student",
                        verbose_name="Group Leader",
                    ),
                ),
                (
                    "members",
                    models.ManyToManyField(
                        related_name="assignment_groups",
                        to="students.student",
                        verbose_name="Group Members",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Assignment Group",
                "verbose_name_plural": "Assignment Groups",
                "ordering": ["assignment", "name"],
            },
        ),
        migrations.CreateModel(
            name="AssignmentSubmission",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "submission_text",
                    models.TextField(
                        blank=True,
                        help_text="Text content of the submission",
                        null=True,
                        verbose_name="Submission Text",
                    ),
                ),
                (
                    "submission_file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="assignment_submissions/",
                        verbose_name="Submission File",
                    ),
                ),
                (
                    "additional_files",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of additional file paths for multi-file submissions",
                        verbose_name="Additional Files",
                    ),
                ),
                (
                    "submitted_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Submitted At"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("submitted", "Submitted"),
                            ("late", "Late Submission"),
                            ("resubmitted", "Resubmitted"),
                            ("graded", "Graded"),
                            ("returned", "Returned for Revision"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "is_late",
                    models.BooleanField(
                        default=False, verbose_name="Is Late Submission"
                    ),
                ),
                (
                    "late_penalty_applied",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Late Penalty Applied (%)",
                    ),
                ),
                (
                    "score",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=6,
                        null=True,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Score",
                    ),
                ),
                (
                    "adjusted_score",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Score after applying late penalties and adjustments",
                        max_digits=6,
                        null=True,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Adjusted Score",
                    ),
                ),
                (
                    "percentage",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Percentage",
                    ),
                ),
                (
                    "grade_letter",
                    models.CharField(
                        blank=True, max_length=5, null=True, verbose_name="Grade Letter"
                    ),
                ),
                (
                    "is_graded",
                    models.BooleanField(default=False, verbose_name="Is Graded"),
                ),
                (
                    "graded_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Graded At"
                    ),
                ),
                (
                    "feedback",
                    models.TextField(
                        blank=True,
                        help_text="Teacher feedback on the submission",
                        null=True,
                        verbose_name="Feedback",
                    ),
                ),
                (
                    "rubric_scores",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Detailed scores for each rubric criterion",
                        verbose_name="Rubric Scores",
                    ),
                ),
                (
                    "plagiarism_score",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Plagiarism Score (%)",
                    ),
                ),
                (
                    "plagiarism_report",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Detailed plagiarism detection results",
                        verbose_name="Plagiarism Report",
                    ),
                ),
                (
                    "similarity_sources",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Sources with high similarity detected",
                        verbose_name="Similarity Sources",
                    ),
                ),
                (
                    "submission_count",
                    models.PositiveIntegerField(
                        default=1,
                        help_text="Number of times this assignment has been submitted",
                        verbose_name="Submission Count",
                    ),
                ),
                (
                    "word_count",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Word Count"
                    ),
                ),
                (
                    "file_size_bytes",
                    models.PositiveBigIntegerField(
                        blank=True, null=True, verbose_name="File Size (Bytes)"
                    ),
                ),
                (
                    "time_spent_minutes",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Estimated time spent on the assignment",
                        null=True,
                        verbose_name="Time Spent (Minutes)",
                    ),
                ),
                (
                    "assignment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="submissions",
                        to="academics.assignment",
                        verbose_name="Assignment",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "graded_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="graded_submissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Graded By",
                    ),
                ),
                (
                    "group_members",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Other students in the group (for group assignments)",
                        related_name="group_submissions",
                        to="students.student",
                        verbose_name="Group Members",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assignment_submissions",
                        to="students.student",
                        verbose_name="Student",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Assignment Submission",
                "verbose_name_plural": "Assignment Submissions",
                "ordering": ["-submitted_at"],
            },
        ),
        migrations.AddIndex(
            model_name="assignment",
            index=models.Index(
                fields=["school", "class_subject", "status"],
                name="academics_a_school__f86bca_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="assignment",
            index=models.Index(
                fields=["due_date", "status"], name="academics_a_due_dat_e6e6f4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="assignment",
            index=models.Index(
                fields=["assigned_date"], name="academics_a_assigne_6637a2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="assignmentanalytics",
            index=models.Index(
                fields=["assignment", "analytics_type"],
                name="academics_a_assignm_7a09c9_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="assignmentanalytics",
            index=models.Index(
                fields=["generated_at"], name="academics_a_generat_ca37a7_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="assignmentgroup",
            unique_together={("assignment", "name")},
        ),
        migrations.AddIndex(
            model_name="assignmentsubmission",
            index=models.Index(
                fields=["school", "assignment", "status"],
                name="academics_a_school__c0bfdd_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="assignmentsubmission",
            index=models.Index(
                fields=["student", "is_graded"], name="academics_a_student_3c5824_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="assignmentsubmission",
            index=models.Index(
                fields=["submitted_at"], name="academics_a_submitt_e5e45b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="assignmentsubmission",
            index=models.Index(
                fields=["plagiarism_score"], name="academics_a_plagiar_adeb76_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="assignmentsubmission",
            unique_together={("assignment", "student")},
        ),
    ]
