# Generated by Django 5.2.4 on 2025-07-13 11:56

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
        ('students', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=100, verbose_name='Account Type Name')),
                ('name_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Account Type Name (Arabic)')),
                ('type', models.CharField(choices=[('asset', 'Asset'), ('liability', 'Liability'), ('equity', 'Equity'), ('revenue', 'Revenue'), ('expense', 'Expense')], max_length=20, verbose_name='Type')),
            ],
            options={
                'verbose_name': 'Account Type',
                'verbose_name_plural': 'Account Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Account Code')),
                ('name', models.CharField(max_length=200, verbose_name='Account Name')),
                ('name_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Account Name (Arabic)')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_header', models.BooleanField(default=False, verbose_name='Is Header Account')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='finance.account', verbose_name='Parent Account')),
                ('account_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='accounts', to='finance.accounttype', verbose_name='Account Type')),
            ],
            options={
                'verbose_name': 'Account',
                'verbose_name_plural': 'Accounts',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='FeeType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('name', models.CharField(max_length=100, verbose_name='Fee Type Name')),
                ('name_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Fee Type Name (Arabic)')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_mandatory', models.BooleanField(default=True, verbose_name='Is Mandatory')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fee_types', to='finance.account', verbose_name='Account')),
            ],
            options={
                'verbose_name': 'Fee Type',
                'verbose_name_plural': 'Fee Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='GradeFee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Amount')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='Due Date')),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grade_fees', to='core.academicyear', verbose_name='Academic Year')),
                ('fee_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grade_fees', to='finance.feetype', verbose_name='Fee Type')),
                ('grade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fees', to='students.grade', verbose_name='Grade')),
            ],
            options={
                'verbose_name': 'Grade Fee',
                'verbose_name_plural': 'Grade Fees',
                'ordering': ['grade', 'fee_type'],
                'unique_together': {('grade', 'fee_type', 'academic_year')},
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('receipt_number', models.CharField(max_length=50, unique=True, verbose_name='Receipt Number')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Amount')),
                ('payment_date', models.DateField(verbose_name='Payment Date')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('bank_transfer', 'Bank Transfer'), ('check', 'Check'), ('credit_card', 'Credit Card'), ('online', 'Online Payment')], max_length=20, verbose_name='Payment Method')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='Reference Number')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('received_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_payments', to=settings.AUTH_USER_MODEL, verbose_name='Received By')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='students.student', verbose_name='Student')),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='StudentFee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Amount')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Discount Amount')),
                ('due_date', models.DateField(verbose_name='Due Date')),
                ('is_paid', models.BooleanField(default=False, verbose_name='Is Paid')),
                ('paid_date', models.DateField(blank=True, null=True, verbose_name='Paid Date')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('grade_fee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_fees', to='finance.gradefee', verbose_name='Grade Fee')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fees', to='students.student', verbose_name='Student')),
            ],
            options={
                'verbose_name': 'Student Fee',
                'verbose_name_plural': 'Student Fees',
                'ordering': ['student', 'due_date'],
                'unique_together': {('student', 'grade_fee')},
            },
        ),
        migrations.CreateModel(
            name='PaymentItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Amount')),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='finance.payment', verbose_name='Payment')),
                ('student_fee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_items', to='finance.studentfee', verbose_name='Student Fee')),
            ],
            options={
                'verbose_name': 'Payment Item',
                'verbose_name_plural': 'Payment Items',
                'ordering': ['payment', 'student_fee'],
            },
        ),
    ]
