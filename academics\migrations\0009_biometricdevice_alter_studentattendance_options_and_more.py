# Generated by Django 5.2.4 on 2025-07-31 08:29

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0008_remove_exam_room_number_exam_room_examanalytics_and_more"),
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("students", "0008_grade_max_age_grade_max_capacity_grade_min_age"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BiometricDevice",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.Bo<PERSON>anField(default=True, verbose_name="Is Active"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Device Name")),
                (
                    "device_type",
                    models.CharField(
                        choices=[
                            ("fingerprint", "Fingerprint Scanner"),
                            ("facial_recognition", "Facial Recognition"),
                            ("iris_scanner", "Iris Scanner"),
                            ("palm_scanner", "Palm Scanner"),
                        ],
                        max_length=20,
                        verbose_name="Device Type",
                    ),
                ),
                (
                    "device_id",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Device ID"
                    ),
                ),
                (
                    "location",
                    models.CharField(max_length=100, verbose_name="Device Location"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP Address"
                    ),
                ),
                (
                    "port",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Port"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("maintenance", "Under Maintenance"),
                            ("error", "Error"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Device Status",
                    ),
                ),
                (
                    "last_sync",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Sync"
                    ),
                ),
                (
                    "configuration",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Device Configuration"
                    ),
                ),
            ],
            options={
                "verbose_name": "Biometric Device",
                "verbose_name_plural": "Biometric Devices",
                "ordering": ["name"],
            },
        ),
        migrations.AlterModelOptions(
            name="studentattendance",
            options={
                "ordering": ["-session__session_date", "student__first_name"],
                "verbose_name": "Student Attendance",
                "verbose_name_plural": "Student Attendance",
            },
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="arrival_time",
            field=models.TimeField(blank=True, null=True, verbose_name="Arrival Time"),
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="biometric_data",
            field=models.JSONField(
                blank=True,
                default=dict,
                help_text="Biometric verification data (fingerprint hash, etc.)",
                verbose_name="Biometric Data",
            ),
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="departure_time",
            field=models.TimeField(
                blank=True, null=True, verbose_name="Departure Time"
            ),
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="device_info",
            field=models.JSONField(
                blank=True,
                default=dict,
                help_text="Information about the device used for marking attendance",
                verbose_name="Device Information",
            ),
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="location_data",
            field=models.JSONField(
                blank=True,
                default=dict,
                help_text="GPS coordinates or location information",
                verbose_name="Location Data",
            ),
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="marked_at",
            field=models.DateTimeField(blank=True, null=True, verbose_name="Marked At"),
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="marking_method",
            field=models.CharField(
                choices=[
                    ("manual", "Manual Entry"),
                    ("biometric", "Biometric Scanner"),
                    ("qr_code", "QR Code Scan"),
                    ("rfid", "RFID Card"),
                    ("mobile_app", "Mobile App"),
                    ("facial_recognition", "Facial Recognition"),
                ],
                default="manual",
                max_length=20,
                verbose_name="Marking Method",
            ),
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="notification_sent_at",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="Notification Sent At"
            ),
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="parent_notified",
            field=models.BooleanField(default=False, verbose_name="Parent Notified"),
        ),
        migrations.AlterField(
            model_name="studentattendance",
            name="class_subject",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="student_attendance",
                to="academics.classsubject",
                verbose_name="Class Subject",
            ),
        ),
        migrations.AlterField(
            model_name="studentattendance",
            name="marked_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="marked_attendance",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Marked By",
            ),
        ),
        migrations.AlterField(
            model_name="studentattendance",
            name="status",
            field=models.CharField(
                choices=[
                    ("present", "Present"),
                    ("absent", "Absent"),
                    ("late", "Late"),
                    ("excused", "Excused"),
                    ("sick", "Sick"),
                    ("authorized_absence", "Authorized Absence"),
                ],
                default="absent",
                max_length=20,
                verbose_name="Attendance Status",
            ),
        ),
        migrations.AlterField(
            model_name="studentattendance",
            name="student",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="attendance_records",
                to="students.student",
                verbose_name="Student",
            ),
        ),
        migrations.CreateModel(
            name="AttendanceRule",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Rule Name")),
                (
                    "rule_type",
                    models.CharField(
                        choices=[
                            ("minimum_attendance", "Minimum Attendance Percentage"),
                            ("late_threshold", "Late Arrival Threshold (minutes)"),
                            ("absence_notification", "Absence Notification Rules"),
                            ("makeup_policy", "Makeup Session Policy"),
                        ],
                        max_length=30,
                        verbose_name="Rule Type",
                    ),
                ),
                (
                    "applicable_to",
                    models.CharField(
                        choices=[
                            ("all", "All Classes"),
                            ("grade", "Specific Grade"),
                            ("class", "Specific Class"),
                            ("subject", "Specific Subject"),
                        ],
                        default="all",
                        max_length=20,
                        verbose_name="Applicable To",
                    ),
                ),
                (
                    "rule_value",
                    models.DecimalField(
                        decimal_places=2, max_digits=5, verbose_name="Rule Value"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("effective_from", models.DateField(verbose_name="Effective From")),
                (
                    "effective_to",
                    models.DateField(
                        blank=True, null=True, verbose_name="Effective To"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "target_classes",
                    models.ManyToManyField(
                        blank=True,
                        related_name="attendance_rules",
                        to="students.class",
                        verbose_name="Target Classes",
                    ),
                ),
                (
                    "target_grades",
                    models.ManyToManyField(
                        blank=True,
                        related_name="attendance_rules",
                        to="students.grade",
                        verbose_name="Target Grades",
                    ),
                ),
                (
                    "target_subjects",
                    models.ManyToManyField(
                        blank=True,
                        related_name="attendance_rules",
                        to="academics.subject",
                        verbose_name="Target Subjects",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Attendance Rule",
                "verbose_name_plural": "Attendance Rules",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="AttendanceSession",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("session_date", models.DateField(verbose_name="Session Date")),
                ("start_time", models.TimeField(verbose_name="Start Time")),
                ("end_time", models.TimeField(verbose_name="End Time")),
                (
                    "session_topic",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Session Topic",
                    ),
                ),
                (
                    "attendance_method",
                    models.CharField(
                        choices=[
                            ("manual", "Manual Entry"),
                            ("biometric", "Biometric Scanner"),
                            ("qr_code", "QR Code Scan"),
                            ("rfid", "RFID Card"),
                            ("mobile_app", "Mobile App"),
                            ("facial_recognition", "Facial Recognition"),
                        ],
                        default="manual",
                        max_length=20,
                        verbose_name="Attendance Method",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="scheduled",
                        max_length=20,
                        verbose_name="Session Status",
                    ),
                ),
                (
                    "qr_code_token",
                    models.UUIDField(
                        default=uuid.uuid4, unique=True, verbose_name="QR Code Token"
                    ),
                ),
                (
                    "qr_code_expires_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="QR Code Expiry"
                    ),
                ),
                (
                    "location",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Session Location",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, null=True, verbose_name="Session Notes"
                    ),
                ),
                (
                    "total_students",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Total Students"
                    ),
                ),
                (
                    "present_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Present Count"
                    ),
                ),
                (
                    "absent_count",
                    models.PositiveIntegerField(default=0, verbose_name="Absent Count"),
                ),
                (
                    "late_count",
                    models.PositiveIntegerField(default=0, verbose_name="Late Count"),
                ),
                (
                    "excused_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Excused Count"
                    ),
                ),
                (
                    "class_subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendance_sessions",
                        to="academics.classsubject",
                        verbose_name="Class Subject",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Attendance Session",
                "verbose_name_plural": "Attendance Sessions",
                "ordering": ["-session_date", "-start_time"],
            },
        ),
        migrations.AlterUniqueTogether(
            name="studentattendance",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="studentattendance",
            name="session",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="attendance_records",
                to="academics.attendancesession",
                verbose_name="Attendance Session",
            ),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name="studentattendance",
            unique_together={("session", "student")},
        ),
        migrations.AddIndex(
            model_name="studentattendance",
            index=models.Index(
                fields=["school", "student"], name="academics_s_school__28e580_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="studentattendance",
            index=models.Index(
                fields=["class_subject", "status"],
                name="academics_s_class_s_86223b_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="studentattendance",
            index=models.Index(
                fields=["marked_at"], name="academics_s_marked__36f375_idx"
            ),
        ),
        migrations.AddField(
            model_name="biometricdevice",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="biometricdevice",
            name="school",
            field=models.ForeignKey(
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
        ),
        migrations.AddField(
            model_name="biometricdevice",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddIndex(
            model_name="attendancerule",
            index=models.Index(
                fields=["school", "rule_type", "is_active"],
                name="academics_a_school__77b4de_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="attendancerule",
            index=models.Index(
                fields=["effective_from", "effective_to"],
                name="academics_a_effecti_92a36a_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="attendancesession",
            index=models.Index(
                fields=["school", "session_date"], name="academics_a_school__a25b52_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="attendancesession",
            index=models.Index(
                fields=["class_subject", "status"],
                name="academics_a_class_s_c8fad0_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="attendancesession",
            index=models.Index(
                fields=["qr_code_token"], name="academics_a_qr_code_c97458_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="attendancesession",
            unique_together={("class_subject", "session_date", "start_time")},
        ),
        migrations.RemoveField(
            model_name="studentattendance",
            name="date",
        ),
        migrations.AddIndex(
            model_name="biometricdevice",
            index=models.Index(
                fields=["school", "status"], name="academics_b_school__4d24ce_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="biometricdevice",
            index=models.Index(
                fields=["device_type"], name="academics_b_device__58590f_idx"
            ),
        ),
    ]
