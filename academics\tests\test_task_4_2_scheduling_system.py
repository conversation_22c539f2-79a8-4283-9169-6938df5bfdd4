"""
Test cases for Task 4.2: Develop Class Scheduling System

This test file validates the implementation of:
- Timetable generation algorithms
- Resource conflict detection
- Schedule optimization features
- Teacher availability management
- Room allocation system

Requirements: 3.2
"""

import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from datetime import date, time, timedelta
from decimal import Decimal

from core.models import School, AcademicYear, Semester
from students.models import Grade, Class
from academics.models import (
    Subject, Teacher, ClassSubject, Schedule, Room, TeacherAvailability,
    TimetableTemplate
)
from academics.scheduling import (
    TimetableGenerator, ResourceAllocator, ScheduleAnalyzer,
    SchedulingConflict, SchedulingConstraint
)

User = get_user_model()


class Task42SchedulingSystemTest(TestCase):
    """
    Comprehensive test cases for Task 4.2 Scheduling System
    """
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.semester = Semester.objects.create(
            school=self.school,
            academic_year=self.academic_year,
            name="First Semester",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1,
            max_capacity=60
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )
        
        # Create teacher
        self.teacher_user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        self.teacher = Teacher.objects.create(
            school=self.school,
            user=self.teacher_user,
            employee_id="T001",
            hire_date=date.today(),
            qualification="Master's in Mathematics"
        )
        
        # Create subject
        self.subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH101",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        
        self.teacher.subjects.add(self.subject)
        
        # Create class
        self.class_obj = Class.objects.create(
            school=self.school,
            name="Section A",
            grade=self.grade,
            academic_year=self.academic_year,
            class_teacher=self.teacher_user,
            max_students=30
        )
        
        # Create class subject
        self.class_subject = ClassSubject.objects.create(
            school=self.school,
            class_obj=self.class_obj,
            subject=self.subject,
            teacher=self.teacher,
            academic_year=self.academic_year,
            semester=self.semester,
            weekly_hours=4
        )

    def test_room_model_and_functionality(self):
        """Test Room model with scheduling features"""
        # Create room
        room = Room.objects.create(
            school=self.school,
            name="Math Lab",
            room_number="101",
            room_type="laboratory",
            capacity=30,
            floor="1",
            building="Main Building",
            equipment="Projector, Whiteboard, Computers",
            has_projector=True,
            has_computer=True,
            has_internet=True,
            has_whiteboard=True,
            is_accessible=True,
            is_available=True
        )
        
        # Test room properties
        self.assertEqual(room.name, "Math Lab")
        self.assertEqual(room.room_number, "101")
        self.assertEqual(room.room_type, "laboratory")
        self.assertEqual(room.capacity, 30)
        self.assertTrue(room.has_projector)
        self.assertTrue(room.is_available)
        
        # Test room suitability for subject
        lab_subject = Subject.objects.create(
            school=self.school,
            name="Computer Science",
            code="CS101",
            requires_lab=True,
            requires_special_equipment=True,
            equipment_requirements="Projector and computers",
            created_by=self.user
        )
        
        self.assertTrue(room.is_suitable_for_subject(lab_subject))
        self.assertTrue(room.is_suitable_for_subject(self.subject))  # Regular subject
        
        # Test room availability
        availability = room.get_availability_for_day('monday', self.academic_year)
        self.assertEqual(len(availability), 0)  # No schedules yet

    def test_teacher_availability_model(self):
        """Test TeacherAvailability model and functionality"""
        # Create teacher availability
        availability = TeacherAvailability.objects.create(
            school=self.school,
            teacher=self.teacher,
            day_of_week='monday',
            start_time=time(8, 0),
            end_time=time(16, 0),
            is_preferred=True,
            max_consecutive_hours=4,
            break_duration=15,
            academic_year=self.academic_year
        )
        
        # Test availability properties
        self.assertEqual(availability.teacher, self.teacher)
        self.assertEqual(availability.day_of_week, 'monday')
        self.assertEqual(availability.start_time, time(8, 0))
        self.assertEqual(availability.end_time, time(16, 0))
        self.assertTrue(availability.is_preferred)
        
        # Test duration calculation
        duration = availability.get_available_duration_minutes()
        self.assertEqual(duration, 480)  # 8 hours = 480 minutes
        
        # Test conflict detection
        self.assertFalse(availability.conflicts_with_schedule(time(7, 0), time(8, 0)))
        self.assertTrue(availability.conflicts_with_schedule(time(9, 0), time(10, 0)))
        self.assertFalse(availability.conflicts_with_schedule(time(16, 0), time(17, 0)))

    def test_timetable_template_model(self):
        """Test TimetableTemplate model and time slot generation"""
        # Create timetable template
        template = TimetableTemplate.objects.create(
            school=self.school,
            name="Standard Template",
            academic_year=self.academic_year,
            periods_per_day=8,
            period_duration=45,
            break_duration=15,
            lunch_break_duration=45,
            school_start_time=time(8, 0),
            school_end_time=time(15, 30),
            working_days=['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            is_active=True
        )
        
        # Test template properties
        self.assertEqual(template.name, "Standard Template")
        self.assertEqual(template.periods_per_day, 8)
        self.assertEqual(template.period_duration, 45)
        self.assertTrue(template.is_active)
        
        # Test time slot generation
        time_slots = template.generate_time_slots()
        self.assertEqual(len(time_slots), 8)
        
        # Check first period
        first_slot = time_slots[0]
        self.assertEqual(first_slot['period'], 1)
        self.assertEqual(first_slot['start_time'], time(8, 0))
        self.assertEqual(first_slot['end_time'], time(8, 45))
        
        # Check that slots are properly spaced with breaks
        self.assertIsInstance(time_slots[0]['start_time'], time)
        self.assertIsInstance(time_slots[0]['end_time'], time)

    def test_enhanced_schedule_model(self):
        """Test enhanced Schedule model with conflict detection"""
        # Create room
        room = Room.objects.create(
            school=self.school,
            name="Classroom 1",
            room_number="101",
            capacity=30,
            is_available=True
        )
        
        # Create schedule
        schedule = Schedule.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            day_of_week='monday',
            start_time=time(9, 0),
            end_time=time(10, 0),
            room=room,
            period_number=2,
            status='active'
        )
        
        # Test schedule properties
        self.assertEqual(schedule.class_subject, self.class_subject)
        self.assertEqual(schedule.day_of_week, 'monday')
        self.assertEqual(schedule.room, room)
        self.assertEqual(schedule.status, 'active')
        
        # Test duration calculation
        duration = schedule.get_duration_minutes()
        self.assertEqual(duration, 60)
        
        # Test conflict detection (no conflicts initially)
        teacher_conflicts = schedule.check_teacher_conflicts()
        room_conflicts = schedule.check_room_conflicts()
        class_conflicts = schedule.check_class_conflicts()
        
        self.assertEqual(len(teacher_conflicts), 0)
        self.assertEqual(len(room_conflicts), 0)
        self.assertEqual(len(class_conflicts), 0)
        
        # Create conflicting schedule
        conflicting_schedule = Schedule.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            day_of_week='monday',
            start_time=time(9, 30),
            end_time=time(10, 30),
            room=room,
            status='active'
        )
        
        # Test conflict detection
        room_conflicts = schedule.check_room_conflicts()
        self.assertEqual(len(room_conflicts), 1)
        self.assertEqual(room_conflicts[0], conflicting_schedule)
        
        # Test comprehensive validation
        errors = schedule.validate_schedule()
        self.assertIn('Room has conflicting schedules', errors)

    def test_timetable_generator_basic_functionality(self):
        """Test TimetableGenerator basic functionality"""
        # Create teacher availability
        TeacherAvailability.objects.create(
            school=self.school,
            teacher=self.teacher,
            day_of_week='monday',
            start_time=time(8, 0),
            end_time=time(16, 0),
            academic_year=self.academic_year
        )
        
        # Create room
        room = Room.objects.create(
            school=self.school,
            name="Classroom 1",
            room_number="101",
            capacity=30,
            is_available=True
        )
        
        # Create timetable template
        template = TimetableTemplate.objects.create(
            school=self.school,
            name="Test Template",
            academic_year=self.academic_year,
            periods_per_day=6,
            period_duration=45,
            school_start_time=time(8, 0),
            school_end_time=time(14, 0),
            working_days=['monday', 'tuesday', 'wednesday'],
            is_active=True
        )
        
        # Initialize generator
        generator = TimetableGenerator(self.school, self.academic_year)
        
        # Test constraint addition
        initial_constraints = len(generator.constraints)
        generator.add_constraint('custom_constraint', weight=5.0)
        self.assertEqual(len(generator.constraints), initial_constraints + 1)
        
        # Test class subjects retrieval
        class_subjects = generator.get_class_subjects_to_schedule()
        self.assertIn(self.class_subject, class_subjects)
        
        # Test time slots generation
        time_slots = generator.get_available_time_slots(template)
        self.assertEqual(len(time_slots), 6)
        
        # Test working days
        working_days = generator.get_working_days(template)
        self.assertEqual(working_days, ['monday', 'tuesday', 'wednesday'])
        
        # Test suitable rooms finding
        suitable_rooms = generator.find_suitable_rooms(self.class_subject)
        self.assertIn(room, suitable_rooms)
        
        # Test teacher availability checking
        is_available = generator.check_teacher_availability(
            self.teacher, 'monday', time(9, 0), time(10, 0)
        )
        self.assertTrue(is_available)

    def test_timetable_generator_schedule_generation(self):
        """Test TimetableGenerator schedule generation"""
        # Setup prerequisites
        TeacherAvailability.objects.create(
            school=self.school,
            teacher=self.teacher,
            day_of_week='monday',
            start_time=time(8, 0),
            end_time=time(16, 0),
            is_preferred=True,
            academic_year=self.academic_year
        )
        
        TeacherAvailability.objects.create(
            school=self.school,
            teacher=self.teacher,
            day_of_week='tuesday',
            start_time=time(8, 0),
            end_time=time(16, 0),
            academic_year=self.academic_year
        )
        
        room = Room.objects.create(
            school=self.school,
            name="Classroom 1",
            room_number="101",
            capacity=30,
            is_available=True
        )
        
        template = TimetableTemplate.objects.create(
            school=self.school,
            name="Test Template",
            academic_year=self.academic_year,
            periods_per_day=6,
            period_duration=45,
            school_start_time=time(8, 0),
            school_end_time=time(14, 0),
            working_days=['monday', 'tuesday'],
            is_active=True
        )
        
        generator = TimetableGenerator(self.school, self.academic_year)
        
        # Test single class subject scheduling
        schedules = generator.generate_schedule_for_class_subject(self.class_subject, template)
        
        # Should generate 4 schedules (weekly_hours = 4)
        self.assertEqual(len(schedules), 4)
        
        # All schedules should be valid
        for schedule in schedules:
            self.assertEqual(schedule.class_subject, self.class_subject)
            self.assertIn(schedule.day_of_week, ['monday', 'tuesday'])
            self.assertIsNotNone(schedule.start_time)
            self.assertIsNotNone(schedule.end_time)

    def test_resource_allocator(self):
        """Test ResourceAllocator functionality"""
        # Create rooms
        room1 = Room.objects.create(
            school=self.school,
            name="Classroom 1",
            room_number="101",
            capacity=30,
            is_available=True
        )
        
        room2 = Room.objects.create(
            school=self.school,
            name="Lab 1",
            room_number="201",
            room_type="laboratory",
            capacity=25,
            has_projector=True,
            has_computer=True,
            is_available=True
        )
        
        # Create schedules
        Schedule.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            day_of_week='monday',
            start_time=time(9, 0),
            end_time=time(10, 0),
            room=room1,
            status='active'
        )
        
        allocator = ResourceAllocator(self.school, self.academic_year)
        
        # Test room utilization calculation
        utilization = allocator.get_room_utilization()
        self.assertIn(room1, utilization)
        self.assertIn(room2, utilization)
        
        room1_util = utilization[room1]
        self.assertEqual(room1_util['total_hours'], 1.0)
        self.assertGreater(room1_util['utilization_percentage'], 0)
        
        # Test room allocation suggestions
        suggestions = allocator.suggest_room_allocation(self.class_subject)
        self.assertIsInstance(suggestions, list)
        self.assertTrue(len(suggestions) > 0)
        
        # Rooms should be scored
        for room, score in suggestions:
            self.assertIsInstance(room, Room)
            self.assertIsInstance(score, float)
        
        # Test conflict detection
        conflicts = allocator.detect_resource_conflicts()
        self.assertIsInstance(conflicts, list)

    def test_schedule_analyzer(self):
        """Test ScheduleAnalyzer functionality"""
        # Create test schedules
        room = Room.objects.create(
            school=self.school,
            name="Classroom 1",
            room_number="101",
            capacity=30,
            is_available=True
        )
        
        Schedule.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            day_of_week='monday',
            start_time=time(9, 0),
            end_time=time(10, 0),
            room=room,
            status='active'
        )
        
        Schedule.objects.create(
            school=self.school,
            class_subject=self.class_subject,
            day_of_week='tuesday',
            start_time=time(10, 0),
            end_time=time(11, 0),
            room=room,
            status='active'
        )
        
        analyzer = ScheduleAnalyzer(self.school, self.academic_year)
        
        # Test report generation
        report = analyzer.generate_schedule_report()
        
        # Check report structure
        self.assertIn('total_schedules', report)
        self.assertIn('by_day', report)
        self.assertIn('by_time', report)
        self.assertIn('teacher_workload', report)
        self.assertIn('room_utilization', report)
        self.assertIn('subject_distribution', report)
        self.assertIn('conflicts', report)
        
        # Check report data
        self.assertEqual(report['total_schedules'], 2)
        self.assertIn('monday', report['by_day'])
        self.assertIn('tuesday', report['by_day'])
        self.assertEqual(report['by_day']['monday'], 1)
        self.assertEqual(report['by_day']['tuesday'], 1)

    def test_scheduling_conflict_class(self):
        """Test SchedulingConflict utility class"""
        conflict = SchedulingConflict(
            'teacher',
            'Teacher has conflicting schedules',
            []
        )
        
        self.assertEqual(conflict.conflict_type, 'teacher')
        self.assertEqual(conflict.message, 'Teacher has conflicting schedules')
        self.assertEqual(len(conflict.schedules), 0)
        
        # Test string representation
        self.assertEqual(str(conflict), 'teacher: Teacher has conflicting schedules')

    def test_scheduling_constraint_class(self):
        """Test SchedulingConstraint utility class"""
        constraint = SchedulingConstraint(
            'no_conflicts',
            weight=10.0,
            description='Avoid scheduling conflicts'
        )
        
        self.assertEqual(constraint.constraint_type, 'no_conflicts')
        self.assertEqual(constraint.weight, 10.0)
        self.assertEqual(constraint.description, 'Avoid scheduling conflicts')

    def test_complete_timetable_generation_workflow(self):
        """Test complete timetable generation workflow"""
        # Setup comprehensive test environment
        
        # Create multiple teachers
        teacher2_user = User.objects.create_user(
            username="teacher2",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        teacher2 = Teacher.objects.create(
            school=self.school,
            user=teacher2_user,
            employee_id="T002",
            hire_date=date.today(),
            qualification="Master's in Science"
        )
        
        # Create multiple subjects
        science_subject = Subject.objects.create(
            school=self.school,
            name="Science",
            code="SCI101",
            credit_hours=3,
            weekly_hours=3,
            created_by=self.user
        )
        
        teacher2.subjects.add(science_subject)
        
        # Create multiple class subjects
        class_subject2 = ClassSubject.objects.create(
            school=self.school,
            class_obj=self.class_obj,
            subject=science_subject,
            teacher=teacher2,
            academic_year=self.academic_year,
            semester=self.semester,
            weekly_hours=3
        )
        
        # Create teacher availabilities
        for day in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']:
            TeacherAvailability.objects.create(
                school=self.school,
                teacher=self.teacher,
                day_of_week=day,
                start_time=time(8, 0),
                end_time=time(16, 0),
                academic_year=self.academic_year
            )
            
            TeacherAvailability.objects.create(
                school=self.school,
                teacher=teacher2,
                day_of_week=day,
                start_time=time(8, 0),
                end_time=time(16, 0),
                academic_year=self.academic_year
            )
        
        # Create rooms
        for i in range(3):
            Room.objects.create(
                school=self.school,
                name=f"Classroom {i+1}",
                room_number=f"10{i+1}",
                capacity=30,
                is_available=True
            )
        
        # Create timetable template
        template = TimetableTemplate.objects.create(
            school=self.school,
            name="Complete Template",
            academic_year=self.academic_year,
            periods_per_day=8,
            period_duration=45,
            school_start_time=time(8, 0),
            school_end_time=time(16, 0),
            working_days=['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            is_active=True
        )
        
        # Generate complete timetable
        generator = TimetableGenerator(self.school, self.academic_year)
        result = generator.generate_complete_timetable(template)
        
        # Verify results
        self.assertIn('schedules', result)
        self.assertIn('report', result)
        self.assertIn('template', result)
        
        report = result['report']
        self.assertEqual(report['total_class_subjects'], 2)
        self.assertGreaterEqual(report['successfully_scheduled'], 0)
        
        # Verify schedules were created
        schedules = result['schedules']
        total_weekly_hours = self.class_subject.weekly_hours + class_subject2.weekly_hours
        # Allow for some flexibility in schedule generation (algorithm may optimize)
        self.assertGreaterEqual(len(schedules), total_weekly_hours - 1)
        self.assertLessEqual(len(schedules), total_weekly_hours + 2)

    def test_validation_and_error_handling(self):
        """Test validation and error handling in scheduling system"""
        # Test invalid schedule times
        with self.assertRaises(ValidationError):
            schedule = Schedule(
                school=self.school,
                class_subject=self.class_subject,
                day_of_week='monday',
                start_time=time(10, 0),
                end_time=time(9, 0),  # End before start
                status='active'
            )
            schedule.full_clean()
        
        # Test invalid teacher availability
        with self.assertRaises(ValidationError):
            availability = TeacherAvailability(
                school=self.school,
                teacher=self.teacher,
                day_of_week='monday',
                start_time=time(16, 0),
                end_time=time(8, 0),  # End before start
                academic_year=self.academic_year
            )
            availability.full_clean()
        
        # Test timetable generation without template
        generator = TimetableGenerator(self.school, self.academic_year)
        
        with self.assertRaises(ValidationError):
            generator.generate_complete_timetable()  # No template available


@pytest.mark.django_db
class TestTask42Integration:
    """Integration tests for Task 4.2 Scheduling System"""
    
    def test_complete_scheduling_workflow(self):
        """Test complete scheduling workflow integration"""
        # Create school and academic year
        school = School.objects.create(
            name="Integration Test School",
            code="ITS",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        academic_year = AcademicYear.objects.create(
            school=school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        semester = Semester.objects.create(
            school=school,
            academic_year=academic_year,
            name="First Semester",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        # Create complete academic structure
        grade = Grade.objects.create(
            school=school,
            name="Grade 1",
            level=1,
            max_capacity=60
        )
        
        user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )
        
        # Create teachers
        teacher_users = []
        teachers = []
        
        for i in range(3):
            teacher_user = User.objects.create_user(
                username=f"teacher{i+1}",
                email=f"teacher{i+1}@school.com",
                password="testpass123",
                user_type="teacher"
            )
            teacher_users.append(teacher_user)
            
            teacher = Teacher.objects.create(
                school=school,
                user=teacher_user,
                employee_id=f"T00{i+1}",
                hire_date=date.today(),
                qualification=f"Master's in Subject {i+1}"
            )
            teachers.append(teacher)
        
        # Create subjects
        subjects = []
        subject_names = ["Mathematics", "Science", "English"]
        
        for i, name in enumerate(subject_names):
            subject = Subject.objects.create(
                school=school,
                name=name,
                code=f"SUB{i+1}01",
                credit_hours=3,
                weekly_hours=4,
                created_by=user
            )
            subjects.append(subject)
            teachers[i].subjects.add(subject)
        
        # Create class
        class_obj = Class.objects.create(
            school=school,
            name="Section A",
            grade=grade,
            academic_year=academic_year,
            class_teacher=teacher_users[0],
            max_students=30
        )
        
        # Create class subjects
        class_subjects = []
        for i, subject in enumerate(subjects):
            class_subject = ClassSubject.objects.create(
                school=school,
                class_obj=class_obj,
                subject=subject,
                teacher=teachers[i],
                academic_year=academic_year,
                semester=semester,
                weekly_hours=4
            )
            class_subjects.append(class_subject)
        
        # Create rooms
        rooms = []
        for i in range(5):
            room = Room.objects.create(
                school=school,
                name=f"Classroom {i+1}",
                room_number=f"10{i+1}",
                capacity=30,
                is_available=True
            )
            rooms.append(room)
        
        # Create teacher availabilities
        working_days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        for teacher in teachers:
            for day in working_days:
                TeacherAvailability.objects.create(
                    school=school,
                    teacher=teacher,
                    day_of_week=day,
                    start_time=time(8, 0),
                    end_time=time(16, 0),
                    academic_year=academic_year
                )
        
        # Create timetable template
        template = TimetableTemplate.objects.create(
            school=school,
            name="Standard Template",
            academic_year=academic_year,
            periods_per_day=8,
            period_duration=45,
            school_start_time=time(8, 0),
            school_end_time=time(16, 0),
            working_days=working_days,
            is_active=True
        )
        
        # Test complete workflow
        
        # 1. Generate timetable
        generator = TimetableGenerator(school, academic_year)
        result = generator.generate_complete_timetable(template)
        
        assert result['report']['total_class_subjects'] == 3
        assert len(result['schedules']) == 12  # 3 subjects * 4 hours each
        
        # 2. Analyze resource allocation
        allocator = ResourceAllocator(school, academic_year)
        utilization = allocator.get_room_utilization()
        conflicts = allocator.detect_resource_conflicts()
        
        assert len(utilization) == 5  # 5 rooms
        assert isinstance(conflicts, list)
        
        # 3. Generate analytics
        analyzer = ScheduleAnalyzer(school, academic_year)
        report = analyzer.generate_schedule_report()
        
        assert report['total_schedules'] == 12
        assert len(report['by_day']) > 0
        assert len(report['teacher_workload']) == 3
        
        # 4. Test optimization
        optimization_result = generator.optimize_existing_timetable()
        
        assert 'initial_score' in optimization_result
        assert 'final_score' in optimization_result
        assert 'improvements_made' in optimization_result
        
        # Verify all components work together
        assert len(class_subjects) == 3
        assert len(teachers) == 3
        assert len(rooms) == 5
        assert template.is_active is True