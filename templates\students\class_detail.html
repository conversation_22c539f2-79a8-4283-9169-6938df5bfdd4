{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Class Details" %} - {{ class_obj.grade.name }} {{ class_obj.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Page header -->
            <div class="page-header d-print-none">
                <div class="row align-items-center">
                    <div class="col">
                        <h2 class="page-title">
                            {{ class_obj.grade.name }} - {{ class_obj.name }}
                        </h2>
                        <div class="text-muted mt-1">{% trans "Academic Year" %}: {{ class_obj.academic_year }}</div>
                    </div>
                    <div class="col-auto ms-auto d-print-none">
                        <div class="btn-list">
                            <a href="{% url 'academics:timetable' %}?class={{ class_obj.id }}" class="btn btn-primary d-none d-sm-inline-block">
                                <i class="fas fa-calendar-week me-2"></i>
                                {% trans "View Timetable" %}
                            </a>
                            <a href="{% url 'students:class_edit' class_obj.id %}" class="btn btn-secondary d-none d-sm-inline-block">
                                <i class="fas fa-edit me-2"></i>
                                {% trans "Edit Class" %}
                            </a>
                            <a href="{% url 'students:class_list' %}" class="btn btn-outline-secondary d-none d-sm-inline-block">
                                <i class="fas fa-arrow-left me-2"></i>
                                {% trans "Back to Classes" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

    <!-- Class details -->
    <div class="row mt-3">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Class Information" %}</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">{% trans "Class Name" %}:</div>
                        <div class="col-md-7">{{ class_obj.name }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">{% trans "Grade" %}:</div>
                        <div class="col-md-7">{{ class_obj.grade.name }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">{% trans "Academic Year" %}:</div>
                        <div class="col-md-7">{{ class_obj.academic_year }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">{% trans "Class Teacher" %}:</div>
                        <div class="col-md-7">
                            {% if class_obj.class_teacher %}
                                {{ class_obj.class_teacher.first_name }} {{ class_obj.class_teacher.last_name }}
                            {% else %}
                                <span class="text-muted">{% trans "Not assigned" %}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">{% trans "Room Number" %}:</div>
                        <div class="col-md-7">
                            {% if class_obj.room_number %}
                                {{ class_obj.room_number }}
                            {% else %}
                                <span class="text-muted">{% trans "Not assigned" %}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">{% trans "Maximum Students" %}:</div>
                        <div class="col-md-7">{{ class_obj.max_students }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">{% trans "Current Students" %}:</div>
                        <div class="col-md-7">{{ class_obj.current_students_count }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">{% trans "Status" %}:</div>
                        <div class="col-md-7">
                            {% if class_obj.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-danger">{% trans "Inactive" %}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Class statistics -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Statistics" %}</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col">{% trans "Total Students" %}</div>
                        <div class="col-auto">
                            <span class="badge bg-primary">{{ class_obj.current_students_count }}</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col">{% trans "Male Students" %}</div>
                        <div class="col-auto">
                            <span class="badge bg-blue">{{ male_students_count|default:"0" }}</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col">{% trans "Female Students" %}</div>
                        <div class="col-auto">
                            <span class="badge bg-pink">{{ female_students_count|default:"0" }}</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col">{% trans "Subjects" %}</div>
                        <div class="col-auto">
                            <span class="badge bg-green">{{ subjects_count|default:"0" }}</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col">{% trans "Weekly Hours" %}</div>
                        <div class="col-auto">
                            <span class="badge bg-orange">{{ weekly_hours|default:"0" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <!-- Students in this class -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">{% trans "Students" %}</h3>
                    <div>
                        <a href="{% url 'students:add_student' %}?class={{ class_obj.id }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus me-2"></i>{% trans "Add Student" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if students %}
                        <div class="table-responsive">
                            <table class="table table-vcenter card-table">
                                <thead>
                                    <tr>
                                        <th>{% trans "ID" %}</th>
                                        <th>{% trans "Name" %}</th>
                                        <th>{% trans "Gender" %}</th>
                                        <th>{% trans "Parent" %}</th>
                                        <th>{% trans "Contact" %}</th>
                                        <th class="w-1"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for student in students %}
                                    <tr>
                                        <td>{{ student.student_id }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="avatar me-2">
                                                    {% if student.photo %}
                                                        <img src="{{ student.photo.url }}" alt="{{ student.full_name }}">
                                                    {% else %}
                                                        {{ student.first_name|first }}{{ student.last_name|first }}
                                                    {% endif %}
                                                </span>
                                                <div>
                                                    <a href="{% url 'students:detail' student.id %}">{{ student.full_name }}</a>
                                                    <div class="text-muted">{{ student.admission_number }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if student.gender == 'M' %}
                                                <span class="badge bg-blue">{% trans "Male" %}</span>
                                            {% else %}
                                                <span class="badge bg-pink">{% trans "Female" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'students:parent_detail' student.parent.id %}">
                                                {{ student.parent.father_name }}
                                            </a>
                                        </td>
                                        <td>{{ student.parent.father_phone }}</td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <div class="dropdown-menu">
                                                    <a class="dropdown-item" href="{% url 'students:detail' student.id %}">
                                                        <i class="fas fa-eye me-2"></i>{% trans "View" %}
                                                    </a>
                                                    <a class="dropdown-item" href="{% url 'students:edit' student.id %}">
                                                        <i class="fas fa-edit me-2"></i>{% trans "Edit" %}
                                                    </a>
                                                    <a class="dropdown-item" href="{% url 'academics:grade_entry' %}?student={{ student.id }}">
                                                        <i class="fas fa-graduation-cap me-2"></i>{% trans "Grades" %}
                                                    </a>
                                                    <a class="dropdown-item" href="{% url 'academics:take_attendance' %}?student={{ student.id }}">
                                                        <i class="fas fa-clipboard-check me-2"></i>{% trans "Attendance" %}
                                                    </a>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty">
                            <div class="empty-icon">
                                <i class="fas fa-users fa-3x text-muted"></i>
                            </div>
                            <p class="empty-title">{% trans "No students in this class" %}</p>
                            <p class="empty-subtitle text-muted">
                                {% trans "There are no students assigned to this class yet." %}
                            </p>
                            <div class="empty-action">
                                <a href="{% url 'students:add_student' %}?class={{ class_obj.id }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>{% trans "Add Student" %}
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Subjects taught in this class -->
            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">{% trans "Subjects" %}</h3>
                </div>
                <div class="card-body">
                    {% if class_subjects %}
                        <div class="table-responsive">
                            <table class="table table-vcenter card-table">
                                <thead>
                                    <tr>
                                        <th>{% trans "Subject" %}</th>
                                        <th>{% trans "Teacher" %}</th>
                                        <th>{% trans "Weekly Hours" %}</th>
                                        <th>{% trans "Schedule" %}</th>
                                        <th class="w-1"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for class_subject in class_subjects %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'academics:subject_detail' class_subject.subject.id %}">
                                                {{ class_subject.subject.name }}
                                            </a>
                                            <div class="text-muted">{{ class_subject.subject.code }}</div>
                                        </td>
                                        <td>
                                            <a href="{% url 'academics:teacher_detail' class_subject.teacher.id %}">
                                                {{ class_subject.teacher }}
                                            </a>
                                        </td>
                                        <td>{{ class_subject.weekly_hours }}</td>
                                        <td>
                                            {% for schedule in class_subject.schedules.all %}
                                                <div>{{ schedule.get_day_of_week_display }}: {{ schedule.start_time|time:"H:i" }} - {{ schedule.end_time|time:"H:i" }}</div>
                                            {% empty %}
                                                <span class="text-muted">{% trans "No schedule" %}</span>
                                            {% endfor %}
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <div class="dropdown-menu">
                                                    <a class="dropdown-item" href="{% url 'academics:schedule_create' %}?class_subject={{ class_subject.id }}">
                                                        <i class="fas fa-calendar-plus me-2"></i>{% trans "Add Schedule" %}
                                                    </a>
                                                    <a class="dropdown-item" href="{% url 'academics:take_attendance' %}?class_subject={{ class_subject.id }}">
                                                        <i class="fas fa-clipboard-check me-2"></i>{% trans "Take Attendance" %}
                                                    </a>
                                                    <a class="dropdown-item" href="{% url 'academics:exam_add' %}?class_subject={{ class_subject.id }}">
                                                        <i class="fas fa-file-alt me-2"></i>{% trans "Create Exam" %}
                                                    </a>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty">
                            <div class="empty-icon">
                                <i class="fas fa-book fa-3x text-muted"></i>
                            </div>
                            <p class="empty-title">{% trans "No subjects assigned" %}</p>
                            <p class="empty-subtitle text-muted">
                                {% trans "There are no subjects assigned to this class yet." %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}