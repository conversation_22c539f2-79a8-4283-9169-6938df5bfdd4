{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Distribute Students" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-users-cog text-primary me-2"></i>{% trans "Distribute Students" %}
                    </h2>
                    <p class="text-muted">{% trans "Assign students to classes" %}</p>
                </div>
            </div>

            <!-- Distribution Form -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{% trans "Student Distribution" %}</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="target_class" class="form-label">{% trans "Target Class" %}</label>
                                        <select class="form-select" id="target_class" name="target_class" required>
                                            <option value="">{% trans "Select Class" %}</option>
                                            {% for class in classes %}
                                                <option value="{{ class.id }}">{{ class.grade.name }} - {{ class.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">{% trans "Distribution Method" %}</label>
                                        <div class="btn-group w-100" role="group">
                                            <input type="radio" class="btn-check" name="method" id="manual" value="manual" checked>
                                            <label class="btn btn-outline-primary" for="manual">{% trans "Manual Selection" %}</label>
                                            
                                            <input type="radio" class="btn-check" name="method" id="automatic" value="automatic">
                                            <label class="btn btn-outline-primary" for="automatic">{% trans "Automatic" %}</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-users me-2"></i>{% trans "Distribute Selected Students" %}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Unassigned Students -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                {% trans "Unassigned Students" %} 
                                <span class="badge bg-warning">{{ unassigned_students.count }}</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if unassigned_students %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <input type="checkbox" id="select-all" class="form-check-input">
                                                </th>
                                                <th>{% trans "Student" %}</th>
                                                <th>{% trans "ID" %}</th>
                                                <th>{% trans "Gender" %}</th>
                                                <th>{% trans "Age" %}</th>
                                                <th>{% trans "Admission Date" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for student in unassigned_students %}
                                                <tr>
                                                    <td>
                                                        <input type="checkbox" name="student_ids" value="{{ student.id }}" class="form-check-input student-checkbox">
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                                <span class="text-white fw-bold">{{ student.first_name|slice:":1" }}{{ student.last_name|slice:":1" }}</span>
                                                            </div>
                                                            <div>
                                                                <h6 class="mb-0">{{ student.full_name }}</h6>
                                                                <small class="text-muted">{{ student.parent.father_name }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{{ student.student_id }}</td>
                                                    <td>
                                                        {% if student.gender == 'M' %}
                                                            <span class="badge bg-info">{% trans "Male" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-pink">{% trans "Female" %}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ student.age }} {% trans "years" %}</td>
                                                    <td>{{ student.admission_date }}</td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="{% url 'students:detail' student.id %}" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="quickAssign({{ student.id }})">
                                                                <i class="fas fa-plus"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>{% trans "All students have been assigned to classes." %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Assign Modal -->
<div class="modal fade" id="quickAssignModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Quick Assign Student" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="quickAssignForm" method="post">
                    {% csrf_token %}
                    <input type="hidden" id="quick-student-id" name="student_ids">
                    <div class="mb-3">
                        <label for="quick-target-class" class="form-label">{% trans "Assign to Class" %}</label>
                        <select class="form-select" id="quick-target-class" name="target_class" required>
                            <option value="">{% trans "Select Class" %}</option>
                            {% for class in classes %}
                                <option value="{{ class.id }}">{{ class.grade.name }} - {{ class.name }} ({{ class.current_students_count }}/{{ class.max_students }})</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="submit" form="quickAssignForm" class="btn btn-primary">{% trans "Assign" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all checkbox functionality
    const selectAllCheckbox = document.getElementById('select-all');
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');
    
    selectAllCheckbox.addEventListener('change', function() {
        studentCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Update select all checkbox when individual checkboxes change
    studentCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.student-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === studentCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < studentCheckboxes.length;
        });
    });
});

function quickAssign(studentId) {
    document.getElementById('quick-student-id').value = studentId;
    const modal = new bootstrap.Modal(document.getElementById('quickAssignModal'));
    modal.show();
}
</script>
{% endblock %}