$(document).ready(function () {
    $("#keyboard").val('');
    $("#searchType").prop("checked", false);
    $("#searchKind").val('studentName');
    var first = true;


var dataTableJ;
studentSearch = function () {
    $.get("/api/studentSearch/search?searchType=" + $("#searchType").is(":checked") + "&&textValue=" + $("#keyboard").val() + "&&searchKind=" + $("#searchKind").val(), (res) => {
        console.log(res)
        if (first) {
            setDataTables(res);
            first = false;
        } else {
            dataTableJ.destroy();
            setDataTables(res)
        }
        $("#studentsSearchModal").modal("show");
    });
}
function setDataTables(data) {
    dataTableJ = $('#tblStudentsSearch').DataTable({
        data: data,
        //"scrollCollapse": true,
        columns:
            [{ data: "number", title: "#" },
                { data: "studentName", title: "اسم الطالب" },
                { data:"keedNumber",title:"رقم القيد"},

            { data: "studentStage", title: "المرحلة" },
            { data: "studentGrade", title: "الصف" },
            { data: "studentClass", title: "الفصل" },
            {
                data: "studentiId",
                "render": function (data, type, row, meta) {
                    if (row.isActive == false) {
                        return `<label class="text-danger">الطالب موقوف</label>`;
                    }
                    else {
                        const encodedId = btoa(data); 
                        return "<button type='button' class='btnEditStudent btn btn-primary' id='" + encodedId + "' class='btn btn-primary'>" + getToken('edit') + "</button>"
                    }
                }
                },
               
            {
                data: "studentiId",
                "render": function (data, type, row, meta) {
                    const encodedId = btoa(data); 
                    return "<button type='button' class='btnDetailsStudent btn btn-primary' id='" + encodedId + "' class='btn btn-success'>" + getToken('details') + "</button>"
                }
                },
                //{
                //    data: "studentiId",
                //    "render": function (data, type, row, meta) {
                //        if (row.isActive == false) {
                //            return `<label class="text-danger">الطالب موقوف</label>`;
                //        }
                //        else {
                //            return "<button type='button' class='btnStudentNotes btn btn-success' id='" + data + "'>" + getToken('StudentNotes') + "</button>"
                //        }
                //    }
                //},
                {
                    data: "studentiId",
                    "render": function (data, type, row, meta) {
                        if (row.isActive === false) {
                            return `<label class="text-danger">الطالب موقوف</label>`;
                        } else {
                            const encodedId = btoa(data); // Base64 encoding
                            return `<button type='button' class='btnStudentNotes btn btn-success' id='${encodedId}'>${getToken('StudentNotes')}</button>`;
                        }
                    }
                },

                {
                data: "imgUrl",
                "render": function (data, type, row, meta) {
                    /// Student Image ///
                    var imgSrc = "";
                    if (data === "null" || data === null || data === "") {
                        imgSrc +=
                            `<a target="blank" href="/assets/media/users/user.png" 
                                data-source="/assets/media/users/user.png">
                              <img src="/assets/media/users/user.png" alt="" width=40px height=50px  /></a>`
                    }
                    else {
                        imgSrc +=
                            `<a target="blank" href="${data}" 
                                data-source="${data}">
                              <img src="${data}" alt="" width=40px height=50px /></a>`
                    }
                    return imgSrc;
                }
            }
            ]
    });
    $("#tblStudentsSearch").on("click", ".btnEditStudent", function () {
        edit(this);
    });


    $("#tblStudentsSearch").on("click", ".btnDetailsStudent", function () {
        details(this);
    });
}
    $("#tblStudentsSearch").on("click", ".btnStudentNotes", function () {
        notes(this);

    }
    );

    function edit(el) {

        window.open("/Admission/student/index?student_id=" + el.id, '_blank');
    }
    function details(el) {
        window.open("/Admission/studentsData/index?student_id=" + el.id, '_blank');
    }
    //// Student Notes ///// 
    function notes(el) {
        bindNoteTable(el.id);
        $.get("/Admission/StudentNotes/ModelDetails", { id: el.id }, (res) => {
            $("#studentNotesModal #NotesDetails").html(res)
            $("#studentNotesModal").modal();
        })
    }
    function bindNoteTable(studentId) {
        $.post("/Admission/StudentNotes/getStudentNotes", { studentId: studentId }, (res) => {
            var count = 1;
            var tr = "";
            $(res.data).each(function () {
                tr += `<tr><td>${count}</td>
<td>${this.type}</td>
<td>${this.note}</td>
<td><button class='btn btn-danger' onclick='deleteNote(${this.id})'>${getToken('delete')}</button></td></tr>`;
                count++;
            });
            $("#stuNotesTable tbody").html(tr);
        })
    }
})
angular.module("SchoolApp_Services").factory("studentsSearch_factory", function ($http) {

    function getSearchData(searchType, textValue, searchKind) {
        return $http.get("/api/studentSearch/search?searchType=" + searchType + "&&textValue=" + textValue + "&&searchKind=" + searchKind)
            .then(function (data) {
                return data;
            })
            .catch(function (error) {
                return error;
            })
    }
    return {       
        _getSearchData: getSearchData
    }

})
