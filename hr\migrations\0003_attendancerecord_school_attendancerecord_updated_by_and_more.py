# Generated by Django 5.2.4 on 2025-07-30 12:17

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("hr", "0002_leavetype_employeedocument_leaverequest_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="attendancerecord",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="attendancerecord",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="department",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="department",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="department",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="employee",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="employee",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="employee",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="employeedocument",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="employeedocument",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="employeedocument",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="leaverequest",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="leaverequest",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="leaverequest",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="leavetype",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="leavetype",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="leavetype",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="payroll",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="payroll",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="payroll",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="payrollperiod",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="payrollperiod",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="payrollperiod",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="performanceevaluation",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="performanceevaluation",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="performanceevaluation",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AddField(
            model_name="position",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Created By",
            ),
        ),
        migrations.AddField(
            model_name="position",
            name="school",
            field=models.ForeignKey(
                default="",
                help_text="School this record belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                to="core.school",
                verbose_name="School",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="position",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Updated By",
            ),
        ),
        migrations.AlterField(
            model_name="attendancerecord",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="department",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="employee",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="employeedocument",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="leaverequest",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="leavetype",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="payroll",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="payrollperiod",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="performanceevaluation",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="position",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, primary_key=True, serialize=False
            ),
        ),
    ]
