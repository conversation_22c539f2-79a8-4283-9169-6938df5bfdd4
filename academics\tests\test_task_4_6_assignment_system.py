"""
Test suite for Assignment Management System (Task 4.6)

This module tests the comprehensive assignment management functionality including:
- Assignment creation and distribution
- Online submission system
- Grading and feedback tools
- Plagiarism detection features
- Assignment analytics

Requirements tested: 3.6
"""

import pytest
from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError
from accounts.models import User
from datetime import datetime, timedelta
from decimal import Decimal
import tempfile
import os

from core.models import School, AcademicYear
from students.models import Grade, Class, Student
from academics.models import (
    Subject, ClassSubject, Assignment, AssignmentSubmission, 
    AssignmentGroup, AssignmentAnalytics
)
from academics.assignment_system import (
    AssignmentManager, SubmissionManager, GroupManager,
    PlagiarismDetector, AnalyticsManager
)


class AssignmentSystemTestCase(TestCase):
    """Base test case with common setup for assignment system tests"""
    
    def setUp(self):
        """Set up test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="555-0123",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date="2020-01-01"
        )
        
        # Create academic year
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date="2024-09-01",
            end_date="2025-06-30",
            is_current=True
        )
        
        # Create grade and class
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 10",
            level=10,
            max_capacity=30
        )
        
        self.class_obj = Class.objects.create(
            school=self.school,
            name="A",
            grade=self.grade,
            academic_year=self.academic_year,
            max_students=25
        )
        
        # Create subject
        self.subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH101",
            credit_hours=3,
            weekly_hours=5
        )
        self.subject.grades.add(self.grade)
        
        # Create teacher user
        self.teacher_user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        # Import Teacher model
        from academics.models import Teacher
        
        # Create teacher
        self.teacher = Teacher.objects.create(
            school=self.school,
            user=self.teacher_user,
            employee_id="T001",
            hire_date="2024-01-01",
            qualification="Master's in Mathematics"
        )
        self.teacher.subjects.add(self.subject)
        
        # Create semester (required for ClassSubject)
        from core.models import Semester
        self.semester = Semester.objects.create(
            school=self.school,
            academic_year=self.academic_year,
            name="First Semester",
            start_date="2024-09-01",
            end_date="2025-01-31",
            is_current=True
        )
        
        # Create class subject
        self.class_subject = ClassSubject.objects.create(
            school=self.school,
            class_obj=self.class_obj,
            subject=self.subject,
            teacher=self.teacher,
            academic_year=self.academic_year,
            semester=self.semester
        )
        
        # Create students
        self.students = []
        for i in range(5):
            student = Student.objects.create(
                school=self.school,
                student_id=f"STU{i+1:03d}",
                first_name=f"Student{i+1}",
                last_name="Test",
                date_of_birth="2008-01-01",
                gender="M" if i % 2 == 0 else "F",
                nationality="US",
                admission_date="2024-09-01",
                current_grade=self.grade,
                is_active=True
            )
            self.students.append(student)
            self.class_obj.students.add(student)
        
        # Initialize managers
        self.assignment_manager = AssignmentManager(self.school)
        self.submission_manager = SubmissionManager(self.school)
        self.group_manager = GroupManager(self.school)
        self.plagiarism_detector = PlagiarismDetector(self.school)
        self.analytics_manager = AnalyticsManager(self.school)


class AssignmentCreationTests(AssignmentSystemTestCase):
    """Test assignment creation and management"""
    
    def test_create_basic_assignment(self):
        """Test creating a basic assignment"""
        due_date = timezone.now() + timedelta(days=7)
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Algebra Homework",
            description="Complete exercises 1-20",
            due_date=due_date,
            assignment_type="homework",
            max_score=100,
            weight_percentage=10
        )
        
        self.assertIsNotNone(assignment)
        self.assertEqual(assignment.title, "Algebra Homework")
        self.assertEqual(assignment.class_subject, self.class_subject)
        self.assertEqual(assignment.status, "draft")
        self.assertEqual(assignment.max_score, 100)
        self.assertEqual(assignment.weight_percentage, 10)
    
    def test_create_group_assignment(self):
        """Test creating a group assignment"""
        due_date = timezone.now() + timedelta(days=14)
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Math Project",
            description="Group project on statistics",
            due_date=due_date,
            assignment_type="project",
            group_assignment=True,
            max_group_size=4,
            max_score=200
        )
        
        self.assertTrue(assignment.group_assignment)
        self.assertEqual(assignment.max_group_size, 4)
        self.assertEqual(assignment.assignment_type, "project")
    
    def test_create_assignment_with_rubric(self):
        """Test creating assignment with grading rubric"""
        due_date = timezone.now() + timedelta(days=10)
        rubric = {
            "criteria": [
                {"name": "Content", "points": 40, "description": "Quality of content"},
                {"name": "Organization", "points": 30, "description": "Structure and flow"},
                {"name": "Grammar", "points": 30, "description": "Language mechanics"}
            ]
        }
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Essay Assignment",
            description="Write a 500-word essay",
            due_date=due_date,
            assignment_type="essay",
            rubric=rubric,
            max_score=100
        )
        
        self.assertEqual(assignment.rubric, rubric)
        self.assertEqual(len(assignment.rubric["criteria"]), 3)
    
    def test_publish_assignment(self):
        """Test publishing an assignment"""
        due_date = timezone.now() + timedelta(days=7)
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Test Assignment",
            description="Test description",
            due_date=due_date
        )
        
        success = self.assignment_manager.publish_assignment(assignment.id)
        
        self.assertTrue(success)
        assignment.refresh_from_db()
        self.assertEqual(assignment.status, "published")
        
        # Check analytics was created
        analytics = AssignmentAnalytics.objects.filter(assignment=assignment).first()
        self.assertIsNotNone(analytics)
    
    def test_get_assignment_statistics(self):
        """Test getting assignment statistics"""
        due_date = timezone.now() + timedelta(days=7)
        
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Test Assignment",
            description="Test description",
            due_date=due_date
        )
        
        stats = self.assignment_manager.get_assignment_statistics(assignment.id)
        
        self.assertIn('total_students', stats)
        self.assertIn('submitted', stats)
        self.assertIn('not_submitted', stats)
        self.assertEqual(stats['total_students'], 5)  # 5 students in class
        self.assertEqual(stats['submitted'], 0)  # No submissions yet
    
    def test_get_overdue_assignments(self):
        """Test getting overdue assignments"""
        # Create overdue assignment
        overdue_date = timezone.now() - timedelta(days=1)
        overdue_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Overdue Assignment",
            description="This is overdue",
            due_date=overdue_date,
            status="published"
        )
        
        # Create future assignment
        future_date = timezone.now() + timedelta(days=7)
        future_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Future Assignment",
            description="This is in the future",
            due_date=future_date,
            status="published"
        )
        
        overdue_assignments = self.assignment_manager.get_overdue_assignments()
        
        self.assertEqual(overdue_assignments.count(), 1)
        self.assertEqual(overdue_assignments.first().title, "Overdue Assignment")
    
    def test_get_upcoming_assignments(self):
        """Test getting upcoming assignments"""
        # Create assignment due in 3 days
        upcoming_date = timezone.now() + timedelta(days=3)
        upcoming_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Upcoming Assignment",
            description="Due soon",
            due_date=upcoming_date,
            status="published"
        )
        
        # Create assignment due in 10 days
        far_future_date = timezone.now() + timedelta(days=10)
        far_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Far Future Assignment",
            description="Due later",
            due_date=far_future_date,
            status="published"
        )
        
        upcoming_assignments = self.assignment_manager.get_upcoming_assignments(days_ahead=7)
        
        self.assertEqual(upcoming_assignments.count(), 1)
        self.assertEqual(upcoming_assignments.first().title, "Upcoming Assignment")
    
    def test_assignment_workload_analysis(self):
        """Test assignment workload analysis"""
        # Create multiple assignments with different estimated durations
        for i in range(3):
            due_date = timezone.now() + timedelta(days=i*7 + 3)
            self.assignment_manager.create_assignment(
                class_subject=self.class_subject,
                title=f"Assignment {i+1}",
                description=f"Assignment {i+1} description",
                due_date=due_date,
                estimated_duration_hours=2.0 + i,
                status="published"
            )
        
        analysis = self.assignment_manager.get_assignment_workload_analysis(
            self.class_obj, 
            date_range_days=30
        )
        
        self.assertIn('total_assignments', analysis)
        self.assertIn('total_estimated_hours', analysis)
        self.assertIn('weekly_breakdown', analysis)
        self.assertEqual(analysis['total_assignments'], 3)
        self.assertEqual(analysis['total_estimated_hours'], 9.0)  # 2+3+4


class SubmissionManagementTests(AssignmentSystemTestCase):
    """Test submission creation and management"""
    
    def setUp(self):
        super().setUp()
        
        # Create a test assignment
        due_date = timezone.now() + timedelta(days=7)
        self.assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Test Assignment",
            description="Test assignment for submissions",
            due_date=due_date,
            max_score=100
        )
        self.assignment_manager.publish_assignment(self.assignment.id)
    
    def test_create_submission(self):
        """Test creating a submission"""
        submission = self.submission_manager.create_submission(
            assignment_id=self.assignment.id,
            student_id=self.students[0].id,
            submission_text="This is my submission text"
        )
        
        self.assertIsNotNone(submission)
        self.assertEqual(submission.assignment, self.assignment)
        self.assertEqual(submission.student, self.students[0])
        self.assertEqual(submission.status, "draft")
    
    def test_submit_assignment(self):
        """Test submitting an assignment"""
        submission = self.submission_manager.create_submission(
            assignment_id=self.assignment.id,
            student_id=self.students[0].id
        )
        
        submission_data = {
            'submission_text': "This is my completed assignment"
        }
        
        success = self.submission_manager.submit_assignment(
            submission.id, 
            submission_data
        )
        
        self.assertTrue(success)
        submission.refresh_from_db()
        self.assertEqual(submission.status, "submitted")
        self.assertIsNotNone(submission.submitted_at)
        self.assertFalse(submission.is_late)
    
    def test_late_submission(self):
        """Test late submission handling"""
        # Create overdue assignment
        overdue_date = timezone.now() - timedelta(days=1)
        overdue_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Overdue Assignment",
            description="This is overdue",
            due_date=overdue_date,
            late_submission_allowed=True,
            late_penalty_per_day=10,
            max_late_days=3
        )
        self.assignment_manager.publish_assignment(overdue_assignment.id)
        
        submission = self.submission_manager.create_submission(
            assignment_id=overdue_assignment.id,
            student_id=self.students[0].id
        )
        
        submission_data = {
            'submission_text': "This is my late submission"
        }
        
        success = self.submission_manager.submit_assignment(
            submission.id, 
            submission_data
        )
        
        self.assertTrue(success)
        submission.refresh_from_db()
        self.assertEqual(submission.status, "late")
        self.assertTrue(submission.is_late)
        self.assertEqual(submission.late_penalty_applied, 10)  # 1 day late
    
    def test_grade_submission(self):
        """Test grading a submission"""
        submission = self.submission_manager.create_submission(
            assignment_id=self.assignment.id,
            student_id=self.students[0].id
        )
        
        # Submit first
        self.submission_manager.submit_assignment(
            submission.id, 
            {'submission_text': "My submission"}
        )
        
        # Grade the submission
        success = self.submission_manager.grade_submission(
            submission_id=submission.id,
            score=85,
            feedback="Good work, but could be improved",
            graded_by=self.teacher_user
        )
        
        self.assertTrue(success)
        submission.refresh_from_db()
        self.assertTrue(submission.is_graded)
        self.assertEqual(submission.score, 85)
        self.assertEqual(submission.percentage, 85)
        self.assertEqual(submission.grade_letter, "B")
        self.assertEqual(submission.graded_by, self.teacher_user)
    
    def test_grade_with_rubric(self):
        """Test grading with rubric scores"""
        # Create assignment with rubric
        rubric = {
            "criteria": [
                {"name": "Content", "points": 50},
                {"name": "Grammar", "points": 50}
            ]
        }
        
        rubric_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Rubric Assignment",
            description="Assignment with rubric",
            due_date=timezone.now() + timedelta(days=7),
            rubric=rubric,
            max_score=100
        )
        
        submission = self.submission_manager.create_submission(
            assignment_id=rubric_assignment.id,
            student_id=self.students[0].id
        )
        
        self.submission_manager.submit_assignment(
            submission.id, 
            {'submission_text': "My submission"}
        )
        
        rubric_scores = {
            "Content": 45,
            "Grammar": 40
        }
        
        success = self.submission_manager.grade_submission(
            submission_id=submission.id,
            score=85,
            rubric_scores=rubric_scores,
            graded_by=self.teacher_user
        )
        
        self.assertTrue(success)
        submission.refresh_from_db()
        self.assertEqual(submission.rubric_scores, rubric_scores)
    
    def test_resubmission(self):
        """Test assignment resubmission"""
        # Create assignment that allows resubmission
        resubmit_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Resubmit Assignment",
            description="Allows resubmission",
            due_date=timezone.now() + timedelta(days=7),
            allow_resubmission=True,
            max_resubmissions=2
        )
        
        submission = self.submission_manager.create_submission(
            assignment_id=resubmit_assignment.id,
            student_id=self.students[0].id
        )
        
        # Initial submission
        self.submission_manager.submit_assignment(
            submission.id, 
            {'submission_text': "First submission"}
        )
        
        # Resubmit
        submission.resubmit()
        
        submission.refresh_from_db()
        self.assertEqual(submission.status, "resubmitted")
        self.assertEqual(submission.submission_count, 2)
        self.assertFalse(submission.is_graded)
    
    def test_get_submissions_for_grading(self):
        """Test getting submissions that need grading"""
        # Create multiple submissions
        for i in range(3):
            submission = self.submission_manager.create_submission(
                assignment_id=self.assignment.id,
                student_id=self.students[i].id
            )
            self.submission_manager.submit_assignment(
                submission.id, 
                {'submission_text': f"Submission {i+1}"}
            )
        
        submissions_to_grade = self.submission_manager.get_submissions_for_grading(
            assignment_id=self.assignment.id
        )
        
        self.assertEqual(submissions_to_grade.count(), 3)
        for submission in submissions_to_grade:
            self.assertFalse(submission.is_graded)
    
    def test_get_student_submissions(self):
        """Test getting submissions for a specific student"""
        student = self.students[0]
        
        # Create multiple assignments and submissions
        for i in range(2):
            assignment = self.assignment_manager.create_assignment(
                class_subject=self.class_subject,
                title=f"Assignment {i+1}",
                description=f"Assignment {i+1} description",
                due_date=timezone.now() + timedelta(days=7+i)
            )
            
            submission = self.submission_manager.create_submission(
                assignment_id=assignment.id,
                student_id=student.id
            )
            self.submission_manager.submit_assignment(
                submission.id, 
                {'submission_text': f"Submission {i+1}"}
            )
        
        student_submissions = self.submission_manager.get_student_submissions(
            student.id
        )
        
        self.assertEqual(student_submissions.count(), 2)
        for submission in student_submissions:
            self.assertEqual(submission.student, student)


class GroupManagementTests(AssignmentSystemTestCase):
    """Test group assignment management"""
    
    def setUp(self):
        super().setUp()
        
        # Create a group assignment
        due_date = timezone.now() + timedelta(days=14)
        self.group_assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Group Project",
            description="Work in groups of 3-4",
            due_date=due_date,
            assignment_type="project",
            group_assignment=True,
            max_group_size=4,
            max_score=200
        )
        self.assignment_manager.publish_assignment(self.group_assignment.id)
    
    def test_create_group(self):
        """Test creating an assignment group"""
        leader = self.students[0]
        members = [self.students[1].id, self.students[2].id]
        
        group = self.group_manager.create_group(
            assignment_id=self.group_assignment.id,
            name="Team Alpha",
            leader_id=leader.id,
            member_ids=members
        )
        
        self.assertIsNotNone(group)
        self.assertEqual(group.name, "Team Alpha")
        self.assertEqual(group.leader, leader)
        self.assertEqual(group.member_count, 3)  # Leader + 2 members
        self.assertFalse(group.is_finalized)
    
    def test_join_group(self):
        """Test joining an existing group"""
        leader = self.students[0]
        
        group = self.group_manager.create_group(
            assignment_id=self.group_assignment.id,
            name="Team Beta",
            leader_id=leader.id
        )
        
        success = self.group_manager.join_group(
            group.id, 
            self.students[1].id
        )
        
        self.assertTrue(success)
        group.refresh_from_db()
        self.assertEqual(group.member_count, 2)
    
    def test_leave_group(self):
        """Test leaving a group"""
        leader = self.students[0]
        member = self.students[1]
        
        group = self.group_manager.create_group(
            assignment_id=self.group_assignment.id,
            name="Team Gamma",
            leader_id=leader.id,
            member_ids=[member.id]
        )
        
        success = self.group_manager.leave_group(group.id, member.id)
        
        self.assertTrue(success)
        group.refresh_from_db()
        self.assertEqual(group.member_count, 1)  # Only leader remains
    
    def test_leader_cannot_leave_group(self):
        """Test that group leader cannot leave the group"""
        leader = self.students[0]
        
        group = self.group_manager.create_group(
            assignment_id=self.group_assignment.id,
            name="Team Delta",
            leader_id=leader.id
        )
        
        success = self.group_manager.leave_group(group.id, leader.id)
        
        self.assertFalse(success)
    
    def test_finalize_group(self):
        """Test finalizing a group"""
        leader = self.students[0]
        members = [self.students[1].id, self.students[2].id]
        
        group = self.group_manager.create_group(
            assignment_id=self.group_assignment.id,
            name="Team Echo",
            leader_id=leader.id,
            member_ids=members
        )
        
        success = self.group_manager.finalize_group(group.id)
        
        self.assertTrue(success)
        group.refresh_from_db()
        self.assertTrue(group.is_finalized)
    
    def test_cannot_modify_finalized_group(self):
        """Test that finalized groups cannot be modified"""
        leader = self.students[0]
        members = [self.students[1].id]
        
        group = self.group_manager.create_group(
            assignment_id=self.group_assignment.id,
            name="Team Foxtrot",
            leader_id=leader.id,
            member_ids=members
        )
        
        self.group_manager.finalize_group(group.id)
        
        # Try to add member to finalized group
        success = self.group_manager.join_group(group.id, self.students[2].id)
        
        self.assertFalse(success)


class PlagiarismDetectionTests(AssignmentSystemTestCase):
    """Test plagiarism detection functionality"""
    
    def setUp(self):
        super().setUp()
        
        # Create assignment with plagiarism detection enabled
        due_date = timezone.now() + timedelta(days=7)
        self.assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Essay Assignment",
            description="Write an original essay",
            due_date=due_date,
            assignment_type="essay",
            plagiarism_check_enabled=True,
            plagiarism_threshold=25.0,
            max_score=100
        )
        self.assignment_manager.publish_assignment(self.assignment.id)
    
    def test_plagiarism_check_on_submission(self):
        """Test plagiarism check when submitting"""
        # Create first submission
        submission1 = self.submission_manager.create_submission(
            assignment_id=self.assignment.id,
            student_id=self.students[0].id
        )
        
        original_text = "This is an original piece of writing about mathematics and its applications in real life."
        
        self.submission_manager.submit_assignment(
            submission1.id,
            {'submission_text': original_text}
        )
        
        # Create second submission with similar text
        submission2 = self.submission_manager.create_submission(
            assignment_id=self.assignment.id,
            student_id=self.students[1].id
        )
        
        similar_text = "This is an original piece of writing about mathematics and its applications in daily life."
        
        self.submission_manager.submit_assignment(
            submission2.id,
            {'submission_text': similar_text}
        )
        
        submission2.refresh_from_db()
        
        # Check that plagiarism score was calculated
        self.assertIsNotNone(submission2.plagiarism_score)
        self.assertGreater(submission2.plagiarism_score, 0)
    
    def test_plagiarism_check_individual_submission(self):
        """Test running plagiarism check on individual submission"""
        submission = self.submission_manager.create_submission(
            assignment_id=self.assignment.id,
            student_id=self.students[0].id
        )
        
        self.submission_manager.submit_assignment(
            submission.id,
            {'submission_text': "This is my unique submission text"}
        )
        
        result = self.plagiarism_detector.check_submission(submission.id)
        
        self.assertIn('plagiarism_score', result)
        self.assertIn('threshold', result)
        self.assertIn('is_flagged', result)
        self.assertEqual(result['threshold'], 25.0)
    
    def test_batch_plagiarism_check(self):
        """Test batch plagiarism check for all submissions"""
        # Create multiple submissions
        texts = [
            "This is the first unique submission about mathematics",
            "This is the second unique submission about science",
            "This is the first unique submission about mathematics"  # Similar to first
        ]
        
        for i, text in enumerate(texts):
            submission = self.submission_manager.create_submission(
                assignment_id=self.assignment.id,
                student_id=self.students[i].id
            )
            
            self.submission_manager.submit_assignment(
                submission.id,
                {'submission_text': text}
            )
        
        result = self.plagiarism_detector.batch_check_assignment(self.assignment.id)
        
        self.assertIn('total_checked', result)
        self.assertIn('flagged_submissions', result)
        self.assertIn('results', result)
        self.assertEqual(result['total_checked'], 3)
    
    def test_high_similarity_detection(self):
        """Test detection of high similarity submissions"""
        # Create two very similar submissions
        base_text = "The quick brown fox jumps over the lazy dog. This is a test sentence for plagiarism detection."
        similar_text = "The quick brown fox jumps over the lazy dog. This is a test sentence for plagiarism detection."
        
        # First submission
        submission1 = self.submission_manager.create_submission(
            assignment_id=self.assignment.id,
            student_id=self.students[0].id
        )
        self.submission_manager.submit_assignment(
            submission1.id,
            {'submission_text': base_text}
        )
        
        # Second submission (identical)
        submission2 = self.submission_manager.create_submission(
            assignment_id=self.assignment.id,
            student_id=self.students[1].id
        )
        self.submission_manager.submit_assignment(
            submission2.id,
            {'submission_text': similar_text}
        )
        
        submission2.refresh_from_db()
        
        # Should detect high similarity
        self.assertGreaterEqual(submission2.plagiarism_score, self.assignment.plagiarism_threshold)
        
        # Check plagiarism alerts
        alerts = self.submission_manager.get_plagiarism_alerts()
        self.assertGreater(alerts.count(), 0)


class AnalyticsTests(AssignmentSystemTestCase):
    """Test assignment analytics functionality"""
    
    def setUp(self):
        super().setUp()
        
        # Create assignment
        due_date = timezone.now() + timedelta(days=7)
        self.assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Analytics Test Assignment",
            description="Assignment for testing analytics",
            due_date=due_date,
            max_score=100
        )
        self.assignment_manager.publish_assignment(self.assignment.id)
        
        # Create submissions with different scores
        scores = [95, 87, 78, 65, 45]
        for i, score in enumerate(scores):
            submission = self.submission_manager.create_submission(
                assignment_id=self.assignment.id,
                student_id=self.students[i].id
            )
            
            self.submission_manager.submit_assignment(
                submission.id,
                {'submission_text': f"Submission {i+1}"}
            )
            
            self.submission_manager.grade_submission(
                submission_id=submission.id,
                score=score,
                graded_by=self.teacher_user
            )
    
    def test_generate_assignment_analytics(self):
        """Test generating assignment analytics"""
        analytics_data = self.analytics_manager.generate_assignment_analytics(
            self.assignment.id
        )
        
        self.assertIn('total_students', analytics_data)
        self.assertIn('submitted', analytics_data)
        self.assertIn('graded', analytics_data)
        self.assertIn('average_score', analytics_data)
        self.assertIn('highest_score', analytics_data)
        self.assertIn('lowest_score', analytics_data)
        
        self.assertEqual(analytics_data['total_students'], 5)
        self.assertEqual(analytics_data['submitted'], 5)
        self.assertEqual(analytics_data['graded'], 5)
        self.assertEqual(analytics_data['average_score'], 74.0)  # (95+87+78+65+45)/5
        self.assertEqual(analytics_data['highest_score'], 95)
        self.assertEqual(analytics_data['lowest_score'], 45)
    
    def test_student_performance_analytics(self):
        """Test student performance analytics"""
        student = self.students[0]  # Student with score 95
        
        analytics_data = self.analytics_manager.get_student_performance_analytics(
            student.id
        )
        
        self.assertIn('student', analytics_data)
        self.assertIn('total_assignments', analytics_data)
        self.assertIn('average_score', analytics_data)
        self.assertIn('on_time_submissions', analytics_data)
        self.assertIn('grade_distribution', analytics_data)
        
        self.assertEqual(analytics_data['total_assignments'], 1)
        self.assertEqual(analytics_data['average_score'], 95.0)
        self.assertEqual(analytics_data['on_time_submissions'], 1)
    
    def test_class_performance_analytics(self):
        """Test class performance analytics"""
        analytics_data = self.analytics_manager.get_class_performance_analytics(
            self.class_obj.id,
            assignment_id=self.assignment.id
        )
        
        self.assertIn('class', analytics_data)
        self.assertIn('total_students', analytics_data)
        self.assertIn('class_average', analytics_data)
        self.assertIn('student_rankings', analytics_data)
        self.assertIn('grade_distribution', analytics_data)
        
        self.assertEqual(analytics_data['total_students'], 5)
        self.assertEqual(analytics_data['class_average'], 74.0)
        self.assertEqual(len(analytics_data['student_rankings']), 5)
        
        # Check that rankings are sorted by score (highest first)
        rankings = analytics_data['student_rankings']
        self.assertEqual(rankings[0]['average_score'], 95.0)
        self.assertEqual(rankings[-1]['average_score'], 45.0)
    
    def test_grade_distribution_calculation(self):
        """Test grade distribution calculation"""
        analytics_data = self.analytics_manager.generate_assignment_analytics(
            self.assignment.id
        )
        
        # Check score distribution
        if 'score_distribution' in analytics_data:
            distribution = analytics_data['score_distribution']
            
            # With scores [95, 87, 78, 65, 45]:
            # A (90-100): 1 (95)
            # B (80-89): 1 (87)  
            # C (70-79): 1 (78)
            # D (60-69): 1 (65)
            # F (0-59): 1 (45)
            
            self.assertEqual(distribution.get('A (90-100)', 0), 1)
            self.assertEqual(distribution.get('B (80-89)', 0), 1)
            self.assertEqual(distribution.get('C (70-79)', 0), 1)
            self.assertEqual(distribution.get('D (60-69)', 0), 1)
            self.assertEqual(distribution.get('F (0-59)', 0), 1)


class AssignmentIntegrationTests(AssignmentSystemTestCase):
    """Integration tests for complete assignment workflows"""
    
    def test_complete_assignment_workflow(self):
        """Test complete assignment workflow from creation to analytics"""
        # 1. Create assignment
        due_date = timezone.now() + timedelta(days=7)
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Integration Test Assignment",
            description="Complete workflow test",
            due_date=due_date,
            assignment_type="homework",
            max_score=100,
            plagiarism_check_enabled=True
        )
        
        # 2. Publish assignment
        success = self.assignment_manager.publish_assignment(assignment.id)
        self.assertTrue(success)
        
        # 3. Students submit assignments
        for i, student in enumerate(self.students[:3]):
            submission = self.submission_manager.create_submission(
                assignment_id=assignment.id,
                student_id=student.id
            )
            
            self.submission_manager.submit_assignment(
                submission.id,
                {'submission_text': f"Student {i+1} submission content"}
            )
        
        # 4. Teacher grades submissions
        submissions = AssignmentSubmission.objects.filter(assignment=assignment)
        scores = [90, 85, 80]
        
        for submission, score in zip(submissions, scores):
            self.submission_manager.grade_submission(
                submission_id=submission.id,
                score=score,
                feedback=f"Good work! Score: {score}",
                graded_by=self.teacher_user
            )
        
        # 5. Generate analytics
        analytics_data = self.analytics_manager.generate_assignment_analytics(
            assignment.id
        )
        
        # 6. Verify complete workflow
        self.assertEqual(analytics_data['total_students'], 5)
        self.assertEqual(analytics_data['submitted'], 3)
        self.assertEqual(analytics_data['graded'], 3)
        self.assertEqual(analytics_data['average_score'], 85.0)
        
        # Check assignment statistics
        stats = self.assignment_manager.get_assignment_statistics(assignment.id)
        self.assertEqual(stats['submission_rate'], 60.0)  # 3/5 * 100
        self.assertEqual(stats['grading_progress'], 100.0)  # 3/3 * 100
    
    def test_group_assignment_workflow(self):
        """Test complete group assignment workflow"""
        # 1. Create group assignment
        due_date = timezone.now() + timedelta(days=14)
        assignment = self.assignment_manager.create_assignment(
            class_subject=self.class_subject,
            title="Group Project",
            description="Team collaboration project",
            due_date=due_date,
            assignment_type="project",
            group_assignment=True,
            max_group_size=3,
            max_score=200
        )
        
        self.assignment_manager.publish_assignment(assignment.id)
        
        # 2. Create groups
        group1 = self.group_manager.create_group(
            assignment_id=assignment.id,
            name="Team A",
            leader_id=self.students[0].id,
            member_ids=[self.students[1].id, self.students[2].id]
        )
        
        group2 = self.group_manager.create_group(
            assignment_id=assignment.id,
            name="Team B",
            leader_id=self.students[3].id,
            member_ids=[self.students[4].id]
        )
        
        # 3. Finalize groups
        self.group_manager.finalize_group(group1.id)
        self.group_manager.finalize_group(group2.id)
        
        # 4. Submit group assignments
        for group in [group1, group2]:
            submission = self.submission_manager.create_submission(
                assignment_id=assignment.id,
                student_id=group.leader.id
            )
            
            # Add group members to submission
            submission.group_members.set(group.members.all())
            
            self.submission_manager.submit_assignment(
                submission.id,
                {'submission_text': f"Group submission from {group.name}"}
            )
        
        # 5. Grade submissions
        submissions = AssignmentSubmission.objects.filter(assignment=assignment)
        for i, submission in enumerate(submissions):
            score = 180 - (i * 10)  # 180, 170
            self.submission_manager.grade_submission(
                submission_id=submission.id,
                score=score,
                graded_by=self.teacher_user
            )
        
        # 6. Verify workflow
        stats = self.assignment_manager.get_assignment_statistics(assignment.id)
        self.assertEqual(stats['submitted'], 2)  # 2 group submissions
        self.assertEqual(stats['graded'], 2)
        
        # Check groups
        groups = AssignmentGroup.objects.filter(assignment=assignment)
        self.assertEqual(groups.count(), 2)
        for group in groups:
            self.assertTrue(group.is_finalized)


if __name__ == '__main__':
    pytest.main([__file__])