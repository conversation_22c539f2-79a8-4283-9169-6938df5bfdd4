# Generated by Django 5.2.4 on 2025-07-29 12:25

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('action', models.CharField(choices=[('CREATE', 'Create'), ('UPDATE', 'Update'), ('DELETE', 'Delete'), ('LOGIN', 'Login'), ('LOGOUT', 'Logout')], max_length=50, verbose_name='Action')),
                ('model_name', models.CharField(max_length=100, verbose_name='Model Name')),
                ('object_id', models.Char<PERSON><PERSON>(max_length=255, verbose_name='Object ID')),
                ('object_repr', models.CharField(max_length=200, verbose_name='Object Representation')),
                ('changes', models.JSONField(blank=True, default=dict, verbose_name='Changes')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address')),
                ('user_agent', models.TextField(blank=True, verbose_name='User Agent')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='Timestamp')),
            ],
            options={
                'verbose_name': 'Audit Log',
                'verbose_name_plural': 'Audit Logs',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddField(
            model_name='academicyear',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created By'),
        ),
        migrations.AddField(
            model_name='academicyear',
            name='registration_end_date',
            field=models.DateField(blank=True, null=True, verbose_name='Registration End Date'),
        ),
        migrations.AddField(
            model_name='academicyear',
            name='registration_start_date',
            field=models.DateField(blank=True, null=True, verbose_name='Registration Start Date'),
        ),
        migrations.AddField(
            model_name='academicyear',
            name='school',
            field=models.ForeignKey(default=1, help_text='School this record belongs to', on_delete=django.db.models.deletion.CASCADE, to='core.school', verbose_name='School'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='academicyear',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated By'),
        ),
        migrations.AddField(
            model_name='school',
            name='academic_year_start_month',
            field=models.IntegerField(default=9, help_text='Month when academic year starts (1-12)', verbose_name='Academic Year Start Month'),
        ),
        migrations.AddField(
            model_name='school',
            name='currency',
            field=models.CharField(default='USD', max_length=3, verbose_name='Currency Code'),
        ),
        migrations.AddField(
            model_name='school',
            name='settings',
            field=models.JSONField(blank=True, default=dict, help_text='JSON field for school-specific configuration', verbose_name='School Settings'),
        ),
        migrations.AddField(
            model_name='school',
            name='timezone',
            field=models.CharField(default='UTC', max_length=50, verbose_name='Timezone'),
        ),
        migrations.AddField(
            model_name='semester',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created By'),
        ),
        migrations.AddField(
            model_name='semester',
            name='exam_end_date',
            field=models.DateField(blank=True, null=True, verbose_name='Exam End Date'),
        ),
        migrations.AddField(
            model_name='semester',
            name='exam_start_date',
            field=models.DateField(blank=True, null=True, verbose_name='Exam Start Date'),
        ),
        migrations.AddField(
            model_name='semester',
            name='name_ar',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Semester Name (Arabic)'),
        ),
        migrations.AddField(
            model_name='semester',
            name='school',
            field=models.ForeignKey(default=1, help_text='School this record belongs to', on_delete=django.db.models.deletion.CASCADE, to='core.school', verbose_name='School'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='semester',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated By'),
        ),
        migrations.AlterField(
            model_name='academicyear',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='school',
            name='code',
            field=models.CharField(help_text='Unique identifier for the school', max_length=20, unique=True, verbose_name='School Code'),
        ),
        migrations.AlterField(
            model_name='school',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='semester',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False),
        ),
        migrations.AlterUniqueTogether(
            name='academicyear',
            unique_together={('school', 'name')},
        ),
        migrations.AlterUniqueTogether(
            name='semester',
            unique_together={('academic_year', 'name')},
        ),
        migrations.AddIndex(
            model_name='academicyear',
            index=models.Index(fields=['school', 'is_current'], name='core_academ_school__90a6f9_idx'),
        ),
        migrations.AddIndex(
            model_name='academicyear',
            index=models.Index(fields=['school', 'start_date'], name='core_academ_school__b2ce6f_idx'),
        ),
        migrations.AddIndex(
            model_name='school',
            index=models.Index(fields=['code'], name='core_school_code_987360_idx'),
        ),
        migrations.AddIndex(
            model_name='school',
            index=models.Index(fields=['is_active'], name='core_school_is_acti_2e0f0f_idx'),
        ),
        migrations.AddIndex(
            model_name='semester',
            index=models.Index(fields=['school', 'is_current'], name='core_semest_school__a38108_idx'),
        ),
        migrations.AddIndex(
            model_name='semester',
            index=models.Index(fields=['academic_year', 'start_date'], name='core_semest_academi_d94616_idx'),
        ),
        migrations.AddField(
            model_name='auditlog',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.school', verbose_name='School'),
        ),
        migrations.AddField(
            model_name='auditlog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['school', 'timestamp'], name='core_auditl_school__3d6e5f_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['user', 'timestamp'], name='core_auditl_user_id_7b678c_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['model_name', 'object_id'], name='core_auditl_model_n_3fb686_idx'),
        ),
    ]
