{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Attendance Summary" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2>
                <i class="fas fa-chart-bar text-primary me-2"></i>{% trans "Attendance Summary" %}
            </h2>
            <p class="text-muted">{% trans "Overview of student attendance statistics" %}</p>
        </div>
    </div>

    <!-- Filter options -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="grade" class="form-label">{% trans "Grade" %}</label>
                            <select class="form-select" id="grade" name="grade">
                                <option value="">{% trans "All Grades" %}</option>
                                <!-- Add grade options here -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="class" class="form-label">{% trans "Class" %}</label>
                            <select class="form-select" id="class" name="class">
                                <option value="">{% trans "All Classes" %}</option>
                                <!-- Add class options here -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="subject" class="form-label">{% trans "Subject" %}</label>
                            <select class="form-select" id="subject" name="subject">
                                <option value="">{% trans "All Subjects" %}</option>
                                <!-- Add subject options here -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="period" class="form-label">{% trans "Period" %}</label>
                            <select class="form-select" id="period" name="period">
                                <option value="current_month">{% trans "Current Month" %}</option>
                                <option value="last_month">{% trans "Last Month" %}</option>
                                <option value="current_semester">{% trans "Current Semester" %}</option>
                                <option value="academic_year">{% trans "Academic Year" %}</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>{% trans "Apply Filters" %}
                            </button>
                            <a href="{% url 'academics:attendance_summary' %}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-redo me-2"></i>{% trans "Reset" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Overall Attendance Rate" %}</h5>
                    <h2 class="display-4">85%</h2>
                    <p class="card-text">{% trans "Academic Year 2024-2025" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Present" %}</h5>
                    <h2 class="display-4">75%</h2>
                    <p class="card-text">{% trans "Total present days" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Late" %}</h5>
                    <h2 class="display-4">10%</h2>
                    <p class="card-text">{% trans "Total late arrivals" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Absent" %}</h5>
                    <h2 class="display-4">15%</h2>
                    <p class="card-text">{% trans "Total absences" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance trend chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{% trans "Attendance Trend" %}</h5>
                </div>
                <div class="card-body">
                    <canvas id="attendanceTrendChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Class comparison -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{% trans "Attendance by Class" %}</h5>
                </div>
                <div class="card-body">
                    <canvas id="attendanceByClassChart" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{% trans "Attendance by Subject" %}</h5>
                </div>
                <div class="card-body">
                    <canvas id="attendanceBySubjectChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Students with low attendance -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{% trans "Students with Low Attendance" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Student ID" %}</th>
                                    <th>{% trans "Student Name" %}</th>
                                    <th>{% trans "Class" %}</th>
                                    <th>{% trans "Attendance Rate" %}</th>
                                    <th>{% trans "Present Days" %}</th>
                                    <th>{% trans "Absent Days" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Sample data - replace with actual data -->
                                <tr>
                                    <td>ST001</td>
                                    <td>Ahmed Mohamed</td>
                                    <td>Grade 10-A</td>
                                    <td>65%</td>
                                    <td>13</td>
                                    <td>7</td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>ST045</td>
                                    <td>Sara Ahmed</td>
                                    <td>Grade 9-B</td>
                                    <td>70%</td>
                                    <td>14</td>
                                    <td>6</td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Attendance trend chart
    const trendCtx = document.getElementById('attendanceTrendChart').getContext('2d');
    const trendChart = new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7', 'Week 8'],
            datasets: [
                {
                    label: '{% trans "Present" %}',
                    data: [90, 85, 88, 82, 87, 89, 86, 84],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.3
                },
                {
                    label: '{% trans "Late" %}',
                    data: [5, 8, 7, 10, 8, 6, 9, 11],
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.3
                },
                {
                    label: '{% trans "Absent" %}',
                    data: [5, 7, 5, 8, 5, 5, 5, 5],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.3
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: '{% trans "Percentage" %}'
                    }
                }
            }
        }
    });

    // Attendance by class chart
    const classCtx = document.getElementById('attendanceByClassChart').getContext('2d');
    const classChart = new Chart(classCtx, {
        type: 'bar',
        data: {
            labels: ['Grade 9-A', 'Grade 9-B', 'Grade 10-A', 'Grade 10-B', 'Grade 11-A', 'Grade 11-B'],
            datasets: [
                {
                    label: '{% trans "Attendance Rate" %}',
                    data: [92, 88, 85, 90, 87, 83],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(255, 205, 86, 0.7)'
                    ],
                    borderColor: [
                        'rgb(54, 162, 235)',
                        'rgb(75, 192, 192)',
                        'rgb(255, 159, 64)',
                        'rgb(153, 102, 255)',
                        'rgb(255, 99, 132)',
                        'rgb(255, 205, 86)'
                    ],
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: '{% trans "Percentage" %}'
                    }
                }
            }
        }
    });

    // Attendance by subject chart
    const subjectCtx = document.getElementById('attendanceBySubjectChart').getContext('2d');
    const subjectChart = new Chart(subjectCtx, {
        type: 'bar',
        data: {
            labels: ['Mathematics', 'Science', 'English', 'Arabic', 'Social Studies', 'Physical Education'],
            datasets: [
                {
                    label: '{% trans "Attendance Rate" %}',
                    data: [88, 92, 85, 90, 82, 95],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(255, 205, 86, 0.7)'
                    ],
                    borderColor: [
                        'rgb(54, 162, 235)',
                        'rgb(75, 192, 192)',
                        'rgb(255, 159, 64)',
                        'rgb(153, 102, 255)',
                        'rgb(255, 99, 132)',
                        'rgb(255, 205, 86)'
                    ],
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: '{% trans "Percentage" %}'
                    }
                }
            }
        }
    });
});
</script>
{% endblock %}