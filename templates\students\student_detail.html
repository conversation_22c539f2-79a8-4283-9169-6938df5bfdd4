{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ student.first_name }} {{ student.last_name }} - {% trans "Student Details" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .student-profile-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        padding: 2rem;
    }
    .student-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        font-weight: bold;
        margin: 0 auto 1rem;
    }
    .info-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .info-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .nav-pills .nav-link {
        border-radius: 25px;
        margin-right: 0.5rem;
    }
    .nav-pills .nav-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'students:list' %}">{% trans "Students" %}</a></li>
                    <li class="breadcrumb-item active">{{ student.first_name }} {{ student.last_name }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Student Profile Header -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="student-profile-card text-center">
                <div class="student-avatar">
                    {{ student.first_name|first }}{{ student.last_name|first }}
                </div>
                <h3 class="mb-1">{{ student.first_name }} {{ student.last_name }}</h3>
                <p class="mb-2">{% trans "Student ID" %}: {{ student.student_id|default:"N/A" }}</p>
                <p class="mb-3">{% trans "Admission No" %}: {{ student.admission_number|default:"N/A" }}</p>
                <div class="row text-center">
                    <div class="col-6">
                        <h5>{{ student.current_class|default:"N/A" }}</h5>
                        <small>{% trans "Current Class" %}</small>
                    </div>
                    <div class="col-6">
                        <h5>{{ student.current_class.grade|default:"N/A" }}</h5>
                        <small>{% trans "Grade Level" %}</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-8">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card info-card">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-alt text-primary fa-2x mb-2"></i>
                            <h5>{{ student.date_of_birth|date:"M d, Y"|default:"N/A" }}</h5>
                            <small class="text-muted">{% trans "Date of Birth" %}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card info-card">
                        <div class="card-body text-center">
                            <i class="fas fa-venus-mars text-info fa-2x mb-2"></i>
                            <h5>{{ student.get_gender_display|default:"N/A" }}</h5>
                            <small class="text-muted">{% trans "Gender" %}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card info-card">
                        <div class="card-body text-center">
                            <i class="fas fa-user-check text-success fa-2x mb-2"></i>
                            <h5>{% trans "Active" %}</h5>
                            <small class="text-muted">{% trans "Status" %}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card info-card">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-plus text-warning fa-2x mb-2"></i>
                            <h5>{{ student.admission_date|date:"M Y"|default:"N/A" }}</h5>
                            <small class="text-muted">{% trans "Admission Date" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{% trans "Quick Actions" %}</h5>
                        <div class="btn-group" role="group">
                            <a href="{% url 'students:edit' student.pk %}" class="btn btn-primary">
                                <i class="fas fa-edit me-2"></i>{% trans "Edit Student" %}
                            </a>
                            <button class="btn btn-success">
                                <i class="fas fa-print me-2"></i>{% trans "Print Profile" %}
                            </button>
                            <button class="btn btn-info">
                                <i class="fas fa-id-card me-2"></i>{% trans "Generate ID Card" %}
                            </button>
                            <button class="btn btn-warning">
                                <i class="fas fa-file-pdf me-2"></i>{% trans "Export PDF" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Information Tabs -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-pills card-header-pills" id="studentTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="personal-tab" data-bs-toggle="pill" data-bs-target="#personal" type="button" role="tab">
                                <i class="fas fa-user me-2"></i>{% trans "Personal Info" %}
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="parent-tab" data-bs-toggle="pill" data-bs-target="#parent" type="button" role="tab">
                                <i class="fas fa-users me-2"></i>{% trans "Parent Info" %}
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="academic-tab" data-bs-toggle="pill" data-bs-target="#academic" type="button" role="tab">
                                <i class="fas fa-graduation-cap me-2"></i>{% trans "Academic" %}
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="attendance-tab" data-bs-toggle="pill" data-bs-target="#attendance" type="button" role="tab">
                                <i class="fas fa-calendar-check me-2"></i>{% trans "Attendance" %}
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="documents-tab" data-bs-toggle="pill" data-bs-target="#documents" type="button" role="tab">
                                <i class="fas fa-file-alt me-2"></i>{% trans "Documents" %}
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="studentTabsContent">
                        <!-- Personal Information Tab -->
                        <div class="tab-pane fade show active" id="personal" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">{% trans "Basic Information" %}</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{% trans "First Name" %}:</strong></td>
                                            <td>{{ student.first_name|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Last Name" %}:</strong></td>
                                            <td>{{ student.last_name|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Date of Birth" %}:</strong></td>
                                            <td>{{ student.date_of_birth|date:"F d, Y"|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Gender" %}:</strong></td>
                                            <td>{{ student.get_gender_display|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Blood Group" %}:</strong></td>
                                            <td>{{ student.blood_group|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Religion" %}:</strong></td>
                                            <td>{{ student.religion|default:"N/A" }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">{% trans "Contact Information" %}</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{% trans "Phone" %}:</strong></td>
                                            <td>{{ student.phone|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Email" %}:</strong></td>
                                            <td>{{ student.user.email|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Address" %}:</strong></td>
                                            <td>{{ student.address|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "City" %}:</strong></td>
                                            <td>{{ student.city|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "State" %}:</strong></td>
                                            <td>{{ student.state|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Country" %}:</strong></td>
                                            <td>{{ student.country|default:"N/A" }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Parent Information Tab -->
                        <div class="tab-pane fade" id="parent" role="tabpanel">
                            {% if student.parent %}
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-primary mb-3">{% trans "Father Information" %}</h6>
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>{% trans "Father Name" %}:</strong></td>
                                                <td>{{ student.parent.father_name|default:"N/A" }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>{% trans "Father Phone" %}:</strong></td>
                                                <td>{{ student.parent.father_phone|default:"N/A" }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>{% trans "Father Occupation" %}:</strong></td>
                                                <td>{{ student.parent.father_occupation|default:"N/A" }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>{% trans "Father Email" %}:</strong></td>
                                                <td>{{ student.parent.father_email|default:"N/A" }}</td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-primary mb-3">{% trans "Mother Information" %}</h6>
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>{% trans "Mother Name" %}:</strong></td>
                                                <td>{{ student.parent.mother_name|default:"N/A" }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>{% trans "Mother Phone" %}:</strong></td>
                                                <td>{{ student.parent.mother_phone|default:"N/A" }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>{% trans "Mother Occupation" %}:</strong></td>
                                                <td>{{ student.parent.mother_occupation|default:"N/A" }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>{% trans "Mother Email" %}:</strong></td>
                                                <td>{{ student.parent.mother_email|default:"N/A" }}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">{% trans "Home Address" %}</h6>
                                        <p>{{ student.parent.home_address|default:"N/A" }}</p>
                                    </div>
                                </div>
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">{% trans "No parent information available" %}</h5>
                                    <a href="{% url 'students:add_parent' %}" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>{% trans "Add Parent Information" %}
                                    </a>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Academic Information Tab -->
                        <div class="tab-pane fade" id="academic" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">{% trans "Current Academic Status" %}</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{% trans "Current Class" %}:</strong></td>
                                            <td>{{ student.current_class|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Grade Level" %}:</strong></td>
                                            <td>{{ student.current_class.grade|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Academic Year" %}:</strong></td>
                                            <td>{{ student.current_class.academic_year|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Roll Number" %}:</strong></td>
                                            <td>{{ student.roll_number|default:"N/A" }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">{% trans "Admission Details" %}</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{% trans "Admission Date" %}:</strong></td>
                                            <td>{{ student.admission_date|date:"F d, Y"|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Admission Number" %}:</strong></td>
                                            <td>{{ student.admission_number|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Previous School" %}:</strong></td>
                                            <td>{{ student.previous_school|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Transfer Certificate" %}:</strong></td>
                                            <td>
                                                {% if student.transfer_certificate %}
                                                    <span class="badge bg-success">{% trans "Available" %}</span>
                                                {% else %}
                                                    <span class="badge bg-warning">{% trans "Not Available" %}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Attendance Tab -->
                        <div class="tab-pane fade" id="attendance" role="tabpanel">
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">{% trans "Attendance records will be displayed here" %}</h5>
                                <p class="text-muted">{% trans "Integration with attendance module coming soon" %}</p>
                            </div>
                        </div>

                        <!-- Documents Tab -->
                        <div class="tab-pane fade" id="documents" role="tabpanel">
                            <div class="text-center py-4">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">{% trans "Student documents will be displayed here" %}</h5>
                                <a href="{% url 'students:documents' %}" class="btn btn-primary">
                                    <i class="fas fa-upload me-2"></i>{% trans "Upload Documents" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Bootstrap tabs
        var triggerTabList = [].slice.call(document.querySelectorAll('#studentTabs button'))
        triggerTabList.forEach(function (triggerEl) {
            var tabTrigger = new bootstrap.Tab(triggerEl)
            
            triggerEl.addEventListener('click', function (event) {
                event.preventDefault()
                tabTrigger.show()
            })
        })
    });
</script>
{% endblock %}
