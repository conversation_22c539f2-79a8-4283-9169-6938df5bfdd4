import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from datetime import date, timedelta
from decimal import Decimal

from core.models import School, AcademicYear, Semester
from students.models import Grade, Class, Student
from academics.models import Subject, Teacher, ClassSubject

User = get_user_model()


class BasicAcademicModelsTest(TestCase):
    """Test cases for basic academic models that are already implemented"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.semester = Semester.objects.create(
            school=self.school,
            academic_year=self.academic_year,
            name="First Semester",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1,
            max_capacity=30
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )

    def test_subject_basic_creation(self):
        """Test basic subject creation with minimal fields"""
        subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH101",
            credit_hours=3
        )
        
        self.assertEqual(subject.name, "Mathematics")
        self.assertEqual(subject.code, "MATH101")
        self.assertEqual(subject.credit_hours, 3)
        self.assertEqual(str(subject), "MATH101 - Mathematics")

    def test_teacher_basic_creation(self):
        """Test basic teacher creation"""
        teacher_user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        teacher = Teacher.objects.create(
            school=self.school,
            user=teacher_user,
            employee_id="T001",
            hire_date=date.today(),
            qualification="Master's in Mathematics"
        )
        
        self.assertEqual(teacher.employee_id, "T001")
        self.assertEqual(teacher.user, teacher_user)
        self.assertEqual(teacher.school, self.school)

    def test_class_subject_basic_creation(self):
        """Test basic class subject creation"""
        # Create a class
        class_obj = Class.objects.create(
            school=self.school,
            name="Section A",
            grade=self.grade,
            academic_year=self.academic_year,
            max_students=30
        )
        
        # Create a subject
        subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH101",
            credit_hours=3
        )
        
        # Create a teacher
        teacher_user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        teacher = Teacher.objects.create(
            school=self.school,
            user=teacher_user,
            employee_id="T001",
            hire_date=date.today(),
            qualification="Master's in Mathematics"
        )
        
        # Add subject to teacher's qualifications
        teacher.subjects.add(subject)
        
        # Create class subject assignment
        class_subject = ClassSubject.objects.create(
            school=self.school,
            class_obj=class_obj,
            subject=subject,
            teacher=teacher,
            academic_year=self.academic_year,
            semester=self.semester,
            weekly_hours=5
        )
        
        self.assertEqual(class_subject.class_obj, class_obj)
        self.assertEqual(class_subject.subject, subject)
        self.assertEqual(class_subject.teacher, teacher)
        self.assertEqual(class_subject.weekly_hours, 5)

    def test_school_multi_tenancy(self):
        """Test that models are properly isolated by school"""
        # Create another school
        school2 = School.objects.create(
            name="Test School 2",
            code="TEST2",
            address="Test Address 2",
            phone="+1234567891",
            email="<EMAIL>",
            principal_name="Test Principal 2",
            established_date=date.today()
        )
        
        # Create subjects for both schools
        subject1 = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH101",
            credit_hours=3
        )
        
        subject2 = Subject.objects.create(
            school=school2,
            name="Mathematics",
            code="MATH101",
            credit_hours=3
        )
        
        # Verify subjects are isolated by school
        school1_subjects = Subject.objects.filter(school=self.school)
        school2_subjects = Subject.objects.filter(school=school2)
        
        self.assertEqual(school1_subjects.count(), 1)
        self.assertEqual(school2_subjects.count(), 1)
        self.assertIn(subject1, school1_subjects)
        self.assertIn(subject2, school2_subjects)
        self.assertNotIn(subject1, school2_subjects)
        self.assertNotIn(subject2, school1_subjects)

    def test_academic_year_current_flag(self):
        """Test academic year current flag functionality"""
        # Create another academic year
        academic_year2 = AcademicYear.objects.create(
            school=self.school,
            name="2025-2026",
            start_date=date(2025, 9, 1),
            end_date=date(2026, 6, 30),
            is_current=False
        )
        
        # Verify only one can be current
        self.assertTrue(self.academic_year.is_current)
        self.assertFalse(academic_year2.is_current)
        
        # Test getting current academic year
        current_year = AcademicYear.objects.filter(
            school=self.school,
            is_current=True
        ).first()
        
        self.assertEqual(current_year, self.academic_year)

    def test_grade_capacity_basic(self):
        """Test basic grade capacity functionality"""
        self.assertEqual(self.grade.max_capacity, 30)
        self.assertEqual(self.grade.level, 1)
        self.assertEqual(self.grade.name, "Grade 1")

    def test_class_student_capacity(self):
        """Test class student capacity"""
        class_obj = Class.objects.create(
            school=self.school,
            name="Section A",
            grade=self.grade,
            academic_year=self.academic_year,
            max_students=25
        )
        
        self.assertEqual(class_obj.max_students, 25)
        self.assertEqual(class_obj.grade, self.grade)
        self.assertEqual(class_obj.academic_year, self.academic_year)


@pytest.mark.django_db
class TestBasicAcademicIntegration:
    """Integration tests for basic academic functionality"""
    
    def test_complete_basic_setup(self):
        """Test setting up a complete basic academic structure"""
        # Create school
        school = School.objects.create(
            name="Integration Test School",
            code="ITS",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        # Create academic year
        academic_year = AcademicYear.objects.create(
            school=school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        # Create semester
        semester = Semester.objects.create(
            school=school,
            academic_year=academic_year,
            name="First Semester",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        # Create grade
        grade = Grade.objects.create(
            school=school,
            name="Grade 1",
            level=1,
            max_capacity=60
        )
        
        # Create class
        class_obj = Class.objects.create(
            school=school,
            name="Section A",
            grade=grade,
            academic_year=academic_year,
            max_students=30
        )
        
        # Create user and teacher
        user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        teacher = Teacher.objects.create(
            school=school,
            user=user,
            employee_id="T001",
            hire_date=date.today(),
            qualification="Master's in Mathematics"
        )
        
        # Create subject
        subject = Subject.objects.create(
            school=school,
            name="Mathematics",
            code="MATH001",
            credit_hours=3
        )
        
        # Add subject to teacher's qualifications
        teacher.subjects.add(subject)
        
        # Create class subject assignment
        class_subject = ClassSubject.objects.create(
            school=school,
            class_obj=class_obj,
            subject=subject,
            teacher=teacher,
            academic_year=academic_year,
            semester=semester,
            weekly_hours=5
        )
        
        # Verify the complete structure
        assert school.name == "Integration Test School"
        assert academic_year.is_current is True
        assert grade.max_capacity == 60
        assert class_obj.grade == grade
        assert teacher.subjects.filter(pk=subject.pk).exists()
        assert class_subject.teacher == teacher
        assert class_subject.weekly_hours == 5