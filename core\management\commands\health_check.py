"""
Management command to perform system health checks
"""
from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.db import connection
from django.conf import settings
import redis
import os


class Command(BaseCommand):
    help = 'Perform system health checks'

    def handle(self, *args, **options):
        self.stdout.write('Performing system health checks...\n')
        
        all_checks_passed = True
        
        # Database check
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            self.stdout.write(
                self.style.SUCCESS('✓ Database connection: OK')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Database connection: FAILED - {str(e)}')
            )
            all_checks_passed = False
        
        # Cache check
        try:
            cache.set('health_check', 'test', 30)
            result = cache.get('health_check')
            if result == 'test':
                self.stdout.write(
                    self.style.SUCCESS('✓ Cache system: OK')
                )
            else:
                raise Exception('Cache test failed')
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Cache system: FAILED - {str(e)}')
            )
            all_checks_passed = False
        
        # Redis check (if configured)
        try:
            redis_host = getattr(settings, 'REDIS_HOST', 'localhost')
            redis_port = getattr(settings, 'REDIS_PORT', 6379)
            redis_db = getattr(settings, 'REDIS_DB', 0)
            
            r = redis.Redis(host=redis_host, port=redis_port, db=redis_db)
            r.ping()
            self.stdout.write(
                self.style.SUCCESS('✓ Redis connection: OK')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Redis connection: FAILED - {str(e)}')
            )
            all_checks_passed = False
        
        # File system checks
        media_root = settings.MEDIA_ROOT
        static_root = settings.STATIC_ROOT
        
        # Check media directory
        if os.path.exists(media_root) and os.access(media_root, os.W_OK):
            self.stdout.write(
                self.style.SUCCESS('✓ Media directory: OK')
            )
        else:
            self.stdout.write(
                self.style.ERROR('✗ Media directory: Not writable or missing')
            )
            all_checks_passed = False
        
        # Check static directory
        if os.path.exists(static_root):
            self.stdout.write(
                self.style.SUCCESS('✓ Static directory: OK')
            )
        else:
            self.stdout.write(
                self.style.WARNING('⚠ Static directory: Missing (run collectstatic)')
            )
        
        # Check logs directory
        logs_dir = os.path.join(settings.BASE_DIR, 'logs')
        if os.path.exists(logs_dir) and os.access(logs_dir, os.W_OK):
            self.stdout.write(
                self.style.SUCCESS('✓ Logs directory: OK')
            )
        else:
            self.stdout.write(
                self.style.ERROR('✗ Logs directory: Not writable or missing')
            )
            all_checks_passed = False
        
        # Check environment variables
        required_env_vars = ['SECRET_KEY']
        for var in required_env_vars:
            if getattr(settings, var, None):
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Environment variable {var}: OK')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'✗ Environment variable {var}: Missing')
                )
                all_checks_passed = False
        
        # Overall status
        self.stdout.write('\n' + '='*50)
        if all_checks_passed:
            self.stdout.write(
                self.style.SUCCESS('All health checks passed! System is ready.')
            )
        else:
            self.stdout.write(
                self.style.ERROR('Some health checks failed. Please review the issues above.')
            )
        
        return all_checks_passed