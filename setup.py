#!/usr/bin/env python
"""
Setup script for School ERP System
"""
import os
import sys
import subprocess
import platform


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False


def main():
    """Main setup function"""
    print("School ERP System Setup")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 10):
        print("✗ Python 3.10 or higher is required")
        sys.exit(1)
    
    print(f"✓ Python {sys.version.split()[0]} detected")
    
    # Check if pipenv is installed
    try:
        subprocess.run(['pipenv', '--version'], check=True, capture_output=True)
        print("✓ Pipenv is available")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ Pipenv is not installed. Please install it first:")
        print("  pip install pipenv")
        sys.exit(1)
    
    # Install dependencies
    if not run_command('pipenv install --dev', 'Installing dependencies'):
        sys.exit(1)
    
    # Create .env file if it doesn't exist
    if not os.path.exists('.env'):
        print("\nCreating .env file...")
        try:
            with open('.env.example', 'r') as example_file:
                content = example_file.read()
            
            with open('.env', 'w') as env_file:
                env_file.write(content)
            
            print("✓ .env file created from .env.example")
            print("⚠ Please review and update the .env file with your settings")
        except Exception as e:
            print(f"✗ Failed to create .env file: {e}")
    else:
        print("✓ .env file already exists")
    
    # Initialize database
    if not run_command('pipenv run python manage.py init_db', 'Initializing database'):
        sys.exit(1)
    
    # Run tests
    if not run_command('pipenv run python manage.py test core --verbosity=1', 'Running tests'):
        print("⚠ Some tests failed, but setup can continue")
    
    # Generate encryption key
    print("\nGenerating encryption key...")
    try:
        result = subprocess.run(
            'pipenv run python manage.py generate_key --type=fernet',
            shell=True, check=True, capture_output=True, text=True
        )
        print("✓ Encryption key generated")
        print("⚠ Please add the generated key to your .env file")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to generate encryption key: {e}")
    
    # Health check
    print("\nRunning health check...")
    subprocess.run('pipenv run python manage.py health_check', shell=True)
    
    print("\n" + "=" * 50)
    print("Setup completed!")
    print("\nNext steps:")
    print("1. Review and update the .env file with your settings")
    print("2. Set up PostgreSQL and Redis (optional for development)")
    print("3. Run: pipenv run python manage.py setup_school --help")
    print("4. Start the development server: pipenv run python manage.py runserver")
    print("\nFor production deployment, see the documentation.")


if __name__ == '__main__':
    main()