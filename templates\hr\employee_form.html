{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
{% if object %}
    {% trans "Edit Employee" %} - {{ object.user.get_full_name }}
{% else %}
    {% trans "Add New Employee" %}
{% endif %}
- {{ block.super }}
{% endblock %}

{% block extra_css %}
<style>
    .form-card {
        border: none;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px 20px 0 0;
        padding: 2rem;
        text-align: center;
    }
    .form-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .form-section h5 {
        color: #495057;
        border-bottom: 2px solid #dee2e6;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .btn-save {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        color: white;
    }
    .btn-save:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        color: white;
    }
    .btn-cancel {
        border: 2px solid #6c757d;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        color: #6c757d;
        background: transparent;
    }
    .btn-cancel:hover {
        background: #6c757d;
        color: white;
    }
    .avatar-upload {
        position: relative;
        display: inline-block;
    }
    .avatar-preview {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid white;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .avatar-upload-btn {
        position: absolute;
        bottom: 0;
        right: 0;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">{% trans "HR Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'hr:employees' %}">{% trans "Employees" %}</a></li>
                    <li class="breadcrumb-item active">
                        {% if object %}
                            {% trans "Edit Employee" %}
                        {% else %}
                            {% trans "Add Employee" %}
                        {% endif %}
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card form-card">
                <div class="form-header">
                    <i class="fas fa-user-plus fa-3x mb-3"></i>
                    <h2 class="mb-1">
                        {% if object %}
                            {% trans "Edit Employee Information" %}
                        {% else %}
                            {% trans "Add New Employee" %}
                        {% endif %}
                    </h2>
                    <p class="mb-0">
                        {% if object %}
                            {% trans "Update employee details and information" %}
                        {% else %}
                            {% trans "Enter employee details to create a new employee record" %}
                        {% endif %}
                    </p>
                </div>

                <div class="card-body p-4">
                    <form method="post" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}
                        
                        <!-- Profile Picture Section -->
                        {% if object and object.user.profile_picture %}
                        <div class="text-center mb-4">
                            <div class="avatar-upload">
                                <img src="{{ object.user.profile_picture.url }}" alt="Profile" class="avatar-preview" id="avatarPreview">
                                <label for="id_profile_picture" class="avatar-upload-btn">
                                    <i class="fas fa-camera"></i>
                                </label>
                            </div>
                            <input type="file" id="id_profile_picture" name="profile_picture" accept="image/*" style="display: none;">
                            <div class="help-text mt-2">{% trans "Click the camera icon to change profile picture" %}</div>
                        </div>
                        {% endif %}

                        <!-- Basic Information -->
                        <div class="form-section">
                            <h5><i class="fas fa-user me-2"></i>{% trans "Basic Information" %}</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.user.id_for_label }}" class="form-label required-field">
                                        {% trans "User Account" %}
                                    </label>
                                    {{ form.user }}
                                    {% if form.user.help_text %}
                                    <div class="help-text">{{ form.user.help_text }}</div>
                                    {% endif %}
                                    {% if form.user.errors %}
                                    <div class="text-danger">{{ form.user.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.employee_id.id_for_label }}" class="form-label required-field">
                                        {% trans "Employee ID" %}
                                    </label>
                                    {{ form.employee_id }}
                                    {% if form.employee_id.help_text %}
                                    <div class="help-text">{{ form.employee_id.help_text }}</div>
                                    {% endif %}
                                    {% if form.employee_id.errors %}
                                    <div class="text-danger">{{ form.employee_id.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Position and Department -->
                        <div class="form-section">
                            <h5><i class="fas fa-briefcase me-2"></i>{% trans "Position and Department" %}</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.position.id_for_label }}" class="form-label required-field">
                                        {% trans "Position" %}
                                    </label>
                                    {{ form.position }}
                                    {% if form.position.help_text %}
                                    <div class="help-text">{{ form.position.help_text }}</div>
                                    {% endif %}
                                    {% if form.position.errors %}
                                    <div class="text-danger">{{ form.position.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.employment_status.id_for_label }}" class="form-label required-field">
                                        {% trans "Employment Status" %}
                                    </label>
                                    {{ form.employment_status }}
                                    {% if form.employment_status.help_text %}
                                    <div class="help-text">{{ form.employment_status.help_text }}</div>
                                    {% endif %}
                                    {% if form.employment_status.errors %}
                                    <div class="text-danger">{{ form.employment_status.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Employment Dates -->
                        <div class="form-section">
                            <h5><i class="fas fa-calendar me-2"></i>{% trans "Employment Dates" %}</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.hire_date.id_for_label }}" class="form-label required-field">
                                        {% trans "Hire Date" %}
                                    </label>
                                    {{ form.hire_date }}
                                    {% if form.hire_date.help_text %}
                                    <div class="help-text">{{ form.hire_date.help_text }}</div>
                                    {% endif %}
                                    {% if form.hire_date.errors %}
                                    <div class="text-danger">{{ form.hire_date.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.termination_date.id_for_label }}" class="form-label">
                                        {% trans "Termination Date" %}
                                    </label>
                                    {{ form.termination_date }}
                                    {% if form.termination_date.help_text %}
                                    <div class="help-text">{{ form.termination_date.help_text }}</div>
                                    {% endif %}
                                    {% if form.termination_date.errors %}
                                    <div class="text-danger">{{ form.termination_date.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Salary Information -->
                        <div class="form-section">
                            <h5><i class="fas fa-dollar-sign me-2"></i>{% trans "Salary Information" %}</h5>
                            <div class="row g-3">
                                <div class="col-md-12">
                                    <label for="{{ form.salary.id_for_label }}" class="form-label">
                                        {% trans "Salary" %}
                                    </label>
                                    {{ form.salary }}
                                    {% if form.salary.help_text %}
                                    <div class="help-text">{{ form.salary.help_text }}</div>
                                    {% endif %}
                                    {% if form.salary.errors %}
                                    <div class="text-danger">{{ form.salary.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="form-section">
                            <h5><i class="fas fa-phone me-2"></i>{% trans "Emergency Contact Information" %}</h5>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="{{ form.emergency_contact_name.id_for_label }}" class="form-label">
                                        {% trans "Contact Name" %}
                                    </label>
                                    {{ form.emergency_contact_name }}
                                    {% if form.emergency_contact_name.help_text %}
                                    <div class="help-text">{{ form.emergency_contact_name.help_text }}</div>
                                    {% endif %}
                                    {% if form.emergency_contact_name.errors %}
                                    <div class="text-danger">{{ form.emergency_contact_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label for="{{ form.emergency_contact_phone.id_for_label }}" class="form-label">
                                        {% trans "Contact Phone" %}
                                    </label>
                                    {{ form.emergency_contact_phone }}
                                    {% if form.emergency_contact_phone.help_text %}
                                    <div class="help-text">{{ form.emergency_contact_phone.help_text }}</div>
                                    {% endif %}
                                    {% if form.emergency_contact_phone.errors %}
                                    <div class="text-danger">{{ form.emergency_contact_phone.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label for="{{ form.emergency_contact_relationship.id_for_label }}" class="form-label">
                                        {% trans "Relationship" %}
                                    </label>
                                    {{ form.emergency_contact_relationship }}
                                    {% if form.emergency_contact_relationship.help_text %}
                                    <div class="help-text">{{ form.emergency_contact_relationship.help_text }}</div>
                                    {% endif %}
                                    {% if form.emergency_contact_relationship.errors %}
                                    <div class="text-danger">{{ form.emergency_contact_relationship.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-save me-3">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}
                                    {% trans "Update Employee" %}
                                {% else %}
                                    {% trans "Create Employee" %}
                                {% endif %}
                            </button>
                            <a href="{% url 'hr:employees' %}" class="btn btn-cancel">
                                <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Profile picture preview
    const profilePictureInput = document.getElementById('id_profile_picture');
    const avatarPreview = document.getElementById('avatarPreview');
    
    if (profilePictureInput) {
        profilePictureInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (avatarPreview) {
                        avatarPreview.src = e.target.result;
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('is-invalid');
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('{% trans "Please fill in all required fields." %}');
        }
    });

    // Auto-generate employee ID if empty
    const employeeIdField = document.getElementById('{{ form.employee_id.id_for_label }}');
    const userField = document.getElementById('{{ form.user.id_for_label }}');
    
    if (employeeIdField && userField && !employeeIdField.value) {
        userField.addEventListener('change', function() {
            if (this.value && !employeeIdField.value) {
                // Generate a simple employee ID based on current timestamp
                const timestamp = Date.now().toString().slice(-6);
                employeeIdField.value = 'EMP' + timestamp;
            }
        });
    }
});
</script>
{% endblock %}
