from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.validators import MinValueValidator
from django.db.models import Q, Sum
from decimal import Decimal
from core.models import BaseModel, AcademicYear
from students.models import Student, Grade


class AccountManager(models.Manager):
    """Custom manager for Account model"""
    
    def active(self):
        """Return only active accounts"""
        return self.filter(is_active=True, archived_at__isnull=True)
    
    def archived(self):
        """Return only archived accounts"""
        return self.filter(archived_at__isnull=False)
    
    def header_accounts(self):
        """Return only header accounts"""
        return self.filter(is_header=True)
    
    def detail_accounts(self):
        """Return only detail accounts (non-header)"""
        return self.filter(is_header=False)
    
    def by_type(self, account_type):
        """Return accounts by type"""
        return self.filter(account_type__type=account_type)
    
    def root_accounts(self):
        """Return root level accounts (no parent)"""
        return self.filter(parent__isnull=True)
    
    def build_tree(self):
        """Build account tree structure"""
        accounts = list(self.active().select_related('parent', 'account_type'))
        account_dict = {acc.id: acc for acc in accounts}
        
        # Add children to each account
        for account in accounts:
            account.children_list = []
        
        for account in accounts:
            if account.parent_id and account.parent_id in account_dict:
                parent = account_dict[account.parent_id]
                parent.children_list.append(account)
        
        # Return root accounts with their children
        return [acc for acc in accounts if acc.parent_id is None]
    
    def get_account_balances(self, as_of_date=None):
        """Get balances for all accounts"""
        from datetime import date
        
        if as_of_date is None:
            as_of_date = date.today()
        
        accounts = self.active().select_related('account_type')
        balances = {}
        
        for account in accounts:
            balances[account.id] = account.get_balance(as_of_date)
        
        return balances


class AccountType(BaseModel):
    """
    Account type model for chart of accounts
    """
    ACCOUNT_TYPES = (
        ('asset', _('Asset')),
        ('liability', _('Liability')),
        ('equity', _('Equity')),
        ('revenue', _('Revenue')),
        ('expense', _('Expense')),
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('Account Type Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Account Type Name (Arabic)')
    )

    type = models.CharField(
        max_length=20,
        choices=ACCOUNT_TYPES,
        verbose_name=_('Type')
    )

    class Meta:
        verbose_name = _('Account Type')
        verbose_name_plural = _('Account Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class Account(BaseModel):
    """
    Chart of accounts model with enhanced hierarchy and controls
    """
    code = models.CharField(
        max_length=20,
        verbose_name=_('Account Code'),
        help_text=_('Unique account code (e.g., 1000, 1100)')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Account Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Account Name (Arabic)')
    )

    account_type = models.ForeignKey(
        AccountType,
        on_delete=models.CASCADE,
        related_name='accounts',
        verbose_name=_('Account Type')
    )

    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name=_('Parent Account')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    is_header = models.BooleanField(
        default=False,
        verbose_name=_('Is Header Account'),
        help_text=_('Header accounts cannot have transactions posted to them')
    )

    # Enhanced fields for account controls
    level = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Account Level'),
        help_text=_('Hierarchy level (0 for root accounts)')
    )

    path = models.CharField(
        max_length=500,
        blank=True,
        verbose_name=_('Account Path'),
        help_text=_('Full path from root to this account')
    )

    is_system_account = models.BooleanField(
        default=False,
        verbose_name=_('Is System Account'),
        help_text=_('System accounts cannot be deleted')
    )

    allow_manual_entries = models.BooleanField(
        default=True,
        verbose_name=_('Allow Manual Entries'),
        help_text=_('Allow manual journal entries to this account')
    )

    is_reconcilable = models.BooleanField(
        default=False,
        verbose_name=_('Is Reconcilable'),
        help_text=_('Account requires reconciliation (e.g., bank accounts)')
    )

    opening_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Opening Balance')
    )

    current_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Current Balance')
    )

    # Archiving fields
    archived_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Archived At')
    )

    archived_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='archived_accounts',
        verbose_name=_('Archived By')
    )

    archive_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Archive Reason')
    )

    objects = AccountManager()

    class Meta:
        verbose_name = _('Account')
        verbose_name_plural = _('Accounts')
        ordering = ['code']
        unique_together = [['school', 'code']]
        indexes = [
            models.Index(fields=['school', 'code']),
            models.Index(fields=['school', 'account_type']),
            models.Index(fields=['school', 'parent']),
            models.Index(fields=['school', 'is_active']),
            models.Index(fields=['school', 'archived_at']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    def clean(self):
        from django.core.exceptions import ValidationError
        
        # Validate account code format
        if not self.code.isdigit():
            raise ValidationError(_('Account code must contain only digits'))
        
        # Validate parent account
        if self.parent:
            if self.parent.id == self.id:
                raise ValidationError(_('Account cannot be its own parent'))
            
            if not self.parent.is_header:
                raise ValidationError(_('Parent account must be a header account'))
            
            # Check for circular references
            if self._check_circular_reference():
                raise ValidationError(_('Circular reference detected in account hierarchy'))
        
        # Header accounts cannot have transactions
        if self.is_header and hasattr(self, 'journal_entries') and self.journal_entries.exists():
            raise ValidationError(_('Header accounts cannot have journal entries'))

    def save(self, *args, **kwargs):
        # Calculate level and path
        if self.parent:
            self.level = self.parent.level + 1
            self.path = f"{self.parent.path}/{self.code}" if self.parent.path else self.code
        else:
            self.level = 0
            self.path = self.code
        
        # Update current balance from opening balance if new account
        is_new = not self.pk
        if is_new:
            self.current_balance = self.opening_balance if self.opening_balance is not None else Decimal('0')
        
        super().save(*args, **kwargs)
        
        # Ensure current_balance is set correctly for new accounts
        if is_new and self.opening_balance:
            Account.objects.filter(pk=self.pk).update(current_balance=self.opening_balance)
        
        # Update children paths if path changed
        if self.children.exists():
            self._update_children_paths()

    def _check_circular_reference(self):
        """Check for circular references in account hierarchy"""
        visited = set()
        current = self.parent
        
        while current:
            if current.id in visited or current.id == self.id:
                return True
            visited.add(current.id)
            current = current.parent
        
        return False

    def _update_children_paths(self):
        """Update paths for all child accounts"""
        for child in self.children.all():
            child.path = f"{self.path}/{child.code}"
            child.save(update_fields=['path'])

    @property
    def full_name(self):
        """Return full account name with code"""
        return f"{self.code} - {self.name}"

    @property
    def is_archived(self):
        """Check if account is archived"""
        return self.archived_at is not None

    @property
    def balance_type(self):
        """Return the normal balance type for this account"""
        if self.account_type.type in ['asset', 'expense']:
            return 'debit'
        else:
            return 'credit'

    def get_balance(self, as_of_date=None):
        """Calculate account balance as of a specific date"""
        from django.db.models import Sum, Q
        from datetime import date
        
        if as_of_date is None:
            as_of_date = date.today()
        
        # Get all journal entries for this account up to the date
        entries = self.journal_entries.filter(
            entry_date__lte=as_of_date,
            is_posted=True
        )
        
        debits = entries.aggregate(total=Sum('debit_amount'))['total'] or 0
        credits = entries.aggregate(total=Sum('credit_amount'))['total'] or 0
        
        # Calculate balance based on account type
        if self.balance_type == 'debit':
            return self.opening_balance + debits - credits
        else:
            return self.opening_balance + credits - debits

    def get_children_recursive(self):
        """Get all child accounts recursively"""
        children = list(self.children.all())
        for child in self.children.all():
            children.extend(child.get_children_recursive())
        return children

    def can_be_deleted(self):
        """Check if account can be deleted"""
        if self.is_system_account:
            return False, _('System accounts cannot be deleted')
        
        if self.journal_entries.exists():
            return False, _('Account has journal entries and cannot be deleted')
        
        if self.children.exists():
            return False, _('Account has child accounts and cannot be deleted')
        
        return True, None

    def archive(self, user, reason=None):
        """Archive the account"""
        from django.utils import timezone
        
        can_archive, error_msg = self.can_be_archived()
        if not can_archive:
            raise ValidationError(error_msg)
        
        self.archived_at = timezone.now()
        self.archived_by = user
        self.archive_reason = reason
        self.is_active = False
        self.save(update_fields=['archived_at', 'archived_by', 'archive_reason', 'is_active'])

    def unarchive(self):
        """Unarchive the account"""
        self.archived_at = None
        self.archived_by = None
        self.archive_reason = None
        self.is_active = True
        self.save(update_fields=['archived_at', 'archived_by', 'archive_reason', 'is_active'])

    def can_be_archived(self):
        """Check if account can be archived"""
        if self.is_system_account:
            return False, _('System accounts cannot be archived')
        
        # Check if any child accounts are active
        if self.children.filter(is_active=True).exists():
            return False, _('Account has active child accounts and cannot be archived')
        
        return True, None


class FeeType(BaseModel):
    """
    Fee type model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Fee Type Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Fee Type Name (Arabic)')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='fee_types',
        verbose_name=_('Account')
    )

    is_mandatory = models.BooleanField(
        default=True,
        verbose_name=_('Is Mandatory')
    )

    class Meta:
        verbose_name = _('Fee Type')
        verbose_name_plural = _('Fee Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class GradeFee(BaseModel):
    """
    Grade-specific fee structure
    """
    grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        related_name='fees',
        verbose_name=_('Grade')
    )

    fee_type = models.ForeignKey(
        FeeType,
        on_delete=models.CASCADE,
        related_name='grade_fees',
        verbose_name=_('Fee Type')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='grade_fees',
        verbose_name=_('Academic Year')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    due_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Due Date')
    )

    class Meta:
        verbose_name = _('Grade Fee')
        verbose_name_plural = _('Grade Fees')
        unique_together = ['grade', 'fee_type', 'academic_year']
        ordering = ['grade', 'fee_type']

    def __str__(self):
        return f"{self.grade} - {self.fee_type} ({self.amount})"


class StudentFee(BaseModel):
    """
    Student fee assignment model
    """
    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='fees',
        verbose_name=_('Student')
    )

    grade_fee = models.ForeignKey(
        GradeFee,
        on_delete=models.CASCADE,
        related_name='student_fees',
        verbose_name=_('Grade Fee')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name=_('Discount Amount')
    )

    due_date = models.DateField(
        verbose_name=_('Due Date')
    )

    is_paid = models.BooleanField(
        default=False,
        verbose_name=_('Is Paid')
    )

    paid_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Paid Date')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Student Fee')
        verbose_name_plural = _('Student Fees')
        unique_together = ['student', 'grade_fee']
        ordering = ['student', 'due_date']

    def __str__(self):
        return f"{self.student} - {self.grade_fee.fee_type} ({self.amount})"

    @property
    def net_amount(self):
        return self.amount - self.discount_amount


class Payment(BaseModel):
    """
    Payment model
    """
    PAYMENT_METHODS = (
        ('cash', _('Cash')),
        ('bank_transfer', _('Bank Transfer')),
        ('check', _('Check')),
        ('credit_card', _('Credit Card')),
        ('online', _('Online Payment')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name=_('Student')
    )

    receipt_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Receipt Number')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    payment_date = models.DateField(
        verbose_name=_('Payment Date')
    )

    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHODS,
        verbose_name=_('Payment Method')
    )

    reference_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Reference Number')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    received_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_payments',
        verbose_name=_('Received By')
    )

    class Meta:
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.receipt_number} - {self.student} ({self.amount})"


class PaymentItem(BaseModel):
    """
    Payment item model - links payments to specific fees
    """
    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('Payment')
    )

    student_fee = models.ForeignKey(
        StudentFee,
        on_delete=models.CASCADE,
        related_name='payment_items',
        verbose_name=_('Student Fee')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    class Meta:
        verbose_name = _('Payment Item')
        verbose_name_plural = _('Payment Items')
        ordering = ['payment', 'student_fee']

    def __str__(self):
        return f"{self.payment.receipt_number} - {self.student_fee.grade_fee.fee_type}"








class CostCenter(BaseModel):
    """
    Cost center model for tracking expenses by department/project
    """
    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Cost Center Code')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Cost Center Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Cost Center Name (Arabic)')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    manager = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_cost_centers',
        verbose_name=_('Manager')
    )

    budget_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Budget Amount')
    )

    class Meta:
        verbose_name = _('Cost Center')
        verbose_name_plural = _('Cost Centers')
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

    @property
    def total_expenses(self):
        """Calculate total expenses for this cost center"""
        from django.db.models import Sum
        return JournalEntry.objects.filter(
            cost_center=self,
            is_posted=True,
            debit_amount__gt=0
        ).aggregate(total=Sum('debit_amount'))['total'] or 0

    @property
    def budget_utilization(self):
        """Calculate budget utilization percentage"""
        if self.budget_amount > 0:
            return (self.total_expenses / self.budget_amount) * 100
        return 0


class Transaction(BaseModel):
    """
    Transaction model for double-entry bookkeeping
    Represents a complete transaction with multiple entries
    """
    TRANSACTION_TYPES = (
        ('manual', _('Manual Transaction')),
        ('automatic', _('Automatic Transaction')),
        ('adjustment', _('Adjustment Transaction')),
        ('closing', _('Closing Transaction')),
        ('payment', _('Payment Transaction')),
        ('receipt', _('Receipt Transaction')),
        ('journal', _('Journal Transaction')),
    )

    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('pending_approval', _('Pending Approval')),
        ('approved', _('Approved')),
        ('posted', _('Posted')),
        ('cancelled', _('Cancelled')),
    )

    transaction_id = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Transaction ID'),
        help_text=_('Unique transaction identifier')
    )

    transaction_date = models.DateField(
        verbose_name=_('Transaction Date')
    )

    transaction_type = models.CharField(
        max_length=20,
        choices=TRANSACTION_TYPES,
        default='manual',
        verbose_name=_('Transaction Type')
    )

    description = models.TextField(
        verbose_name=_('Description')
    )

    reference = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Reference'),
        help_text=_('External reference number or document')
    )

    total_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Total Amount'),
        help_text=_('Total transaction amount (should equal sum of debits/credits)')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('Status')
    )

    # Approval workflow fields
    requires_approval = models.BooleanField(
        default=False,
        verbose_name=_('Requires Approval'),
        help_text=_('Whether this transaction requires approval before posting')
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_transactions',
        verbose_name=_('Approved By')
    )

    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Approved At')
    )

    approval_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Approval Notes')
    )

    # Posting fields
    posted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='posted_transactions',
        verbose_name=_('Posted By')
    )

    posted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Posted At')
    )

    # Audit trail
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_transactions',
        verbose_name=_('Created By')
    )

    modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='modified_transactions',
        verbose_name=_('Modified By')
    )

    class Meta:
        verbose_name = _('Transaction')
        verbose_name_plural = _('Transactions')
        ordering = ['-transaction_date', '-created_at']
        indexes = [
            models.Index(fields=['school', 'transaction_date']),
            models.Index(fields=['school', 'status']),
            models.Index(fields=['school', 'transaction_type']),
            models.Index(fields=['transaction_id']),
        ]

    def __str__(self):
        return f"{self.transaction_id} - {self.description[:50]}"

    def clean(self):
        from django.core.exceptions import ValidationError
        
        # Validate transaction date
        if hasattr(self, 'entries') and self.entries.exists():
            # Check if all entries have the same date as transaction
            entry_dates = set(self.entries.values_list('entry_date', flat=True))
            if len(entry_dates) > 1 or (entry_dates and list(entry_dates)[0] != self.transaction_date):
                raise ValidationError(_('All transaction entries must have the same date as the transaction'))

    def save(self, *args, **kwargs):
        # Generate transaction ID if not provided
        if not self.transaction_id:
            self.transaction_id = self.generate_transaction_id()
        
        # Set modified_by if updating
        if self.pk and hasattr(self, '_current_user'):
            self.modified_by = self._current_user
        
        super().save(*args, **kwargs)

    def generate_transaction_id(self):
        """Generate unique transaction ID"""
        from datetime import datetime
        import uuid
        
        # Format: TXN-YYYY-XXXXXX (where X is sequential number)
        year = datetime.now().year
        prefix = f"TXN-{year}-"
        
        # Get the last transaction for this year
        last_transaction = Transaction.objects.filter(
            school=self.school,
            transaction_id__startswith=prefix
        ).order_by('-transaction_id').first()
        
        if last_transaction:
            try:
                last_number = int(last_transaction.transaction_id.split('-')[-1])
                next_number = last_number + 1
            except (ValueError, IndexError):
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{next_number:06d}"

    @property
    def is_balanced(self):
        """Check if transaction is balanced (debits = credits)"""
        if not hasattr(self, 'entries') or not self.entries.exists():
            return False
        
        from django.db.models import Sum
        totals = self.entries.aggregate(
            total_debits=Sum('debit_amount'),
            total_credits=Sum('credit_amount')
        )
        
        total_debits = totals['total_debits'] or 0
        total_credits = totals['total_credits'] or 0
        
        return abs(total_debits - total_credits) < 0.01  # Allow for small rounding differences

    @property
    def total_debits(self):
        """Get total debit amount"""
        from django.db.models import Sum
        return self.entries.aggregate(total=Sum('debit_amount'))['total'] or 0

    @property
    def total_credits(self):
        """Get total credit amount"""
        from django.db.models import Sum
        return self.entries.aggregate(total=Sum('credit_amount'))['total'] or 0

    def can_be_posted(self):
        """Check if transaction can be posted"""
        if self.status == 'posted':
            return False, _('Transaction is already posted')
        
        if self.status == 'cancelled':
            return False, _('Cancelled transactions cannot be posted')
        
        if not self.is_balanced:
            return False, _('Transaction is not balanced (debits ≠ credits)')
        
        if not self.entries.exists():
            return False, _('Transaction has no entries')
        
        if self.requires_approval and self.status != 'approved':
            return False, _('Transaction requires approval before posting')
        
        # Check if all accounts allow manual entries (for manual transactions)
        if self.transaction_type == 'manual':
            invalid_accounts = self.entries.filter(account__allow_manual_entries=False)
            if invalid_accounts.exists():
                account_names = ', '.join(invalid_accounts.values_list('account__name', flat=True))
                return False, _('Some accounts do not allow manual entries: {}').format(account_names)
        
        return True, None

    def post(self, user):
        """Post the transaction"""
        from django.utils import timezone
        from django.core.exceptions import ValidationError
        
        can_post, error_msg = self.can_be_posted()
        if not can_post:
            raise ValidationError(error_msg)
        
        # Update transaction status
        self.status = 'posted'
        self.posted_by = user
        self.posted_at = timezone.now()
        self.save(update_fields=['status', 'posted_by', 'posted_at'])
        
        # Update account balances
        for entry in self.entries.all():
            entry.post()
        
        # Create audit log entry
        self.create_audit_log('posted', user)

    def approve(self, user, notes=None):
        """Approve the transaction"""
        from django.utils import timezone
        from django.core.exceptions import ValidationError
        
        if self.status != 'pending_approval':
            raise ValidationError(_('Only pending transactions can be approved'))
        
        self.status = 'approved'
        self.approved_by = user
        self.approved_at = timezone.now()
        self.approval_notes = notes
        self.save(update_fields=['status', 'approved_by', 'approved_at', 'approval_notes'])
        
        # Create audit log entry
        self.create_audit_log('approved', user, notes)

    def cancel(self, user, reason=None):
        """Cancel the transaction"""
        from django.core.exceptions import ValidationError
        
        if self.status == 'posted':
            raise ValidationError(_('Posted transactions cannot be cancelled'))
        
        self.status = 'cancelled'
        self.save(update_fields=['status'])
        
        # Create audit log entry
        self.create_audit_log('cancelled', user, reason)

    def create_audit_log(self, action, user, notes=None):
        """Create audit log entry"""
        TransactionAuditLog.objects.create(
            transaction=self,
            action=action,
            user=user,
            notes=notes,
            school=self.school,
            created_by=user
        )


class TransactionEntry(BaseModel):
    """
    Transaction entry model for individual debit/credit entries
    Part of double-entry bookkeeping system
    """
    transaction = models.ForeignKey(
        Transaction,
        on_delete=models.CASCADE,
        related_name='entries',
        verbose_name=_('Transaction')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='transaction_entries',
        verbose_name=_('Account')
    )

    entry_date = models.DateField(
        verbose_name=_('Entry Date'),
        help_text=_('Should match transaction date')
    )

    description = models.TextField(
        verbose_name=_('Description'),
        help_text=_('Specific description for this entry')
    )

    debit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Debit Amount')
    )

    credit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Credit Amount')
    )

    cost_center = models.ForeignKey(
        CostCenter,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='transaction_entries',
        verbose_name=_('Cost Center')
    )

    # Additional fields for tracking
    reference = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Entry Reference')
    )

    is_posted = models.BooleanField(
        default=False,
        verbose_name=_('Is Posted'),
        help_text=_('Whether this entry has been posted to the account')
    )

    posted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Posted At')
    )

    class Meta:
        verbose_name = _('Transaction Entry')
        verbose_name_plural = _('Transaction Entries')
        ordering = ['transaction', 'id']
        indexes = [
            models.Index(fields=['school', 'account', 'entry_date']),
            models.Index(fields=['school', 'is_posted']),
            models.Index(fields=['transaction', 'account']),
        ]

    def __str__(self):
        amount = self.debit_amount if self.debit_amount > 0 else self.credit_amount
        entry_type = 'Dr' if self.debit_amount > 0 else 'Cr'
        return f"{self.account.code} - {entry_type} {amount}"

    def clean(self):
        from django.core.exceptions import ValidationError
        
        # Validate that entry has either debit or credit, but not both
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError(_('An entry cannot have both debit and credit amounts'))
        
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError(_('An entry must have either a debit or credit amount'))
        
        # Validate that entry date matches transaction date
        if self.transaction_id and self.entry_date != self.transaction.transaction_date:
            raise ValidationError(_('Entry date must match transaction date'))
        
        # Validate that account is not a header account
        if self.account and self.account.is_header:
            raise ValidationError(_('Cannot post entries to header accounts'))
        
        # Validate account allows manual entries (for manual transactions)
        if (self.transaction and self.transaction.transaction_type == 'manual' 
            and self.account and not self.account.allow_manual_entries):
            raise ValidationError(_('Account does not allow manual entries'))

    def save(self, *args, **kwargs):
        # Set entry date to match transaction date if not set
        if self.transaction_id and not self.entry_date:
            self.entry_date = self.transaction.transaction_date
        
        super().save(*args, **kwargs)

    @property
    def amount(self):
        """Get the entry amount (debit or credit)"""
        return self.debit_amount if self.debit_amount > 0 else self.credit_amount

    @property
    def entry_type(self):
        """Get entry type (debit or credit)"""
        return 'debit' if self.debit_amount > 0 else 'credit'

    def post(self):
        """Post this entry to the account balance"""
        from django.utils import timezone
        
        if self.is_posted:
            return
        
        # Update account balance
        if self.debit_amount > 0:
            if self.account.balance_type == 'debit':
                self.account.current_balance += self.debit_amount
            else:
                self.account.current_balance -= self.debit_amount
        else:
            if self.account.balance_type == 'credit':
                self.account.current_balance += self.credit_amount
            else:
                self.account.current_balance -= self.credit_amount
        
        self.account.save(update_fields=['current_balance'])
        
        # Mark entry as posted
        self.is_posted = True
        self.posted_at = timezone.now()
        self.save(update_fields=['is_posted', 'posted_at'])


class TransactionAuditLog(BaseModel):
    """
    Audit log for transaction changes
    """
    ACTIONS = (
        ('created', _('Created')),
        ('modified', _('Modified')),
        ('approved', _('Approved')),
        ('posted', _('Posted')),
        ('cancelled', _('Cancelled')),
        ('deleted', _('Deleted')),
    )

    transaction = models.ForeignKey(
        Transaction,
        on_delete=models.CASCADE,
        related_name='audit_logs',
        verbose_name=_('Transaction')
    )

    action = models.CharField(
        max_length=20,
        choices=ACTIONS,
        verbose_name=_('Action')
    )

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='transaction_audit_logs',
        verbose_name=_('User')
    )

    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Timestamp')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_('IP Address')
    )

    changes = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Changes'),
        help_text=_('JSON representation of changes made')
    )

    class Meta:
        verbose_name = _('Transaction Audit Log')
        verbose_name_plural = _('Transaction Audit Logs')
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.transaction.transaction_id} - {self.action} by {self.user.username}"


class JournalEntry(BaseModel):
    """
    Journal entry model for double-entry bookkeeping
    """
    ENTRY_TYPES = (
        ('manual', _('Manual Entry')),
        ('automatic', _('Automatic Entry')),
        ('adjustment', _('Adjustment Entry')),
        ('closing', _('Closing Entry')),
    )

    reference_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Reference Number')
    )

    entry_date = models.DateField(
        verbose_name=_('Entry Date')
    )

    entry_type = models.CharField(
        max_length=20,
        choices=ENTRY_TYPES,
        default='manual',
        verbose_name=_('Entry Type')
    )

    description = models.TextField(
        verbose_name=_('Description')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='journal_entries',
        verbose_name=_('Account')
    )

    cost_center = models.ForeignKey(
        CostCenter,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_entries',
        verbose_name=_('Cost Center')
    )

    debit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Debit Amount')
    )

    credit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Credit Amount')
    )

    is_posted = models.BooleanField(
        default=False,
        verbose_name=_('Is Posted')
    )

    posted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='posted_entries',
        verbose_name=_('Posted By')
    )

    posted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Posted At')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_entries',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Journal Entry')
        verbose_name_plural = _('Journal Entries')
        ordering = ['-entry_date', '-created_at']

    def __str__(self):
        return f"{self.reference_number} - {self.description[:50]}"

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError(_('An entry cannot have both debit and credit amounts'))
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError(_('An entry must have either a debit or credit amount'))


class FinancialYear(BaseModel):
    """
    Financial year model
    """
    name = models.CharField(
        max_length=50,
        verbose_name=_('Financial Year Name')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    is_current = models.BooleanField(
        default=False,
        verbose_name=_('Is Current Year')
    )

    is_closed = models.BooleanField(
        default=False,
        verbose_name=_('Is Closed')
    )

    class Meta:
        verbose_name = _('Financial Year')
        verbose_name_plural = _('Financial Years')
        ordering = ['-start_date']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.is_current:
            # Ensure only one financial year is current
            FinancialYear.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)


class Bank(BaseModel):
    """
    Bank model for managing bank accounts
    """
    name = models.CharField(
        max_length=200,
        verbose_name=_('Bank Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Bank Name (Arabic)')
    )

    account_number = models.CharField(
        max_length=50,
        verbose_name=_('Account Number')
    )

    account_name = models.CharField(
        max_length=200,
        verbose_name=_('Account Name')
    )

    branch = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Branch')
    )

    swift_code = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('SWIFT Code')
    )

    iban = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('IBAN')
    )

    opening_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Opening Balance')
    )

    current_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Current Balance')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='bank_accounts',
        verbose_name=_('Linked Account')
    )

    class Meta:
        verbose_name = _('Bank Account')
        verbose_name_plural = _('Bank Accounts')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.account_number}"


class Invoice(BaseModel):
    """
    Invoice model for generating student invoices
    """
    INVOICE_TYPES = (
        ('tuition', _('Tuition Invoice')),
        ('fees', _('Fees Invoice')),
        ('other', _('Other Invoice')),
    )

    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('sent', _('Sent')),
        ('paid', _('Paid')),
        ('overdue', _('Overdue')),
        ('cancelled', _('Cancelled')),
    )

    invoice_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Invoice Number')
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='invoices',
        verbose_name=_('Student')
    )

    invoice_type = models.CharField(
        max_length=20,
        choices=INVOICE_TYPES,
        verbose_name=_('Invoice Type')
    )

    invoice_date = models.DateField(
        verbose_name=_('Invoice Date')
    )

    due_date = models.DateField(
        verbose_name=_('Due Date')
    )

    subtotal = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Subtotal')
    )

    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Tax Amount')
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Discount Amount')
    )

    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Total Amount')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('Status')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_invoices',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Invoice')
        verbose_name_plural = _('Invoices')
        ordering = ['-invoice_date']

    def __str__(self):
        return f"{self.invoice_number} - {self.student.full_name}"

    @property
    def is_overdue(self):
        from datetime import date
        return self.status in ['sent'] and self.due_date < date.today()

    def save(self, *args, **kwargs):
        # Auto-calculate total amount
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        super().save(*args, **kwargs)


class InvoiceItem(BaseModel):
    """
    Invoice line items model
    """
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('Invoice')
    )

    description = models.CharField(
        max_length=200,
        verbose_name=_('Description')
    )

    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=1,
        verbose_name=_('Quantity')
    )

    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Unit Price')
    )

    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Total Price')
    )

    fee_type = models.ForeignKey(
        FeeType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Fee Type')
    )

    class Meta:
        verbose_name = _('Invoice Item')
        verbose_name_plural = _('Invoice Items')
        ordering = ['invoice', 'id']

    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.description}"

    def save(self, *args, **kwargs):
        # Auto-calculate total price
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)
