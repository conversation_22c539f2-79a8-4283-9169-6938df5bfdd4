{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Student Transfers" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-exchange-alt text-primary me-2"></i>{% trans "Student Transfers" %}
                            </h2>
                            <p class="text-muted">{% trans "Manage student transfers between classes and schools" %}</p>
                        </div>
                        <div>
                            <a href="{% url 'students:add_transfer' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "New Transfer" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter options -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="type" class="form-label">{% trans "Transfer Type" %}</label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="">{% trans "All Types" %}</option>
                                        <option value="incoming" {% if request.GET.type == 'incoming' %}selected{% endif %}>{% trans "Incoming" %}</option>
                                        <option value="outgoing" {% if request.GET.type == 'outgoing' %}selected{% endif %}>{% trans "Outgoing" %}</option>
                                        <option value="internal" {% if request.GET.type == 'internal' %}selected{% endif %}>{% trans "Internal" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="status" class="form-label">{% trans "Status" %}</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">{% trans "All Statuses" %}</option>
                                        <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>{% trans "Pending" %}</option>
                                        <option value="approved" {% if request.GET.status == 'approved' %}selected{% endif %}>{% trans "Approved" %}</option>
                                        <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>{% trans "Completed" %}</option>
                                        <option value="rejected" {% if request.GET.status == 'rejected' %}selected{% endif %}>{% trans "Rejected" %}</option>
                                        <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>{% trans "Cancelled" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request.GET.date_from|default:'' }}">
                                </div>
                                <div class="col-md-3">
                                    <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request.GET.date_to|default:'' }}">
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter me-2"></i>{% trans "Apply Filters" %}
                                    </button>
                                    <a href="{% url 'students:transfers' %}" class="btn btn-outline-secondary ms-2">
                                        <i class="fas fa-redo me-2"></i>{% trans "Reset" %}
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transfers list -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Transfer Requests" %}</h5>
                        </div>
                        <div class="card-body">
                            {% if transfers %}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Student" %}</th>
                                                <th>{% trans "Type" %}</th>
                                                <th>{% trans "From" %}</th>
                                                <th>{% trans "To" %}</th>
                                                <th>{% trans "Date" %}</th>
                                                <th>{% trans "Status" %}</th>
                                                <th>{% trans "Requested By" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for transfer in transfers %}
                                                <tr>
                                                    <td>
                                                        <a href="{% url 'students:detail' transfer.student.id %}">
                                                            {{ transfer.student.full_name }}
                                                        </a>
                                                    </td>
                                                    <td>
                                                        {% if transfer.transfer_type == 'incoming' %}
                                                            <span class="badge bg-success">{% trans "Incoming" %}</span>
                                                        {% elif transfer.transfer_type == 'outgoing' %}
                                                            <span class="badge bg-danger">{% trans "Outgoing" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-info">{% trans "Internal" %}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if transfer.transfer_type == 'incoming' %}
                                                            {{ transfer.from_school }}
                                                        {% elif transfer.transfer_type == 'internal' %}
                                                            {{ transfer.from_class }}
                                                        {% else %}
                                                            {{ transfer.student.current_class }}
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if transfer.transfer_type == 'outgoing' %}
                                                            {{ transfer.to_school }}
                                                        {% elif transfer.transfer_type == 'internal' %}
                                                            {{ transfer.to_class }}
                                                        {% else %}
                                                            {{ transfer.to_class }}
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ transfer.transfer_date }}</td>
                                                    <td>
                                                        {% if transfer.status == 'pending' %}
                                                            <span class="badge bg-warning">{% trans "Pending" %}</span>
                                                        {% elif transfer.status == 'approved' %}
                                                            <span class="badge bg-success">{% trans "Approved" %}</span>
                                                        {% elif transfer.status == 'completed' %}
                                                            <span class="badge bg-primary">{% trans "Completed" %}</span>
                                                        {% elif transfer.status == 'rejected' %}
                                                            <span class="badge bg-danger">{% trans "Rejected" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">{% trans "Cancelled" %}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ transfer.requested_by.get_full_name }}</td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#transferModal{{ transfer.id }}">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            {% if transfer.status == 'pending' %}
                                                                <a href="#" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#approveModal{{ transfer.id }}">
                                                                    <i class="fas fa-check"></i>
                                                                </a>
                                                                <a href="#" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#rejectModal{{ transfer.id }}">
                                                                    <i class="fas fa-times"></i>
                                                                </a>
                                                            {% endif %}
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Pagination -->
                                {% if transfers.has_other_pages %}
                                    <nav aria-label="Page navigation" class="mt-4">
                                        <ul class="pagination justify-content-center">
                                            {% if transfers.has_previous %}
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                                    </a>
                                                </li>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page={{ transfers.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                                        <span aria-hidden="true">&laquo;</span>
                                                    </a>
                                                </li>
                                            {% else %}
                                                <li class="page-item disabled">
                                                    <a class="page-link" href="#" aria-label="First">
                                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                                    </a>
                                                </li>
                                                <li class="page-item disabled">
                                                    <a class="page-link" href="#" aria-label="Previous">
                                                        <span aria-hidden="true">&laquo;</span>
                                                    </a>
                                                </li>
                                            {% endif %}
                                            
                                            {% for i in transfers.paginator.page_range %}
                                                {% if transfers.number == i %}
                                                    <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                                                {% elif i > transfers.number|add:'-3' and i < transfers.number|add:'3' %}
                                                    <li class="page-item">
                                                        <a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a>
                                                    </li>
                                                {% endif %}
                                            {% endfor %}
                                            
                                            {% if transfers.has_next %}
                                                <li class="page-item">
                                                    <a class="page-link" href="?page={{ transfers.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                                        <span aria-hidden="true">&raquo;</span>
                                                    </a>
                                                </li>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page={{ transfers.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                                    </a>
                                                </li>
                                            {% else %}
                                                <li class="page-item disabled">
                                                    <a class="page-link" href="#" aria-label="Next">
                                                        <span aria-hidden="true">&raquo;</span>
                                                    </a>
                                                </li>
                                                <li class="page-item disabled">
                                                    <a class="page-link" href="#" aria-label="Last">
                                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                                    </a>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </nav>
                                {% endif %}
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>{% trans "No transfer requests found matching your criteria." %}
                                </div>
                                <div class="text-center mt-4">
                                    <a href="{% url 'students:add_transfer' %}" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>{% trans "Create New Transfer" %}
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Details Modals -->
{% for transfer in transfers %}
    <div class="modal fade" id="transferModal{{ transfer.id }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{% trans "Transfer Details" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>{% trans "Student:" %}</strong> {{ transfer.student.full_name }}</p>
                            <p><strong>{% trans "Transfer Type:" %}</strong> {{ transfer.get_transfer_type_display }}</p>
                            <p><strong>{% trans "Transfer Date:" %}</strong> {{ transfer.transfer_date }}</p>
                            <p><strong>{% trans "Status:" %}</strong> {{ transfer.get_status_display }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{% trans "Requested By:" %}</strong> {{ transfer.requested_by.get_full_name }}</p>
                            <p><strong>{% trans "Requested On:" %}</strong> {{ transfer.created_at|date:"d/m/Y H:i" }}</p>
                            {% if transfer.approved_by %}
                                <p><strong>{% trans "Approved By:" %}</strong> {{ transfer.approved_by.get_full_name }}</p>
                                <p><strong>{% trans "Approved On:" %}</strong> {{ transfer.approved_at|date:"d/m/Y H:i" }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">{% trans "From" %}</h6>
                                </div>
                                <div class="card-body">
                                    {% if transfer.transfer_type == 'incoming' %}
                                        <p><strong>{% trans "School:" %}</strong> {{ transfer.from_school }}</p>
                                    {% elif transfer.transfer_type == 'internal' %}
                                        <p><strong>{% trans "Class:" %}</strong> {{ transfer.from_class }}</p>
                                    {% else %}
                                        <p><strong>{% trans "Class:" %}</strong> {{ transfer.student.current_class }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">{% trans "To" %}</h6>
                                </div>
                                <div class="card-body">
                                    {% if transfer.transfer_type == 'outgoing' %}
                                        <p><strong>{% trans "School:" %}</strong> {{ transfer.to_school }}</p>
                                    {% elif transfer.transfer_type == 'internal' %}
                                        <p><strong>{% trans "Class:" %}</strong> {{ transfer.to_class }}</p>
                                    {% else %}
                                        <p><strong>{% trans "Class:" %}</strong> {{ transfer.to_class }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">{% trans "Reason for Transfer" %}</h6>
                        </div>
                        <div class="card-body">
                            <p>{{ transfer.reason }}</p>
                        </div>
                    </div>
                    
                    {% if transfer.notes %}
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">{% trans "Additional Notes" %}</h6>
                            </div>
                            <div class="card-body">
                                <p>{{ transfer.notes }}</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Approve Modal -->
    {% if transfer.status == 'pending' %}
        <div class="modal fade" id="approveModal{{ transfer.id }}" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{% trans "Approve Transfer" %}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form method="post" action="{% url 'students:transfers' %}?action=approve&id={{ transfer.id }}">
                        {% csrf_token %}
                        <div class="modal-body">
                            <p>{% trans "Are you sure you want to approve this transfer request for" %} <strong>{{ transfer.student.full_name }}</strong>?</p>
                            
                            <div class="mb-3">
                                <label for="notes{{ transfer.id }}" class="form-label">{% trans "Additional Notes" %}</label>
                                <textarea class="form-control" id="notes{{ transfer.id }}" name="notes" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                            <button type="submit" class="btn btn-success">{% trans "Approve" %}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Reject Modal -->
        <div class="modal fade" id="rejectModal{{ transfer.id }}" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{% trans "Reject Transfer" %}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form method="post" action="{% url 'students:transfers' %}?action=reject&id={{ transfer.id }}">
                        {% csrf_token %}
                        <div class="modal-body">
                            <p>{% trans "Are you sure you want to reject this transfer request for" %} <strong>{{ transfer.student.full_name }}</strong>?</p>
                            
                            <div class="mb-3">
                                <label for="rejection_reason{{ transfer.id }}" class="form-label">{% trans "Rejection Reason" %} <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="rejection_reason{{ transfer.id }}" name="rejection_reason" rows="3" required></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                            <button type="submit" class="btn btn-danger">{% trans "Reject" %}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    {% endif %}
{% endfor %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
});
</script>
{% endblock %}