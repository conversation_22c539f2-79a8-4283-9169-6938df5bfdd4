"""
Tests for core functionality
"""
import uuid
from datetime import date, timedelta
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.cache import cache
from django.test.utils import override_settings
from unittest.mock import patch, MagicMock
from .models import School, AcademicYear, Semester, AuditLog
from .utils import (
    CacheManager, EncryptionManager, IDGenerator, 
    SecurityUtils, DateUtils, ValidationUtils
)
from .permissions import SchoolPermission, has_school_permission
from .middleware import SchoolMiddleware, AuditMiddleware

User = get_user_model()


class SchoolModelTest(TestCase):
    """Test School model"""
    
    def setUp(self):
        self.school_data = {
            'name': 'Test School',
            'name_ar': 'مدرسة الاختبار',
            'code': 'TEST001',
            'address': '123 Test Street',
            'phone': '+1234567890',
            'email': '<EMAIL>',
            'principal_name': '<PERSON>',
            'established_date': date(2020, 1, 1),
            'currency': 'USD',
            'timezone': 'UTC'
        }
    
    def test_school_creation(self):
        """Test school creation with valid data"""
        school = School.objects.create(**self.school_data)
        
        self.assertEqual(school.name, 'Test School')
        self.assertEqual(school.code, 'TEST001')
        self.assertTrue(school.is_active)
        self.assertIsInstance(school.id, uuid.UUID)
    
    def test_school_str_representation(self):
        """Test school string representation"""
        school = School.objects.create(**self.school_data)
        self.assertEqual(str(school), 'Test School')
    
    def test_school_code_uniqueness(self):
        """Test that school codes must be unique"""
        School.objects.create(**self.school_data)
        
        # Try to create another school with same code
        with self.assertRaises(Exception):
            School.objects.create(**self.school_data)
    
    def test_academic_year_start_month_validation(self):
        """Test academic year start month validation"""
        invalid_data = self.school_data.copy()
        invalid_data['academic_year_start_month'] = 13
        
        school = School(**invalid_data)
        with self.assertRaises(ValidationError):
            school.clean()


class BaseModelTest(TestCase):
    """Test BaseModel functionality"""
    
    def setUp(self):
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='John Doe',
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_academic_year_creation(self):
        """Test academic year creation"""
        academic_year = AcademicYear.objects.create(
            school=self.school,
            name='2023-2024',
            start_date=date(2023, 9, 1),
            end_date=date(2024, 6, 30),
            is_current=True
        )
        
        self.assertEqual(academic_year.school, self.school)
        self.assertTrue(academic_year.is_current)
        self.assertIsInstance(academic_year.id, uuid.UUID)
    
    def test_only_one_current_academic_year(self):
        """Test that only one academic year can be current per school"""
        # Create first academic year
        ay1 = AcademicYear.objects.create(
            school=self.school,
            name='2023-2024',
            start_date=date(2023, 9, 1),
            end_date=date(2024, 6, 30),
            is_current=True
        )
        
        # Create second academic year as current
        ay2 = AcademicYear.objects.create(
            school=self.school,
            name='2024-2025',
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        # Refresh first academic year from database
        ay1.refresh_from_db()
        
        # First should no longer be current
        self.assertFalse(ay1.is_current)
        self.assertTrue(ay2.is_current)
    
    def test_semester_creation(self):
        """Test semester creation"""
        academic_year = AcademicYear.objects.create(
            school=self.school,
            name='2023-2024',
            start_date=date(2023, 9, 1),
            end_date=date(2024, 6, 30)
        )
        
        semester = Semester.objects.create(
            school=self.school,
            academic_year=academic_year,
            name='Fall 2023',
            start_date=date(2023, 9, 1),
            end_date=date(2024, 1, 31),
            is_current=True
        )
        
        self.assertEqual(semester.academic_year, academic_year)
        self.assertTrue(semester.is_current)


class CacheManagerTest(TestCase):
    """Test CacheManager utility"""
    
    def setUp(self):
        cache.clear()
    
    def test_cache_key_generation(self):
        """Test cache key generation"""
        key = CacheManager.get_key('test', 'arg1', 'arg2')
        self.assertEqual(key, 'test:arg1_arg2')
    
    def test_cache_set_and_get(self):
        """Test cache set and get operations"""
        from django.conf import settings
        
        # Skip cache test if using dummy cache
        if 'dummy' in settings.CACHES['default']['BACKEND'].lower():
            self.skipTest("Skipping cache test with dummy cache backend")
        
        CacheManager.set('test', 'key1', value='test_value')
        result = CacheManager.get('test', 'key1')
        self.assertEqual(result, 'test_value')
    
    def test_cache_get_with_default(self):
        """Test cache get with default value"""
        result = CacheManager.get('test', 'nonexistent', default='default_value')
        self.assertEqual(result, 'default_value')
    
    def test_cache_delete(self):
        """Test cache delete operation"""
        CacheManager.set('test', 'key1', value='test_value')
        CacheManager.delete('test', 'key1')
        result = CacheManager.get('test', 'key1')
        self.assertIsNone(result)


class EncryptionManagerTest(TestCase):
    """Test EncryptionManager utility"""
    
    def test_encryption_decryption(self):
        """Test encryption and decryption"""
        from cryptography.fernet import Fernet
        
        # Generate a proper Fernet key for testing
        test_key = Fernet.generate_key().decode()
        
        with override_settings(ENCRYPTION_KEY=test_key):
            manager = EncryptionManager()
            
            # Skip test if no encryption key
            if not manager.cipher:
                self.skipTest("No encryption key configured")
            
            original_text = "sensitive_data"
            encrypted = manager.encrypt(original_text)
            decrypted = manager.decrypt(encrypted)
            
            self.assertNotEqual(encrypted, original_text)
            self.assertEqual(decrypted, original_text)
    
    def test_encryption_with_empty_data(self):
        """Test encryption with empty data"""
        manager = EncryptionManager()
        result = manager.encrypt("")
        self.assertEqual(result, "")


class IDGeneratorTest(TestCase):
    """Test IDGenerator utility"""
    
    def setUp(self):
        cache.clear()
    
    def test_student_id_generation(self):
        """Test student ID generation"""
        student_id = IDGenerator.generate_student_id('TEST', 2023)
        self.assertTrue(student_id.startswith('TEST23'))
        self.assertEqual(len(student_id), 10)  # TEST + 23 + 0001
    
    def test_employee_id_generation(self):
        """Test employee ID generation"""
        employee_id = IDGenerator.generate_employee_id('TEST')
        self.assertTrue(employee_id.startswith('EMPTEST'))
        self.assertEqual(len(employee_id), 12)  # EMP + TEST + 00001
    
    def test_sequential_id_generation(self):
        """Test that IDs are generated sequentially"""
        from django.conf import settings
        
        # Skip ID generator test if using dummy cache
        if 'dummy' in settings.CACHES['default']['BACKEND'].lower():
            self.skipTest("Skipping ID generator test with dummy cache backend")
        
        id1 = IDGenerator.generate_student_id('TEST', 2023)
        id2 = IDGenerator.generate_student_id('TEST', 2023)
        
        # Extract sequence numbers
        seq1 = int(id1[-4:])
        seq2 = int(id2[-4:])
        
        self.assertEqual(seq2, seq1 + 1)


class SecurityUtilsTest(TestCase):
    """Test SecurityUtils"""
    
    def test_generate_secure_token(self):
        """Test secure token generation"""
        token = SecurityUtils.generate_secure_token()
        self.assertIsInstance(token, str)
        self.assertGreater(len(token), 0)
    
    def test_password_hashing(self):
        """Test password hashing and verification"""
        password = "test_password_123"
        password_hash, salt = SecurityUtils.hash_password(password)
        
        self.assertIsInstance(password_hash, str)
        self.assertIsInstance(salt, str)
        self.assertNotEqual(password_hash, password)
        
        # Test verification
        self.assertTrue(SecurityUtils.verify_password(password, password_hash, salt))
        self.assertFalse(SecurityUtils.verify_password("wrong_password", password_hash, salt))


class ValidationUtilsTest(TestCase):
    """Test ValidationUtils"""
    
    def test_phone_number_validation(self):
        """Test phone number validation"""
        valid_phones = ['+1234567890', '1234567890', '+123456789012345']
        invalid_phones = ['123', '+123456789012345678901', 'abc123', '']
        
        for phone in valid_phones:
            self.assertTrue(ValidationUtils.validate_phone_number(phone))
        
        for phone in invalid_phones:
            self.assertFalse(ValidationUtils.validate_phone_number(phone))
    
    def test_email_validation(self):
        """Test email validation"""
        valid_emails = ['<EMAIL>', '<EMAIL>']
        invalid_emails = ['invalid_email', '@domain.com', 'user@', '']
        
        for email in valid_emails:
            self.assertTrue(ValidationUtils.validate_email(email))
        
        for email in invalid_emails:
            self.assertFalse(ValidationUtils.validate_email(email))


class SchoolPermissionTest(TestCase):
    """Test SchoolPermission class"""
    
    def setUp(self):
        self.school1 = School.objects.create(
            name='School 1',
            code='SCH001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Principal 1',
            established_date=date(2020, 1, 1)
        )
        
        self.school2 = School.objects.create(
            name='School 2',
            code='SCH002',
            address='456 Test Avenue',
            phone='+1234567891',
            email='<EMAIL>',
            principal_name='Principal 2',
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.superuser = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
    
    def test_superuser_has_permission(self):
        """Test that superuser has permission to access any school"""
        self.assertTrue(has_school_permission(self.superuser, self.school1))
        self.assertTrue(has_school_permission(self.superuser, self.school2))
    
    def test_regular_user_without_school_no_permission(self):
        """Test that regular user without school has no permission"""
        self.assertFalse(has_school_permission(self.user, self.school1))


class AuditLogTest(TestCase):
    """Test AuditLog model"""
    
    def setUp(self):
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='John Doe',
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_audit_log_creation(self):
        """Test audit log creation"""
        audit_log = AuditLog.objects.create(
            school=self.school,
            user=self.user,
            action='CREATE',
            model_name='Student',
            object_id='123',
            object_repr='John Doe',
            changes={'name': 'John Doe'},
            ip_address='127.0.0.1'
        )
        
        self.assertEqual(audit_log.school, self.school)
        self.assertEqual(audit_log.user, self.user)
        self.assertEqual(audit_log.action, 'CREATE')
        self.assertIsInstance(audit_log.id, uuid.UUID)


class MiddlewareTest(TestCase):
    """Test middleware functionality"""
    
    def setUp(self):
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='John Doe',
            established_date=date(2020, 1, 1)
        )
    
    def test_school_middleware_sets_school_from_header(self):
        """Test that SchoolMiddleware sets school from header"""
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.get('/', HTTP_X_SCHOOL_CODE='TEST001')
        request.user = MagicMock()
        request.user.is_authenticated = False
        
        middleware = SchoolMiddleware(lambda r: None)
        middleware.process_request(request)
        
        self.assertEqual(request.school, self.school)


class TaskTest(TransactionTestCase):
    """Test Celery tasks"""
    
    def setUp(self):
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='John Doe',
            established_date=date(2020, 1, 1)
        )
    
    @patch('core.tasks.send_mail')
    def test_send_email_notification_task(self, mock_send_mail):
        """Test email notification task"""
        from .tasks import send_email_notification
        
        mock_send_mail.return_value = True
        
        result = send_email_notification(
            '<EMAIL>',
            'Test Subject',
            'Test Message'
        )
        
        self.assertIn('Email <NAME_EMAIL>', result)
        mock_send_mail.assert_called_once()
    
    def test_cleanup_old_logs_task(self):
        """Test cleanup old logs task"""
        from .tasks import cleanup_old_logs
        
        # Create old audit log
        old_log = AuditLog.objects.create(
            school=self.school,
            action='CREATE',
            model_name='Test',
            object_id='123',
            object_repr='Test Object'
        )
        
        # Manually set old timestamp
        old_timestamp = old_log.timestamp - timedelta(days=400)
        AuditLog.objects.filter(id=old_log.id).update(timestamp=old_timestamp)
        
        result = cleanup_old_logs()
        
        self.assertIn('Cleaned up', result)
        # Verify log was deleted
        self.assertFalse(AuditLog.objects.filter(id=old_log.id).exists())