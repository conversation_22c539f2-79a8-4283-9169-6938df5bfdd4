{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Subject Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .subject-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
        overflow: hidden;
    }
    .subject-card:hover {
        transform: translateY(-2px);
    }
    .subject-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        text-align: center;
    }
    .subject-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
    }
    .subject-stats {
        padding: 1rem;
    }
    .stat-item {
        text-align: center;
        padding: 0.5rem;
    }
    .stat-number {
        font-size: 1.25rem;
        font-weight: bold;
        color: #495057;
    }
    .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
    }
    .subject-actions {
        padding: 1rem;
        border-top: 1px solid #e9ecef;
        background: #f8f9fa;
    }
    .grade-badge {
        background: #e9ecef;
        color: #495057;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.75rem;
        margin: 0.125rem;
        display: inline-block;
    }
    .mandatory-badge {
        background: #d4edda;
        color: #155724;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.75rem;
    }
    .optional-badge {
        background: #fff3cd;
        color: #856404;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.75rem;
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .subject-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-book text-primary me-2"></i>{% trans "Subject Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage academic subjects and curriculum" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#newSubjectModal">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Subject" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export List" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card subject-card">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-2x text-primary mb-2"></i>
                    <h3 class="mb-1">{{ total_subjects|default:24 }}</h3>
                    <p class="mb-0">{% trans "Total Subjects" %}</p>
                    <small class="text-muted">{% trans "Active subjects" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card subject-card">
                <div class="card-body text-center">
                    <i class="fas fa-star fa-2x text-success mb-2"></i>
                    <h3 class="mb-1">{{ mandatory_subjects|default:18 }}</h3>
                    <p class="mb-0">{% trans "Mandatory Subjects" %}</p>
                    <small class="text-muted">{% trans "Required subjects" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card subject-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-info mb-2"></i>
                    <h3 class="mb-1">{{ total_credit_hours|default:0 }}</h3>
                    <p class="mb-0">{% trans "Total Credit Hours" %}</p>
                    <small class="text-muted">{% trans "All subjects combined" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card subject-card">
                <div class="card-body text-center">
                    <i class="fas fa-layer-group fa-2x text-warning mb-2"></i>
                    <h3 class="mb-1">{{ grade_levels|default:0 }}</h3>
                    <p class="mb-0">{% trans "Grade Levels" %}</p>
                    <small class="text-muted">{% trans "Covered grades" %}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <h5 class="mb-3">{% trans "Filter Subjects" %}</h5>
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="grade" class="form-label">{% trans "Grade Level" %}</label>
                <select class="form-select" id="grade" name="grade">
                    <option value="">{% trans "All Grades" %}</option>
                    <option value="1">{% trans "Grade 1" %}</option>
                    <option value="2">{% trans "Grade 2" %}</option>
                    <option value="3">{% trans "Grade 3" %}</option>
                    <option value="4">{% trans "Grade 4" %}</option>
                    <option value="5">{% trans "Grade 5" %}</option>
                    <option value="6">{% trans "Grade 6" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="type" class="form-label">{% trans "Subject Type" %}</label>
                <select class="form-select" id="type" name="type">
                    <option value="">{% trans "All Types" %}</option>
                    <option value="mandatory">{% trans "Mandatory" %}</option>
                    <option value="optional">{% trans "Optional" %}</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="search" class="form-label">{% trans "Search" %}</label>
                <input type="text" class="form-control" id="search" name="search" placeholder="{% trans 'Search subjects...' %}">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Subjects Grid -->
    <div class="subject-grid">
        <!-- Mathematics -->
        <div class="card subject-card">
            <div class="subject-header">
                <div class="subject-icon">
                    <i class="fas fa-calculator"></i>
                </div>
                <h5 class="mb-1">{% trans "Mathematics" %}</h5>
                <small>MATH-001</small>
            </div>
            <div class="subject-stats">
                <div class="row">
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">4</div>
                            <div class="stat-label">{% trans "Credit Hours" %}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">6</div>
                            <div class="stat-label">{% trans "Grade Levels" %}</div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="mb-2">
                    <span class="mandatory-badge">{% trans "Mandatory" %}</span>
                </div>
                <div class="mb-2">
                    <span class="grade-badge">{% trans "Grade 1" %}</span>
                    <span class="grade-badge">{% trans "Grade 2" %}</span>
                    <span class="grade-badge">{% trans "Grade 3" %}</span>
                    <span class="grade-badge">{% trans "Grade 4" %}</span>
                    <span class="grade-badge">{% trans "Grade 5" %}</span>
                    <span class="grade-badge">{% trans "Grade 6" %}</span>
                </div>
            </div>
            <div class="subject-actions">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> {% trans "View" %}
                    </button>
                    <button class="btn btn-outline-success btn-sm">
                        <i class="fas fa-edit"></i> {% trans "Edit" %}
                    </button>
                    <button class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash"></i> {% trans "Delete" %}
                    </button>
                </div>
            </div>
        </div>

        <!-- Arabic Language -->
        <div class="card subject-card">
            <div class="subject-header" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="subject-icon">
                    <i class="fas fa-language"></i>
                </div>
                <h5 class="mb-1">{% trans "Arabic Language" %}</h5>
                <small>ARAB-001</small>
            </div>
            <div class="subject-stats">
                <div class="row">
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">5</div>
                            <div class="stat-label">{% trans "Credit Hours" %}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">6</div>
                            <div class="stat-label">{% trans "Grade Levels" %}</div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="mb-2">
                    <span class="mandatory-badge">{% trans "Mandatory" %}</span>
                </div>
                <div class="mb-2">
                    <span class="grade-badge">{% trans "Grade 1" %}</span>
                    <span class="grade-badge">{% trans "Grade 2" %}</span>
                    <span class="grade-badge">{% trans "Grade 3" %}</span>
                    <span class="grade-badge">{% trans "Grade 4" %}</span>
                    <span class="grade-badge">{% trans "Grade 5" %}</span>
                    <span class="grade-badge">{% trans "Grade 6" %}</span>
                </div>
            </div>
            <div class="subject-actions">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> {% trans "View" %}
                    </button>
                    <button class="btn btn-outline-success btn-sm">
                        <i class="fas fa-edit"></i> {% trans "Edit" %}
                    </button>
                    <button class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash"></i> {% trans "Delete" %}
                    </button>
                </div>
            </div>
        </div>

        <!-- Science -->
        <div class="card subject-card">
            <div class="subject-header" style="background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);">
                <div class="subject-icon">
                    <i class="fas fa-flask"></i>
                </div>
                <h5 class="mb-1">{% trans "Science" %}</h5>
                <small>SCI-001</small>
            </div>
            <div class="subject-stats">
                <div class="row">
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">3</div>
                            <div class="stat-label">{% trans "Credit Hours" %}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">4</div>
                            <div class="stat-label">{% trans "Grade Levels" %}</div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="mb-2">
                    <span class="mandatory-badge">{% trans "Mandatory" %}</span>
                </div>
                <div class="mb-2">
                    <span class="grade-badge">{% trans "Grade 3" %}</span>
                    <span class="grade-badge">{% trans "Grade 4" %}</span>
                    <span class="grade-badge">{% trans "Grade 5" %}</span>
                    <span class="grade-badge">{% trans "Grade 6" %}</span>
                </div>
            </div>
            <div class="subject-actions">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> {% trans "View" %}
                    </button>
                    <button class="btn btn-outline-success btn-sm">
                        <i class="fas fa-edit"></i> {% trans "Edit" %}
                    </button>
                    <button class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash"></i> {% trans "Delete" %}
                    </button>
                </div>
            </div>
        </div>

        <!-- English Language -->
        <div class="card subject-card">
            <div class="subject-header" style="background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);">
                <div class="subject-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <h5 class="mb-1">{% trans "English Language" %}</h5>
                <small>ENG-001</small>
            </div>
            <div class="subject-stats">
                <div class="row">
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">3</div>
                            <div class="stat-label">{% trans "Credit Hours" %}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">6</div>
                            <div class="stat-label">{% trans "Grade Levels" %}</div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="mb-2">
                    <span class="mandatory-badge">{% trans "Mandatory" %}</span>
                </div>
                <div class="mb-2">
                    <span class="grade-badge">{% trans "Grade 1" %}</span>
                    <span class="grade-badge">{% trans "Grade 2" %}</span>
                    <span class="grade-badge">{% trans "Grade 3" %}</span>
                    <span class="grade-badge">{% trans "Grade 4" %}</span>
                    <span class="grade-badge">{% trans "Grade 5" %}</span>
                    <span class="grade-badge">{% trans "Grade 6" %}</span>
                </div>
            </div>
            <div class="subject-actions">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> {% trans "View" %}
                    </button>
                    <button class="btn btn-outline-success btn-sm">
                        <i class="fas fa-edit"></i> {% trans "Edit" %}
                    </button>
                    <button class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash"></i> {% trans "Delete" %}
                    </button>
                </div>
            </div>
        </div>

        <!-- Art -->
        <div class="card subject-card">
            <div class="subject-header" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="subject-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <h5 class="mb-1">{% trans "Art" %}</h5>
                <small>ART-001</small>
            </div>
            <div class="subject-stats">
                <div class="row">
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">2</div>
                            <div class="stat-label">{% trans "Credit Hours" %}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">6</div>
                            <div class="stat-label">{% trans "Grade Levels" %}</div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="mb-2">
                    <span class="optional-badge">{% trans "Optional" %}</span>
                </div>
                <div class="mb-2">
                    <span class="grade-badge">{% trans "Grade 1" %}</span>
                    <span class="grade-badge">{% trans "Grade 2" %}</span>
                    <span class="grade-badge">{% trans "Grade 3" %}</span>
                    <span class="grade-badge">{% trans "Grade 4" %}</span>
                    <span class="grade-badge">{% trans "Grade 5" %}</span>
                    <span class="grade-badge">{% trans "Grade 6" %}</span>
                </div>
            </div>
            <div class="subject-actions">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> {% trans "View" %}
                    </button>
                    <button class="btn btn-outline-success btn-sm">
                        <i class="fas fa-edit"></i> {% trans "Edit" %}
                    </button>
                    <button class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash"></i> {% trans "Delete" %}
                    </button>
                </div>
            </div>
        </div>

        <!-- Physical Education -->
        <div class="card subject-card">
            <div class="subject-header" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #495057;">
                <div class="subject-icon">
                    <i class="fas fa-running"></i>
                </div>
                <h5 class="mb-1">{% trans "Physical Education" %}</h5>
                <small>PE-001</small>
            </div>
            <div class="subject-stats">
                <div class="row">
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">2</div>
                            <div class="stat-label">{% trans "Credit Hours" %}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">6</div>
                            <div class="stat-label">{% trans "Grade Levels" %}</div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="mb-2">
                    <span class="mandatory-badge">{% trans "Mandatory" %}</span>
                </div>
                <div class="mb-2">
                    <span class="grade-badge">{% trans "Grade 1" %}</span>
                    <span class="grade-badge">{% trans "Grade 2" %}</span>
                    <span class="grade-badge">{% trans "Grade 3" %}</span>
                    <span class="grade-badge">{% trans "Grade 4" %}</span>
                    <span class="grade-badge">{% trans "Grade 5" %}</span>
                    <span class="grade-badge">{% trans "Grade 6" %}</span>
                </div>
            </div>
            <div class="subject-actions">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> {% trans "View" %}
                    </button>
                    <button class="btn btn-outline-success btn-sm">
                        <i class="fas fa-edit"></i> {% trans "Edit" %}
                    </button>
                    <button class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash"></i> {% trans "Delete" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Subject Modal -->
<div class="modal fade" id="newSubjectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add New Subject" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="subjectName" class="form-label">{% trans "Subject Name" %}</label>
                            <input type="text" class="form-control" id="subjectName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="subjectNameAr" class="form-label">{% trans "Subject Name (Arabic)" %}</label>
                            <input type="text" class="form-control" id="subjectNameAr">
                        </div>
                        <div class="col-md-6">
                            <label for="subjectCode" class="form-label">{% trans "Subject Code" %}</label>
                            <input type="text" class="form-control" id="subjectCode" required>
                        </div>
                        <div class="col-md-6">
                            <label for="creditHours" class="form-label">{% trans "Credit Hours" %}</label>
                            <input type="number" class="form-control" id="creditHours" min="1" max="10" required>
                        </div>
                        <div class="col-md-6">
                            <label for="subjectType" class="form-label">{% trans "Subject Type" %}</label>
                            <select class="form-select" id="subjectType" required>
                                <option value="">{% trans "Select Type" %}</option>
                                <option value="mandatory">{% trans "Mandatory" %}</option>
                                <option value="optional">{% trans "Optional" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="grades" class="form-label">{% trans "Grade Levels" %}</label>
                            <select class="form-select" id="grades" multiple>
                                <option value="1">{% trans "Grade 1" %}</option>
                                <option value="2">{% trans "Grade 2" %}</option>
                                <option value="3">{% trans "Grade 3" %}</option>
                                <option value="4">{% trans "Grade 4" %}</option>
                                <option value="5">{% trans "Grade 5" %}</option>
                                <option value="6">{% trans "Grade 6" %}</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="description" class="form-label">{% trans "Description" %}</label>
                            <textarea class="form-control" id="description" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Create Subject" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate subject code
    const subjectName = document.getElementById('subjectName');
    const subjectCode = document.getElementById('subjectCode');
    
    if (subjectName && subjectCode) {
        subjectName.addEventListener('input', function() {
            const name = this.value.toUpperCase().replace(/\s+/g, '');
            if (name.length >= 3) {
                subjectCode.value = name.substring(0, 4) + '-001';
            }
        });
    }

    // Auto-submit filters
    const filterSelects = document.querySelectorAll('select[name="grade"], select[name="type"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});
</script>
{% endblock %}
