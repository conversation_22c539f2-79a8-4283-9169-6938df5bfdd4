{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load dashboard_tags %}

{% block title %}{% trans "Student Performance Report" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .student-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .student-card:hover {
        transform: translateY(-2px);
    }
    .student-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .student-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid white;
    }
    .grade-card {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        text-align: center;
        padding: 20px;
    }
    .subject-performance {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        background-color: #f8f9fa;
    }
    .performance-meter {
        height: 25px;
        background-color: #e9ecef;
        border-radius: 12px;
        overflow: hidden;
        position: relative;
    }
    .performance-bar {
        height: 100%;
        border-radius: 12px;
        transition: width 0.5s ease;
        position: relative;
    }
    .performance-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-weight: bold;
        font-size: 0.9em;
        color: #333;
    }
    .chart-container {
        position: relative;
        height: 300px;
    }
    .trend-up { color: #28a745; }
    .trend-down { color: #dc3545; }
    .trend-stable { color: #6c757d; }
    .attendance-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }
    .present { background-color: #28a745; }
    .absent { background-color: #dc3545; }
    .late { background-color: #ffc107; }
    .excused { background-color: #17a2b8; }
    .achievement-badge {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #333;
        border-radius: 20px;
        padding: 8px 15px;
        font-size: 0.9em;
        font-weight: bold;
        margin: 5px;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-graduate me-2"></i>{% trans "Student Performance Report" %}
                    </h1>
                    <p class="text-muted">{% trans "Detailed academic performance analysis" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>{% trans "Export Report" %}
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printReport()">
                        <i class="fas fa-print me-2"></i>{% trans "Print" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Selection -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="student_filter" class="form-label">{% trans "Select Student" %}</label>
                            <select name="student" id="student_filter" class="form-select">
                                <option value="">{% trans "Choose a student..." %}</option>
                                {% for student in students %}
                                    <option value="{{ student.id }}" {% if selected_student and student.id == selected_student.id %}selected{% endif %}>
                                        {{ student.user.first_name }} {{ student.user.last_name }} - {{ student.student_id }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="semester_filter" class="form-label">{% trans "Semester" %}</label>
                            <select name="semester" id="semester_filter" class="form-select">
                                <option value="">{% trans "Current Semester" %}</option>
                                <option value="first" {% if selected_semester == 'first' %}selected{% endif %}>{% trans "First Semester" %}</option>
                                <option value="second" {% if selected_semester == 'second' %}selected{% endif %}>{% trans "Second Semester" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="year_filter" class="form-label">{% trans "Academic Year" %}</label>
                            <select name="year" id="year_filter" class="form-select">
                                <option value="">{% trans "Current Year" %}</option>
                                {% for year in academic_years %}
                                    <option value="{{ year.id }}" {% if selected_year and year.id == selected_year.id %}selected{% endif %}>
                                        {{ year.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>{% trans "View" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if selected_student %}
        <!-- Student Information -->
        <div class="row mb-4">
            <div class="col-lg-4">
                <div class="card student-card">
                    <div class="card-header student-header text-center">
                        {% if selected_student.user.profile_picture %}
                            <img src="{{ selected_student.user.profile_picture.url }}" alt="{{ selected_student.user.get_full_name }}" class="student-avatar mb-3">
                        {% else %}
                            <div class="student-avatar bg-light d-flex align-items-center justify-content-center mb-3 mx-auto">
                                <i class="fas fa-user fa-2x text-secondary"></i>
                            </div>
                        {% endif %}
                        <h5 class="mb-1">{{ selected_student.user.first_name }} {{ selected_student.user.last_name }}</h5>
                        <p class="mb-0">{{ selected_student.student_id }}</p>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <strong>{% trans "Class" %}</strong><br>
                                <span class="text-muted">{{ selected_student.current_class.grade.name }} - {{ selected_student.current_class.name }}</span>
                            </div>
                            <div class="col-6">
                                <strong>{% trans "Rank" %}</strong><br>
                                <span class="text-muted">#{{ student_rank|default:"N/A" }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-8">
                <div class="row">
                    <div class="col-md-3">
                        <div class="grade-card">
                            <h3 class="mb-1">{{ overall_average|floatformat:1|default:"0.0" }}%</h3>
                            <p class="mb-0">{% trans "Overall Average" %}</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card student-card">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-check text-success mb-2" style="font-size: 2rem;"></i>
                                <h4 class="text-success">{{ attendance_rate|floatformat:1|default:"0.0" }}%</h4>
                                <p class="mb-0">{% trans "Attendance" %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card student-card">
                            <div class="card-body text-center">
                                <i class="fas fa-trophy text-warning mb-2" style="font-size: 2rem;"></i>
                                <h4 class="text-warning">{{ total_assignments|default:0 }}</h4>
                                <p class="mb-0">{% trans "Assignments" %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card student-card">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line text-info mb-2" style="font-size: 2rem;"></i>
                                <h4 class="text-info 
                                    {% if grade_trend == 'up' %}trend-up
                                    {% elif grade_trend == 'down' %}trend-down
                                    {% else %}trend-stable{% endif %}">
                                    {% if grade_trend == 'up' %}
                                        <i class="fas fa-arrow-up"></i>
                                    {% elif grade_trend == 'down' %}
                                        <i class="fas fa-arrow-down"></i>
                                    {% else %}
                                        <i class="fas fa-minus"></i>
                                    {% endif %}
                                </h4>
                                <p class="mb-0">{% trans "Trend" %}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Charts -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card student-card">
                    <div class="card-header">
                        <h5 class="mb-0">{% trans "Grade Trends" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="gradeTrendsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card student-card">
                    <div class="card-header">
                        <h5 class="mb-0">{% trans "Subject Performance" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="subjectRadarChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subject Performance Details -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card student-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-book me-2"></i>{% trans "Subject Performance Details" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if subject_grades %}
                            {% for subject in subject_grades %}
                                <div class="subject-performance">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <h6 class="mb-1">{{ subject.name }}</h6>
                                            <small class="text-muted">{{ subject.teacher|default:"No teacher assigned" }}</small>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="performance-meter">
                                                <div class="performance-bar 
                                                    {% if subject.average >= 90 %}bg-success
                                                    {% elif subject.average >= 80 %}bg-primary
                                                    {% elif subject.average >= 70 %}bg-warning
                                                    {% else %}bg-danger{% endif %}"
                                                    style="width: {{ subject.average|default:0 }}%">
                                                </div>
                                                <div class="performance-text">{{ subject.average|floatformat:1|default:"0.0" }}%</div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <div class="d-flex justify-content-end align-items-center">
                                                <div class="me-3">
                                                    <small class="text-muted">{% trans "Assignments:" %} {{ subject.assignment_count|default:0 }}</small><br>
                                                    <small class="text-muted">{% trans "Last Grade:" %} {{ subject.last_grade|floatformat:1|default:"N/A" }}%</small>
                                                </div>
                                                <div>
                                                    {% if subject.trend == 'up' %}
                                                        <i class="fas fa-arrow-up trend-up"></i>
                                                    {% elif subject.trend == 'down' %}
                                                        <i class="fas fa-arrow-down trend-down"></i>
                                                    {% else %}
                                                        <i class="fas fa-minus trend-stable"></i>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-book fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{% trans "No grade data available for this student." %}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance and Achievements -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card student-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-check me-2"></i>{% trans "Attendance Overview" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if attendance_data %}
                            <div class="row mb-3">
                                <div class="col-6">
                                    <div class="text-center">
                                        <h4 class="text-success">{{ attendance_data.present_days|default:0 }}</h4>
                                        <small class="text-muted">{% trans "Present Days" %}</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <h4 class="text-danger">{{ attendance_data.absent_days|default:0 }}</h4>
                                        <small class="text-muted">{% trans "Absent Days" %}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <h6>{% trans "Recent Attendance" %}</h6>
                                {% for record in recent_attendance %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <span class="attendance-indicator {{ record.status }}"></span>
                                            {{ record.date|date:"M d, Y" }}
                                        </div>
                                        <span class="badge 
                                            {% if record.status == 'present' %}bg-success
                                            {% elif record.status == 'absent' %}bg-danger
                                            {% elif record.status == 'late' %}bg-warning
                                            {% else %}bg-info{% endif %}">
                                            {% if record.status == 'present' %}{% trans "Present" %}
                                            {% elif record.status == 'absent' %}{% trans "Absent" %}
                                            {% elif record.status == 'late' %}{% trans "Late" %}
                                            {% else %}{% trans "Excused" %}{% endif %}
                                        </span>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-calendar fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{% trans "No attendance data available." %}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card student-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-medal me-2"></i>{% trans "Achievements & Recognition" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if achievements %}
                            {% for achievement in achievements %}
                                <div class="achievement-badge">
                                    <i class="fas fa-trophy me-2"></i>{{ achievement.title }}
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{% trans "No achievements recorded yet." %}</p>
                                <small class="text-muted">{% trans "Keep up the good work!" %}</small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Grades Table -->
        <div class="row">
            <div class="col-12">
                <div class="card student-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>{% trans "Recent Grades" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "Date" %}</th>
                                        <th>{% trans "Subject" %}</th>
                                        <th>{% trans "Assessment Type" %}</th>
                                        <th>{% trans "Grade" %}</th>
                                        <th>{% trans "Max Grade" %}</th>
                                        <th>{% trans "Percentage" %}</th>
                                        <th>{% trans "Status" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if recent_grades %}
                                        {% for grade in recent_grades %}
                                            <tr>
                                                <td>{{ grade.date_recorded|date:"M d, Y" }}</td>
                                                <td>{{ grade.subject.name }}</td>
                                                <td>
                                                    <span class="badge bg-info">
                                                        {% if grade.assessment_type == 'quiz' %}{% trans "Quiz" %}
                                                        {% elif grade.assessment_type == 'exam' %}{% trans "Exam" %}
                                                        {% elif grade.assessment_type == 'assignment' %}{% trans "Assignment" %}
                                                        {% elif grade.assessment_type == 'project' %}{% trans "Project" %}
                                                        {% else %}{{ grade.assessment_type }}{% endif %}
                                                    </span>
                                                </td>
                                                <td class="fw-bold">{{ grade.grade|floatformat:1 }}</td>
                                                <td>{{ grade.max_grade|default:100 }}</td>
                                                <td>
                                                    {% with percentage=grade.grade|mul:100|div:grade.max_grade %}
                                                        <span class="fw-bold 
                                                            {% if percentage >= 90 %}text-success
                                                            {% elif percentage >= 80 %}text-primary
                                                            {% elif percentage >= 70 %}text-warning
                                                            {% else %}text-danger{% endif %}">
                                                            {{ percentage|floatformat:0 }}%
                                                        </span>
                                                    {% endwith %}
                                                </td>
                                                <td>
                                                    {% with percentage=grade.grade|mul:100|div:grade.max_grade %}
                                                        {% if percentage >= 90 %}
                                                            <span class="badge bg-success">{% trans "Excellent" %}</span>
                                                        {% elif percentage >= 80 %}
                                                            <span class="badge bg-primary">{% trans "Good" %}</span>
                                                        {% elif percentage >= 70 %}
                                                            <span class="badge bg-warning">{% trans "Average" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-danger">{% trans "Needs Improvement" %}</span>
                                                        {% endif %}
                                                    {% endwith %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="alert alert-info mb-0">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    {% trans "No recent grades available for this student." %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="row">
            <div class="col-12">
                <div class="card student-card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-user-graduate fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted">{% trans "Select a Student to View Performance Report" %}</h4>
                        <p class="text-muted">{% trans "Choose a student from the dropdown above to generate a detailed performance analysis." %}</p>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    {% if selected_student %}
        // Grade Trends Chart
        const gradeTrendsCtx = document.getElementById('gradeTrendsChart').getContext('2d');
        const gradeTrendsChart = new Chart(gradeTrendsCtx, {
            type: 'line',
            data: {
                labels: {{ grade_trend_labels|safe|default:"[]" }},
                datasets: [{
                    label: '{% trans "Grade %" %}',
                    data: {{ grade_trend_data|safe|default:"[]" }},
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // Subject Radar Chart
        const subjectRadarCtx = document.getElementById('subjectRadarChart').getContext('2d');
        const subjectRadarChart = new Chart(subjectRadarCtx, {
            type: 'radar',
            data: {
                labels: {{ subject_names|safe|default:"[]" }},
                datasets: [{
                    label: '{% trans "Performance %" %}',
                    data: {{ subject_performance_data|safe|default:"[]" }},
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.2)',
                    pointBackgroundColor: '#28a745',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#28a745'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    {% endif %}

    function exportReport() {
        window.location.href = '{% url "reports:student_performance_export" %}?student={{ selected_student.id|default:"" }}';
    }

    function printReport() {
        window.print();
    }

    // Auto-submit form on student change
    document.getElementById('student_filter').addEventListener('change', function() {
        this.form.submit();
    });
</script>
{% endblock %}
