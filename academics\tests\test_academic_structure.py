import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from datetime import date, timedelta
from decimal import Decimal

from core.models import School, AcademicYear, Semester
from students.models import Grade, Class, Student, Parent
from academics.models import (
    Subject, Teacher, ClassSubject, Schedule, Exam, StudentGrade,
    StudentAttendance, Curriculum, CurriculumSubject, CurriculumPlan,
    GradeCapacityManagement
)

User = get_user_model()


class SubjectModelTest(TestCase):
    """Test cases for the enhanced Subject model"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1,
            max_capacity=30
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )

    def test_subject_creation(self):
        """Test basic subject creation"""
        subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            name_ar="الرياضيات",
            code="MATH101",
            subject_type="mathematics",
            credit_hours=3,
            weekly_hours=5,
            created_by=self.user
        )
        
        self.assertEqual(subject.name, "Mathematics")
        self.assertEqual(subject.code, "MATH101")
        self.assertEqual(subject.credit_hours, 3)
        self.assertEqual(subject.weekly_hours, 5)
        self.assertEqual(str(subject), "MATH101 - Mathematics")

    def test_subject_validation(self):
        """Test subject validation rules"""
        # Test credit hours validation
        with self.assertRaises(ValidationError):
            subject = Subject(
                school=self.school,
                name="Invalid Subject",
                code="INV001",
                credit_hours=15,  # Exceeds maximum
                weekly_hours=5,
                created_by=self.user
            )
            subject.full_clean()

    def test_prerequisite_management(self):
        """Test prerequisite functionality"""
        # Create prerequisite subject
        prereq_subject = Subject.objects.create(
            school=self.school,
            name="Basic Math",
            code="MATH001",
            credit_hours=2,
            weekly_hours=3,
            created_by=self.user
        )
        
        # Create main subject with prerequisite
        main_subject = Subject.objects.create(
            school=self.school,
            name="Advanced Math",
            code="MATH201",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        main_subject.prerequisites.add(prereq_subject)
        
        # Test prerequisite chain
        chain = main_subject.get_prerequisite_chain()
        self.assertIn(prereq_subject, chain)

    def test_circular_prerequisite_detection(self):
        """Test circular prerequisite detection"""
        subject1 = Subject.objects.create(
            school=self.school,
            name="Subject 1",
            code="SUB001",
            credit_hours=2,
            weekly_hours=3,
            created_by=self.user
        )
        
        subject2 = Subject.objects.create(
            school=self.school,
            name="Subject 2",
            code="SUB002",
            credit_hours=2,
            weekly_hours=3,
            created_by=self.user
        )
        
        # Create circular dependency
        subject1.prerequisites.add(subject2)
        subject2.prerequisites.add(subject1)
        
        # Test circular detection
        self.assertTrue(subject1.detect_circular_prerequisites())
        self.assertTrue(subject2.detect_circular_prerequisites())

    def test_subject_capacity_calculation(self):
        """Test subject capacity calculation"""
        subject = Subject.objects.create(
            school=self.school,
            name="Test Subject",
            code="TEST001",
            credit_hours=2,
            weekly_hours=3,
            max_students_per_class=25,
            created_by=self.user
        )
        
        # Test available capacity calculation
        capacity = subject.get_available_capacity(self.academic_year)
        self.assertIsInstance(capacity, int)

    def test_difficulty_level_calculation(self):
        """Test difficulty level calculation"""
        # Beginner level (no prerequisites)
        beginner_subject = Subject.objects.create(
            school=self.school,
            name="Beginner Subject",
            code="BEG001",
            credit_hours=2,
            weekly_hours=3,
            created_by=self.user
        )
        self.assertEqual(beginner_subject.get_difficulty_level(), 'Beginner')
        
        # Advanced level (with prerequisites)
        prereq1 = Subject.objects.create(
            school=self.school,
            name="Prereq 1",
            code="PRE001",
            credit_hours=2,
            weekly_hours=3,
            created_by=self.user
        )
        
        prereq2 = Subject.objects.create(
            school=self.school,
            name="Prereq 2",
            code="PRE002",
            credit_hours=2,
            weekly_hours=3,
            created_by=self.user
        )
        
        advanced_subject = Subject.objects.create(
            school=self.school,
            name="Advanced Subject",
            code="ADV001",
            credit_hours=4,
            weekly_hours=6,
            created_by=self.user
        )
        advanced_subject.prerequisites.add(prereq1, prereq2)
        
        self.assertEqual(advanced_subject.get_difficulty_level(), 'Advanced')

    def test_workload_estimation(self):
        """Test workload estimation"""
        subject = Subject.objects.create(
            school=self.school,
            name="Test Subject",
            code="TEST001",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        
        workload = subject.get_workload_estimate()
        expected_workload = 3 * 2.5 + 4  # credit_hours * 2.5 + weekly_hours
        self.assertEqual(workload, expected_workload)


class GradeCapacityManagementTest(TestCase):
    """Test cases for Grade Capacity Management"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1,
            max_capacity=60
        )

    def test_capacity_management_creation(self):
        """Test grade capacity management creation"""
        capacity_mgmt = GradeCapacityManagement.objects.create(
            school=self.school,
            grade=self.grade,
            academic_year=self.academic_year,
            total_capacity=60,
            minimum_class_size=15,
            maximum_class_size=30
        )
        
        self.assertEqual(capacity_mgmt.total_capacity, 60)
        self.assertEqual(capacity_mgmt.available_capacity, 60)
        self.assertFalse(capacity_mgmt.is_full)

    def test_capacity_validation(self):
        """Test capacity management validation"""
        with self.assertRaises(ValidationError):
            capacity_mgmt = GradeCapacityManagement(
                school=self.school,
                grade=self.grade,
                academic_year=self.academic_year,
                total_capacity=60,
                minimum_class_size=35,  # Greater than maximum
                maximum_class_size=30
            )
            capacity_mgmt.full_clean()

    def test_enrollment_percentage_calculation(self):
        """Test enrollment percentage calculation"""
        capacity_mgmt = GradeCapacityManagement.objects.create(
            school=self.school,
            grade=self.grade,
            academic_year=self.academic_year,
            total_capacity=60,
            current_enrollment=30
        )
        
        self.assertEqual(capacity_mgmt.enrollment_percentage, 50.0)

    def test_section_creation_logic(self):
        """Test automatic section creation logic"""
        capacity_mgmt = GradeCapacityManagement.objects.create(
            school=self.school,
            grade=self.grade,
            academic_year=self.academic_year,
            total_capacity=60,
            current_enrollment=30,
            maximum_class_size=30,
            auto_create_sections=True
        )
        
        # Test suggested sections count
        suggested_sections = capacity_mgmt.get_suggested_sections_count()
        self.assertEqual(suggested_sections, 1)  # 30 students / 30 max = 1 section
        
        # Test new section creation
        new_class = capacity_mgmt.create_new_section("Section A")
        self.assertEqual(new_class.name, "Section A")
        self.assertEqual(new_class.grade, self.grade)

    def test_enrollment_eligibility(self):
        """Test enrollment eligibility checking"""
        capacity_mgmt = GradeCapacityManagement.objects.create(
            school=self.school,
            grade=self.grade,
            academic_year=self.academic_year,
            total_capacity=60,
            current_enrollment=60,  # Full capacity
            is_enrollment_open=True
        )
        
        can_enroll, message = capacity_mgmt.can_enroll_student()
        self.assertFalse(can_enroll)
        self.assertIn("full capacity", message)


class CurriculumPlanTest(TestCase):
    """Test cases for Curriculum Planning"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1,
            max_capacity=30
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )

    def test_curriculum_plan_creation(self):
        """Test curriculum plan creation"""
        curriculum = CurriculumPlan.objects.create(
            school=self.school,
            name="Primary Curriculum",
            code="PRI2024",
            curriculum_type="national",
            academic_year=self.academic_year,
            objectives="Primary education objectives",
            total_credit_hours=120,
            minimum_credit_hours=100,
            effective_date=date.today(),
            created_by=self.user
        )
        
        self.assertEqual(curriculum.name, "Primary Curriculum")
        self.assertEqual(curriculum.code, "PRI2024")
        self.assertTrue(curriculum.is_current)

    def test_curriculum_validation(self):
        """Test curriculum validation rules"""
        with self.assertRaises(ValidationError):
            curriculum = CurriculumPlan(
                school=self.school,
                name="Invalid Curriculum",
                code="INV2024",
                academic_year=self.academic_year,
                total_credit_hours=100,
                minimum_credit_hours=120,  # Greater than total
                effective_date=date.today(),
                created_by=self.user
            )
            curriculum.full_clean()

    def test_curriculum_structure_validation(self):
        """Test curriculum structure validation"""
        curriculum = CurriculumPlan.objects.create(
            school=self.school,
            name="Test Curriculum",
            code="TEST2024",
            academic_year=self.academic_year,
            objectives="Test objectives",
            total_credit_hours=60,
            core_subjects_required=5,
            effective_date=date.today(),
            created_by=self.user
        )
        
        # Create subjects
        subject1 = Subject.objects.create(
            school=self.school,
            name="Math",
            code="MATH001",
            subject_type="core",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        
        subject2 = Subject.objects.create(
            school=self.school,
            name="Science",
            code="SCI001",
            subject_type="core",
            credit_hours=3,
            weekly_hours=4,
            created_by=self.user
        )
        
        # Add subjects to curriculum
        CurriculumSubject.objects.create(
            school=self.school,
            curriculum=curriculum,
            subject=subject1,
            credit_hours=3,
            weekly_hours=4,
            learning_outcomes="Math outcomes",
            assessment_criteria="Math assessment"
        )
        
        CurriculumSubject.objects.create(
            school=self.school,
            curriculum=curriculum,
            subject=subject2,
            credit_hours=3,
            weekly_hours=4,
            learning_outcomes="Science outcomes",
            assessment_criteria="Science assessment"
        )
        
        # Test structure validation
        errors = curriculum.validate_curriculum_structure()
        self.assertIsInstance(errors, list)


class CurriculumSubjectTest(TestCase):
    """Test cases for Curriculum Subject mapping"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1,
            max_capacity=30
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            user_type="admin"
        )
        
        self.curriculum = CurriculumPlan.objects.create(
            school=self.school,
            name="Test Curriculum",
            code="TEST2024",
            academic_year=self.academic_year,
            objectives="Test objectives",
            total_credit_hours=60,
            effective_date=date.today(),
            created_by=self.user
        )
        
        self.subject = Subject.objects.create(
            school=self.school,
            name="Mathematics",
            code="MATH001",
            credit_hours=4,
            weekly_hours=5,
            created_by=self.user
        )

    def test_curriculum_subject_creation(self):
        """Test curriculum subject mapping creation"""
        curriculum_subject = CurriculumSubject.objects.create(
            school=self.school,
            curriculum=self.curriculum,
            subject=self.subject,
            credit_hours=3,
            weekly_hours=4,
            semester="first",
            sequence_order=1,
            learning_outcomes="Math learning outcomes",
            assessment_criteria="Math assessment criteria",
            assessment_breakdown={"midterm": 30, "final": 40, "assignments": 30}
        )
        
        self.assertEqual(curriculum_subject.credit_hours, 3)
        self.assertEqual(curriculum_subject.weekly_hours, 4)
        self.assertEqual(curriculum_subject.semester, "first")

    def test_assessment_breakdown_validation(self):
        """Test assessment breakdown validation"""
        with self.assertRaises(ValidationError):
            curriculum_subject = CurriculumSubject(
                school=self.school,
                curriculum=self.curriculum,
                subject=self.subject,
                credit_hours=3,
                weekly_hours=4,
                learning_outcomes="Test outcomes",
                assessment_criteria="Test criteria",
                assessment_breakdown={"midterm": 30, "final": 40, "assignments": 20}  # Total = 90%
            )
            curriculum_subject.full_clean()

    def test_workload_calculation(self):
        """Test workload calculation"""
        curriculum_subject = CurriculumSubject.objects.create(
            school=self.school,
            curriculum=self.curriculum,
            subject=self.subject,
            credit_hours=3,
            weekly_hours=4,
            learning_outcomes="Test outcomes",
            assessment_criteria="Test criteria",
            assessment_breakdown={"midterm": 30, "final": 40, "assignments": 30}
        )
        
        workload = curriculum_subject.calculate_workload()
        
        self.assertEqual(workload['weekly_teaching_hours'], 4)
        self.assertEqual(workload['weekly_study_hours'], 10.0)  # 4 * 2.5
        self.assertEqual(workload['credit_hours'], 3)

    def test_assessment_schedule_generation(self):
        """Test assessment schedule generation"""
        curriculum_subject = CurriculumSubject.objects.create(
            school=self.school,
            curriculum=self.curriculum,
            subject=self.subject,
            credit_hours=3,
            weekly_hours=4,
            learning_outcomes="Test outcomes",
            assessment_criteria="Test criteria",
            assessment_breakdown={"midterm": 30, "final": 40, "quiz": 30}
        )
        
        schedule = curriculum_subject.generate_assessment_schedule(self.academic_year)
        
        self.assertEqual(len(schedule), 3)
        self.assertTrue(all('assessment_type' in item for item in schedule))
        self.assertTrue(all('suggested_week' in item for item in schedule))

    def test_prerequisite_sequence_checking(self):
        """Test prerequisite sequence validation"""
        # Create prerequisite subject
        prereq_subject = Subject.objects.create(
            school=self.school,
            name="Basic Math",
            code="MATH000",
            credit_hours=2,
            weekly_hours=3,
            created_by=self.user
        )
        
        # Add prerequisite relationship
        self.subject.prerequisites.add(prereq_subject)
        
        # Create curriculum mappings
        prereq_mapping = CurriculumSubject.objects.create(
            school=self.school,
            curriculum=self.curriculum,
            subject=prereq_subject,
            credit_hours=2,
            weekly_hours=3,
            semester="first",
            sequence_order=1,
            learning_outcomes="Basic math outcomes",
            assessment_criteria="Basic math criteria"
        )
        
        main_mapping = CurriculumSubject.objects.create(
            school=self.school,
            curriculum=self.curriculum,
            subject=self.subject,
            credit_hours=3,
            weekly_hours=4,
            semester="second",
            sequence_order=1,
            learning_outcomes="Advanced math outcomes",
            assessment_criteria="Advanced math criteria"
        )
        
        # Test prerequisite sequence checking
        errors = main_mapping.check_prerequisite_sequence()
        self.assertEqual(len(errors), 0)  # Should be no errors with correct sequence


@pytest.mark.django_db
class TestAcademicStructureIntegration:
    """Integration tests for academic structure models"""
    
    def test_complete_academic_structure_setup(self):
        """Test setting up a complete academic structure"""
        # Create school and academic year
        school = School.objects.create(
            name="Integration Test School",
            code="ITS",
            address="Test Address",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        academic_year = AcademicYear.objects.create(
            school=school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        semester = Semester.objects.create(
            school=school,
            academic_year=academic_year,
            name="First Semester",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 1, 31),
            is_current=True
        )
        
        # Create grade
        grade = Grade.objects.create(
            school=school,
            name="Grade 1",
            level=1,
            max_capacity=60
        )
        
        # Create capacity management
        capacity_mgmt = GradeCapacityManagement.objects.create(
            school=school,
            grade=grade,
            academic_year=academic_year,
            total_capacity=60,
            maximum_class_size=30
        )
        
        # Create class
        class_obj = Class.objects.create(
            school=school,
            name="Section A",
            grade=grade,
            academic_year=academic_year,
            max_students=30
        )
        
        # Create user and teacher
        user = User.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="testpass123",
            user_type="teacher"
        )
        
        teacher = Teacher.objects.create(
            school=school,
            user=user,
            employee_id="T001",
            hire_date=date.today(),
            qualification="Master's in Mathematics"
        )
        
        # Create subject
        subject = Subject.objects.create(
            school=school,
            name="Mathematics",
            code="MATH001",
            subject_type="mathematics",
            credit_hours=3,
            weekly_hours=5,
            created_by=user
        )
        
        # Add subject to teacher's qualifications
        teacher.subjects.add(subject)
        
        # Create curriculum plan
        curriculum = CurriculumPlan.objects.create(
            school=school,
            name="Primary Mathematics Curriculum",
            code="PMC2024",
            academic_year=academic_year,
            objectives="Primary math education",
            total_credit_hours=120,
            effective_date=date.today(),
            created_by=user
        )
        
        # Add grade to curriculum
        curriculum.grades.add(grade)
        
        # Create curriculum subject mapping
        curriculum_subject = CurriculumSubject.objects.create(
            school=school,
            curriculum=curriculum,
            subject=subject,
            credit_hours=3,
            weekly_hours=5,
            semester="first",
            learning_outcomes="Basic arithmetic skills",
            assessment_criteria="Tests and assignments",
            assessment_breakdown={"midterm": 30, "final": 40, "assignments": 30}
        )
        
        # Add grade to curriculum subject
        curriculum_subject.applicable_grades.add(grade)
        
        # Create class subject assignment
        class_subject = ClassSubject.objects.create(
            school=school,
            class_obj=class_obj,
            subject=subject,
            teacher=teacher,
            academic_year=academic_year,
            semester=semester,
            weekly_hours=5
        )
        
        # Verify the complete structure
        assert school.name == "Integration Test School"
        assert academic_year.is_current is True
        assert grade.max_capacity == 60
        assert capacity_mgmt.total_capacity == 60
        assert class_obj.grade == grade
        assert teacher.subjects.filter(pk=subject.pk).exists()
        assert curriculum.grades.filter(pk=grade.pk).exists()
        assert curriculum_subject.curriculum == curriculum
        assert class_subject.teacher == teacher
        
        # Test capacity calculations
        assert capacity_mgmt.available_capacity == 60
        assert not capacity_mgmt.is_full
        
        # Test subject methods
        workload = subject.get_workload_estimate()
        assert workload > 0
        
        difficulty = subject.get_difficulty_level()
        assert difficulty == 'Beginner'
        
        # Test curriculum methods
        assert curriculum.is_current is True
        
        distribution = curriculum.get_subject_distribution()
        assert 'mathematics' in distribution
        
        # Test curriculum subject methods
        workload_data = curriculum_subject.calculate_workload()
        assert workload_data['weekly_teaching_hours'] == 5
        assert workload_data['credit_hours'] == 3
        
        # Test class subject methods
        assert class_subject.effective_max_students == 30
        assert class_subject.current_enrollment == 0
        assert class_subject.available_capacity == 30