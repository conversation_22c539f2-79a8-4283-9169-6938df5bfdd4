from django import template
from django.utils.translation import gettext as _
from django.utils.safestring import mark_safe
from django.urls import reverse
from django.db.models import Count, Sum
from django.utils import timezone
from students.models import Student, Class
from academics.models import Teacher, Subject
from hr.models import Employee, Department
import json

register = template.Library()


@register.inclusion_tag('components/stat_card.html')
def stat_card(icon, number, label, color='primary', url=None):
    """
    Render a statistics card component
    """
    return {
        'icon': icon,
        'number': number,
        'label': label,
        'color': color,
        'url': url,
    }


@register.inclusion_tag('components/quick_action_btn.html')
def quick_action_btn(url, icon, label, color='primary'):
    """
    Render a quick action button component
    """
    return {
        'url': url,
        'icon': icon,
        'label': label,
        'color': color,
    }


@register.inclusion_tag('components/data_table.html')
def data_table(headers, rows, actions=None, table_id='data-table'):
    """
    Render a data table component
    """
    return {
        'headers': headers,
        'rows': rows,
        'actions': actions,
        'table_id': table_id,
    }


@register.inclusion_tag('components/filter_form.html')
def filter_form(form_fields, action_url='', method='get'):
    """
    Render a filter form component
    """
    return {
        'form_fields': form_fields,
        'action_url': action_url,
        'method': method,
    }


@register.inclusion_tag('components/pagination.html')
def pagination_nav(page_obj, request):
    """
    Render pagination navigation
    """
    return {
        'page_obj': page_obj,
        'request': request,
    }


@register.simple_tag
def get_student_stats():
    """
    Get student statistics
    """
    return {
        'total': Student.objects.filter(is_active=True).count(),
        'by_grade': Student.objects.filter(is_active=True).values(
            'current_class__grade__name'
        ).annotate(count=Count('id')),
        'new_this_month': Student.objects.filter(
            is_active=True,
            created_at__month=timezone.now().month
        ).count(),
    }


@register.simple_tag
def get_teacher_stats():
    """
    Get teacher statistics
    """
    return {
        'total': Teacher.objects.filter(is_active=True).count(),
        'by_subject': Teacher.objects.filter(is_active=True).values(
            'subjects__name'
        ).annotate(count=Count('id')),
    }


@register.simple_tag
def get_class_stats():
    """
    Get class statistics
    """
    return {
        'total': Class.objects.filter(is_active=True).count(),
        'by_grade': Class.objects.filter(is_active=True).values(
            'grade__name'
        ).annotate(count=Count('id')),
        'average_students': Class.objects.filter(is_active=True).annotate(
            student_count=Count('students')
        ).aggregate(avg=Sum('student_count'))['avg'] or 0,
    }


@register.filter
def get_item(dictionary, key):
    """
    Get item from dictionary by key
    """
    return dictionary.get(key)


@register.filter
def percentage(value, total):
    """
    Calculate percentage
    """
    if not total or total == 0:
        return 0
    return round((value / total) * 100, 1)


@register.filter
def mul(value, arg):
    """
    Multiply value by argument
    """
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0


@register.filter
def div(value, arg):
    """
    Divide value by argument
    """
    try:
        if float(arg) == 0:
            return 0
        return float(value) / float(arg)
    except (ValueError, TypeError):
        return 0


@register.simple_tag
def user_can_access(user, permission):
    """
    Check if user has permission
    """
    return user.has_perm(permission)


@register.filter
def split(value, delimiter):
    """Split string by delimiter."""
    return value.split(delimiter)


@register.filter
def get(dictionary, key):
    """Get value from dictionary by key."""
    if hasattr(dictionary, 'get'):
        return dictionary.get(key)
    return None


@register.filter
def filter_by_time(schedule_list, time_slot):
    """Filter schedule items by time slot."""
    if not schedule_list:
        return None

    try:
        from datetime import datetime
        start_time_str, end_time_str = time_slot.split('-')
        start_time = datetime.strptime(start_time_str, '%H:%M').time()
        end_time = datetime.strptime(end_time_str, '%H:%M').time()

        for schedule in schedule_list:
            if schedule.start_time and schedule.end_time:
                if schedule.start_time <= start_time and schedule.end_time >= end_time:
                    return schedule
                # Also check if the schedule overlaps with the time slot
                if (schedule.start_time <= start_time < schedule.end_time or
                    schedule.start_time < end_time <= schedule.end_time):
                    return schedule
        return None
    except (ValueError, AttributeError):
        return None


@register.filter
def json_script(value, element_id=None):
    """
    Convert Python object to JSON for use in JavaScript.
    Similar to Django's json_script but as a filter.
    """
    json_str = json.dumps(value, ensure_ascii=False)
    if element_id:
        return mark_safe(f'<script id="{element_id}" type="application/json">{json_str}</script>')
    return mark_safe(json_str)


@register.inclusion_tag('components/breadcrumb.html')
def breadcrumb(items):
    """
    Render breadcrumb navigation
    """
    return {'items': items}


@register.inclusion_tag('components/alert.html')
def alert(message, alert_type='info', dismissible=True):
    """
    Render alert component
    """
    return {
        'message': message,
        'alert_type': alert_type,
        'dismissible': dismissible,
    }


@register.inclusion_tag('components/modal.html')
def modal(modal_id, title, body_content='', footer_content='', size=''):
    """
    Render modal component
    """
    return {
        'modal_id': modal_id,
        'title': title,
        'body_content': body_content,
        'footer_content': footer_content,
        'size': size,
    }


# Navigation Tags
@register.simple_tag(takes_context=True)
def breadcrumb_nav(context):
    """Generate breadcrumb navigation based on current URL"""
    request = context['request']
    current_url = request.resolver_match

    if not current_url:
        return ""

    breadcrumbs = []

    # Home breadcrumb - use safe URL resolution
    try:
        home_url = reverse('accounts:dashboard')
    except:
        home_url = '/'

    breadcrumbs.append({
        'name': _('Home'),
        'url': home_url,
        'icon': 'fas fa-home',
        'active': False
    })

    # Helper function to safely get URL
    def safe_reverse(url_name, default='#'):
        try:
            return reverse(url_name)
        except:
            return default

    # App-specific breadcrumbs
    app_name = current_url.app_name
    url_name = current_url.url_name

    if app_name == 'students':
        breadcrumbs.append({
            'name': _('Students'),
            'url': safe_reverse('students:dashboard'),
            'icon': 'fas fa-user-graduate',
            'active': url_name == 'dashboard'
        })

    elif app_name == 'academics':
        breadcrumbs.append({
            'name': _('Academics'),
            'url': safe_reverse('academics:dashboard'),
            'icon': 'fas fa-graduation-cap',
            'active': url_name == 'dashboard'
        })

        if url_name in ['subjects', 'subject_create', 'subject_detail', 'subject_edit']:
            breadcrumbs.append({
                'name': _('Subjects'),
                'url': safe_reverse('academics:subjects'),
                'icon': 'fas fa-book',
                'active': url_name in ['subjects', 'subject_create']
            })

        elif url_name in ['schedules', 'schedule_create', 'schedule_detail', 'schedule_edit', 'timetable']:
            breadcrumbs.append({
                'name': _('Schedules'),
                'url': safe_reverse('academics:schedules'),
                'icon': 'fas fa-calendar',
                'active': url_name in ['schedules', 'schedule_create']
            })

        elif url_name in ['exams', 'exam_add', 'exam_detail', 'exam_edit']:
            breadcrumbs.append({
                'name': _('Exams'),
                'url': safe_reverse('academics:exams'),
                'icon': 'fas fa-clipboard-list',
                'active': url_name in ['exams', 'exam_add']
            })

        elif url_name in ['grades', 'grade_create', 'grade_detail', 'grade_update']:
            breadcrumbs.append({
                'name': _('Grades'),
                'url': safe_reverse('academics:grades'),
                'icon': 'fas fa-star',
                'active': url_name in ['grades', 'grade_create']
            })

        elif url_name in ['attendance', 'attendance_create', 'attendance_detail', 'attendance_update']:
            breadcrumbs.append({
                'name': _('Attendance'),
                'url': safe_reverse('academics:attendance'),
                'icon': 'fas fa-user-check',
                'active': url_name in ['attendance', 'attendance_create']
            })

    elif app_name == 'hr':
        breadcrumbs.append({
            'name': _('Human Resources'),
            'url': safe_reverse('hr:dashboard'),
            'icon': 'fas fa-users-cog',
            'active': url_name == 'dashboard'
        })

    elif app_name == 'finance':
        breadcrumbs.append({
            'name': _('Finance'),
            'url': safe_reverse('finance:dashboard'),
            'icon': 'fas fa-money-bill-wave',
            'active': url_name == 'dashboard'
        })

    elif app_name == 'reports':
        breadcrumbs.append({
            'name': _('Reports'),
            'url': safe_reverse('reports:dashboard'),
            'icon': 'fas fa-chart-bar',
            'active': url_name == 'dashboard'
        })

    elif app_name == 'core':
        breadcrumbs.append({
            'name': _('Administration'),
            'url': safe_reverse('core:dashboard'),
            'icon': 'fas fa-cog',
            'active': url_name == 'dashboard'
        })

    # Generate HTML
    html = '<nav aria-label="breadcrumb"><ol class="breadcrumb modern-breadcrumb">'

    for i, crumb in enumerate(breadcrumbs):
        if i == len(breadcrumbs) - 1:  # Last item
            html += f'''
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="{crumb['icon']} me-1"></i>
                    {crumb['name']}
                </li>
            '''
        else:
            html += f'''
                <li class="breadcrumb-item">
                    <a href="{crumb['url']}" class="breadcrumb-link">
                        <i class="{crumb['icon']} me-1"></i>
                        {crumb['name']}
                    </a>
                </li>
            '''

    html += '</ol></nav>'

    return mark_safe(html)


@register.simple_tag(takes_context=True)
def section_nav(context):
    """Generate section navigation for easy switching between modules"""
    request = context['request']
    user = request.user

    if not user.is_authenticated:
        return ""

    # Helper function to safely get URL
    def safe_reverse(url_name, default='/'):
        try:
            return reverse(url_name)
        except:
            return default

    sections = [
        {
            'name': _('Dashboard'),
            'url': safe_reverse('accounts:dashboard'),
            'icon': 'fas fa-home',
            'color': 'primary',
            'active': request.resolver_match.app_name == 'accounts'
        },
        {
            'name': _('Students'),
            'url': safe_reverse('students:dashboard'),
            'icon': 'fas fa-user-graduate',
            'color': 'success',
            'active': request.resolver_match.app_name == 'students'
        },
        {
            'name': _('Academics'),
            'url': safe_reverse('academics:dashboard'),
            'icon': 'fas fa-graduation-cap',
            'color': 'info',
            'active': request.resolver_match.app_name == 'academics'
        },
        {
            'name': _('HR'),
            'url': safe_reverse('hr:dashboard'),
            'icon': 'fas fa-users-cog',
            'color': 'warning',
            'active': request.resolver_match.app_name == 'hr'
        },
        {
            'name': _('Finance'),
            'url': safe_reverse('finance:dashboard'),
            'icon': 'fas fa-money-bill-wave',
            'color': 'danger',
            'active': request.resolver_match.app_name == 'finance'
        },
        {
            'name': _('Reports'),
            'url': safe_reverse('reports:dashboard'),
            'icon': 'fas fa-chart-bar',
            'color': 'secondary',
            'active': request.resolver_match.app_name == 'reports'
        }
    ]

    # Add admin section for admin users
    if hasattr(user, 'user_type') and user.user_type == 'admin':
        sections.append({
            'name': _('Admin'),
            'url': safe_reverse('core:dashboard'),
            'icon': 'fas fa-cog',
            'color': 'dark',
            'active': request.resolver_match.app_name == 'core'
        })

    # Generate HTML
    html = '<div class="section-nav-container"><div class="section-nav">'

    for section in sections:
        active_class = 'active' if section['active'] else ''
        html += f'''
            <a href="{section['url']}" class="section-nav-item {active_class}" data-color="{section['color']}">
                <div class="section-nav-icon">
                    <i class="{section['icon']}"></i>
                </div>
                <span class="section-nav-label">{section['name']}</span>
            </a>
        '''

    html += '</div></div>'

    return mark_safe(html)


@register.simple_tag(takes_context=True)
def current_section_title(context):
    """Get the current section title"""
    request = context['request']
    app_name = request.resolver_match.app_name if request.resolver_match else None

    titles = {
        'accounts': _('Dashboard'),
        'students': _('Student Management'),
        'academics': _('Academic Management'),
        'hr': _('Human Resources'),
        'finance': _('Financial Management'),
        'reports': _('Reports & Analytics'),
        'core': _('System Administration')
    }

    return titles.get(app_name, _('School ERP System'))
